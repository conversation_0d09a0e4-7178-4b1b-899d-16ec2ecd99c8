<div #overlayContainer></div>
<p-panel [header]="'workOrdersWidget.title' | translate" [toggleable]="true">
    <ng-template pTemplate="icons">
        <button
            type="button"
            pButton
            pRipple
            class="p-button-outlined"
            *ngIf="showCreateWorkOrderButton()"
            [disabled]="isProcessing"
            (click)="createWorkOrderButtonClick()"
            size="small">
            {{ 'workOrdersWidget.createWorkOrder' | translate }}
        </button>
    </ng-template>

    <app-create-work-order-widget *ngIf="showCreateWorkOrder" (closeWidget)="closeCreateWorkOrder()">
    </app-create-work-order-widget>

    <div *ngIf="!hasWorkOrderReadPermission" fxFlex="nogrow" fxLayoutAlign="start center">
        <p-tag severity="warn" value="{{ 'common.lackOfPermissions' | translate }}"> </p-tag>
    </div>
    <app-work-orders-list-widget *ngIf="hasWorkOrderReadPermission"> </app-work-orders-list-widget>
</p-panel>
