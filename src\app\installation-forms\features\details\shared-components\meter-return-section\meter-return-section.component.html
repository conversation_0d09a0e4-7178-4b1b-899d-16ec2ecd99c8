<div #overlayContainer></div>
<div [formGroup]="form">
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['meterReturnReturnOption']" for="meterReturnReturnOption">
                {{ 'meterReturnWidget.returnOption' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="meterReturnReturnOption"
                [options]="returnOptionOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                [showClear]="true"
                formControlName="meterReturnReturnOption">
            </p-select>
            <small [controlValidationErrors]="form.controls['meterReturnReturnOption']" class="p-error"></small>
        </div>
    </div>

    <ng-container *ngIf="this.isGridCompanyMeterReturnSectionVisible()">
        <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label [labelRequired]="form.controls['meterReturnName']" for="meterReturnName">
                    {{ 'meterReturnWidget.name' | translate }}
                </label>
            </div>
            <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                <input id="meterReturnName" type="text" pInputText maxlength="100" formControlName="meterReturnName" />
                <small [controlValidationErrors]="form.controls['meterReturnName']" class="p-error"></small>
            </div>
        </div>

        <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label for="meterReturnAttention">{{ 'meterReturnWidget.attention' | translate }} </label>
            </div>
            <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                <input id="meterReturnAttention" type="text" pInputText maxlength="100" formControlName="meterReturnAttention" />
                <small [controlValidationErrors]="form.controls['meterReturnAttention']" class="p-error"></small>
            </div>
        </div>

        <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label [labelRequired]="form.controls['meterReturnAddress']" for="meterReturnAddress">
                    {{ 'meterReturnWidget.address' | translate }}
                </label>
            </div>
            <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                <app-address-search id="meterReturnAddress" formControlName="meterReturnAddress"> </app-address-search>
                <small [controlValidationErrors]="form.controls['meterReturnAddress']" class="p-error"></small>
            </div>
        </div>
    </ng-container>
</div>
