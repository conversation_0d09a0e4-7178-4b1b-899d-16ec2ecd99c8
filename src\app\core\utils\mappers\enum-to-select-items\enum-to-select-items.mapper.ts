import { SelectItem } from 'primeng/api';

export const enumMapper = {
    map<Type extends {}>(translations: any, type: Type): SelectItem[] {
        return Object.values(type)
            .filter((x) => typeof x === 'string')
            .map((x) => {
                return { value: x, label: translations[x as keyof typeof translations] ?? x } as SelectItem;
            });
    },
    mapArray<Type extends {}>(translations: any, items: Type[]): SelectItem[] {
        return items.map((item) => {
            return { value: item, label: translations[item] ?? item } as SelectItem;
        });
    }
};
