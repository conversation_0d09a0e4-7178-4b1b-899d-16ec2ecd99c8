<div fxLayoutGap="20px" fxLayout="row" fxLayout.lt-sm="column" fxLayoutAlign="center" fxFlexFill>
    <div fxFlex="20">
        <p-card id="typeAndStateCard" [header]="'details.typeAndState' | translate" styleClass="box-shadow">
            <h2 *ngIf="hasCategory">
                {{ 'enums.type.' + type | translate }} - {{ 'enums.category.' + category | translate }},
                {{ 'enums.state.' + state | translate }}
            </h2>
            <h2 *ngIf="!hasCategory">{{ 'enums.type.' + type | translate }}, {{ 'enums.state.' + state | translate }}</h2>
        </p-card>
    </div>

    <div fxFlex="20">
        <p-card id="installationAddressCard" [header]="'details.installationAddress' | translate" styleClass="box-shadow">
            <h2>
                {{ installationAddress.formattedAddress }}
            </h2>
        </p-card>
    </div>

    <div fxFlex="20">
        <p-card id="creationDateTimeCard" [header]="'details.importantDates' | translate" styleClass="box-shadow">
            <h2>{{ 'details.creationDateTime' | translate }}</h2>
            <div>{{ creationDateTime | formatDateTime }}</div>
            <h2 *ngIf="lastChangedDateTime">{{ 'details.lastChanged' | translate }}</h2>
            <div>{{ lastChangedDateTime | formatDateTime }}</div>
        </p-card>
    </div>

    <div fxFlex="20">
        <p-card id="meterNumberCard" [header]="'details.meterNumber' | translate" styleClass="box-shadow">
            <h2>
                {{ meterNumber }}
            </h2>
        </p-card>
    </div>

    <div fxFlex="20">
        <p-card id="screeningStatusCard" [header]="'details.screeningStatus' | translate" styleClass="box-shadow">
            <div *ngIf="screeningStatus">
                <h2>
                    {{ 'enums.screeningStatus.' + screeningStatus | translate }}
                </h2>
            </div>
        </p-card>
    </div>
</div>
