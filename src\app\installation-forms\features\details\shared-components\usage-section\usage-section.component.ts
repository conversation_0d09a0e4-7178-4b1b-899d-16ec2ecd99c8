import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { Subscription } from 'rxjs';
import {
    ApplicationEntry,
    ApplicationType,
    ApplicationTypeToAllowedFieldsConfigurationEntry
} from 'src/app/api/installation-forms-client';
import { MEDIUM_TABLE_ROWS_PER_PAGE, PhaseCountOptions } from 'src/app/core/constants/constants';
import { ApplicationsConfigService } from 'src/app/core/services/applications-config/applications-config.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import conditionallyRequiredValidator from 'src/app/core/utils/validators/conditionally-required/conditionally-required.validator';
import { WidgetWithFormComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget-with-form.component';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';

@Component({
    selector: 'app-usage-section',
    templateUrl: './usage-section.component.html',
    standalone: false
})
export class UsageSectionComponent extends WidgetWithFormComponent implements OnInit, OnDestroy {
    @Input() form!: FormGroup;

    applicationTypeOptions: SelectItem[] = [];

    phaseCountOptions: SelectItem[] = PhaseCountOptions;

    //should be moved to store
    typeToAllowedFields: ApplicationTypeToAllowedFieldsConfigurationEntry[] = [];

    configurationForSelectedType?: ApplicationTypeToAllowedFieldsConfigurationEntry;

    isAddingOpened: boolean = false;
    addApplicationForm: FormGroup;

    private commonTranslations: any;
    subscription: Subscription = new Subscription();

    rowsPerPage = MEDIUM_TABLE_ROWS_PER_PAGE;

    constructor(
        private readonly fb: FormBuilder,
        private readonly translateService: TranslateService,
        private readonly messageServiceHelper: MessageServiceHelper,
        private readonly applicationsConfigService: ApplicationsConfigService,
        protected readonly formDataService: FormDataService
    ) {
        super(formDataService);
        this.addApplicationForm = this.fb.group({
            applicationType: [null, Validators.required],
            effect: [null, (control: AbstractControl) => conditionallyRequiredValidator(control, this.isEffectRequired)],
            phaseCount: [null, (control: AbstractControl) => conditionallyRequiredValidator(control, this.isPhaseCountRequired)],
            remark: [null, (control: AbstractControl) => conditionallyRequiredValidator(control, this.isRemarkRequired)]
        });
    }

    ngOnInit(): void {
        this.commonTranslations = this.translateService.instant('common');

        this.subscription.add(
            this.applicationsConfigService.getConfigData(this.formDataService.supplyType!).subscribe({
                next: (result) => {
                    this.getEnumTranslations(
                        result.categoryToAllowedTypes.find((obj) => obj.category === this.formDataService.category)
                            ?.allowedApplicationTypes
                    );
                    this.typeToAllowedFields = result.typeToAllowedFields;
                },
                error: (_) => {
                    this.messageServiceHelper.showError({
                        key: this.formDataService.formId!
                    });
                }
            })
        );

        this.subscription.add(
            this.addApplicationForm.get('applicationType')?.valueChanges.subscribe((_) => {
                let applicationType = this.addApplicationForm.get('applicationType')?.value;
                if (applicationType) {
                    this.configurationForSelectedType = this.typeToAllowedFields.find((x) => x.type === applicationType);
                } else {
                    this.configurationForSelectedType = undefined;
                }

                this.addApplicationForm.patchValue({
                    effect: null,
                    phaseCount: null,
                    remark: null
                });
            })
        );
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    createApplication() {
        this.isAddingOpened = true;
    }

    cancelCreateApplication() {
        this.isAddingOpened = false;
        this.addApplicationForm.reset();
    }

    saveApplication() {
        if (!this.addApplicationForm.valid) {
            this.messageServiceHelper.showWarning({
                detail: this.commonTranslations['allRequiredFieldsShouldBeCompleted'],
                key: this.formDataService.formId!
            });
            return;
        }

        let applications = this.form.get('applications')?.value as ApplicationEntry[];

        const addApplicationFormValue = this.addApplicationForm.value;
        const newApplication = new ApplicationEntry({
            type: addApplicationFormValue.applicationType,
            effect: addApplicationFormValue.effect,
            remark: addApplicationFormValue.remark,
            phaseCount: addApplicationFormValue.phaseCount
        });

        applications.push(newApplication);
        this.form.patchValue({
            applications: [...applications],
            hasApplicationsChanged: true
        });
        this.addApplicationForm.reset();
        this.isAddingOpened = false;
    }

    removeItem(item: ApplicationEntry): void {
        let applications = this.form.get('applications')?.value as ApplicationEntry[];
        const index = applications.indexOf(item);
        if (index !== -1) {
            applications.splice(index, 1);
        }
        this.form.patchValue({
            applications: applications,
            hasApplicationsChanged: true
        });
    }

    private getEnumTranslations(applicationTypes: ApplicationType[] | undefined) {
        if (!applicationTypes) {
            return;
        }
        this.applicationTypeOptions = enumMapper.mapArray(
            this.translateService.instant('enums.applicationType'),
            applicationTypes
        );
    }

    isEffectRequired = () => {
        return !!this.configurationForSelectedType?.hasEffect;
    };

    isPhaseCountRequired = () => {
        return !!this.configurationForSelectedType?.hasPhaseCount;
    };

    isRemarkRequired = () => {
        return !!this.configurationForSelectedType?.hasRemark;
    };
}
