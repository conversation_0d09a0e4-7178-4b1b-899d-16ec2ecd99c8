import { Component, forwardRef, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { Subscription } from 'rxjs';
import { ValueListType } from 'src/app/api/installation-forms-client';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { ValueListsService } from 'src/app/core/services/value-lists/value-lists.service';
import { FormDataService } from '../../../core/services/form-data/form-data.service';

@Component({
    selector: 'app-value-list-multiselect',
    templateUrl: './value-list-multiselect.component.html',
    styleUrl: './value-list-multiselect.component.scss',
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => ValueListMultiselectComponent),
            multi: true
        }
    ],
    standalone: false,
    host: { hostID: crypto.randomUUID().toString() }
})
export class ValueListMultiselectComponent implements OnInit, OnDestroy, ControlValueAccessor {
    @Input() valueListType!: ValueListType;
    @Input() placeholder: string = '';
    @Input() optionLabel: string = '';
    @Input() appendTo: any;
    @Input() display: 'comma' | 'chip' = 'comma';

    private subscription: Subscription = new Subscription();
    valueListTranslations: any;
    multiselectOptions: SelectItem[] = [];

    constructor(
        private readonly valueListsService: ValueListsService,
        private readonly messageServiceHelper: MessageServiceHelper,
        private readonly translateService: TranslateService,
        private readonly formDataService: FormDataService
    ) {}

    value: any[] = [];
    isDisabled: boolean = false;

    // Functions to propagate changes to the form control
    private onChange: (value: any[]) => void = () => {};
    private onTouched: () => void = () => {};

    ngOnInit(): void {
        this.valueListTranslations = this.translateService.instant('valueListComponent');

        this.getValueListOptions();
    }
    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    getValueListOptions() {
        return this.valueListsService.getValueList(this.valueListType).subscribe((valueList) => {
            if (valueList) {
                this.multiselectOptions = valueList.valueItems.map((x) => {
                    return { label: x.displayValue, value: x.id };
                });
            }
        });
    }

    onSelect(value: any[]) {
        this.value = value;
        this.onChange(this.value);
        this.onTouched();
    }

    writeValue(value: any[]): void {
        this.value = value || [];
    }

    registerOnChange(fn: any): void {
        this.onChange = fn;
    }

    registerOnTouched(fn: any): void {
        this.onTouched = fn;
    }

    setDisabledState(isDisabled: boolean): void {
        this.isDisabled = isDisabled;
    }
}
