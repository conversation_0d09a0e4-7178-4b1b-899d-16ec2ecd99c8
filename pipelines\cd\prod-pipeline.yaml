name: Prod_$(date:yyyyMMdd)$(rev:.r)

trigger: none

pool:
  name: "kmd-platform-build-agents-internal"

variables:
  - template: ../global-variables-template.yaml

resources:
  pipelines:
    - pipeline: "_app_image"
      source: "[release] InstallationFormsUI - build container image"
      trigger:
        branches:
          include:
            - refs/heads/release/*
          exclude:
            - refs/heads/master
            - refs/heads/temp-release/*
            - refs/heads/feature/*
            - refs/heads/bug/*
    - pipeline: "_Scripts"
      source: "[master] Scripts"
      branch: master
      trigger: none
  repositories:
    - repository: commonPipelines
      type: git
      name: COMBAS/KMD.Elements.Pipelines
      ref: "master"
    - repository: helmCharts
      type: git
      name: COMBAS/KMD.Elements.HelmCharts.Elements
      ref: "master"

extends:
  template: cd/prod/stag-prod-template.yaml@commonPipelines
  parameters:
    aksApplicationName: ${{ variables.applicationName }}
    pathToImageInfoFile: ${{ variables.pathToImageInfoFile }}
