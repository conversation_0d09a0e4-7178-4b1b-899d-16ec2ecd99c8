<div #overlayContainer></div>

<div fxLayout="column" class="mt-10 zebra-container" [formGroup]="form">
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['invoiceType']" for="invoiceType">
                {{ 'invoicesWidget.invoiceType' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="invoiceType"
                [options]="invoiceTypeOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="invoiceType">
            </p-select>
            <small [controlValidationErrors]="form.controls['invoiceType']" class="p-error"></small>
        </div>
    </div>
    <div *ngIf="canSelectFee()" class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['feeId']" for="feeId">
                {{ 'invoicesWidget.fee' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="feeId"
                [options]="feeOptions"
                [appendTo]="overlayContainer"
                optionValue="value"
                optionLabel="label"
                [placeholder]="'common.selectValue' | translate"
                formControlName="feeId">
            </p-select>
            <small [controlValidationErrors]="form.controls['feeId']" class="p-error"></small>
        </div>
    </div>
    <div class="flex justify-content-end mt-10">
        <button
            id="cancelButton"
            [disabled]="isProcessing"
            type="button"
            pButton
            pRipple
            class="mr-2 p-button-secondary"
            (click)="onCancelClicked()">
            {{ 'common.cancel' | translate | titlecase }}
        </button>
        <button
            id="saveButton"
            [disabled]="isProcessing || !this.form.valid"
            type="button"
            pButton
            pRipple
            (click)="onCreateClicked()">
            <span *ngIf="isProcessing" class="fa fa-spin fa-spinner"></span>
            {{ 'common.create' | translate | titlecase }}
        </button>
    </div>
</div>
