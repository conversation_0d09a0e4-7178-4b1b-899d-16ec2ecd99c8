import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { Observable, map } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class BooleanOptionsService {
    constructor(private readonly translateService: TranslateService) {}

    /**
     * Get translated boolean options (Yes/No)
     * @param includeEmpty Whether to include an empty option
     */
    getBooleanOptions(includeEmpty = false): Observable<SelectItem[]> {
        return this.translateService.get(['common.yesOption', 'common.noOption', 'common.emptyValue']).pipe(
            map((translations) => {
                const options: SelectItem[] = [
                    { value: true, label: translations['common.yesOption'] },
                    { value: false, label: translations['common.noOption'] }
                ];

                if (includeEmpty) {
                    options.unshift({ value: null, label: translations['common.emptyValue'] });
                }

                return options;
            })
        );
    }
}
