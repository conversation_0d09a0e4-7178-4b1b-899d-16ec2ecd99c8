name: $(date:yyyyMMdd)$(rev:.r)

trigger:
  batch: true
  branches:
    include:
      - master
  paths:
    exclude:
      - /pipelines

schedules:
  - cron: "0 0 * * *"
    always: true
    displayName: Daily midnight build
    branches:
      include:
        - master

pool:
  vmImage: 'ubuntu-22.04'

resources:
  repositories:
    - repository: commonPipelines
      type: git
      name: COMBAS/KMD.Elements.Pipelines

extends:
  template: ci/templates/sca-npm-job-template.yaml@commonPipelines
