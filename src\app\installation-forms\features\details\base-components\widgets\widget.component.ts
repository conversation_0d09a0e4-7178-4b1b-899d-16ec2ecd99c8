import { FormDataService } from 'src/app/core/services/form-data/form-data.service';

export abstract class WidgetComponent {
    private _isDuringForceRefresh: boolean = false;

    get isProcessing() {
        return this.formDataService.isProcessing;
    }

    get isReadOnly() {
        return !this.formDataService.canCurrentUserEditData || this.isProcessing;
    }

    get canUpdatePaymentDetails() {
        return (
            this.hasUserWritePermission && this.isCurrentUserAssignedAsCaseWorker && this.formDataService.canUpdatePaymentDetails
        );
    }

    get hasUserWritePermission() {
        return this.formDataService.hasUserWritePermission;
    }

    get isCurrentUserAssignedAsCaseWorker() {
        return this.formDataService.isCurrentUserAssignedAsCaseWorker;
    }

    constructor(protected readonly formDataService: FormDataService) {}

    protected processingStarted() {
        this.formDataService.processingStarted();
    }

    protected processingFinished() {
        this.formDataService.processingFinished();
    }

    forceRefreshStarted() {
        this._isDuringForceRefresh = true;
    }

    forceRefreshFinished() {
        this._isDuringForceRefresh = false;
    }

    get isDuringForceRefresh(): boolean {
        return this._isDuringForceRefresh;
    }
}
