import { ChangeField, ChangeFieldType } from 'src/app/core/constants/changes-details';

export class TerminationChanges {
    // Installation information
    installationAddress: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };
    connectionPoint: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };
    meterFrame: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };
    consumptionMeteringPoint: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };
    contactPersonCompanyName: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    contactPersonName: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    contactPersonEmail: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    contactPersonPhoneNumber: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    remarksToInstallation: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    tags: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };

    //  Payer
    payerName: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    payerType: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    payerEmail: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    payerContactPersonName: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    payerContactPersonEmail: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    payerContactPersonPhoneNumber: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    payerAddress: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };
    requisition: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    cvrOrSeNumber: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    eanNumber: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };

    // Termination information
    terminationScope: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    terminationDateEod: ChangeField = { change: false, type: ChangeFieldType.Date, dtoFieldName: undefined };

    // meterReturnInformation
    meterReturnReturnOption: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    meterReturnName: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    meterReturnAttention: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    meterReturnAddress: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };

    // related forms
    hasRelatedFormsChanged: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
}
