import { ColumnTransformationType } from 'src/app/core/enums/column-transformation-type';
import { RulesListColumn } from '../../models/rules-list-column';
import { automaticEmailsTranslationPath } from './automatic-emails.consts';

export const AUTOMATIC_EMAILS_COLUMNS: RulesListColumn[] = [
    {
        field: 'displayName',
        header: `${automaticEmailsTranslationPath}.displayName`,
        columnTransformationType: ColumnTransformationType.None,
        isDefault: true
    },
    {
        field: 'formTypes',
        header: `${automaticEmailsTranslationPath}.formTypes`,
        translationPath: 'enums.type.',
        columnTransformationType: ColumnTransformationType.TranslateEnumArray,
        isDefault: true
    },
    {
        field: 'formStates',
        header: `${automaticEmailsTranslationPath}.formStates`,
        translationPath: 'enums.state.',
        columnTransformationType: ColumnTransformationType.TranslateEnumArray,
        isDefault: true
    },
    {
        field: 'formCategories',
        header: `${automaticEmailsTranslationPath}.formCategories`,
        translationPath: 'enums.category.',
        columnTransformationType: ColumnTransformationType.TranslateEnumArray,
        isDefault: true
    },
    {
        field: 'emailTemplateName',
        header: `${automaticEmailsTranslationPath}.emailTemplateName`,
        columnTransformationType: ColumnTransformationType.None,
        isDefault: true
    }
];
