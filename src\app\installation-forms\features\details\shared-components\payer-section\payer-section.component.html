<div #overlayContainer></div>
<div>
    <p-toggleButton
        id="showPayerDataToggleButton"
        [onLabel]="'payerWidget.payer.addPayerData' | translate"
        [offLabel]="'payerWidget.payer.deletePayerData' | translate"
        onIcon="fa-solid fa-plus"
        offIcon="fa-regular fa-trash-can"
        [ngModel]="!isPayerDataVisible"
        iconPos="left"
        class="mb-20 p-button-outlined"
        (onChange)="onShowPayerDataToggleChange($event)"
        *ngIf="!isPayerRequired && (!isReadOnly || canUpdatePaymentDetails)"></p-toggleButton>
    <div [formGroup]="form" *ngIf="isPayerDataVisible">
        <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label [labelRequired]="form.controls['payerType']" for="payerType">
                    {{ 'payerWidget.payer.type' | translate }}
                </label>
            </div>
            <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                <p-select
                    id="payerType"
                    [options]="payerTypeOptions"
                    optionValue="value"
                    optionLabel="label"
                    [appendTo]="overlayContainer"
                    [placeholder]="'common.selectValue' | translate"
                    formControlName="payerType">
                </p-select>
                <small [controlValidationErrors]="form.controls['payerType']" class="p-error"></small>
            </div>
        </div>
        <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label [labelRequired]="form.controls['payerName']" for="payerName">
                    {{ 'payerWidget.payer.name' | translate }}
                </label>
            </div>
            <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                <input id="payerName" type="text" pInputText maxlength="100" formControlName="payerName" />
                <small [controlValidationErrors]="form.controls['payerName']" class="p-error"></small>
            </div>
        </div>
        <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label [labelRequired]="form.controls['payerEmail']" for="payerEmail">
                    {{ 'payerWidget.payer.email' | translate }}
                </label>
            </div>
            <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                <input id="payerEmail" type="text" pInputText maxlength="100" formControlName="payerEmail" />
                <small [controlValidationErrors]="form.controls['payerEmail']" class="p-error"></small>
            </div>
        </div>
        <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label [labelRequired]="form.controls['payerAddress']" for="payerAddress">
                    {{ 'payerWidget.payerAddress.title' | translate }}
                </label>
            </div>
            <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                <app-address-search id="payerAddress" formControlName="payerAddress"> </app-address-search>
                <small [controlValidationErrors]="form.controls['payerAddress']" class="p-error"></small>
            </div>
        </div>

        <p-accordion [multiple]="true" [value]="[0, 1, 2]">
            <p-accordion-panel [value]="0">
                <p-accordion-header>{{ 'payerWidget.contactPerson.title' | translate }}</p-accordion-header>
                <p-accordion-content>
                    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
                        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                            <label for="payerContactPersonName">{{ 'payerWidget.contactPerson.name' | translate }} </label>
                        </div>
                        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                            <input
                                id="payerContactPersonName"
                                type="text"
                                pInputText
                                maxlength="100"
                                formControlName="payerContactPersonName" />
                            <small [controlValidationErrors]="form.controls['payerContactPersonName']" class="p-error"></small>
                        </div>
                    </div>
                    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
                        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                            <label for="payerContactPersonPhoneNumber">{{
                                'payerWidget.contactPerson.phoneNumber' | translate
                            }}</label>
                        </div>
                        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                            <input
                                id="payerContactPersonPhoneNumber"
                                type="text"
                                pInputText
                                maxlength="100"
                                formControlName="payerContactPersonPhoneNumber" />
                        </div>
                    </div>
                    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
                        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                            <label for="payerContactPersonEmail">{{ 'payerWidget.contactPerson.email' | translate }} </label>
                        </div>
                        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                            <input
                                id="payerContactPersonEmail"
                                type="text"
                                pInputText
                                maxlength="100"
                                formControlName="payerContactPersonEmail" />
                            <small [controlValidationErrors]="form.controls['payerContactPersonEmail']" class="p-error"></small>
                        </div>
                    </div>
                </p-accordion-content>
            </p-accordion-panel>

            <p-accordion-panel *ngIf="!isPayerTypePrivate()" [value]="1">
                <p-accordion-header>{{ 'payerWidget.financialInformation.title' | translate }}</p-accordion-header>
                <p-accordion-content>
                    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
                        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                            <label for="requisition">{{ 'payerWidget.financialInformation.requisition' | translate }}</label>
                        </div>
                        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                            <input id="requisition" type="text" pInputText maxlength="100" formControlName="requisition" />
                        </div>
                    </div>

                    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
                        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                            <label [labelRequired]="form.controls['cvrOrSeNumber']" for="cvrOrSeNumber">
                                {{ 'payerWidget.financialInformation.cvrOrSeNumber' | translate }}
                            </label>
                        </div>
                        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                            <input
                                id="cvrOrSeNumber"
                                type="text"
                                pInputText
                                minlength="8"
                                maxLength="8"
                                formControlName="cvrOrSeNumber" />
                            <small [controlValidationErrors]="form.controls['cvrOrSeNumber']" class="p-error"></small>
                        </div>
                    </div>

                    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
                        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                            <label [labelRequired]="form.controls['eanNumber']" for="eanNumber">
                                {{ 'payerWidget.financialInformation.eanNumber' | translate }}
                            </label>
                        </div>
                        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                            <input
                                id="eanNumber"
                                type="text"
                                pInputText
                                minlength="13"
                                maxLength="13"
                                formControlName="eanNumber" />
                            <small [controlValidationErrors]="form.controls['eanNumber']" class="p-error"></small>
                        </div>
                    </div>
                </p-accordion-content>
            </p-accordion-panel>

            <p-accordion-panel *ngIf="showCommonReadings()" [value]="2">
                <p-accordion-header>{{ 'commonReadings.sectionTitle' | translate }}</p-accordion-header>
                <p-accordion-content>
                    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
                        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                            <label for="payerContactPersonPhoneNumber">{{ 'commonReadings.commonReadingId' | translate }}</label>
                            <app-icon-master-data-path [path]="formDataService.masterDataToCompareResult?.commonReadings?.path">
                            </app-icon-master-data-path>
                        </div>
                        <div
                            class="zebra-edit"
                            [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70"
                            fxFlex.lt-md="50">
                            <input
                                id="commonReadingId"
                                type="text"
                                pInputText
                                [value]="commonReadingId | valueListSingleItem: meterFrameCommonReadingValueListType | async"
                                [disabled]="true" />
                        </div>
                        <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields">
                            <input
                                id="commonReadingIdMD"
                                type="text"
                                pInputText
                                [value]="
                                    formDataService.masterDataToCompareResult?.commonReadings?.value ?? ''
                                        | valueListSingleItem: meterFrameCommonReadingValueListType
                                        | async
                                "
                                [disabled]="true" />
                        </div>
                    </div>
                    <div fxFlexFill *ngIf="commonReadings?.length">
                        <br />
                        <app-common-readings [items]="visibleCommonReadings!"></app-common-readings>
                        <div
                            class="mb-10"
                            fxFlexFill
                            fxLayoutAlign="center center"
                            *ngIf="commonReadings && commonReadings.length > showMoreCommonReadingsCount">
                            <a class="show-more" *ngIf="!showAllCommonReadings" (click)="handleShowMoreCommonReadings()"
                                >{{ 'common.showMore' | translate }}
                            </a>
                            <a class="show-more" *ngIf="showAllCommonReadings" (click)="handleShowLessCommonReadings()"
                                >{{ 'common.showLess' | translate }}
                            </a>
                        </div>
                    </div>
                </p-accordion-content>
            </p-accordion-panel>
        </p-accordion>
    </div>
</div>
