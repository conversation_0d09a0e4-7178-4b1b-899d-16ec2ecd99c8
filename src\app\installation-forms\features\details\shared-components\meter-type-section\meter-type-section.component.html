<ng-container [formGroup]="form">
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="showMeterPlacement">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['meterPlacementId']" for="meterPlacementId">
                {{ 'technicalInformationWidget.meterType.meterPlacementId' | translate }}
            </label>
            <app-icon-master-data-path [path]="formDataService.masterDataToCompareResult?.meterPlacementId?.path">
            </app-icon-master-data-path>
        </div>
        <div class="zebra-edit" [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70" fxFlex.lt-md="50">
            <cmbs-value-list-dropdown
                id="meterPlacementId"
                formControlName="meterPlacementId"
                [valueListType]="meterFramePlacementValueListType"
                [messageKey]="this.formDataService.formId"
                [valueList]="valueListsService.getValueList(meterFramePlacementValueListType) | async">
            </cmbs-value-list-dropdown>
            <small [controlValidationErrors]="form.controls['meterPlacementId']" class="p-error"></small>
        </div>
        <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields">
            <input
                id="meterPlacementIdMD"
                type="text"
                pInputText
                [value]="
                    formDataService.masterDataToCompareResult?.meterPlacementId?.value ?? ''
                        | valueListSingleItem: meterFramePlacementValueListType
                        | async
                "
                [disabled]="true" />
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['meterSize']" for="meterSize">
                {{ 'technicalInformationWidget.meterType.meterSize' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="meterSize"
                [options]="meterSizeOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="meterSize"
                (onChange)="onMeterSizeChange()">
            </p-select>
            <small [controlValidationErrors]="form.controls['meterSize']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isConnectionTypeVisible()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['connectionTypeId']" for="connectionTypeId">
                {{ 'technicalInformationWidget.meterType.connectionTypeId' | translate }}
            </label>
            <app-icon-master-data-path [path]="formDataService.masterDataToCompareResult?.connectionTypeId?.path">
            </app-icon-master-data-path>
        </div>
        <div class="zebra-edit" [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70" fxFlex.lt-md="50">
            <cmbs-value-list-dropdown
                id="connectionTypeId"
                formControlName="connectionTypeId"
                [showClear]="false"
                [valueListType]="meterFrameConnectionTypeValueListType"
                [messageKey]="this.formDataService.formId"
                [valueList]="valueListsService.getValueList(meterFrameConnectionTypeValueListType) | async">
            </cmbs-value-list-dropdown>
            <small [controlValidationErrors]="form.controls['connectionTypeId']" class="p-error"></small>
        </div>
        <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields">
            <input
                id="connectionTypeIdMD"
                type="text"
                pInputText
                [value]="
                    formDataService.masterDataToCompareResult?.connectionTypeId?.value ?? ''
                        | valueListSingleItem: meterFrameConnectionTypeValueListType
                        | async
                "
                [disabled]="true" />
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isMeterTransformerVisible()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['meterTransformerId']" for="meterTransformerId">
                {{ 'technicalInformationWidget.meterType.meterTransformerId' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <cmbs-value-list-dropdown
                id="meterTransformerId"
                formControlName="meterTransformerId"
                [valueListType]="meterFrameMeterTransformerCodeValueListType"
                [messageKey]="this.formDataService.formId"
                [valueList]="valueListsService.getValueList(meterFrameMeterTransformerCodeValueListType) | async">
            </cmbs-value-list-dropdown>
            <small [controlValidationErrors]="form.controls['meterTransformerId']" class="p-error"></small>
        </div>
    </div>
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isMeterTransformerRemarkVisible()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['meterTransformerRemark']" for="meterTransformerRemark">
                {{ 'technicalInformationWidget.meterType.meterTransformerRemark' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <textarea
                id="meterTransformerRemark"
                pTextarea
                maxlength="2000"
                [autoResize]="true"
                formControlName="meterTransformerRemark"></textarea>
            <small [controlValidationErrors]="form.controls['meterTransformerRemark']" class="p-error"></small>
        </div>
    </div>
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isConnectionTypeChangeVisible()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['connectionTypeChange']" for="connectionTypeChange">
                {{ 'technicalInformationWidget.meterType.connectionTypeChange' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="connectionTypeChange"
                [options]="connectionTypeChangeOptions"
                optionValue="value"
                optionLabel="label"
                [showClear]="true"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="connectionTypeChange">
            </p-select>
            <small [controlValidationErrors]="form.controls['connectionTypeChange']" class="p-error"></small>
        </div>
    </div>
</ng-container>
<div #overlayContainer></div>
