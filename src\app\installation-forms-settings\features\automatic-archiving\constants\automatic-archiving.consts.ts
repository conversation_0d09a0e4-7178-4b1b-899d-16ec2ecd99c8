import { FormState } from 'src/app/api/installation-forms-client';

export const automaticArchivingTabName: string = 'AutomaticArchiving';
export const automaticArchivingTranslationPath: string = 'formsSettings.automaticArchiving';

export const automaticArchivingMarkdownEnUs: string = `
## Configuration of automatic archiving for form management

**Rules:** Adding a rule below will cause the system to automatically archive the form when all criteria defined in the rule are met.

**Trigger:** The form will be archived when all the below conditions are met in a rule:
- The form is set up to allow automatization.
- The form has no problems with the severity type "Error".
- Connection point and primary meter frame has been selected on the form.
- Form is not already archived.
`;

export const automaticArchivingMarkdownDaDK: string = `
## Konfiguration af automatisk arkivering af blanketter

**Regler:** Når der tilføjes en regel herunder, vil systemet automatisk arkivere blanketten, når alle kriterier defineret i reglen er opfyldt.

**Trigger:** Blanketten vil blive arkiveret, når alle nedenstående betingelser er opfyldt i en regel:
- Blanketten er opsat til automatisk håndtering.
- Blanketten har ingen problemer af alvorlighedsgraden "fejl".
- Blanketten har et valgt forbindelsespunkt og en primær målerramme.
- Blanketten er ikke allerede arkiveret.
`;

export const automaticArchivingRelevantStates: FormState[] = [
    FormState.Completed,
    FormState.Cancelled,
    FormState.Instructed,
    FormState.Registered
];
