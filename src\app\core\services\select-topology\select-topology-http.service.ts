import { Injectable } from '@angular/core';
import {
    BoundingBoxModel,
    ContainerModel,
    Edge,
    LocationModel,
    Node,
    TopologyApiService,
    TopologyModel
} from '@kmd-elements-ui/topology-selector';
import { map, Observable } from 'rxjs';
import {
    BoundingBox,
    ElectricityEquipmentContainer,
    ElectricityEquipmentContainersSearchByBoundingBoxModel,
    ElectricityTopologyItemType,
    ElectricityTopologyNavigationItem,
    GetElectricityTopologyNodeNavigationItemRequest,
    GetElectricityTopologyPathFromNodeToContainerRequest,
    InstallationFormsClient
} from 'src/app/api/installation-forms-client';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class SelectTopologyHttpService implements TopologyApiService {
    constructor(private readonly client: InstallationFormsClient) {}

    getContainers(request: BoundingBoxModel): Observable<ContainerModel[]> {
        return this.client
            .searchElectricityEquipmentContainersByBoundingBox(uuidv4(), SelectTopologyHttpService.toBoundingBox(request))
            .pipe(
                map((response) => response.result.map((container) => SelectTopologyHttpService.mapToContainerModel(container)))
            );
    }

    getNodes(topologyItem: Node, includeParent = true): Observable<TopologyModel> {
        const req = new GetElectricityTopologyNodeNavigationItemRequest({
            itemId: topologyItem.id,
            itemType: topologyItem.type as ElectricityTopologyItemType
        });

        return this.client.getElectricityTopologyNavigationItem(uuidv4(), req).pipe(
            map((response) => {
                const nodes: Node[] = [];
                const edges: Edge[] = [];

                for (const ch of response.result?.children ?? []) {
                    const nodeType = ch.type;
                    nodes.push({ id: ch.id, name: ch.name!, kind: ch.kind, type: nodeType });
                    edges.push({ id: `${topologyItem.id}_${ch.id}`, source: topologyItem.id, target: ch.id });
                }

                if (includeParent) {
                    for (const p of response.result?.parents ?? []) {
                        const nodeType = p.type;
                        nodes.push({ id: p.id, name: p.name!, kind: p.kind, type: nodeType });
                        edges.push({ id: `${p.id}_${topologyItem.id}`, source: p.id, target: topologyItem.id });
                    }
                }

                return { nodes: nodes, edges: edges };
            })
        );
    }

    getTopology(containerId: string, nodeId: string): Observable<TopologyModel> {
        const req = new GetElectricityTopologyPathFromNodeToContainerRequest({
            topologyNodeId: nodeId,
            equipmentContainerId: containerId
        });

        return this.client
            .getElectricityTopologyPathFromNodeToContainer(uuidv4(), req)
            .pipe(map((response) => SelectTopologyHttpService.mapToTopology(response.result)));
    }

    private static mapToTopology(graph: ElectricityTopologyNavigationItem[]): TopologyModel {
        const nodes: Node[] = [];
        const edges: Edge[] = [];

        const nodeSet = new Set<string>();
        // Creating nodes
        graph.forEach((node) => {
            if (!nodeSet.has(node.id)) {
                nodes.push({
                    id: node.id,
                    name: node.name!,
                    kind: node.kind,
                    type: node.type as 'EquipmentContainer' | 'TopologyNode' | 'MeterFrame'
                });
                nodeSet.add(node.id);
            }
        });

        // Creating edges
        graph.forEach((node) => {
            if (node.children) {
                node.children.forEach((child) => {
                    edges.push({
                        id: `${node.id}_${child.id}`,
                        source: node.id,
                        target: child.id
                    });
                });
            }
        });

        return { nodes: nodes, edges: edges };
    }

    private static toBoundingBox(request: BoundingBoxModel): ElectricityEquipmentContainersSearchByBoundingBoxModel {
        return {
            boundingBox: {
                left: request.left,
                right: request.right,
                top: request.top,
                bottom: request.bottom
            } as BoundingBox
        } as ElectricityEquipmentContainersSearchByBoundingBoxModel;
    }

    private static mapToContainerModel(container: ElectricityEquipmentContainer): ContainerModel {
        return {
            name: container.name,
            kind: container.kind,
            id: container.id,
            location: { lon: container.location?.x, lat: container.location?.y } as LocationModel
        } as ContainerModel;
    }
}
