<div #overlayContainer></div>
<div fxLayoutGap="0.5rem" fxLayout="row">
    <div fxFlex>
        <p-autoComplete
            [(ngModel)]="value"
            [showEmptyMessage]="true"
            [suggestions]="options"
            field="meterFrameNumber"
            [appendTo]="overlayContainer"
            [minLength]="1"
            [forceSelection]="true"
            [disabled]="isDisabled"
            (completeMethod)="search($event)"
            (onClear)="onClear()"
            (onSelect)="onSelect($event.value)">
        </p-autoComplete>
    </div>
    <div *ngIf="!!value?.id" fxFlex="nogrow" fxLayoutAlign="start center">
        <a class="add-point" (click)="onDetailsClick()"><i class="fa-solid fa-up-right-from-square"></i></a>
    </div>
    <div *ngIf="!value?.id" fxFlex="nogrow" fxLayoutAlign="start center">
        <p-tag severity="warn" value="{{ 'meterFrameSearch.notSelectedWarning' | translate }}"> </p-tag>
    </div>
</div>
