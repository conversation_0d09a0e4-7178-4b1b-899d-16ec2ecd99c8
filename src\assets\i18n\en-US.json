{"common": {"emptyList": "No records found", "of": "of", "from": "from", "searchResults": "Search results", "singleChangedFieldInformation": "You have made {{amount}} change", "pluralChangedFieldsInformation": "You have made {{amount}} changes", "add": "Add", "create": "Create", "cancel": "Cancel", "saveChanges": "Save", "cancelChanges": "Cancel changes", "successTitle": "Success", "validationTitle": "Warning", "errorTitle": "Error", "errorRefreshingDetail": "Error refreshing data", "staleDataTitle": "Warning", "staleDataDetail": "Data was changed by other user or system. You need to refresh your form and will lose any unsaved changes.", "concurrentModification": "The record you attempted to edit was modified by another user after you got the original value. Please reload the page.", "allRequiredFieldsShouldBeCompleted": "All required fields should be completed", "fieldIsRequired": "Field is required", "fieldIsInWrongFormat": "Value is in the wrong format", "fieldIsOutsideCorrectMinRange": "The correct min value is {{min}}", "fieldIsOutsideCorrectMaxRange": "The correct max value is {{max}}", "selectValue": "Select", "showMore": "Show more", "showLess": "Show less", "lackOfPermissions": "You do not have permissions to see this area. Please contact your user administrator.", "yesOption": "Yes", "noOption": "No", "emptyValue": "Empty Value", "addressSearchError": "Error occurred while searching for addresses.", "meterFramesSearchError": "Error occurred while searching for meter frames.", "formNotValid": "This action cannot be executed because the form is not valid", "columnsReset": "Reset columns", "filterLabelContains": "Contains", "filterLabelNotContains": "Not Contains", "filterLabelStartsWith": "Starts With", "filterLabelEndsWith": "Ends With", "filterLabelEquals": "Equals", "filterLabelNotEquals": "Not Equals", "filterLabelDateIs": "Date Is", "filterLabelDateIsNot": "Date Is Not", "filterLabelDateIsBefore": "Date Is Before", "filterLabelDateIsAfter": "Date Is After", "filterLabelGreaterThanOrEqualTo": "Greater Than Or Equal To", "filterLabelGreaterThan": "Greater Than", "filterLabelLessThanOrEqualTo": "Less Than Or Equal To", "filterLabelLessThan": "Less Than", "yesNoPlaceholder": "Yes/No", "loadMasterDataProcessTemplatesError": "Error happened while loading master data templates.", "loadEmailTemplatesError": "Error happened while loading email templates", "dateMustNotBePast": "Date must not be in the past.", "displayNameAlreadyUsed": "Display name already used.", "multipleWhitespaces": "Multiple whitespaces are not allowed."}, "home": {"tabAreaLabel": "Forms", "tabLabel": "Search", "columnHeaders": {"tags": "Tags", "formNumber": "Form number", "category": "Category", "installationAddress": "Installation address", "state": "State", "type": "Form type", "problemCategories": "Problem categories", "formRequiresAttention": "Requires attention", "createdDate": "Creation date", "hasUnreadMessages": "Unread messages", "installerName": "Installer name", "installerAuthorizationNumber": "Authorization number", "caseWorkerName": "Case worker name", "caseWorkerEmail": "Case worker email", "scopeOfDeliverySize": "Scope of delivery (A)", "installInConstructionPhase": "Install in construction phase", "processLevelIndicators": "Process level identificators", "latestRegistrationDate": "Latest registration date"}}, "details": {"form": "Form", "formNumber": "Form number", "category": "Category", "state": "State", "type": "Type", "installationAddress": "Installation address", "typeAndState": "Type and state", "creationDateTime": "Creation:", "lastChanged": "Last changed:", "importantDates": "Important dates", "meterNumber": "Meter number", "screeningStatus": "Allow automatization", "problemCategories": "Problem categories", "formRequiresAttention": "Requires attention"}, "address": {"darId": "DAR Address ID", "country": "Country", "city": "City", "postalCode": "ZIP code", "buildingNumber": "House number", "streetName": "Street name", "floor": "Floor", "door": "Door", "addressDetails": "Address details", "notValidWarning": "Address is not valid", "pattern": "Street & number, floor & door, zip code & city"}, "caseWorkerWidget": {"email": "Email", "name": "Name", "phoneNumber": "Phone number", "assignMe": "Assign me", "unassign": "Unassign", "title": "Assigned to", "unassignSuccessDetails": "Case worker unassigned", "assignSuccessDetails": "Case worker assigned", "takeOverFormQuestion": "Are you sure you want to change case worker?", "onlyAssignedUserCanEditFormInformation": "Only assigned user can edit this form"}, "workOrdersWidget": {"title": "Work orders", "workOrderType": "Work order type", "workOrderDescription": "Work description", "workOrderPurpose": "Work purpose", "meterFrameNumber": "Meter frame number", "createWorkOrder": "Create", "createWorkOrderFailed": "Failed to create the work order", "getConfigError": "Error occurred while getting work orders configuration.", "noWorkOrdersFound": "No work orders found.", "columnNames": {"workOrderType": "Type", "workOrderPurpose": "Purpose", "status": "Status", "workOrderStarted": "Started"}, "enums": {"workOrderEntryState": {"Creating": "Creating", "AwaitingValidation": "Awaiting validation", "ValidationFailed": "Failed to create", "Created": "Created", "Draft": "Draft", "Unassigned": "Unassigned", "Assigned": "Assigned", "Active": "Active", "OnHold": "On hold", "ReceivedCompleted": "Received completed", "ClosedByTechnician": "Closed by technician", "Cancelled": "Cancelled", "Done": "Done"}}}, "masterDataProcessesWidget": {"title": "Master data processes", "masterDataProcessType": "Type", "masterDataProcessTemplate": "Template", "createMasterDataProcess": "Create", "createMasterDataProcessFailed": "Failed to create the master data process", "noMasterDataProcessesFound": "No master data processes found.", "postMasterDataProcessesMeterTransformerSetToOther": "Master data process cannot be started because the field Meter transformer must be filled by a value from the value list different from \"Other\".", "columnNames": {"masterDataProcessType": "Type", "status": "Status", "masterDataProcessStarted": "Started"}, "enums": {"masterDataProcessEntryState": {"Creating": "Creating", "AwaitingForProcessCreation": "Awaiting for process creation", "FailedToCreateProcess": "Failed to create process", "New": "New", "InProgress": "In progress", "Failed": "Failed", "RequiresManualHandling": "Requires manual handling", "ManuallyHandled": "Manually handled", "Completed": "Completed", "Cancelled": "Cancelled", "Awaiting": "Awaiting"}}}, "chatAndNotesWidget": {"title": "Communication", "chat": {"title": "Cha<PERSON>", "sendMessage": "Send", "markAllAsRead": "Mark all as read", "getChatMessagesError": "Error occurred while getting chat messages.", "createNewChatMessageError": "Error occurred while creating new chat message.", "markAllAsReadError": "Error occurred while marking all messages as read.", "markAllAsReadSuccess": "All messages are marked as read.", "syncFailedAt": "Syncing the chat message failed."}, "notes": {"title": "Notes", "content": "Content", "modifiedBy": "Last modified by", "add": "Add", "cancel": "Cancel", "save": "Save", "edit": "Edit", "getNotesError": "Error occurred while getting list of notes.", "createNewNoteError": "Error occurred while creating new note.", "modifyNoteError": "Error occurred while modifying note.", "deleteNoteError": "Error occurred while deleting note.", "noNotesFound": "No notes found.", "deleteConfirmationTitle": "Delete note", "deleteConfirmationMessage": "Are you sure you want to delete this note?"}}, "problemsWidget": {"title": "<PERSON><PERSON><PERSON>", "errors": "Errors", "warnings": "Warnings", "info": "Info", "acknowledged": "Acknowledged", "markAsAcknowledged": "<PERSON> as acknowledged", "markAsAcknowledgedError": "Error occurred while marking the problem as acknowledged.", "markAsAcknowledgedConfirmationTitle": "<PERSON> as acknowledged", "markAsAcknowledgedConfirmationMessage": "Are you sure you want to mark the problem as acknowledged? Doing so can have unforeseen consequences as automations could start with incorrect or missing data and should be used with caution. "}, "installerWidget": {"title": "Installer", "companyName": "Company name", "companyPhoneNumber": "Company phone number", "companyEmail": "Company email", "cvr": "CVR", "companyAuthorizationNumber": "Authorization number", "name": "Installer name", "phoneNumber": "Phone number", "email": "Email", "secondaryContact": {"title": "Secondary contact person", "name": "Name", "phoneNumber": "Phone number", "email": "Email"}}, "installationWidget": {"title": "Installation", "installationAddress": {"title": "Address"}, "connectionPoint": {"title": "Connection point", "navigateLink": "Connection point details", "notSelectedWarning": "Connection point not selected", "connectionStateNotValid": "Connection state not valid"}, "meterFrame": {"title": "Meter frame", "navigateLink": "Meter frame details", "notSelectedWarning": "Meter frame not selected"}, "gridAreaId": {"title": "Grid area id"}, "consumptionMeteringPointId": {"navigateLink": "Metering point details", "title": "Consumption metering point id"}, "productionMeteringPointId": {"navigateLink": "Metering point details", "title": "Production metering point id"}, "remarksToInstallation": "Remarks", "temporaryInstallationDuration": "Temporary installation duration", "contactPerson": {"title": "Contact person", "companyName": "Company name", "name": "Name", "phoneNumber": "Phone number", "email": "Email", "addContactPersonData": "Add contact person data", "deleteContactPersonData": "Delete contact person data"}, "tags": "Tags", "loadMeteringPointsError": "Error happened while loading metering point data", "noMatchingMeteringPointsWarning": "No matching metering point found", "multipleMeteringPointsWarning": "Multiple matching metering points found (wrong configuration)", "meterFramesSearchError": "Error occurred while searching for meter frames.", "connectionPointsSearchError": "Error occurred while searching for connection points.", "loadGridAreasError": "Error happened while loading grid areas."}, "meterFrameSearch": {"navigateLink": "Meter frame details", "notSelectedWarning": "Meter frame not selected"}, "payerWidget": {"title": "Payer", "payer": {"title": "Payer", "type": "Type", "name": "Name", "email": "Email", "addPayerData": "Add payer data", "deletePayerData": "Delete payer data"}, "payerAddress": {"title": "Payer address"}, "financialInformation": {"title": "Financial information", "cvrOrSeNumber": "CVR or SE number", "requisition": "Requisition number", "eanNumber": "EAN number"}, "contactPerson": {"title": "Contact person", "name": "Name", "phoneNumber": "Phone number", "email": "Email"}}, "meterDeliveryWidget": {"title": "Meter delivery", "requestedConnectionDateEod": "Requested connection date", "installedDuringConstruction": "Installed during construction", "deliveryOption": "Delivery option", "meterPlacementId": "Meter placement", "deliveryInformation": {"title": "Delivery information", "name": "Recipient name", "attention": "Recipient att.", "address": "Recipient address"}}, "meterReturnWidget": {"title": "Meter return", "returnOption": "Return option", "name": "Warehouse name", "attention": "Warehouse att.", "address": "Warehouse address"}, "terminationWidget": {"title": "Termination", "scope": "Scope of termination", "terminationDateEod": "Date of termination"}, "technicalInformationWidget": {"title": "Technical information", "branchLine": {"title": "Branch line", "type": "Branch line type", "fuseTypeOfMainProtection": "Main protection fuse type", "fuseSizeOfMainProtection": "Main protection size (A)", "protectionTypeOfMainProtection": "Main protection type", "cableDimensionId": "Cable dimensions", "numberOfPairs": "Number of pairs", "protectiveEarthingDimension": "PE (mm2)", "meterFrame": "Meter frame"}, "connection": {"title": "Connection", "scopeOfDelivery": "Scope of delivery (A)", "transformerStationNumber": "Transform station no.", "cabinetNumber": "Cabinet no.", "groundingMethod": "Grounding method", "fuseSizeOfPreProtection": "Pre-protection size (A)", "protectionTypeOfPreProtection": "Pre-protection type", "phaseCount": "Phase count"}, "meterType": {"title": "Meter type", "meterPlacementId": "Meter placement", "installInConstructionPhase": "Installed during construction", "meterSize": "Meter size", "connectionTypeId": "Connection type", "connectionTypeChange": "Connection type change", "meterTransformerId": "Meter transformer", "meterTransformerRemark": "Remark"}}, "instructionDataWidget": {"title": "Instruction information", "remark": "Remark", "meterResponsible": "Meter Responsible", "branchLineResponsible": "Branch Line Responsible", "connectionFee": {"validUntilEod": "Connection fee valid up to and inclusive", "fee": "Connection fee excl. VAT [DKK]", "currency": "<PERSON><PERSON><PERSON><PERSON>"}, "instructionTextsPicker": {"openPicker": "Open instruction texts picker", "closePicker": "Close instruction texts picker", "apply": "Add selected instruction texts", "selectInstructionTexts": "Select instruction texts"}, "getInstructionTextsError": "Error occurred while getting instruction texts."}, "connectionRights": {"title": "Connection rights", "updateButton": "Update connection rights", "addTransferButton": "Add transfer", "sectionTitle": "Transfer of connection rights", "summaryInfo": "Total transfer {{TotalConnectionRightsTransfer}}A", "noTransfersFound": "No transfers found.", "columnHeaders": {"meterFrameNumber": "Meter frame number", "value": "Transfer (A)"}, "updateConnectionRightsSucceded": "Connection rights updated on the meter frame.", "updateConnectionRightsFailed": "Error occurred while updating connection rights."}, "commonReadings": {"sectionTitle": "Common Readings", "commonReadingId": "Common Reading", "columnHeaders": {"type": "Type", "number": "Number"}}, "sealWidget": {"title": "Seal", "meterPlacementId": "Meter placement", "responsibleForSeal": "Responsible for re-seal", "reasonForChange": "Reason for change"}, "moveMeterWidget": {"title": "Move meter", "reasonForChange": "Reason for change"}, "changeOfMeterWidget": {"title": "Change of meter", "reasonForChange": "Reason for change", "responsibleForSeal": "Responsible for re-seal"}, "changeBranchLineWidget": {"title": "Change branch line", "newMeterRequested": "New meter requested"}, "extensionWidget": {"title": "Extension", "requestedDeliveryExtensionDateEod": "Requested delivery extension date", "newMeterRequested": "New meter requested"}, "flagsWidget": {"types": {"MeterInstalled": "Meter installed", "SupplierSelected": "Supplier selected", "ConnectionFeePaid": "Connection fee paid", "ReadyForMeter": "Ready for meter", "SubmittedForProjectPlanning": "Submitted for project planning"}, "manuallySetDate": "Manually set date", "readyForMeterDate": "Ready for meter date", "title": "Flags"}, "usageSection": {"title": "Application", "applicationType": "Application", "effect": "Effect [kW]", "phaseCount": "Phase count", "remark": "Remark", "addApplication": "Add application", "noApplicationsFound": "No applications found."}, "voltageSection": {"voltageLevelId": "Voltage level subcategory", "currentTransformer": "Current transformer", "voltageTransformer": "Voltage transformer"}, "emailsWidget": {"create": "Create", "template": "Template", "title": "Emails", "cancel": "Cancel", "sendEmail": "Send", "sendEmailError": "Error happened while sending email", "noEmailsFound": "No emails found.", "postSendEmailMissingData": "Could not send email due to missing data: {{FieldName}}.", "columnNames": {"templateName": "Template name", "status": "Status", "createdDate": "Creation date", "recipientAddress": "Email"}, "emailStatuses": {"Failed": "Failed", "InProgress": "In progress", "Completed": "Completed"}}, "invoicesWidget": {"title": "Invoices", "columnNames": {"number": "Number", "createdDate": "Creation date", "createdInErpDate": "Invoice date", "status": "Status", "totalSum": "Total sum", "isRequired": "Required"}, "invoiceStatuses": {"Draft": "Being created", "Created": "Created", "Calculated": "Calculated", "CalculationFailed": "Calculation failed", "Transferred": "Transferred", "TransferStarted": "Transfer started", "Paid": "Paid", "CreationFailed": "Creation failed"}, "createInvoice": "Create", "invoiceType": "Invoice type", "fee": "Fee", "loadPricesError": "Error occurred while loading price definitions.", "postInvoicesError": "Error occurred while creating an invoice.", "postInvoicesFormNotValid": "You can only create an invoice from a valid form.", "postInvoicesSuccess": "Successfully created new invoice.", "getInvoicesError": "Error occurred while getting invoices.", "noInvoicesFound": "No invoices found.", "transferButton": "Transfer", "transferInvoiceSingleInvoicingProblem": "An error occurred while transferring invoice. Could not transfer invoice."}, "tasksWidget": {"title": "Tasks", "columnNames": {"name": "Task", "state": "Status", "startedDate": "Started date", "completionDate": "Completion date", "isManual": ""}, "taskStatuses": {"NotStarted": "Not started", "InProgress": "In progress", "Completed": "Completed", "Failed": "Failed"}, "taskGroups": {"AllowAutomatization": "Allow automatization", "CreateInvoice": "Create invoice", "AutomaticEmails": "Automatic emails", "CreateWorkOrder": "Create work order", "CreateMasterDataProcess": "Create master data process", "CreateMasterDataProcessWithCloseDown": "Create master data process with close down", "Archiving": "Archiving", "AutomaticInstructionText": "Automatic instruction text"}, "getTasksError": "Error occurred while getting tasks.", "markAsCompleted": "Complete", "noTasksFound": "No tasks found.", "reExecute": "Re-run", "manualTask": "Manual task", "automaticTask": "Automatic task"}, "relatedFormsSection": {"title": "Related forms", "searchError": "Error occurred while searching for forms by form number.", "formAlreadyHasRelationWarning": "Selected form already has relation with current form.", "formNumber": "Form number", "state": "State", "formType": "Type", "relationType": "Relation type", "addRelatedForm": "Add manual relation", "origin": "Relation origin", "direction": "Relation direction", "noRelatedFormsFound": "No related forms found.", "createdDate": "Received"}, "energyProductionSection": {"title": "Energy production", "common": {"energyProductionType": "Energy production type", "energyProductionConnectionType": "Energy production connection type", "owner": {"name": "Owner's name", "address": "Owner's address", "identificationType": "Owner's identification type", "identifier": "Owner's identifier"}, "moreThan125kW": "More than 125 kW", "includesStorage": "Includes storage", "commissioningDateSod": "Commissioning date"}, "connectionAddress": {"title": "Connection address", "address": "Address", "consumptionUnitType": "Consumption unit type", "locationType": "Location type", "cadastralDistrictIdentifier": "Cadastral district identifier", "cadastralRegistrationNumber": "Cadastral registration number", "locationUtmX": "Location [Utm X]", "locationUtmY": "Location [Utm Y]", "buildingNumber": "Building number", "propertyNumber": "Property number", "connectionPointUtmX": "Connection point [Utm X]", "connectionPointUtmY": "Connection point [Utm Y]", "foundationElevation": "Foundation elevation"}, "windPlant": {"certificate": "Certificate", "certificationProvider": "Certification provider", "rotorDiameterInMeters": "Rotor diameter in meters", "hubHeightInMeters": "Hub height in meters"}, "powerPlant": {"primaryEnergySource": "Primary energy source", "primaryEnergyShareInPercent": "Primary energy share in percent", "secondaryEnergySource": "Secondary energy source", "secondaryEnergyShareInPercent": "Secondary energy share in percent", "plantTypeName": "Plant type name"}, "plants": {"plant": "Plant", "title": "Plants", "phaseCount": "Phase count", "totalPowerInKw": "Total power [kW]", "commissioningDateSod": "Commissioning date", "brandName": "Brand name", "modelName": "Model name", "typeName": "Type name", "solarSystemAreaInM2": "Solar system area [m^2]", "addPlant": "Add plant", "calculatedTotalPowerInKw": "Calculated total power [kW]", "totalCount": "Total count:"}, "calculatedTotalProductionCapacity": "Calculated total production capacity [kW]"}, "actionBar": {"stateHandling": {"Returned": "Return", "Instructed": "Instruct", "Cancelled": "Cancel and archive", "Rejected": "Reject and archive", "Archived": "Archive", "SaveAndContinue": "Save and continue", "stateChangeComment": "Change comment"}, "screeningStatusHandling": {"HandleManually": "Handle manually", "Screened": "Allow automatization", "screeningStatusDialogTitle": "Allow automatization", "revertToHandleManuallyQuestion": "Automatization is already allowed. Are you sure you want to handle form manually?"}}, "valueListComponent": {"valueListsNotFoundError": "Value list not found.", "valueListsError": "An error occurred while fetching value lists."}, "enums": {"fieldNames": {"contactPerson": "Contact person", "contactPersonName": "Contact person name", "contactPersonEmail": "Contact person email", "consumptionMeteringPointId": "Consumption metering point id", "templateId": "Template id"}, "pipeDimension": {"Pe6CU": "6 CU", "Pe10CU": "10 CU", "Pe16CU": "16 CU", "Pe25CU": "25 CU", "Pe35CU": "35 CU", "Pe50CU": "50 CU", "Pe70CU": "70 CU", "Pe95CU": "95 CU", "Pe120CU": "120 CU", "Pe150CU": "150 CU", "Pe185CU": "185 CU", "Pe240CU": "240 CU", "Pe300CU": "300 CU", "Pe16AL": "16 AL", "Pe25AL": "25 AL", "Pe35AL": "35 AL", "Pe50AL": "50 AL", "Pe70AL": "70 AL", "Pe95AL": "95 AL", "Pe120AL": "120 AL", "Pe150AL": "150 AL", "Pe185AL": "185 AL", "Pe240AL": "240 AL", "Pe300AL": "300 AL"}, "connectionGroundingMethod": {"Tt": "TT", "TnS": "TN-S", "TnC": "TN-C", "TnCS": "TN-C-S", "It": "IT", "Other": "Other"}, "branchLineType": {"Existing": "Existing shared", "New": "New shared", "Own": "Own"}, "preProtectionType": {"Fuse": "<PERSON><PERSON>", "MaxBreaker": "Max breaker", "None": "None"}, "mainProtectionType": {"Fuse": "<PERSON><PERSON>", "MaxBreaker": "Max breaker"}, "supplyType": {"Electricity": "Electricity", "Water": "Water", "Heating": "Heating"}, "commonReadingType": {"LargeApartment": "Large apartment", "SmallApartment": "Small apartment", "TerracedHouse": "Terraced house", "Common": "Common", "Business": "Business"}, "type": {"NewInstallation": "New installation", "SealBreach": "Seal breach", "Termination": "Termination", "ChangeMeter": "Change of meter", "MoveMeter": "Move meter", "ChangeBranchLine": "Change of branch line", "Extension": "Extension", "EnergyProduction": "Energy production"}, "state": {"InTransit": "In transit", "Registered": "Registered", "Returned": "Returned", "Instructed": "Instructed", "Completed": "Completed", "Cancelled": "Cancelled", "Rejected": "Rejected", "Archived": "Archived", "AwaitingFormSystemUpdate": "Awaiting form system"}, "screeningStatus": {"HandleManually": "No", "Screened": "Yes"}, "category": {"BusinessOrOther": "Other/Business", "ParcelOrCottage": "Parcel/Cottage", "TerracedHouse": "Terraced house", "StandardApartment": "Standard apartment", "LargeApartment": "Large apartment", "YouthOrRetirementOrNursingHome": "Young, Old and Nursing home", "Allotment": "Allotment", "SmallInstallation": "Small installation", "TemporaryInstallation": "Temporary installation", "Solar": "Solar", "Wind": "Wind", "Consumption": "Consumption", "PowerPlant": "Power plant"}, "problemCategory": {"IntegrationIssue": "Integration Issue", "DataIssue": "Data Issue", "ProcessingIssue": "Processing Issue"}, "meterDeliveryOption": {"GridCompanyWillHandle": "Grid company will handle", "InstallerWillHandlePickUp": "Installer will handle, pick-up", "InstallerWillHandleSendMeter": "Installer will handle, send meter"}, "meterConnectionTypeChange": {"PinToWire": "Change from pin to wire", "WireToPin": "Change from wire to pin"}, "meterReturnOption": {"GridCompanyWillHandle": "Grid company will handle", "InstallerWillHandleDropOff": "Installer will handle, Drop-off", "InstallerWillHandleSendMeter": "Installer will handle, send meter"}, "meterSize": {"Below63A": "Below 63A", "Above63A": "Above 63A"}, "payerType": {"Company": "Company", "Private": "Private", "Public": "Public"}, "terminationScope": {"EntireInstallation": "Entire installation", "EnergyProduction": "Energy production"}, "meterTransformerId": {"C300_5": "300/5", "C600_5": "600/5", "C1000_5": "1000/5", "C1200_5": "1200/5", "Other": "Other"}, "responsibleForSeal": {"GridCompany": "Grid company", "Installer": "Installer"}, "responsibleForMeter": {"GridCompany": "Grid company", "Installer": "Installer"}, "reasonForChange": {"TroubleshootingOrMaintenance": "Troubleshooting or maintenance", "NewBranchlineOrSupply": "New branchline/supply", "ChangeOfDashboardOrElectricalPanel": "Change of dashboard/electrical panel", "Thermography": "Thermography", "Other": "Other", "Remodeling": "Remodelling", "MoveMeter": "Move meter", "NewBranchline": "New branchline", "ChangeMeterBoard": "Change meter board", "MeterBurned": "Meter burned", "MeterDefect": "Meter defect", "ChangeToWire": "Change to wire", "ChangeToPin": "Change to pin"}, "meterResponsible": {"SetupByGridCompany": "Setup by grid company", "PickedUpAndSetupByInstaller": "Picked up and setup by installer", "SendByGridCompanySetupByInstaller": "Send by grid company, setup by installer", "TakenDownAndReturnedByInstaller": "Taken down and returned by installer", "SetupByGridCompanyReturnedByInstaller": "Setup by grid company, returned by installer", "PickedUpSetupAndReturnedByInstaller": "Picked up, setup and returned by installer"}, "branchLineResponsible": {"GridCompanyConnects": "Grid company connects", "InstallerConnects": "Installer connects", "GridCompanyDisconnects": "Grid company disconnects", "InstallerDisconnects": "Installer disconnects", "InstallerConnectsAndDisconnects": "Installer connects and disconnects", "GridCompanyConnectsAndDisconnects": "Grid company connects and disconnects"}, "applicationType": {"Motor": "Motor", "ThermalLoad": "Thermal load", "PowerPlant": "Power plant", "BatterySystem": "Battery system", "ChargingStand": "Charging stand", "HeatPump": "Heat pump", "PhaseCompensationSystem": "Phase compensation system", "EmergencyFacility": "Emergency facility", "MachinePlant": "Machine plant", "FieldIrrigationSystem": "Field irrigation system", "StreetLights": "Street lights", "Sign": "Sign", "CommonInstallation": "Common installation", "PowerGasWaterHeat": "Power gas water heat", "Telecommunications": "Telecommunications", "Other": "Other", "ElectricFence": "Electric fence", "AntennaAmplifier": "Antenna amplifier", "TrafficCounter": "Traffic counter", "PressureSensor": "Pressure sensor", "WarningSiren": "Warning siren", "Defibrillator": "Defibrillator", "StandardResidentialInstallation": "Standard residential installation", "ConstructionSite": "Construction site", "CulturalEvent": "Cultural event"}, "energyProductionConnectionType": {"ConnectedByInstallation": "Connected by installation", "ConnectedDirectly": "Connected directly"}, "ownerIdentificationType": {"Cvr": "CVR", "DateOfBirth": "Date of birth", "CommitmentIdentifier": "Commitment identifier"}, "locationType": {"Land": "Land", "Sea": "Sea"}, "formsRelationType": {"Manual": "Manual", "Automatic": "Automatic", "AutoDetected": "Auto-detected"}, "formsRelationOrigin": {"ManualEntry": "Manual entry", "ConnectionPointMatch": "Connection point match", "Eltilmelding": "Eltilmelding"}, "formsRelationDirection": {"NoDirection": "-", "Parent": "Parent relation", "Child": "Child relation"}, "temporaryInstallationDurationType": {"UpTo12Months": "Limited period (max. 12 months)", "Over12Months": "Construction period over 12 months"}, "connectionRightsCategories": {"Allotment": "Allotment", "ExtensionAHigh": "Extension A High", "ExtensionALow": "Extension A Low", "ExtensionBHigh": "Extension B High", "ExtensionBLow": "Extension B Low", "ExtensionCLevel": "Extension C Level", "KWMaxBHigh": "kW Max B High", "KWMaxBLow": "kW Max B Low", "KWMaxCLevel": "kW Max C level", "LargeApartment": "Large Apartment", "Other": "Other", "SmallApartment": "Small Apartment", "SmallInstallation": "Small Installation", "Parcel": "<PERSON><PERSON><PERSON>", "YoungOldAndNursery": "Young, Old and Nuserary", "TerracedHouse": "Terraced House"}, "automationVoltageLevel": {"LowVoltage": "C", "HighVoltageBLow": "B-Low", "HighVoltageBHigh": "B-High", "HighVoltageALow": "A-Low", "HighVoltageAHigh": "A-High"}, "masterDataProcessType": {"ModifyConnectionPoint": "Modify connection point", "CreateConnectionPoint": "Create connection point", "CloseDownSupplyType": "Close down supply type", "CloseDownProduction": "Close down energy production"}, "masterDataProcessAutomationLevel": {"InitiateProcessOnly": "Initiate process only", "InitiateAndRunProcess": "Initiate and run process"}, "masterDataProcessWorkOrderAutomationLevel": {"Manual": "Manual", "PrimaryMeterFrameOnly": "Primary meter frame only", "AllMeterFrames": "All meter frames"}, "transferConnectionRightUom": {"Ampere": "A", "Kilowatt": "kW", "KilovoltAmpere": "kVA"}, "processLevelIndicators": {"None": "None", "MeterInstalled": "Meter Installed", "SupplierSelected": "Supplier Selected", "ReadyForMeter": "Ready For Meter", "NoWorkOrderCreated": "No Work Order Created", "MasterDataCreated": "Master  Data Created", "MasterDataUpdated": "Master Data Updated", "SupplyTypeClosedDown": "Supply Type Closed Down", "NoInvoiceCreated": " No Invoice Created", "NoInvoiceTransferred": "No Invoice Transferred", "InvoicePaid": "Invoice Paid", "WelcomeLetterSent": "Welcome Letter Sent", "EnergyProductionClosedDown": "Energy Production Closed Down"}, "workOrderType": {"InstallMeter": "Install meter", "ReplaceMeter": "Replace meter", "RemoveMeter": "Remove meter", "General": "General"}, "invoiceType": {"ConnectionRights": "Connection rights", "StandardFee": "Standard fee", "CreatedExternally": "Created externally"}}, "problemCodes": {"Unknown": "Unknown. Cause: '{{Message}}'", "CarUpdateChangedDarId": "{{[propertyPath]PropertyPath}} in the Central Address Registry has a new DAR ID after an update. CAR ID: '{{carId}}'. Old DAR ID: '{{oldDarId}}'. New DAR ID: '{{newDarId}}'.", "NullValidator": "'{{<PERSON><PERSON><PERSON>}}' must be null.", "NotNullValidator": "'{{<PERSON><PERSON><PERSON>}}' is required.", "EanNumberValidator": "'{{PropertyName}}' must be valid EAN-13 number. Current value '{{PropertyValue}}'.", "CvrNumberValidator": "'{{PropertyName}}' must be valid CVR (SE) number. You entered '{{PropertyValue}}'.", "NotEmptyValidator": "'{{<PERSON><PERSON><PERSON>}}' must not be empty.", "LengthValidator": "'{{<PERSON><PERSON><PERSON>}}' must be between {{<PERSON>}} and {{<PERSON>}} characters. You entered {{TotalLength}} characters.", "EmailValidator": "'{{<PERSON><PERSON><PERSON>}}' is not a valid email address.", "PhoneValidator": "", "WorkOrderValidationFailed": "Work order based on Process Id: '{{WorkOrderProcessId}}' failed to be created.", "WorkOrderCreationProblem": "Could not create work order due to technical error", "CarNotFound": "{{[propertyPath]PropertyPath}} not found in the Central Address Registry. {{IdName}}: '{{Id}}', Supply type: '{{[enums.supplyType]SupplyType}}'.", "CarInvalidSupplyType": "{{[propertyPath]PropertyPath}} exists in the Central Address Registry, but has an incorrect supply type. {{IdName}}: '{{Id}}', Supply type: '{{[enums.supplyType]SupplyType}}'.", "CarHasNoDetails": "{{[propertyPath]PropertyPath}} in the Central Address Registry has no details. {{IdName}}: '{{Id}}', Supply type: '{{[enums.supplyType]SupplyType}}'.", "CarNoDarIdAssigned": "{{[propertyPath]PropertyPath}} in the Central Address Registry has no DAR ID assigned. {{IdName}}: '{{Id}}', Supply type: '{{[enums.supplyType]SupplyType}}'.", "CarInvalidDarStatus": "{{[propertyPath]PropertyPath}} in the Central Address Registry has an invalid DAR status (expected: 'Yes'). {{IdName}}: '{{Id}}', Supply type: '{{[enums.supplyType]SupplyType}}'.", "CarError": "Error retrieving {{[propertyPath]PropertyPath}} from the Central Address Registry. Status code: '{{StatusCode}}'. {{IdName}}: '{{Id}}', Supply type: '{{[enums.supplyType]SupplyType}}'.", "CarNotActive": "{{[propertyPath]PropertyPath}} exists in the Central Address Registry, but it is not active. {{IdName}}: '{{Id}}', Supply type: '{{[enums.supplyType]SupplyType}}'.", "CarMultipleMatching": "Multiple matching active entries for {{[propertyPath]PropertyPath}} found in the Central Address Registry. {{IdName}}: '{{Id}}', Supply type: '{{[enums.supplyType]SupplyType}}'.", "ConnectionPointNotFound": "Connection point not found.", "ConnectionPointInvalidSupplyType": "Connection point exists in Connection Points service, but it doesn't have the correct supply type. {{IdName}}: '{{Id}}', Supply type: '{{[enums.supplyType]SupplyType}}'.", "ConnectionPointNotActive": "Connection point exists in Connection Points service but, address is not active. {{IdName}}: '{{Id}}', Supply type: '{{[enums.supplyType]SupplyType}}'.", "ConnectionPointMultipleMatching": "Search resulted in more than one connection point. {{IdName}}: '{{Id}}', Supply type: '{{[enums.supplyType]SupplyType}}', Connection point number: '{{ConnectionPointNumber}}'.", "ConnectionPointError": "An error occurred while fetching {{[propertyPath]PropertyPath}} from the Connection Points service. Response status code '{{StatusCode}}'. {{IdName}}: '{{Id}}', Supply type: '{{[enums.supplyType]SupplyType}}'.", "MeterFrameNotFound": "{{[propertyPath]PropertyPath}} not found. {{IdName}}: '{{Id}}', Supply type: '{{[enums.supplyType]SupplyType}}'.", "MeterFrameInvalidSupplyType": "{{[propertyPath]PropertyPath}} exists but it doesn't have the correct supply type. Id: '{{Id}}', Meter frame number: '{{MeterFrameNumber}}', Supply type: '{{[enums.supplyType]SupplyType}}'.", "MeterFrameMeterMissing": "Meter is reported missing for meter frame with id '{{Id}}' and meter frame number '{{MeterFrameNumber}}'.", "MeteringPointMultipleMatching": "Multiple matching metering points.", "MeteringPointNotFound": "Metering point not found.", "MeteringPointError": "Error encountered while accessing Metering Points service. Response status code '{{StatusCode}}'.", "InvoicingError": "Error occurred while creating an invoice.", "AddressDetailsValidator": "{{[propertyPath]PropertyPath}} is missing address details. DAR ID: '{{DarID}}'.", "MasterDataByRowIdNotFound": "Form with Id '{{FormId}}' is not related to Master Data Process with Row Id '{{MasterDataProcessRowId}}'.", "MasterDataValidationFailed": "Master Data Process based on Row Id: '{{MasterDataProcessRowId}}' failed to be created. Master Data Errors: '{{MasterDataErrors}}'", "MasterDataCreationProblem": "Could not create Master Data Process due to technical error.", "AttachmentFromFormSystemHasInvalidFileName": "Attachment from installer has invalid file name: '{{FileName}}'.", "AttachmentFromFormSystemHasNoFileName": "Attachment from installer is missing a file name.", "AttachmentFromFormSystemExceedsSizeLimit": "Attachment from installer, with file name '{{FileName}}', exceeds allowed file size.", "AttachmentFromFormSystemWithoutFileNameExceedsSizeLimit": "Attachment from installer exceeds allowed file size.", "AttachmentFromFormSystemAlreadyExists": "Attachment from installer with file name '{{FileName}}' already exists.", "AttachmentFromFormSystemWithoutFileNameAlreadyExists": "Attachment from installer already exists.", "AttachmentSyncFailedAccessForbidden": "Attachment handling for installation forms missing permissions.", "AttachmentSyncFailedAttachmentTypeNotEnabled": "Attachment handling for installation forms not enabled.", "AttachmentFromFormSystemFailed": "Adding attachment with name '{{FileName}}' from installer failed. Reason: '{{Message}}'", "AttachmentFromFormSystemWithoutFileNameFailed": "Adding attachment from installer failed. Reason: '{{Message}}'", "AttachmentFromElementsFailed": "Adding attachment with name '{{<PERSON>N<PERSON>}}' from case worker failed. Reason: '{{Message}}'", "AttachmentFromElementsWithoutFileNameFailed": "Adding attachment from case worker failed. Reason: '{{Message}}'", "FormSystemValidationFailed": "Validation failed in {{FormSystemName}}. Reason: '{{Message}}'", "FormSystemError": "Failure communicating with {{FormSystemName}}. Reason: '{{Message}}'", "FlagMeterSetupCannotBeUnsetByDso": "The flag 'Meter installed' cannot be unset by the case worker/DSO", "FlagReadyForMeterCannotBeUnsetByDso": "The flag 'Ready for meter' cannot be unset by the case worker/DSO", "MeterInstalledFlagFailedToUpdateInFormSystem": "Updating the flag 'Meter installed' failed in {{FormSystemName}}", "SupplierSelectedFlagFailedToUpdateInFormSystem": "Updating the flag 'Supplier selected' failed in {{FormSystemName}}", "ConnectionFeePaidFlagFailedToUpdateInFormSystem": "Updating the flag 'Connection fee paid' failed in {{FormSystemName}}", "ReadyForMeterFlagFailedToUpdateInFormSystem": "Updating the flag 'Ready for meter' failed in {{FormSystemName}}", "SubmittedForProjectPlanningFlagFailedToUpdateInFormSystem": "Updating the flag 'Submitted for project planning' failed in {{FormSystemName}}", "FormDataUpdateFailedInFormSystemDuringArchive": "Failed to update form data in {{FormSystemName}} before archiving", "FormInstructionDataUpdateFailedInFormSystemDuringArchive": "Failed to update instruction data in {{FormSystemName}} before archiving", "ChatMessageSyncFailed": "The chat message was not delivered to {{FormSystemName}}. Reason: '{{Message}}'", "ValueListItemMappingFromEltilmeldingCodeFailed": "Failed to map to value list item from Eltilmelding code. Value list name: '{{ValueListName}}', Eltilmelding code: '{{ExternalCode}}'.", "ValueListItemMappingToEltilmeldingCodeFailed": "Failed to map value list item to valid Eltilmelding code. Value list name: '{{ValueListName}}', Display value: '{{ValueListItemDisplayValue}}'.", "CommunicationTemplatesNoTemplateFound": "No Communication Template found.", "CommunicationTemplatesMoreThanOneTemplateFound": "More than one Communication Template found.", "RangeValidator": "'{{<PERSON><PERSON><PERSON>}}' must be in range of {{<PERSON>}} and {{<PERSON>}}, but {{ActualValue}} was provided.", "CarEmptyAddress": "{{[propertyPath]PropertyPath}} is missing required address details. {{IdName}}: '{{Id}}', Supply type: '{{[enums.supplyType]SupplyType}}'.", "MarketParticipantsError": "Error encountered while accessing Market Participants service. Response status code '{{StatusCode}}'.", "MarketParticipantsNoGridAreaFound": "No Grid Area with id '{{GridAreaId}}' found.", "MarketParticipantsMoreThanOneGridAreaFound": "More than one Grid Area with id '{{GridAreaId}}' found.", "MarketParticipantsCouldNotSetDefaultGridArea": "Could not set default grid area. Service returned {{GridAreasCount}} grid areas. Please select a valid grid area.", "ApplicationsMultipleWithSameType": "Applications contain {{ApplicationTypeCount}} rows of type '{{[enums.applicationType]ApplicationTypeName}}'. Max one of each is allowed.", "MeterTransformerIdSetToOther": "The field Meter transformer must be filled by a value from the value list different from \"Other\".", "FlagDoesNotExist": "Flag does not exist.", "MeterFrameMultipleMatching": "Search resulted in more than one {{[propertyPath]PropertyPath}}. {{IdName}}: '{{Id}}', Supply type: '{{[enums.supplyType]SupplyType}}'.", "MDPConnectionPointTemplateError": "Error occurred while fetching master data process template. Template id: '{{Id}}'", "MDPConnectionPointTemplateNotFound": "Selected master data process template does not exist. Template id: '{{Id}}'", "MeterFrameNotFoundByConnectionPointAndMeterNumber": "{{[propertyPath]PropertyPath}} not found. Connection point number: '{{ConnectionPointNumber}}',  Meter number: '{{MeterNumber}}', Supply type: '{{[enums.supplyType]SupplyType}}'.", "ConnectionPointErrorSearchMeterFrame": "An error occurred while fetching {{[propertyPath]PropertyPath}} from the Connection Points service. Response status code '{{StatusCode}}'. Connection point number: '{{ConnectionPointNumber}}',  Meter number: '{{MeterNumber}}', Supply type: '{{[enums.supplyType]SupplyType}}'.", "MeterFrameMultipleMatchingByConnectionPointAndMeterNumber": "Search resulted in more than one {{[propertyPath]PropertyPath}}. Connection point number: '{{ConnectionPointNumber}}', Meter number: '{{MeterNumber}}'", "ConnectionRightsReadError": "An error occurred while trying to read connection rights.", "InvoiceCreationFailedNoConnectionRightsToInvoice": "There're no connection rights left to invoice. No invoice has been created.", "InvoiceCreationFailedPricesDefinitionFetchError": "An error occurred while generating invoice. Prices could not be fetched.", "InvoiceCreationFailedPriceElementForConnectionRightsCategoryIsMissing": "Price element for connection rights category {{Category}} is missing. Invoice not created.", "InvoiceCreationFailedPriceElementForConnectionRightsCategoryIsDuplicated": "Price element for connection rights category {{Category}} is duplicated. Invoice not created.", "InvoiceCreationFailedSingleInvoicingCreateInvoiceError": "An error occurred while generating invoice. Could not create invoice.", "FormShouldNotHaveMeterNumberUponReceival": "Form should not contain meter number upon registration.", "MeterNeedsReconfiguration": "Meter needs reconfiguration.", "MeterNeedsChange": "Meter needs change.", "ConnectionFeeUpdateErrorInvalidInvoiceCurrency": "Error updating connection fee under instruction information. Invalid currency for related invoices: {{InvoiceIds}}. Supply type: '{{[enums.supplyType]SupplyType}}'.", "ManuallySetConnectionFeeDiffersFromInvoiceTotals": "Manually set connection fee under instruction information is different from the invoiced total sum. Supply type: '{{[enums.supplyType]SupplyType}}', Calculated connection fee: {{CalculatedConnectionFee}}, Manually set connection fee: {{PreviouslyManuallySetConnectionFee}}.", "ConnectionFeeUpdatedAfterInstruction": "Connection fee updated after instruction. Supply type: '{{[enums.supplyType]SupplyType}}', Calculated connection fee: {{CalculatedConnectionFee}}, Connection fee under instruction information: {{PreviouslySetConnectionFee}}.", "ConnectionPointSetButItWasNotExpected": "There is an existing connection point. Automatic processing is stopped until manual verification of a form.", "SharedBranchLineMeterFrameMissingOwnedMainBranchLine": "The meter frame selected for shared branch line must have an owned main branch line. Meter frame Id: '{{MeterFrameId}}', Meter frame number: '{{MeterFrameNumber}}', Supply type: '{{[enums.supplyType]SupplyType}}'.", "SharedBranchLineNestingCorrected": "Meter frame for shared branch line was changed to use owned main branch line. '{{ConnectedMeterFrameNumber}}' meter frame's branch line will be used for requested meter frame '{{MeterFrameNumber}}'. Meter frame Id: '{{MeterFrameId}}', Meter frame number: '{{MeterFrameNumber}}', Connected meter frame Id: '{{ConnectedMeterFrameId}}', Connected meter frame number: '{{ConnectedMeterFrameNumber}}', Supply type: '{{[enums.supplyType]SupplyType}}'.", "SharedBranchLineInfo": "Branch line type is shared, so changes will not be automatically sent to the main branch line and must be handled manually.", "AddressEmpty": "{{[propertyPath]PropertyPath}} has neither DAR Id nor CAR Id assigned.", "MeterFrameInvalidElectricityPurpose": "Meter frame electricity purpose is invalid. Expected value is 'Måling' (Measurement).", "NoConnectionPointFoundOnTheAddress": "No connection point found on the address, please manually select the correct connection point. Address: '{{Address}}', Car Id: '{{CarId}}', Dar Id: '{{DarId}}', Supply type: '{{[enums.supplyType]SupplyType}}'.", "MeterNumberDoesNotMatchConnectionPoint": "Meter number '{{MeterNumber}}' does not match connection point matched by address. Address: '{{Address}}', Meter number: '{{MeterNumber}}', Supply type: '{{[enums.supplyType]SupplyType}}'.", "MeteringPointIdDoesNotMatchConnectionPoint": "Metering point id '{{MeteringPointId}}' does not match connection point matched by the address '{{Address}}'. Address: '{{Address}}', Metering point Id: '{{MeteringPointId}}', Supply type: '{{[enums.supplyType]SupplyType}}'.", "ConnectionPointNumberDoesNotMatchConnectionPoint": "Connection point number '{{ConnectionPointNumber}}' does not match connection point matched by the address '{{Address}}'. Address: '{{Address}}', Connection point number: '{{ConnectionPointNumber}}', Supply type: '{{[enums.supplyType]SupplyType}}'.", "NotPossibleToMatchUniqueConnectionPoint": "It was not possible to match a unique connection point based on address '{{Address}}', meter number '{{MeterNumber}}', metering point id '{{MeteringPointId}}' or connection point number '{{ConnectionPointNumber}}'. Address: '{{Address}}', Meter number: '{{MeterNumber}}', Metering point Id: '{{MeteringPointId}}', Connection point number: '{{ConnectionPointNumber}}', Supply type: '{{[enums.supplyType]SupplyType}}'.", "MeterNumberWasNotProvidedByFormSystemButRequired": "Meter number was not provided by form system, but it's required. Supply type: '{{[enums.supplyType]SupplyType}}'.", "MeterFramesError": "Error encountered while accessing Meter frames service. Response status code '{{StatusCode}}'.", "MeterFramesNotFound": "Some meter frames were not found: {{Ids}}.", "MeterRemovalNotStarted": "Work order must be started to remove meter for meter frame: '{{MeterFrameNumber}}'.", "ScopeOfDeliveryMissingError": "Scope of deliver data missing on the form. Create connection rights task cannot be completed.", "MatchingConnectionPointWithNewlyCreatedElectricityConnectionStatusExistsInfo": "Matching Connection Point with newly created electricity connection status for corresponding address already exists. Please verify it and select if correct.", "ConnectionRightsBelowScopeOfDeliveryAfterConnectionFeePaidFlagSet": "Scope of delivery of the assigned meter frame has changed and is now lower than the scope of delivery on the form, but the connection fee paid flag has already been set.", "InvoiceCreationFailedPriceElementForStandardFeeIsMissing": "Selected price '{{InvoicingPriceName}}' does not have a price definition or it has no version for date ({{PriceDate}}). Invoice not created.", "InvoicingPriceDoesNotHavePriceDefinition": "Selected price '{{InvoicingPriceName}}' does not point to an existing price definition. Invoice not created."}, "formsSettings": {"invoice": {"tabLabel": "Form settings", "tabAreaLabel": "Invoicing", "displayName": "Name", "formTypes": "Form types", "formCategories": "Form categories", "voltageLevels": "Voltage levels", "scopeOfDelivery": "Scope of delivery", "scopeOfDeliveryRange": "Scope of delivery", "scopeOfDeliveryMin": "Scope of delivery min.", "scopeOfDeliveryMax": "Scope of delivery max.", "canTransferInvoiceAutomatically": "Transfer invoice automatically", "getInvoiceRulesError": "Error occurred while getting automatic invoice rules.", "createRule": "Create rule", "invoiceRuleCreationError": "Error occurred while creating automatic invoice rule.", "invoiceRuleWarningMinGreaterThanMax": "Scope of delivery min. must be less than scope of delivery max.", "invoiceRuleEditError": "Error occurred while editing automatic invoice rule.", "invoiceRuleDeleteError": "Error occurred while deleting automatic invoice rule.", "invoiceRuleReorderError": "Error occurred while reordering automatic invoice rules.", "deleteConfirmationTitle": "Delete rule", "deleteConfirmationMessage": "Are you sure you want to delete this automatic invoice rule?", "productionCapacityRange": "Production capacity", "productionCapacityMin": "Production capacity min.", "productionCapacityMax": "Production capacity max.", "hadExistingProduction": "Existing production", "invoiceType": "Invoice type", "tariff": "Tariff"}, "automaticEmails": {"tabLabel": "Form settings", "tabAreaLabel": "Emails", "displayName": "Name", "formTypes": "Form types", "formStates": "Form states", "formCategories": "Form categories", "emailTemplateName": "Email template name", "createRule": "Create rule", "deleteConfirmationMessage": "Are you sure you want to delete this automatic emails rule?", "deleteConfirmationTitle": "Delete rule", "automaticEmailsRuleCreationError": "Error occurred while creating automatic emails rule.", "automaticEmailsRuleEditError": "Error occurred while editing automatic emails rule.", "automaticEmailsRuleDeleteError": "Error occurred while deleting automatic emails rule.", "automaticEmailsRuleReorderError": "Error occurred while reordering automatic emails rules."}, "masterData": {"tabLabel": "Form settings", "tabAreaLabel": "Master data", "displayName": "Name", "formTypes": "Form types", "formStates": "Form states", "voltageLevels": "Voltage levels", "scopeOfDelivery": "Scope of delivery", "scopeOfDeliveryMin": "Scope of delivery min.", "scopeOfDeliveryMax": "Scope of delivery max.", "scopeOfDeliveryUom": "Scope of delivery unit", "totalCapacity": "Total capacity", "totalCapacityMin": "Total capacity min. [kW]", "totalCapacityMax": "Total capacity max. [kW]", "startsAsConstruction": "Starts as construction", "terminationScope": "Termination scope", "masterDataProcessType": "Process type", "masterDataProcessTemplate": "Process template", "masterDataProcessAutomationLevel": "Automation level", "masterDataProcessWorkOrderAutomationLevel": "Work order automation level", "getMasterDataRulesError": "Error occurred while getting master data rules.", "createRule": "Create rule", "masterDataRuleCreationError": "Error occurred while creating master data rule.", "masterDataRuleEditError": "Error occurred while editing master data rule.", "masterDataRuleDeleteError": "Error occurred while deleting master data rule.", "masterDataRuleReorderError": "Error occurred while reordering master data rules.", "scopeOfDeliveryMinMaxEmptyWarning": "Remove scope of delivery unit or set min/max values", "scopeOfDeliveryMinGreaterThanMaxWarning": "Scope of delivery min. must be less than scope of delivery max.", "totalCapacityMinGreaterThanMaxWarning": "Total capacity min. must be less than total capacity max.", "deleteConfirmationTitle": "Delete rule", "deleteConfirmationMessage": "Are you sure you want to delete this master data rule?", "workOrderType": "Work order type", "workOrderDescriptionId": "Work order description", "workOrderPurposeId": "Work order purpose"}, "workOrders": {"tabLabel": "Form settings", "tabAreaLabel": "Work orders", "displayName": "Name", "formTypes": "Form types", "formStates": "Form states", "sealResponsibles": "Seal responsibles", "meterResponsibles": "Meter delivery responsible", "meterNeedsChange": "Meter needs change", "startsAsConstruction": "Starts as construction", "invoicePaid": "Invoice paid", "readyForMeter": "Meter ready", "supplierSelected": "Supplier selected", "workOrderType": "Work order type", "workOrderDescriptionId": "Work order description", "workOrderPurposeId": "Work order purpose", "getWorkOrderRulesError": "Error occurred while getting work order rules.", "createRule": "Create rule", "workOrderRuleCreationError": "Error occurred while creating work order rule.", "workOrderRuleEditError": "Error occurred while editing work order rule.", "workOrderRuleDeleteError": "Error occurred while deleting work order rule.", "workOrderRuleReorderError": "Error occurred while reordering work order rules.", "deleteConfirmationTitle": "Delete rule", "deleteConfirmationMessage": "Are you sure you want to delete this work order rule?"}, "automaticScreening": {"tabLabel": "Form settings", "tabAreaLabel": "Allow automatization", "displayName": "Name", "formTypes": "Form types", "formStates": "Form states", "formCategories": "Form categories", "voltageLevels": "Voltage levels", "scopeOfDelivery": "Scope of delivery", "scopeOfDeliveryMin": "Scope of delivery min.", "scopeOfDeliveryMax": "Scope of delivery max.", "scopeOfDeliveryUom": "Scope of delivery unit", "terminationScope": "Termination scope", "meterReturnOptionTypes": "Meter return option types", "meterPlacementCodes": "Meter placement codes", "meterSize": "Meter size", "groundingMethods": "Grounding methods", "reasonsForChange": "Reasons for change", "getAutomaticScreeningRulesError": "Error occurred while getting automatic allow automatization rules.", "createRule": "Create rule", "automaticScreeningRuleCreationError": "Error occurred while creating automatic allow automatization rule.", "automaticScreeningRuleEditError": "Error occurred while editing automatic allow automatization rule.", "automaticScreeningRuleDeleteError": "Error occurred while deleting automatic allow automatization rule.", "automaticScreeningRuleReorderError": "Error occurred while reordering automatic allow automatization rules.", "scopeOfDeliveryMinMaxEmptyWarning": "Remove scope of delivery unit or set min/max values", "scopeOfDeliveryMinGreaterThanMaxWarning": "Scope of delivery min. must be less than scope of delivery max.", "totalCapacityMinGreaterThanMaxWarning": "Total capacity min. must be less than total capacity max.", "deleteConfirmationTitle": "Delete rule", "deleteConfirmationMessage": "Are you sure you want to delete this automatic allow automatization rule?"}, "defaultInstructionTexts": {"tabLabel": "Form settings", "tabAreaLabel": "Default instruction texts", "text": "Text", "getInstructionTextsError": "Error occurred while getting default instruction texts.", "addText": "Add text", "instructionTextCreationError": "Error occurred while creating default instruction text.", "instructionTextEditError": "Error occurred while editing default instruction text.", "instructionTextDeleteError": "Error occurred while deleting default instruction text.", "instructionTextReorderError": "Error occurred while reordering default instruction texts.", "deleteConfirmationTitle": "Delete text", "deleteConfirmationMessage": "Are you sure you want to delete this default instruction text?"}, "automaticArchiving": {"tabLabel": "Form settings", "tabAreaLabel": "Archiving", "displayName": "Name", "formTypes": "Form types", "formStates": "Form states", "responsibleForMeter": "Meter delivery responsible", "meterInstalled": "Meter installed", "startsAsConstruction": "Starts as construction", "verifiedWorkOrderType": "Verified work order type", "verifiedWorkOrderDescription": "Verified work order description", "masterDataProcessTypes": "Master data process types", "getAutomaticArchivingRulesError": "Error occurred while getting automatic archivization rules.", "createRule": "Create rule", "automaticArchivingRuleCreationError": "Error occurred while creating automatic archivization rule.", "automaticArchivingRuleEditError": "Error occurred while editing automatic archivization rule.", "automaticArchivingRuleDeleteError": "Error occurred while deleting automatic archivization rule.", "automaticArchivingRuleReorderError": "Error occurred while reordering automatic archivization rules.", "verifiedWorkOrderDescriptionRequiredWarning": "Verified work wrder description is required value if work order type was selected.", "deleteConfirmationTitle": "Delete rule", "deleteConfirmationMessage": "Are you sure you want to delete this automatic archivization rule?"}, "automaticInstructionTexts": {"tabLabel": "Form settings", "tabAreaLabel": "Instruction texts", "displayName": "Name", "formTypes": "Form types", "formCategories": "Form categories", "meterNeedsChange": "Meter needs change", "meterNeedsReconfiguration": "Meter needs reconfiguration", "formIsScreened": "Form allows automatization", "defaultInstructionTexts": "Default instruction texts", "getAutomaticInstructionTextRulesError": "Error occurred while getting automatic instruction text rules.", "createRule": "Create rule", "automaticInstructionTextRuleCreationError": "Error occurred while creating automatic instruction text rule.", "automaticInstructionTextRuleEditError": "Error occurred while editing automatic instruction text rule.", "automaticInstructionTextRuleDeleteError": "Error occurred while deleting automatic instruction text rule.", "automaticInstructionTextRuleReorderError": "Error occurred while reordering automatic instruction text rules.", "deleteConfirmationTitle": "Delete rule", "deleteConfirmationMessage": "Are you sure you want to delete this automatic instruction text rule?"}, "priceDefinitions": {"tabLabel": "Form settings", "tabAreaLabel": "Price definitions", "addPriceDefinition": "Add price definition", "displayName": "Name", "displayNameNotUnique": "Name must be unique within supply type.", "externalPriceName": "External price name", "supplyType": "Supply type", "addError": "Error occurred while adding price definition.", "updateError": "Error occurred while updating price definition.", "deleteConfirmationTitle": "Delete price definition", "deleteConfirmationMessage": "Are you sure you want to delete this price definition?", "deleteError": "Error occurred while deleting price definition.", "loadError": "Error occurred while loading price definitions.", "validation": {"requiredFields": "All fields are required."}, "externalPriceMissing": "Price definition does not exist."}, "internalResource": {"tabLabel": "Form settings", "tabAreaLabel": "Internal resource", "defaultSenderAddress": "Sender", "defaultReceiverAddress": "Receiver", "defaultSubject": "Subject", "template": "Template", "getError": "Error occurred while loading internal resource settings.", "updateError": "Error occurred while updating internal resource settings.", "noPermissionToReadEmailTemplates": "You don't have permission to read email templates."}}, "propertyPath": {"Address": "Installation address", "BranchLine": {"MeterFrame": "Shared branch line meter frame"}, "ConnectionAddress": {"Address": "Connection address"}, "MeterDelivery": {"DeliveryInformation": {"Address": "Recipient address"}}, "MeterFrame": "Meter frame", "MeterReturn": {"Information": {"Address": "Warehouse address"}}, "Owner": {"Address": "Owner address"}, "Payer": {"Address": "Payer address"}}, "masterDataCompare": {"path": {"MeterFrame.PlacementCode": "Meter frame -> Placement code", "MeterFrame.ElectricityAttributes.ConnectionType": "Meter frame -> Electricity attributes -> Connection type", "MeterFrame.ElectricityAttributes.RatioCt": "Meter frame -> Electricity attributes -> Ratio CT", "MeterFrame.ElectricityAttributes.MeterFrameFuse": "Meter frame -> Electricity attributes -> Meter frame fuse", "MeterFrame.GisPropertiesElectricity.BranchLineFuseType": "Meter frame -> GIS attributes -> Branch line fuse type", "MeterFrame.GisPropertiesElectricity.BranchLineFuseAmps": "Meter frame -> GIS attributes -> Branch line fuse amps", "MeterFrame.MainBranchLineElectricity.NumberOfCables": "Meter frame -> Branch lines -> Main branch line -> Number of cables", "MeterFrame.GisPropertiesElectricity.CabinetNumber": "Meter frame -> GIS attributes -> Cabinet number", "MeterFrame.GisPropertiesElectricity.StationNumber": "Meter frame -> GIS attributes -> Station number", "MeterFrame.ElectricityAttributes.TarifConnectionPoint": "Meter frame -> Electricity attributes -> Tarif connection point", "MeterFrame.CommonReading": "Meter frame -> Common reading", "MeterFrame.ElectricityAttributes.BreakerBeforeMeter": "Meter frame -> Electricity attributes -> Breaker before meter", "MeterFrame.MainBranchLineElectricity.BranchLineType": "Meter frame -> Branch lines -> Main branch line -> Branchline type", "MeterFrame.MainBranchLineElectricity.SystemGrounding": "Meter frame -> Branch lines -> Main branch line -> System grounding", "ConnectionPoint.ElectricityAttributes.GridAreaId": "Connection point -> Electricity attributes -> Grid area id", "ConnectionRight.Value": "Meter frame -> Connection rights -> Value"}, "tooltipPrefix": "Master data path: ", "toggleLabel": "Master data compare", "getDataError": "Failed to load master data compare"}}