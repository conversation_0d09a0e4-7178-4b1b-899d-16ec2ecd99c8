import { ColumnTransformationType } from 'src/app/core/enums/column-transformation-type';
import { RulesListColumn } from '../../models/rules-list-column';
import { automaticArchivingTranslationPath } from './automatic-archiving.consts';

export const AUTOMATIC_ARCHIVING_COLUMNS: RulesListColumn[] = [
    {
        field: 'displayName',
        header: `${automaticArchivingTranslationPath}.displayName`,
        columnTransformationType: ColumnTransformationType.None,
        isDefault: true
    },
    {
        field: 'formTypes',
        header: `${automaticArchivingTranslationPath}.formTypes`,
        translationPath: 'enums.type.',
        columnTransformationType: ColumnTransformationType.TranslateEnumArray,
        isDefault: true
    },
    {
        field: 'formStates',
        header: `${automaticArchivingTranslationPath}.formStates`,
        translationPath: 'enums.state.',
        columnTransformationType: ColumnTransformationType.TranslateEnumArray,
        isDefault: true
    },
    {
        field: 'responsibleForMeter',
        header: `${automaticArchivingTranslationPath}.responsibleForMeter`,
        translationPath: 'enums.responsibleForMeter.',
        columnTransformationType: ColumnTransformationType.TranslateEnumArray,
        isDefault: true
    },
    {
        field: 'meterInstalled',
        header: `${automaticArchivingTranslationPath}.meterInstalled`,
        columnTransformationType: ColumnTransformationType.Boolean,
        isDefault: true
    },
    {
        field: 'startsAsConstruction',
        header: `${automaticArchivingTranslationPath}.startsAsConstruction`,
        columnTransformationType: ColumnTransformationType.Boolean,
        isDefault: true
    },
    {
        field: 'verifiedWorkOrderType',
        header: `${automaticArchivingTranslationPath}.verifiedWorkOrderType`,
        translationPath: 'enums.workOrderType.',
        columnTransformationType: ColumnTransformationType.Translate,
        isDefault: true
    },
    {
        field: 'verifiedWorkOrderDescription',
        header: `${automaticArchivingTranslationPath}.verifiedWorkOrderDescription`,
        columnTransformationType: ColumnTransformationType.ValueListSingleItem,
        isDefault: true
    },
    {
        field: 'masterDataProcessTypes',
        header: `${automaticArchivingTranslationPath}.masterDataProcessTypes`,
        translationPath: 'enums.masterDataProcessType.',
        columnTransformationType: ColumnTransformationType.TranslateEnumArray,
        isDefault: true
    }
];
