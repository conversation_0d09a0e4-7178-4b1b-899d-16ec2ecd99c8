import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { Subscription, distinctUntilChanged } from 'rxjs';
import { CommonReading, FormType, PayerType, ValueListType } from 'src/app/api/installation-forms-client';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import { WidgetWithFormComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget-with-form.component';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';

@Component({
    selector: 'app-payer-section',
    templateUrl: './payer-section.component.html',
    styleUrls: ['./payer-section.component.scss'],
    standalone: false
})
export class PayerSectionComponent extends WidgetWithFormComponent implements OnInit, OnDestroy {
    readonly meterFrameCommonReadingValueListType: ValueListType = ValueListType.MeterFrameCommonReading;

    private _commonReadings: CommonReading[] | null = null;
    public visibleCommonReadings: CommonReading[] | null = null;
    public showMoreCommonReadingsCount: number = 2;
    public showAllCommonReadings: boolean = false;

    private _isPayerDataVisible: boolean = false;

    @Input()
    set isPayerDataVisible(value: boolean) {
        if (this._isPayerDataVisible !== value) {
            this._isPayerDataVisible = value;
        }
    }

    get isPayerDataVisible() {
        return this._isPayerDataVisible;
    }

    private _isPayerRequired: boolean = false;

    @Input()
    set isPayerRequired(value: boolean) {
        this._isPayerRequired = value;
        if (this._isPayerRequired) {
            this.isPayerDataVisible = true;
            this.payerDataVisibilityChanged.emit(true);
        }
    }

    get isPayerRequired() {
        return this._isPayerRequired;
    }

    @Output()
    payerDataVisibilityChanged: EventEmitter<boolean> = new EventEmitter();

    payerTypeOptions: SelectItem[] = [];

    isPayerTypeNotPublic = () => {
        return this.getSelectedPayerType() !== PayerType.Public;
    };

    isPayerTypeCompany = () => {
        return this.getSelectedPayerType() === PayerType.Company;
    };

    isPayerTypePrivate = () => {
        return this.getSelectedPayerType() === PayerType.Private;
    };

    isPayerTypePublic = () => {
        return this.getSelectedPayerType() === PayerType.Public;
    };

    getSelectedPayerType(): PayerType {
        return this.form?.get('payerType')?.value;
    }

    @Input() form!: FormGroup;

    get commonReadings(): CommonReading[] | null {
        return this._commonReadings || new Array<CommonReading>();
    }

    @Input()
    set commonReadings(value: CommonReading[] | null | undefined) {
        this._commonReadings = value || null;
        this.handleShowLessCommonReadings();
    }

    @Input() commonReadingId!: string;

    @Output() changesMade = new EventEmitter<{ key: string; hasChanges: boolean }>();

    subscription: Subscription = new Subscription();

    constructor(
        private readonly translateService: TranslateService,
        readonly formDataService: FormDataService
    ) {
        super(formDataService);
    }

    ngOnInit(): void {
        this.subscription.add(
            this.form
                .get('payerType')
                ?.valueChanges.pipe(distinctUntilChanged())
                .subscribe({
                    next: (payerTypeValue) => {
                        this.onPayerTypeChanged(this.form, payerTypeValue);
                    }
                })
        );

        this.getEnumTranslations();
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    onPayerTypeChanged(formGroup: FormGroup, payerType?: PayerType) {
        if (this.form.disabled) {
            return;
        }

        if (payerType === PayerType.Private) {
            formGroup.get('requisition')?.reset();
            formGroup.get('requisition')?.disable();

            formGroup.get('cvrOrSeNumber')?.reset();
            formGroup.get('cvrOrSeNumber')?.disable();

            formGroup.get('eanNumber')?.reset();
            formGroup.get('eanNumber')?.disable();
            return;
        }

        formGroup.get('requisition')?.enable();
        formGroup.get('cvrOrSeNumber')?.enable();
        formGroup.get('eanNumber')?.enable();
    }

    handleShowMoreCommonReadings() {
        if (!this.commonReadings) {
            return;
        }

        this.visibleCommonReadings = this.commonReadings.slice();
        this.showAllCommonReadings = true;
    }

    handleShowLessCommonReadings() {
        if (!this.commonReadings) {
            return;
        }

        this.visibleCommonReadings = this.commonReadings.slice(0, this.showMoreCommonReadingsCount);
        this.showAllCommonReadings = false;
    }

    onShowPayerDataToggleChange(event: any) {
        const newVal = !event.checked;
        this.payerDataVisibilityChanged.emit(newVal);
    }

    showCommonReadings() {
        return this.formDataService.type == FormType.NewInstallation;
    }

    private getEnumTranslations() {
        this.payerTypeOptions = enumMapper.map(this.translateService.instant('enums.payerType'), PayerType);
    }
}
