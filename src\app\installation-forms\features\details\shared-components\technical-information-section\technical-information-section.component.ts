import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { distinctUntilChanged, Subscription } from 'rxjs';
import {
    BranchLineType,
    ConnectionTypeChange,
    GroundingMethod,
    MainProtectionType,
    MeterSize,
    PreProtectionType,
    ValueListType
} from 'src/app/api/installation-forms-client';
import { METER_TRANSFORMER_OTHER_VALUE_ID, PhaseCountOptions } from 'src/app/core/constants/constants';
import { FormDataService } from 'src/app/core/services/form-data/form-data.service';
import { ValueListsService } from 'src/app/core/services/value-lists/value-lists.service';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import { WidgetWithFormComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget-with-form.component';
import { MeterFrameSearchItemModel } from 'src/app/shared/components/meter-frame-search/meter-frame-search-item.model';

@Component({
    selector: 'app-technical-information-section',
    templateUrl: './technical-information-section.component.html',
    standalone: false
})
export class TechnicalInformationSectionComponent extends WidgetWithFormComponent implements OnInit, OnDestroy {
    @Input() form!: FormGroup;
    @Input() showMeterPlacement: boolean = false;
    @Input() showPhaseCount: boolean = false;
    @Input()
    showInstallInConstructionPhase = false;

    @Input()
    branchLineMeterFrameOptions: MeterFrameSearchItemModel[] = [];

    readonly meterFramePlacementValueListType: ValueListType = ValueListType.MeterFramePlacementCode;
    readonly meterFrameMeterTransformerCodeValueListType: ValueListType = ValueListType.MeterFrameElectricityAttributesRatioCT;
    readonly meterFrameConnectionTypeValueListType: ValueListType = ValueListType.MeterFrameElectricityConnectionType;
    readonly branchLineCableDimensionValueListType: ValueListType = ValueListType.BranchLineElectricityCableDimension;
    readonly branchLineElectricityPEmm2ValueListType: ValueListType = ValueListType.BranchLineElectricityPEmm2;

    get meterTypeForm(): FormGroup {
        return this.form?.get('meterType') as FormGroup;
    }
    get connectionForm(): FormGroup {
        return this.form?.get('connection') as FormGroup;
    }
    get branchLineForm(): FormGroup {
        return this.form?.get('branchLine') as FormGroup;
    }

    subscription: Subscription = new Subscription();

    phaseCountOptions: SelectItem[] = PhaseCountOptions;
    protectionTypeOfPreProtectionOptions: SelectItem[] = [];
    groundingMethodOptions: SelectItem[] = [];

    protectionTypeOfMainProtectionOptions: SelectItem[] = [];
    branchLineTypeOptions: SelectItem[] = [];

    connectionTypeChangeOptions: SelectItem[] = [];
    meterSizeOptions: SelectItem[] = [];
    yesNoOptions: SelectItem[] = [];

    constructor(
        private readonly translateService: TranslateService,
        protected readonly formDataService: FormDataService,
        protected readonly valueListsService: ValueListsService
    ) {
        super(formDataService);
    }

    ngOnInit(): void {
        this.subscription.add(
            this.branchLineForm
                .get('protectionTypeOfMainProtection')
                ?.valueChanges.pipe(distinctUntilChanged())
                .subscribe({
                    next: (protectionTypeOfMainProtection) => {
                        this.onMainProtectionChange(this.branchLineForm, protectionTypeOfMainProtection);
                    }
                })
        );

        this.subscription.add(
            this.connectionForm
                .get('protectionTypeOfPreProtection')
                ?.valueChanges.pipe(distinctUntilChanged())
                .subscribe({
                    next: (protectionTypeOfPreProtection) => {
                        this.onPreProtectionChange(this.connectionForm, protectionTypeOfPreProtection);
                    }
                })
        );

        this.subscription.add(
            this.meterTypeForm.get('meterSize')?.valueChanges.subscribe(() => {
                if (!this.isConnectionTypeVisible()) {
                    this.meterTypeForm.get('connectionTypeId')?.setValue(null);
                } else {
                    this.meterTypeForm.get('meterTransformerId')?.setValue(null);
                }
            })
        );

        this.subscription.add(
            this.meterTypeForm.get('meterTransformerId')?.valueChanges.subscribe(() => {
                if (!this.isMeterTransformerRemarkVisible()) {
                    this.meterTypeForm.get('meterTransformerRemark')?.setValue(null);
                }
            })
        );

        this.getEnumTranslations();

        this.yesNoOptions = [
            { label: this.translateService.instant('common.yesOption'), value: true },
            { label: this.translateService.instant('common.noOption'), value: false }
        ];
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    onMainProtectionChange(formGroup: FormGroup, mainProtectionType?: MainProtectionType): void {
        if (this.form.disabled) {
            return;
        }
        const fuseTypeOfMainProtectionFieldName = 'fuseTypeOfMainProtection';
        if (mainProtectionType === MainProtectionType.MaxBreaker) {
            formGroup.get(fuseTypeOfMainProtectionFieldName)?.reset();
            formGroup.get(fuseTypeOfMainProtectionFieldName)?.disable();
            return;
        }

        formGroup.get(fuseTypeOfMainProtectionFieldName)?.enable();
    }

    onPreProtectionChange(formGroup: FormGroup, protectionTypeOfPreProtection?: PreProtectionType): void {
        if (this.form.disabled) {
            return;
        }

        const fuseSizeOfPreProtectionFieldName = 'fuseSizeOfPreProtection';
        if (protectionTypeOfPreProtection === PreProtectionType.None) {
            formGroup.get(fuseSizeOfPreProtectionFieldName)?.reset();
            formGroup.get(fuseSizeOfPreProtectionFieldName)?.disable();
            return;
        }

        formGroup.get(fuseSizeOfPreProtectionFieldName)?.enable();
    }

    isConnectionTypeVisible = () => {
        return this.getMeterSize() === MeterSize.Below63A;
    };

    isConnectionTypeChangeVisible = () => {
        return this.meterTypeForm.get('connectionTypeChange') != null; // Only show if defined in FormGroup.
    };

    isMeterTransformerVisible = () => {
        return this.getMeterSize() === MeterSize.Above63A;
    };

    getMeterSize() {
        return this.meterTypeForm?.get('meterSize')?.value;
    }

    isMeterTransformerRemarkVisible = () => {
        return this.isMeterTransformerVisible() && this.getMeterTransformer() === METER_TRANSFORMER_OTHER_VALUE_ID;
    };

    getMeterTransformer() {
        return this.meterTypeForm?.get('meterTransformerId')?.value;
    }

    isPreProtectionFuseSizeRequired = () => {
        const value = this.connectionForm?.get('protectionTypeOfPreProtection')?.value;
        return value === PreProtectionType.MaxBreaker || value === PreProtectionType.Fuse;
    };

    isFuseTypeOfMainProtectionRequired = () => {
        const value = this.branchLineForm?.get('protectionTypeOfMainProtection')?.value;
        return value === MainProtectionType.Fuse;
    };

    areBranchLineTypeIdentifiersVisible = () => {
        return this.branchLineForm?.get('type')?.value === BranchLineType.Existing;
    };

    private getEnumTranslations() {
        this.groundingMethodOptions = enumMapper.map(
            this.translateService.instant('enums.connectionGroundingMethod'),
            GroundingMethod
        );
        this.protectionTypeOfPreProtectionOptions = enumMapper.map(
            this.translateService.instant('enums.preProtectionType'),
            PreProtectionType
        );
        this.protectionTypeOfMainProtectionOptions = enumMapper.map(
            this.translateService.instant('enums.mainProtectionType'),
            MainProtectionType
        );
        this.branchLineTypeOptions = enumMapper.map(this.translateService.instant('enums.branchLineType'), BranchLineType);
        this.connectionTypeChangeOptions = enumMapper.map(
            this.translateService.instant('enums.meterConnectionTypeChange'),
            ConnectionTypeChange
        );
        this.meterSizeOptions = enumMapper.map(this.translateService.instant('enums.meterSize'), MeterSize);
    }
}
