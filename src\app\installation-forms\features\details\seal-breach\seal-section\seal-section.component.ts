import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { Subscription } from 'rxjs';
import { ReasonForChange, ResponsibleForSeal, ValueListType } from 'src/app/api/installation-forms-client';
import { ValueListsService } from 'src/app/core/services/value-lists/value-lists.service';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import { WidgetWithFormComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget-with-form.component';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';

@Component({
    selector: 'app-seal-breach-seal-section',
    templateUrl: './seal-section.component.html',
    styleUrls: ['./seal-section.component.scss'],
    standalone: false
})
export class SealBreachSealSectionComponent extends WidgetWithFormComponent implements OnInit, OnDestroy {
    @Input() form!: FormGroup;

    subscription: Subscription = new Subscription();

    responsibleForSealOptions: SelectItem[] = [];
    reasonForChangeOptions: SelectItem[] = [];
    readonly meterFramePlacementValueListType: ValueListType = ValueListType.MeterFramePlacementCode;

    constructor(
        private readonly translateService: TranslateService,
        readonly formDataService: FormDataService,
        protected readonly valueListsService: ValueListsService
    ) {
        super(formDataService);
    }

    ngOnInit(): void {
        this.getEnumTranslations();
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    getEnumTranslations() {
        this.responsibleForSealOptions = enumMapper.map(
            this.translateService.instant('enums.responsibleForSeal'),
            ResponsibleForSeal
        );

        this.reasonForChangeOptions = enumMapper.map(this.translateService.instant('enums.reasonForChange'), ReasonForChange);
    }
}
