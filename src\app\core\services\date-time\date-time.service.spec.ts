import { TestBed } from '@angular/core/testing';
import { ConfigurationService, TenantConfiguration } from '@kmd-elements-ui/configuration';
import { of } from 'rxjs';
import { DateTimeService } from './date-time.service';

describe('DateTimeService', () => {
    let service: DateTimeService;
    let mockConfigService: ConfigurationService;

    const mockTenantConfig: TenantConfiguration = {
        ianaTimezoneId: 'Europe/Copenhagen'
        // Add other required properties for TenantConfiguration here
    } as TenantConfiguration;

    beforeEach(() => {
        // Create mock for ConfigurationService
        mockConfigService = {
            tenantConfiguration$: of(mockTenantConfig)
        } as Partial<ConfigurationService> as ConfigurationService;

        TestBed.configureTestingModule({
            providers: [DateTimeService, { provide: ConfigurationService, useValue: mockConfigService }]
        });

        service = TestBed.inject(DateTimeService);
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    describe('transformDateTime', () => {
        it('should return empty string for undefined date', () => {
            const result = service.transformDateTime(undefined);
            expect(result).toBe('');
        });

        it('should return empty string for null date', () => {
            const result = service.transformDateTime(null as unknown as Date);
            expect(result).toBe('');
        });

        it('should return empty string for invalid date', () => {
            const invalidDate = new Date('invalid date');
            const result = service.transformDateTime(invalidDate);
            expect(result).toBe('');
        });

        it('should format date with time in sv-SE locale', () => {
            // We need to mock toLocaleString since it depends on browser locale settings
            const testDate = new Date(2023, 4, 15, 14, 30); // May 15, 2023, 14:30
            const mockToLocaleString = jest.spyOn(testDate, 'toLocaleString').mockReturnValue('2023-05-15 14:30');

            const result = service.transformDateTime(testDate);

            expect(result).toBe('2023-05-15 14:30');
            expect(mockToLocaleString).toHaveBeenCalledWith('sv-SE', {
                timeZone: 'Europe/Copenhagen',
                timeStyle: 'short',
                dateStyle: 'short'
            });

            mockToLocaleString.mockRestore();
        });
    });

    describe('transformDate', () => {
        it('should return empty string for undefined date', () => {
            const result = service.transformDate(undefined);
            expect(result).toBe('');
        });

        it('should return empty string for null date', () => {
            const result = service.transformDate(null as unknown as Date);
            expect(result).toBe('');
        });

        it('should return empty string for invalid date', () => {
            const invalidDate = new Date('invalid date');
            const result = service.transformDate(invalidDate);
            expect(result).toBe('');
        });

        it('should format date without time in sv-SE locale', () => {
            // We need to mock toLocaleString since it depends on browser locale settings
            const testDate = new Date(2023, 4, 15); // May 15, 2023
            const mockToLocaleString = jest.spyOn(testDate, 'toLocaleString').mockReturnValue('2023-05-15');

            const result = service.transformDate(testDate);

            expect(result).toBe('2023-05-15');
            expect(mockToLocaleString).toHaveBeenCalledWith('sv-SE', {
                timeZone: 'Europe/Copenhagen',
                timeStyle: undefined,
                dateStyle: 'short'
            });

            mockToLocaleString.mockRestore();
        });
    });
});
