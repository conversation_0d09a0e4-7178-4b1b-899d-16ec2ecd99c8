{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"installation-forms": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "ngx-build-plus:browser", "options": {"outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["node_modules/primeflex/primeflex.css", "node_modules/@kmd-elements/kmd-elements-theme/theme/theme.css", "node_modules/@kmd-elements/kmd-elements-theme/theme/fonts/fontawesome-pro/css/all.css", "src/styles.scss"], "scripts": [], "extraWebpackConfig": "webpack.config.js", "commonChunk": false}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "4kb", "maximumError": "8kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all", "extraWebpackConfig": "webpack.prod.config.js"}, "ci-debug": {"outputPath": "dist-debug", "budgets": [{"type": "anyComponentStyle", "maximumWarning": "6kb"}], "assets": ["src/assets", "src/favicon.ico", "src/web.config", "src/appconfig.json"], "optimization": false, "sourceMap": true, "namedChunks": true, "extractLicenses": false, "vendorChunk": true, "buildOptimizer": false}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "proxy": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.proxy.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"builder": "ngx-build-plus:dev-server", "configurations": {"production": {"buildTarget": "installation-forms:build:production", "extraWebpackConfig": "webpack.prod.config.js"}, "development": {"buildTarget": "installation-forms:build:development"}, "proxy": {"extraWebpackConfig": "webpack.dev.proxy.config.js", "buildTarget": "installation-forms:build:development,proxy"}}, "defaultConfiguration": "development", "options": {"port": 4207, "publicHost": "http://localhost:4207", "extraWebpackConfig": "webpack.config.js"}}, "extract-i18n": {"builder": "ngx-build-plus:extract-i18n", "options": {"buildTarget": "installation-forms:build", "extraWebpackConfig": "webpack.config.js"}}, "test": {"builder": "@angular-builders/jest:run", "options": {"configPath": "./jest.config.js"}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"eslintConfig": "eslint.config.mjs", "lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"schematicCollections": ["@angular-eslint/schematics"], "analytics": false}}