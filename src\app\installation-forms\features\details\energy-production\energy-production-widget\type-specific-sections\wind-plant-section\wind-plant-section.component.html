<ng-container [formGroup]="form">
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['certificate']" for="certificate">
                {{ 'energyProductionSection.windPlant.certificate' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input
                id="certificate"
                type="text"
                pInputText
                maxlength="100"
                formControlName="certificate"
                (ngModelChange)="onValueChange()" />
            <small [controlValidationErrors]="form.controls['certificate']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['certificationProvider']" for="certificationProvider">
                {{ 'energyProductionSection.windPlant.certificationProvider' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input
                id="certificationProvider"
                type="text"
                pInputText
                maxlength="100"
                formControlName="certificationProvider"
                (ngModelChange)="onValueChange()" />
            <small [controlValidationErrors]="form.controls['certificationProvider']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['rotorDiameterInMeters']" for="rotorDiameterInMeters">
                {{ 'energyProductionSection.windPlant.rotorDiameterInMeters' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-inputNumber
                id="rotorDiameterInMeters"
                formControlName="rotorDiameterInMeters"
                [showClear]="true"
                (ngModelChange)="onValueChange()"></p-inputNumber>
            <small [controlValidationErrors]="form.controls['rotorDiameterInMeters']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['hubHeightInMeters']" for="hubHeightInMeters">
                {{ 'energyProductionSection.windPlant.hubHeightInMeters' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-inputNumber
                id="hubHeightInMeters"
                formControlName="hubHeightInMeters"
                [showClear]="true"
                (ngModelChange)="onValueChange()"></p-inputNumber>
            <small [controlValidationErrors]="form.controls['hubHeightInMeters']" class="p-error"></small>
        </div>
    </div>
</ng-container>
