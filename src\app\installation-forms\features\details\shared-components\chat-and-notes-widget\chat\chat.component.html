<p-scrollPanel styleClass="chat-scroll-panel" #scrollPanel>
    <div class="pr-20 pb-20">
        <div class="flex flex-column gap-2 pr-3">
            <ng-container *ngFor="let message of messages">
                <p-card [header]="message.author" class="flex chat-message" *ngIf="message.sourceType === 'FormSystem'">
                    <p>{{ message.message }}</p>
                    <div class="flex align-items-center justify-content-between mt-2">
                        <h4>{{ message.timestamp | formatDateTime }}</h4>
                        <i *ngIf="message.isUnread" class="pi pi-circle-fill ml-2 color-warning"></i>
                    </div>
                </p-card>

                <div class="flex justify-content-end" *ngIf="message.sourceType === 'Elements'">
                    <p-card [header]="message.author" class="flex chat-message">
                        <p [innerText]="message.message"></p>
                        <div class="flex align-items-center justify-content-between mt-2">
                            <h4 class="mt-2">{{ message.timestamp | formatDateTime }}</h4>
                            <div [ngSwitch]="message.deliveryState">
                                <i *ngSwitchCase="'Sent'" class="fa-solid fa-check color-info"></i>
                                <i *ngSwitchCase="'Delivered'" class="fa-solid fa-check-double color-info"></i>
                                <i *ngSwitchCase="'Failed'" class="fa-solid fa-xmark color-error"></i>
                            </div>
                        </div>
                        <h4 class="color-error mt-2" *ngIf="message.deliveryState === 'Failed'">
                            {{ 'chatAndNotesWidget.chat.syncFailedAt' | translate }}
                        </h4>
                    </p-card>
                </div>
            </ng-container>
        </div>
    </div>
</p-scrollPanel>

<div *ngIf="canCreateChatMessage">
    <p-divider />
    <div class="flex flex-row gap-2" [formGroup]="form">
        <textarea
            pTextarea
            class="add-chat-or-note-textarea"
            [autoResize]="true"
            [maxlength]="1000"
            formControlName="message"
            [rows]="1"></textarea>
        <div class="flex flex-row gap-2">
            <button
                id="sendChatMessageButton"
                type="button"
                pButton
                pRipple
                [disabled]="isProcessing || formMessageIsEmpty()"
                class="p-button-primary h-max"
                (click)="createNewChatMessage()">
                <span *ngIf="isProcessing" class="fa fa-spin fa-spinner"></span>
                {{ 'chatAndNotesWidget.chat.sendMessage' | translate }}
            </button>
            <button
                id="markAllAsRead"
                type="button"
                pButton
                pRipple
                *ngIf="hasUserWritePermission && isCurrentUserAssignedAsCaseWorker"
                [disabled]="isProcessing || !hasUnreadMessages()"
                class="p-button-secondary button-no-wrap h-max"
                (click)="markAllMessagesAsRead()">
                <span *ngIf="isProcessing" class="fa fa-spin fa-spinner"></span>
                {{ 'chatAndNotesWidget.chat.markAllAsRead' | translate }}
            </button>
        </div>
    </div>
</div>
