import { Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { Subscription } from 'rxjs';
import { MoveMeterChangeReason, ResponsibleForSeal, ValueListType } from 'src/app/api/installation-forms-client';
import { ValueListsService } from 'src/app/core/services/value-lists/value-lists.service';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import { WidgetWithFormComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget-with-form.component';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';

@Component({
    selector: 'app-move-meter-section',
    templateUrl: './move-meter-section.component.html',
    styleUrls: ['./move-meter-section.component.scss'],
    standalone: false
})
export class MoveMeterSectionComponent extends WidgetWithFormComponent implements OnInit, OnDestroy {
    @Input() form!: FormGroup;

    subscription: Subscription = new Subscription();

    reasonForChangeOptions: SelectItem[] = [];
    responsibleForSealOptions: SelectItem[] = [];
    readonly meterFramePlacementValueListType: ValueListType = ValueListType.MeterFramePlacementCode;

    get meterTypeForm(): FormGroup {
        return this.form?.get('meterType') as FormGroup;
    }

    constructor(
        private readonly translateService: TranslateService,
        readonly formDataService: FormDataService,
        protected readonly valueListsService: ValueListsService
    ) {
        super(formDataService);
    }

    ngOnInit(): void {
        this.getEnumTranslations();
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    getEnumTranslations() {
        this.reasonForChangeOptions = enumMapper.map(
            this.translateService.instant('enums.reasonForChange'),
            MoveMeterChangeReason
        );

        this.responsibleForSealOptions = enumMapper.map(
            this.translateService.instant('enums.responsibleForSeal'),
            ResponsibleForSeal
        );
    }
}
