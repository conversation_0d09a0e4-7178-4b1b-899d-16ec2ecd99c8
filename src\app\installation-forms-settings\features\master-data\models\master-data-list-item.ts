import {
    AutomationVoltageLevel,
    FormState,
    FormType,
    MasterDataProcessAutomationLevel,
    MasterDataProcessType,
    MasterDataProcessWorkOrderAutomationLevel,
    TerminationScope,
    WorkOrderType
} from 'src/app/api/installation-forms-client';

export interface MasterDataListItem {
    id: string;
    order: number;
    displayName: string;
    formTypes: FormType[];
    formStates: FormState[];
    voltageLevels: AutomationVoltageLevel[];
    totalCapacity?: string;
    scopeOfDelivery?: string;
    startsAsConstruction?: boolean;
    terminationScope?: TerminationScope;
    masterDataProcessType: MasterDataProcessType;
    masterDataProcessTemplate?: string;
    masterDataProcessAutomationLevel: MasterDataProcessAutomationLevel;
    masterDataProcessWorkOrderAutomationLevel?: MasterDataProcessWorkOrderAutomationLevel;
    workOrderType?: WorkOrderType;
    workOrderDescriptionId?: string;
    workOrderPurposeId?: string;
}
