import { Pipe, PipeTransform } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Pipe({
    name: 'masterDataPathTooltip',
    standalone: false
})
export class MasterDataPathTooltipPipe implements PipeTransform {
    tooltipPrefix: string;

    constructor(private readonly translateService: TranslateService) {
        this.tooltipPrefix = this.translateService.instant('masterDataCompare.tooltipPrefix');
    }

    transform(path?: string): string {
        if (!path) {
            return '';
        }

        return `${this.tooltipPrefix} ${this.translateService.instant('masterDataCompare.path.' + path)}`;
    }
}
