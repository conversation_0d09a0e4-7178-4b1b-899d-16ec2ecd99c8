import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { AuthorizationService } from '@kmd-elements-ui/authorization';
import { TranslateService } from '@ngx-translate/core';
import { MenuItem, SelectItem } from 'primeng/api';
import { Subscription, catchError, filter, map } from 'rxjs';
import { FormType, SupplyType } from 'src/app/api/installation-forms-client';
import { Permissions } from 'src/app/core/constants/permissions';
import { AutomaticFormRefreshService } from 'src/app/core/services/automatic-form-refresh/automatic-form-refresh.service';
import { EmailTemplatesService } from 'src/app/core/services/email-templates/email-templates.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { WidgetComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget.component';
import { FormDataService } from '../../../../../../core/services/form-data/form-data.service';

@Component({
    selector: 'app-emails-widget',
    templateUrl: './emails-widget.component.html',
    standalone: false
})
export class EmailsWidgetComponent extends WidgetComponent implements OnInit, OnDestroy {
    menuItems: MenuItem[] = [];
    visibleMenuItems: MenuItem[] = [];
    activeAction = {
        sendEmail: false
    };
    templateOptions: SelectItem[] = [];

    hasUserSendEmailPermission = false;
    hasUserReadEmailPermission = false;

    _isCurrentUserAssignedAsCaseWorker: boolean = false;

    @Input()
    set isCurrentUserAssignedAsCaseWorker(value: boolean) {
        this._isCurrentUserAssignedAsCaseWorker = value;
        this.refreshVisibleMenuItems();
    }

    get isCurrentUserAssignedAsCaseWorker(): boolean {
        return this._isCurrentUserAssignedAsCaseWorker;
    }

    subscription: Subscription = new Subscription();

    constructor(
        private readonly translateService: TranslateService,
        private readonly messageServiceHelper: MessageServiceHelper,
        private readonly emailTemplatesService: EmailTemplatesService,
        private readonly authService: AuthorizationService,
        protected readonly formDataService: FormDataService,
        private readonly automaticFormRefreshService: AutomaticFormRefreshService
    ) {
        super(formDataService);
    }

    ngOnInit(): void {
        this.authService
            .hasPermissions([
                Permissions.meteringPoints.read,
                Permissions.communicationChannelEmail.read,
                Permissions.communicationChannelEmail.write,
                Permissions.communicationTemplates.read
            ])
            .subscribe((x) => (this.hasUserSendEmailPermission = x));

        this.authService
            .hasPermissions([Permissions.communicationChannelEmail.read])
            .subscribe((x) => (this.hasUserReadEmailPermission = x));

        this.loadTemplates();
    }

    private loadTemplates() {
        if (!this.hasUserSendEmailPermission) {
            return;
        }
        this.emailTemplatesService
            .getEmailTemplates(this.formDataService.supplyType as SupplyType)
            .pipe(
                filter((response) => response.result !== undefined),
                map((response) =>
                    response.result!.map(
                        (result) =>
                            ({
                                value: result.id,
                                label: result.name
                            }) as SelectItem
                    )
                ),
                catchError((err) => {
                    throw err;
                })
            )
            .subscribe({
                next: (mappedResult) => {
                    this.templateOptions = mappedResult;
                },
                error: (_) => {
                    this.messageServiceHelper.showError({
                        detail: this.translateService.instant('common.loadEmailTemplatesError'),
                        key: this.formDataService.formId!
                    });
                }
            });
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    onEmailSent() {
        this.onClosePanelRequested();
        this.automaticFormRefreshService.restartPolling();
    }

    onClosePanelRequested() {
        this.activeAction = {
            sendEmail: false
        };
    }

    private refreshVisibleMenuItems() {
        this.visibleMenuItems = this.isCurrentUserAssignedAsCaseWorker ? this.menuItems : [];
    }

    protected showSendEmailButton = () =>
        this.isFormTypeIncludedInArray([FormType.NewInstallation]) &&
        this.formDataService.canCurrentUserExecuteActions &&
        this.hasUserSendEmailPermission;

    protected openSendEmail() {
        this.activeAction.sendEmail = true;
    }

    private isFormTypeIncludedInArray(types: FormType[]): boolean {
        if (!this.formDataService.type) return false;
        let included = types.includes(this.formDataService.type);
        return included;
    }
}
