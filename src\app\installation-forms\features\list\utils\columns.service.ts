import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import {
    FormCategory,
    FormProblemCategory,
    FormState,
    FormType,
    ProcessLevelIndicators,
    ValueListType
} from 'src/app/api/installation-forms-client';
import { ColumnTransformationType } from 'src/app/core/enums/column-transformation-type';
import { SearchOperator } from 'src/app/core/enums/search-operator';
import { ColumnFilterType } from '../../../../core/enums/column-filter-type';
import { InstallationFormsListColumn } from '../models/list-column';

@Injectable({
    providedIn: 'root'
})
export class InstallationFormsColumnsService {
    constructor(private readonly translateService: TranslateService) {}

    getColumns(): InstallationFormsListColumn[] {
        const columns: InstallationFormsListColumn[] = [
            {
                field: 'formNumber',
                header: this.translateService.instant('home.columnHeaders.formNumber'),
                columnFilterType: ColumnFilterType.String,
                operators: [
                    SearchOperator.StartsWith,
                    SearchOperator.Contains,
                    SearchOperator.NotContains,
                    SearchOperator.EndsWith,
                    SearchOperator.Equals,
                    SearchOperator.NotEquals
                ],
                columnTransformationType: ColumnTransformationType.None,
                isVisible: true
            },
            {
                field: 'type',
                header: this.translateService.instant('home.columnHeaders.type'),
                translationPath: 'enums.type.',
                columnFilterType: ColumnFilterType.Multiselect,
                enumReference: FormType,
                columnTransformationType: ColumnTransformationType.Translate,
                isVisible: true
            },
            {
                field: 'category',
                header: this.translateService.instant('home.columnHeaders.category'),
                translationPath: 'enums.category.',
                columnFilterType: ColumnFilterType.Multiselect,
                enumReference: FormCategory,
                columnTransformationType: ColumnTransformationType.NoValueEnumTranslate,
                isVisible: true
            },
            {
                field: 'state',
                header: this.translateService.instant('home.columnHeaders.state'),
                translationPath: 'enums.state.',
                columnFilterType: ColumnFilterType.Multiselect,
                enumReference: FormState,
                columnTransformationType: ColumnTransformationType.Translate,
                isVisible: true
            },
            {
                field: 'installationAddress',
                header: this.translateService.instant('home.columnHeaders.installationAddress'),
                columnFilterType: ColumnFilterType.Address,
                operators: [SearchOperator.Contains],
                columnTransformationType: ColumnTransformationType.None,
                isVisible: true
            },
            {
                field: 'formRequiresAttention',
                header: this.translateService.instant('home.columnHeaders.formRequiresAttention'),
                columnFilterType: ColumnFilterType.Boolean,
                columnTransformationType: ColumnTransformationType.Boolean,
                isVisible: true
            },
            {
                field: 'hasUnreadMessages',
                header: this.translateService.instant('home.columnHeaders.hasUnreadMessages'),
                columnFilterType: ColumnFilterType.Boolean,
                columnTransformationType: ColumnTransformationType.Boolean,
                isVisible: false
            },
            {
                field: 'problemCategory',
                header: this.translateService.instant('home.columnHeaders.problemCategories'),
                translationPath: 'enums.problemCategory.',
                columnFilterType: ColumnFilterType.Multiselect,
                enumReference: FormProblemCategory,
                columnTransformationType: ColumnTransformationType.TranslateEnumArray,
                isVisible: true
            },
            {
                field: 'createdDate',
                header: this.translateService.instant('home.columnHeaders.createdDate'),
                columnFilterType: ColumnFilterType.Date,
                operators: [
                    SearchOperator.DateIs,
                    SearchOperator.DateIsNot,
                    SearchOperator.DateIsBefore,
                    SearchOperator.DateIsAfter
                ],
                columnTransformationType: ColumnTransformationType.Date,
                isVisible: true
            },
            {
                field: 'caseWorkerName',
                header: this.translateService.instant('home.columnHeaders.caseWorkerName'),
                columnFilterType: ColumnFilterType.StringWithNoValueFilter,
                operators: [
                    SearchOperator.StartsWith,
                    SearchOperator.Contains,
                    SearchOperator.NotContains,
                    SearchOperator.EndsWith,
                    SearchOperator.Equals,
                    SearchOperator.NotEquals,
                    SearchOperator.Empty
                ],
                columnTransformationType: ColumnTransformationType.None,
                isVisible: false
            },
            {
                field: 'caseWorkerEmail',
                header: this.translateService.instant('home.columnHeaders.caseWorkerEmail'),
                columnFilterType: ColumnFilterType.StringWithNoValueFilter,
                operators: [
                    SearchOperator.StartsWith,
                    SearchOperator.Contains,
                    SearchOperator.NotContains,
                    SearchOperator.EndsWith,
                    SearchOperator.Equals,
                    SearchOperator.NotEquals,
                    SearchOperator.Empty
                ],
                columnTransformationType: ColumnTransformationType.None,
                isVisible: false
            },
            {
                field: 'installerName',
                header: this.translateService.instant('home.columnHeaders.installerName'),
                isVisible: false,
                columnFilterType: ColumnFilterType.String,
                operators: [
                    SearchOperator.StartsWith,
                    SearchOperator.Contains,
                    SearchOperator.NotContains,
                    SearchOperator.EndsWith,
                    SearchOperator.Equals,
                    SearchOperator.NotEquals
                ],
                columnTransformationType: ColumnTransformationType.None
            },
            {
                field: 'installerAuthorizationNumber',
                header: this.translateService.instant('home.columnHeaders.installerAuthorizationNumber'),
                isVisible: false,
                columnFilterType: ColumnFilterType.String,
                operators: [
                    SearchOperator.StartsWith,
                    SearchOperator.Contains,
                    SearchOperator.NotContains,
                    SearchOperator.EndsWith,
                    SearchOperator.Equals,
                    SearchOperator.NotEquals
                ],
                columnTransformationType: ColumnTransformationType.None
            },
            {
                field: 'scopeOfDeliverySize',
                header: this.translateService.instant('home.columnHeaders.scopeOfDeliverySize'),
                isVisible: false,
                columnFilterType: ColumnFilterType.Number,
                operators: [
                    SearchOperator.Equals,
                    SearchOperator.NotEquals,
                    SearchOperator.LessThan,
                    SearchOperator.LessThanOrEqualTo,
                    SearchOperator.GreaterThan,
                    SearchOperator.GreaterThanOrEqualTo
                ],
                columnTransformationType: ColumnTransformationType.None
            },
            {
                field: 'tags',
                header: this.translateService.instant('home.columnHeaders.tags'),
                columnFilterType: ColumnFilterType.ValueList,
                valueListType: ValueListType.InstallationFormTags,
                columnTransformationType: ColumnTransformationType.Array,
                isVisible: true
            },
            {
                field: 'installInConstructionPhase',
                header: this.translateService.instant('home.columnHeaders.installInConstructionPhase'),
                columnFilterType: ColumnFilterType.Boolean,
                columnTransformationType: ColumnTransformationType.Boolean,
                isVisible: false
            },
            {
                field: 'processLevelIndicators',
                header: this.translateService.instant('home.columnHeaders.processLevelIndicators'),
                translationPath: 'enums.processLevelIndicators.',
                enumReference: ProcessLevelIndicators,
                columnFilterType: ColumnFilterType.Multiselect,
                columnTransformationType: ColumnTransformationType.TranslateEnumArray,
                isVisible: false
            },
            {
                field: 'latestRegistrationDate',
                header: this.translateService.instant('home.columnHeaders.latestRegistrationDate'),
                columnFilterType: ColumnFilterType.Date,
                operators: [
                    SearchOperator.DateIs,
                    SearchOperator.DateIsNot,
                    SearchOperator.DateIsBefore,
                    SearchOperator.DateIsAfter,
                    SearchOperator.Empty
                ],
                columnTransformationType: ColumnTransformationType.Date,
                isVisible: false
            }
        ];

        return columns;
    }
}
