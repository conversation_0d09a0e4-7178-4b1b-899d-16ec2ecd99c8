import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { <PERSON><PERSON>rray, FormBuilder, FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { finalize, take } from 'rxjs';
import { Flag, FlagType, FlagUpdate, FlagValue, InstallationFormsClient } from 'src/app/api/installation-forms-client';
import { ChangeFieldType } from 'src/app/core/constants/changes-details';
import { WidgetNames } from 'src/app/core/constants/widget-names';
import { AutomaticFormRefreshService } from 'src/app/core/services/automatic-form-refresh/automatic-form-refresh.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { WidgetWithChangesModelComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget-with-changes-model.component';
import { v4 as uuidv4 } from 'uuid';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';

@Component({
    selector: 'app-flags-widget',
    templateUrl: './flags-widget.component.html',
    styleUrls: ['./flags-widget.component.scss'],
    standalone: false
})
export class FlagsWidgetComponent extends WidgetWithChangesModelComponent implements OnInit {
    flagTypes = FlagType;
    private _flags: Flag[] = [];
    private _readyForMeterDate?: Date;
    private commonTranslations: any;
    form!: FormGroup;

    private static readonly flagsOrder = [
        FlagType.SubmittedForProjectPlanning,
        FlagType.SupplierSelected,
        FlagType.ConnectionFeePaid,
        FlagType.ReadyForMeter,
        FlagType.MeterInstalled,
        FlagType.SentToInternalResource,
        FlagType.InternalResourceResponseReceived
    ];

    private static readonly readonlyFlags = [FlagType.SupplierSelected];

    @Input()
    set flags(value: Flag[] | null | undefined) {
        const isInit = value && !this._flags.length;
        if (
            !isInit &&
            !this.isDuringForceRefresh &&
            this.hasConflictingChanges(
                this.convertToComparisonModel(this._flags || []),
                this.getFormComparisonModel(),
                this.convertToComparisonModel(value || [])
            )
        ) {
            this.automaticFormRefreshService.notifyConflictingChangeOccurred();
            return;
        }
        this.dataLoaded();
        this._flags = value || [];
        if (isInit) {
            this.initForm();
        }
        if (this.canSupplyFormData) {
            this.supplyFormData();
        }
    }

    get flags(): Flag[] {
        return this._flags;
    }

    get flagControls(): FormGroup[] {
        if (!this.form) {
            return [];
        }

        return (this.form.get('flags') as FormArray).controls as FormGroup[];
    }

    @Input()
    set readyForMeterDate(value: Date | null | undefined) {
        this._readyForMeterDate = value || undefined;
    }

    get readyForMeterDate(): Date | undefined {
        return this._readyForMeterDate;
    }

    @Output() changesMade = new EventEmitter<{ key: string; hasChanges: boolean }>();

    constructor(
        private readonly fb: FormBuilder,
        private readonly cd: ChangeDetectorRef,
        private readonly messageServiceHelper: MessageServiceHelper,
        private readonly translateService: TranslateService,
        private readonly client: InstallationFormsClient,
        protected readonly formDataService: FormDataService,
        protected readonly automaticFormRefreshService: AutomaticFormRefreshService
    ) {
        super(formDataService, automaticFormRefreshService);

        this.changesModel = {};
        this.form = this.fb.group({});
    }

    override ngOnInit(): void {
        super.ngOnInit();

        this.commonTranslations = this.translateService.instant('common');

        this.updateFormEditability();
        this.formDataService.editabilityChange$.subscribe(() => {
            this.supplyFormData();
            this.updateFormEditability();
        });
    }

    initForm() {
        this.form.addControl(
            'flags',
            this.fb.array(
                this.flags.map((flag) => {
                    this.changesModel[flag.type!] = {
                        change: false,
                        type: ChangeFieldType.Default,
                        dtoFieldName: undefined
                    };
                    return this.fb.group({
                        type: this.fb.control(flag.type),
                        isSet: this.fb.control(flag.value !== FlagValue.NotSet, {}),
                        manuallySetDate: this.fb.control(flag.manuallySetDate || null, {})
                    });
                })
            )
        );
        this.subscription.add((this.form.get('flags') as FormArray).valueChanges.subscribe(() => this.onChange()));
    }

    override get isReadOnly(): boolean {
        return !this.formDataService.canChangeFlags;
    }

    private supplyFormData() {
        const flagsArray = this.form?.get('flags') as FormArray;
        if (!flagsArray?.controls?.length) return;
        flagsArray.setValue(
            this.flags
                .map((flag) => {
                    return {
                        type: flag.type,
                        isSet: flag.value !== FlagValue.NotSet,
                        manuallySetDate: flag.manuallySetDate || null
                    };
                })
                .sort(
                    (a, b) => FlagsWidgetComponent.flagsOrder.indexOf(a.type!) - FlagsWidgetComponent.flagsOrder.indexOf(b.type!)
                )
        );

        setTimeout(() => {
            this.flags
                .filter((x) => x.isReadonly)
                .forEach((readOnlyFlag) => {
                    flagsArray.controls
                        .filter((flagGroup) => flagGroup.value.type === readOnlyFlag.type)
                        .forEach((flagFormGroup) => flagFormGroup.disable());
                });

            // Force readonly of specific flags no matter the domain rules.
            FlagsWidgetComponent.readonlyFlags.forEach((readOnlyFlag) => {
                flagsArray.controls
                    .filter((flagGroup) => flagGroup.value.type === readOnlyFlag)
                    .forEach((flagFormGroup) => flagFormGroup.disable());
            });
        });
    }

    onCancelChangesClicked() {
        this.supplyFormData();
        this.onChange();
    }

    onChange() {
        if (this.isReadOnly) {
            return;
        }
        const initalValues: { [key: string]: any } = this.convertToComparisonModel(this.flags);
        const currentValues: { [key: string]: any } = this.getFormComparisonModel();
        this.emitDirty(this.recalculateChanges(initalValues, currentValues));
    }

    getFormComparisonModel(): any {
        const values: { [key: string]: any } = {};
        const formValue = this.form.value;
        formValue.flags.forEach((flagFormValue: any) => {
            values[flagFormValue.type!] = flagFormValue.isSet;
        });
        return values;
    }

    convertToComparisonModel(flags: Flag[]): any {
        const formValue = this.form.value;
        const values: { [key: string]: any } = {};
        formValue.flags.forEach((flagFormValue: any) => {
            values[flagFormValue.type!] = flags.filter((x) => x.type === flagFormValue.type)[0].value !== FlagValue.NotSet;
        });

        return values;
    }

    onSaveClicked() {
        if (this.isFormValid()) {
            const updateModel = this.createFlagsUpdate();
            this.processingStarted();
            this.client
                .updateInstallationFormFlagValues(
                    this.formDataService.formId!,
                    uuidv4(),
                    this.formDataService.rowVersion,
                    updateModel
                )
                .pipe(
                    take(1),
                    finalize(() => this.processingFinished())
                )
                .subscribe({
                    error: (_) => {
                        this.messageServiceHelper.showError({
                            key: this.formDataService.formId!
                        });
                    },
                    complete: () => {
                        this.messageServiceHelper.showSuccess({
                            key: this.formDataService.formId!
                        });

                        this.reinitAfterSave(updateModel);
                    }
                });
        } else {
            this.messageServiceHelper.showWarning({
                detail: this.commonTranslations['allRequiredFieldsShouldBeCompleted'],
                key: this.formDataService.formId!
            });
        }
    }

    private reinitAfterSave(updateModel: FlagUpdate[]) {
        updateModel.forEach((f) => {
            const currentFlag = this.flags.filter((x) => x.type === f.type)[0];
            if (currentFlag.value === FlagValue.NotSet) {
                if (f.isSet) {
                    currentFlag.value = FlagValue.SetManually;
                }
            } else if (!f.isSet) {
                currentFlag.value = FlagValue.NotSet;
            }
        });

        this.changesAmount = 0;
        this.cd.detectChanges();
        this.emitDirty(false);
        this.automaticFormRefreshService.forceRefresh();
    }

    private createFlagsUpdate(): FlagUpdate[] {
        const formValue = this.form.value;
        return formValue.flags.map((flag: any) => {
            return {
                isSet: flag.isSet,
                type: flag.type
            };
        });
    }

    private emitDirty(hasChanges: boolean) {
        this.changesMade.emit({
            key: WidgetNames.flagsWidget,
            hasChanges: hasChanges
        });
    }

    private isFormValid(): boolean {
        Object.keys(this.form.controls).forEach((field) => {
            const control = this.form.get(field)!;
            control.updateValueAndValidity({
                onlySelf: true,
                emitEvent: false
            });
        });

        return this.form.valid;
    }
}
