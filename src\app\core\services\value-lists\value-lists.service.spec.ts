import { TestBed } from '@angular/core/testing';
import { ValueListHeaderModel, ValueListItemModel, ValueListModel } from '@kmd-elements/core-kit';
import { TranslateService } from '@ngx-translate/core';
import { of } from 'rxjs';
import { InstallationFormsClient, ValueListType } from '../../../api/installation-forms-client';
import { MessageServiceHelper } from '../message/message.service';
import { ValueListsService } from './value-lists.service';

// Mock UUID to make testing deterministic
jest.mock('uuid', () => ({
    v4: () => 'test-uuid'
}));

describe('ValueListsService', () => {
    let service: ValueListsService;
    let mockClient: InstallationFormsClient;
    let mockMessageServiceHelper: jest.Mocked<MessageServiceHelper>;
    let mockTranslateService: jest.Mocked<TranslateService>;

    // Create mock value lists response
    const mockValueLists = {
        result: {
            models: {
                [ValueListType.BranchLineElectricityCableDimension]: {
                    id: ValueListType.BranchLineElectricityCableDimension,
                    activeHeaders: [{ id: 'header1', systemIdentifier: 'testHeader' } as ValueListHeaderModel],
                    valueItems: [
                        {
                            id: 'item1',
                            attributes: [{ valueListHeaderId: 'header1', value: 'testValue' }]
                        } as ValueListItemModel
                    ]
                } as ValueListModel
            }
        }
    };

    beforeEach(() => {
        // Create mock for the client
        mockClient = {
            getValueLists: jest.fn().mockReturnValue(of(mockValueLists))
        } as Partial<InstallationFormsClient> as InstallationFormsClient;

        // Create mocks for MessageServiceHelper and TranslateService
        mockMessageServiceHelper = {
            showError: jest.fn()
        } as unknown as jest.Mocked<MessageServiceHelper>;

        mockTranslateService = {
            instant: jest.fn().mockImplementation((key) => key)
        } as unknown as jest.Mocked<TranslateService>;

        TestBed.configureTestingModule({
            providers: [
                ValueListsService,
                { provide: InstallationFormsClient, useValue: mockClient },
                { provide: MessageServiceHelper, useValue: mockMessageServiceHelper },
                { provide: TranslateService, useValue: mockTranslateService }
            ]
        });

        service = TestBed.inject(ValueListsService);
        mockClient = TestBed.inject(InstallationFormsClient);
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    describe('fetchValueLists', () => {
        it('should call client with correct parameters', () => {
            service.fetchValueLists();

            expect(mockClient.getValueLists).toHaveBeenCalledWith(
                'test-uuid',
                expect.any(Array) // This would be all value list types
            );
        });

        it('should store value lists in the service', (done) => {
            service.fetchValueLists();

            // Check if we can retrieve a stored value list
            service.getValueList(ValueListType.BranchLineElectricityCableDimension).subscribe((valueList) => {
                expect(valueList).toEqual(mockValueLists.result.models[ValueListType.BranchLineElectricityCableDimension]);
                done();
            });
        });
    });

    describe('getValueList', () => {
        it('should return the correct value list by type', (done) => {
            service.getValueList(ValueListType.BranchLineElectricityCableDimension).subscribe((valueList) => {
                expect(valueList).toEqual(mockValueLists.result.models[ValueListType.BranchLineElectricityCableDimension]);
                done();
            });
        });

        it('should return undefined for non-existent value list type', (done) => {
            service.getValueList('NON_EXISTENT_TYPE' as ValueListType).subscribe((valueList) => {
                expect(valueList).toBeUndefined();
                done();
            });
        });
    });

    describe('checkIfValueListItemAttributeMatch', () => {
        it('should return false if valueListItemId is undefined', () => {
            const result = service.checkIfValueListItemAttributeMatch(
                ValueListType.BranchLineElectricityCableDimension,
                undefined,
                'testHeader',
                ['testValue']
            );

            expect(result).toBe(false);
        });

        it('should return true when attribute matches', () => {
            const result = service.checkIfValueListItemAttributeMatch(
                ValueListType.BranchLineElectricityCableDimension,
                'item1',
                'testHeader',
                ['testValue']
            );

            expect(result).toBe(true);
        });

        it('should return false when attribute does not match', () => {
            const result = service.checkIfValueListItemAttributeMatch(
                ValueListType.BranchLineElectricityCableDimension,
                'item1',
                'testHeader',
                ['wrongValue']
            );

            expect(result).toBe(false);
        });

        it('should be case insensitive when matching attributes', () => {
            const result = service.checkIfValueListItemAttributeMatch(
                ValueListType.BranchLineElectricityCableDimension,
                'item1',
                'TestHeader', // Different case in header
                ['TESTvalue'] // Different case in value
            );

            expect(result).toBe(true);
        });

        it('should return false when item not found', () => {
            const result = service.checkIfValueListItemAttributeMatch(
                ValueListType.BranchLineElectricityCableDimension,
                'nonExistentItem',
                'testHeader',
                ['testValue']
            );

            expect(result).toBe(false);
        });

        it('should return false when header not found', () => {
            const result = service.checkIfValueListItemAttributeMatch(
                ValueListType.BranchLineElectricityCableDimension,
                'item1',
                'nonExistentHeader',
                ['testValue']
            );

            expect(result).toBe(false);
        });
    });
});
