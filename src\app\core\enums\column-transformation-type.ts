export enum ColumnTransformationType {
    None = 'None',
    Date = 'Date',
    DateTime = 'DateTime',
    Translate = 'Translate',
    TranslateEnumArray = 'TranslateEnumArray',
    Array = 'Array',
    NoValueEnumTranslate = 'NoValueEnumTranslate',
    Boolean = 'Boolean',
    ValueListSingleItem = 'ValueListSingleItem',
    ValueListMultiItem = 'ValueListMultiItem',
    InstructionTexts = 'InstructionTexts',
    InvoicingPrice = 'InvoicingPrice'
}
