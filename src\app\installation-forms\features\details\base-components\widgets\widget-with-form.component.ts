import { AbstractControl, FormGroup } from '@angular/forms';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';
import { WidgetComponent } from './widget.component';

export abstract class WidgetWithFormComponent extends WidgetComponent {
    abstract form: FormGroup;

    constructor(protected readonly formDataService: FormDataService) {
        super(formDataService);
    }

    public shouldShowErrors(fieldName: string, optionalForm: AbstractControl = this.form): boolean {
        const field = optionalForm?.get(fieldName);
        return (field && field.invalid && (field.dirty || field.touched)) || false;
    }

    public hasError(fieldName: string, errorType: string, optionalForm: FormGroup = this.form): boolean {
        const field = optionalForm?.get(fieldName);
        return field && field.errors && field.errors[errorType];
    }

    public controlHasError(control: AbstractControl<any, any>, fieldName: string, errorType: string): boolean {
        const field = control?.get(fieldName);
        return field && field.errors && field.errors[errorType];
    }

    protected processingFinished() {
        super.processingFinished();
        if (!this.form.valid) {
            this.form.markAllAsTouched();
        }
    }
}
