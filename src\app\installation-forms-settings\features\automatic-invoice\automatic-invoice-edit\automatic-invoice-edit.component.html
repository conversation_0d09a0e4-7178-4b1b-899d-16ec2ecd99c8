<div #overlayContainer></div>
<div fxLayout="column" class="mt-10 zebra-container" [formGroup]="form">
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['displayName']" for="displayName">
                {{ 'formsSettings.invoice.displayName' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input id="displayName" type="text" pInputText maxlength="100" formControlName="displayName" autoTrimWhitespaces />
            <small [controlValidationErrors]="form.controls['displayName']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['formTypes']" for="formTypes">
                {{ 'formsSettings.invoice.formTypes' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-multiSelect
                id="formTypes"
                [options]="formTypeOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="formTypes"
                [maxSelectedLabels]="formTypeOptions.length">
            </p-multiSelect>
            <small [controlValidationErrors]="form.controls['formTypes']" class="p-error"></small>
        </div>
    </div>

    <div
        class="zebra-item"
        fxLayout="row"
        fxLayout.lt-sm="column"
        fxLayoutGap="15px"
        *ngIf="settingsService.isFormCategoryRelevant(form)">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['formCategories']" for="formCategories">
                {{ 'formsSettings.invoice.formCategories' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-multiSelect
                id="formCategories"
                [options]="formCategoriesOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="formCategories"
                [maxSelectedLabels]="formCategoriesOptions.length">
            </p-multiSelect>
            <small [controlValidationErrors]="form.controls['formCategories']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['voltageLevels']" for="voltageLevels">
                {{ 'formsSettings.invoice.voltageLevels' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-multiSelect
                id="voltageLevels"
                [options]="voltageLevelOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="voltageLevels"
                [maxSelectedLabels]="voltageLevelOptions.length">
            </p-multiSelect>
            <small [controlValidationErrors]="form.controls['voltageLevels']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['scopeOfDeliveryMin']" for="scopeOfDeliveryMin">
                {{ 'formsSettings.invoice.scopeOfDeliveryMin' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-inputNumber id="scopeOfDeliveryMin" formControlName="scopeOfDeliveryMin"> </p-inputNumber>
            <small [controlValidationErrors]="form.controls['scopeOfDeliveryMin']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['scopeOfDeliveryMax']" for="scopeOfDeliveryMax">
                {{ 'formsSettings.invoice.scopeOfDeliveryMax' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-inputNumber id="scopeOfDeliveryMax" formControlName="scopeOfDeliveryMax"> </p-inputNumber>
            <small [controlValidationErrors]="form.controls['scopeOfDeliveryMax']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['canTransferInvoiceAutomatically']" for="canTransferInvoiceAutomatically">
                {{ 'formsSettings.invoice.canTransferInvoiceAutomatically' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-checkbox formControlName="canTransferInvoiceAutomatically" [binary]="true"></p-checkbox>
            <small [controlValidationErrors]="form.controls['canTransferInvoiceAutomatically']" class="p-error"></small>
        </div>
    </div>

    <div
        class="zebra-item"
        fxLayout="row"
        fxLayout.lt-sm="column"
        fxLayoutGap="15px"
        *ngIf="settingsService.isEnergyProductionSelected(form)">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['productionCapacityMin']" for="productionCapacityMin">
                {{ 'formsSettings.invoice.productionCapacityMin' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-inputNumber
                id="productionCapacityMin"
                formControlName="productionCapacityMin"
                [min]="0"
                [max]="2147483647"
                [useGrouping]="false"
                [minFractionDigits]="0"
                [maxFractionDigits]="1"
                [showClear]="true">
            </p-inputNumber>
            <small [controlValidationErrors]="form.controls['productionCapacityMin']" class="p-error"></small>
        </div>
    </div>

    <div
        class="zebra-item"
        fxLayout="row"
        fxLayout.lt-sm="column"
        fxLayoutGap="15px"
        *ngIf="settingsService.isEnergyProductionSelected(form)">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['productionCapacityMax']" for="productionCapacityMax">
                {{ 'formsSettings.invoice.productionCapacityMax' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-inputNumber
                id="productionCapacityMax"
                formControlName="productionCapacityMax"
                [min]="0"
                [max]="2147483647"
                [useGrouping]="false"
                [minFractionDigits]="0"
                [maxFractionDigits]="1"
                [showClear]="true">
            </p-inputNumber>
            <small [controlValidationErrors]="form.controls['productionCapacityMax']" class="p-error"></small>
        </div>
    </div>

    <div
        class="zebra-item"
        fxLayout="row"
        fxLayout.lt-sm="column"
        fxLayoutGap="15px"
        *ngIf="settingsService.isEnergyProductionSelected(form)">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['hadExistingProduction']" for="hadExistingProduction">
                {{ 'formsSettings.invoice.hadExistingProduction' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="hadExistingProduction"
                formControlName="hadExistingProduction"
                [options]="(booleanOptionsService.getBooleanOptions() | async) ?? []"
                [appendTo]="overlayContainer"
                [showClear]="true"
                [placeholder]="'common.selectValue' | translate">
            </p-select>
            <small [controlValidationErrors]="form.controls['hadExistingProduction']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['invoiceType']" for="invoiceType">
                {{ 'formsSettings.invoice.invoiceType' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="invoiceType"
                [options]="invoiceTypeOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="invoiceType">
            </p-select>
            <small [controlValidationErrors]="form.controls['invoiceType']" class="p-error"></small>
        </div>
    </div>

    <div *ngIf="settingsService.showTariff(form)" class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['tariff']" for="tariff">
                {{ 'formsSettings.invoice.tariff' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="tariff"
                [options]="tariffOptions"
                [appendTo]="overlayContainer"
                optionValue="value"
                optionLabel="label"
                [placeholder]="'common.selectValue' | translate"
                formControlName="tariff">
            </p-select>
            <small [controlValidationErrors]="form.controls['tariff']" class="p-error"></small>
        </div>
    </div>

    <div class="mb-2 mt-2 flex justify-content-end">
        <button
            id="cancelButton"
            [disabled]="isProcessing"
            type="button"
            pButton
            pRipple
            class="mr-2 p-button-secondary"
            (click)="cancelEditClick()">
            {{ 'common.cancel' | translate }}
        </button>
        <button
            id="saveButton"
            type="button"
            pButton
            pRipple
            (click)="saveEditedClick()"
            [disabled]="form.invalid || isProcessing">
            {{ 'common.saveChanges' | translate }}
        </button>
    </div>
</div>
