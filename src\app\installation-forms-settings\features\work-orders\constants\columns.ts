import { ColumnTransformationType } from 'src/app/core/enums/column-transformation-type';
import { RulesListColumn } from '../../models/rules-list-column';
import { workOrderTranslationPath } from '../work-orders.consts';

export const WORK_ORDERS_COLUMNS: RulesListColumn[] = [
    {
        field: 'displayName',
        header: `${workOrderTranslationPath}.displayName`,
        columnTransformationType: ColumnTransformationType.None,
        isDefault: true
    },
    {
        field: 'formTypes',
        header: `${workOrderTranslationPath}.formTypes`,
        translationPath: 'enums.type.',
        columnTransformationType: ColumnTransformationType.TranslateEnumArray,
        isDefault: true
    },
    {
        field: 'formStates',
        header: `${workOrderTranslationPath}.formStates`,
        translationPath: 'enums.state.',
        columnTransformationType: ColumnTransformationType.TranslateEnumArray,
        isDefault: true
    },
    {
        field: 'sealResponsibles',
        header: `${workOrderTranslationPath}.sealResponsibles`,
        translationPath: 'enums.responsibleForSeal.',
        columnTransformationType: ColumnTransformationType.TranslateEnumArray,
        isDefault: true
    },
    {
        field: 'meterResponsibles',
        header: `${workOrderTranslationPath}.meterResponsibles`,
        translationPath: 'enums.responsibleForMeter.',
        columnTransformationType: ColumnTransformationType.TranslateEnumArray,
        isDefault: true
    },
    {
        field: 'meterNeedsChange',
        header: `${workOrderTranslationPath}.meterNeedsChange`,
        columnTransformationType: ColumnTransformationType.Boolean,
        isDefault: true
    },
    {
        field: 'invoicePaid',
        header: `${workOrderTranslationPath}.invoicePaid`,
        columnTransformationType: ColumnTransformationType.Boolean,
        isDefault: true
    },
    {
        field: 'readyForMeter',
        header: `${workOrderTranslationPath}.readyForMeter`,
        columnTransformationType: ColumnTransformationType.Boolean,
        isDefault: true
    },
    {
        field: 'supplierSelected',
        header: `${workOrderTranslationPath}.supplierSelected`,
        columnTransformationType: ColumnTransformationType.Boolean,
        isDefault: true
    },
    {
        field: 'startsAsConstruction',
        header: `${workOrderTranslationPath}.startsAsConstruction`,
        columnTransformationType: ColumnTransformationType.Boolean,
        isDefault: true
    },
    {
        field: 'workOrderType',
        header: `${workOrderTranslationPath}.workOrderType`,
        translationPath: 'enums.workOrderType.',
        columnTransformationType: ColumnTransformationType.Translate,
        isDefault: true
    },
    {
        field: 'workOrderPurposeId',
        header: `${workOrderTranslationPath}.workOrderPurposeId`,
        columnTransformationType: ColumnTransformationType.ValueListSingleItem,
        isDefault: true
    },
    {
        field: 'workOrderDescriptionId',
        header: `${workOrderTranslationPath}.workOrderDescriptionId`,
        columnTransformationType: ColumnTransformationType.ValueListSingleItem,
        isDefault: true
    }
];
