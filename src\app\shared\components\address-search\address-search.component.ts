import { Component, EventEmitter, forwardRef, OnDestroy, OnInit, Optional, Output } from '@angular/core';
import { <PERSON><PERSON><PERSON>r, ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { of, Subject, Subscription } from 'rxjs';
import { catchError, map, switchMap, takeUntil } from 'rxjs/operators';
import { InstallationFormsClient } from 'src/app/api/installation-forms-client';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { convertSupplyTypeToEnum } from 'src/app/core/utils/mappers/supply-type/supply-type.mapper';
import { v4 as uuidv4 } from 'uuid';
import { FormDataService } from '../../../core/services/form-data/form-data.service';
import { AddressSearchItemModel } from './address-search-item.model';

@Component({
    selector: 'app-address-search',
    templateUrl: './address-search.component.html',
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => AddressSearchComponent),
            multi: true
        }
    ],
    standalone: false,
    host: { hostID: crypto.randomUUID().toString() }
})
export class AddressSearchComponent implements ControlValueAccessor, OnInit, OnDestroy {
    static readonly empty = { id: '', text: '' } as AddressSearchItemModel;
    @Output() newAddressSelected = new EventEmitter<void>();

    private readonly destroy$ = new Subject<void>();
    private commonTranslations: any;

    onChange!: (value: AddressSearchItemModel) => void;
    onTouched!: () => void;

    filtered: AddressSearchItemModel[] = [];
    private readonly searchSubject$ = new Subject<string>();
    private readonly clearSubject$ = new Subject<void>();

    subscription: Subscription = new Subscription();

    value: AddressSearchItemModel = AddressSearchComponent.empty;
    isDisabled: boolean = false;

    constructor(
        @Optional() readonly controlContainer: ControlContainer,
        private readonly client: InstallationFormsClient,
        private readonly router: Router,
        private readonly messageServiceHelper: MessageServiceHelper,
        private readonly formDataService: FormDataService,
        private readonly translateService: TranslateService
    ) {}

    ngOnInit(): void {
        this.registerActions();
        this.commonTranslations = this.translateService.instant('common');
    }

    ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }

    private registerActions() {
        this.searchSubject$
            .pipe(
                takeUntil(this.destroy$),
                switchMap((query) => {
                    query = query.trim();
                    if (!query || query.length === 0) {
                        return of([]);
                    }
                    return this.client
                        .searchAddressLines(uuidv4(), convertSupplyTypeToEnum(this.formDataService.supplyType!), query)
                        .pipe(
                            catchError((_error) => {
                                this.messageServiceHelper.showError({
                                    detail: this.commonTranslations['addressSearchError'],
                                    key: this.formDataService.formId
                                });
                                return of(undefined);
                            }),
                            map((response) => {
                                if (!response) {
                                    return [];
                                }
                                return response.result.map(
                                    (result) =>
                                        ({
                                            id: result.id,
                                            text: result.addressLines
                                        }) as AddressSearchItemModel
                                );
                            })
                        );
                })
            )
            .subscribe((x) => {
                this.filtered = x;
            });

        this.clearSubject$.subscribe(() => {
            this.value = AddressSearchComponent.empty;
            this.onSelect(AddressSearchComponent.empty);
        });
    }

    search(event: any) {
        this.searchSubject$.next(event.query);
    }

    onClear() {
        this.clearSubject$.next();
    }

    onSelect(value: AddressSearchItemModel) {
        this.value = value;
        this.onChange(this.value);
        this.onTouched();
        this.newAddressSelected.emit();
    }

    writeValue(value: AddressSearchItemModel): void {
        this.value = value;
    }

    registerOnChange(fn: any): void {
        this.onChange = fn;
    }

    registerOnTouched(fn: any): void {
        this.onTouched = fn;
    }

    setDisabledState(isDisabled: boolean): void {
        this.isDisabled = isDisabled;
    }

    async onDetailsClick() {
        if (this.value?.id) {
            const address = this.value;
            await this.router.navigate([`central-address-registry/${address.id}`]);
        }
    }
}
