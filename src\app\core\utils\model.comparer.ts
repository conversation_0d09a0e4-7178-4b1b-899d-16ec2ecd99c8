import { ChangeFieldType, ChangesModel } from 'src/app/core/constants/changes-details';

export class ModelComparer {
    private compareModelsResult!: CompareModelsResult;
    constructor(private changesModel: ChangesModel) {}

    public compareModels(from: any, to: any): CompareModelsResult {
        const lhs = from;
        const rhs = to;
        this.compareModelsResult = {
            changesAmount: 0,
            changesModel: { ...this.changesModel }
        };

        Object.keys(this.changesModel).forEach((field) =>
            this.changesModel[field].type !== undefined
                ? this.checkIfChanged(lhs, rhs, field, this.getComparisonFunction(this.changesModel[field].type))
                : null
        );
        return this.compareModelsResult;
    }

    private checkIfChanged(lhs: any, rhs: any, key: string, comparator: Function) {
        const lhsValue = lhs[key];
        const rhsValue = rhs[key];
        if (!lhsValue && !rhsValue) {
            this.compareModelsResult.changesModel[key].change = false;
            return;
        }
        if (!rhsValue || !lhsValue) {
            this.compareModelsResult.changesModel[key].change = true;
            this.compareModelsResult.changesAmount += 1;
            return;
        }
        if (comparator(lhsValue, rhsValue)) {
            this.compareModelsResult.changesModel[key].change = false;
            return;
        }
        this.compareModelsResult.changesModel[key].change = true;
        this.compareModelsResult.changesAmount += 1;
    }

    private compareDates(x: Date, y: Date) {
        const xDate = new Date(x);
        const yDate = new Date(y);

        // We want to compare date part only
        xDate.setHours(0, 0, 0, 0);
        yDate.setHours(0, 0, 0, 0);

        return xDate.getTime() === yDate.getTime();
    }

    private compareArrays(x: any[], y: any[]) {
        const xSorted = [...x].sort((a, b) => (a ?? '').localeCompare(b ?? ''));
        const ySorted = [...y].sort((a, b) => (a ?? '').localeCompare(b ?? ''));
        return xSorted.length === ySorted.length && xSorted.every((value, index) => value === ySorted[index]);
    }

    private getComparisonFunction(type: ChangeFieldType | undefined) {
        switch (type) {
            case ChangeFieldType.Date:
                return this.compareDates;
            case ChangeFieldType.Array:
                return this.compareArrays;
            default:
                return (x: unknown, y: unknown) => x === y || x?.toString() === y?.toString();
        }
    }
}

export interface CompareModelsResult {
    changesAmount: number;
    changesModel: ChangesModel;
}
