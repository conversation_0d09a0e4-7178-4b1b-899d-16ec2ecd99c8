import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { isAuthenticatedAndAuthorized } from '@kmd-elements-ui/authorization';
import { Permissions } from '../core/constants/permissions';
import { AutomaticArchivingComponent } from './features/automatic-archiving/automatic-archiving.component';
import { AutomaticEmailsComponent } from './features/automatic-emails/automatic-emails.component';
import { AutomaticInstructionTextComponent } from './features/automatic-instruction-text/automatic-instruction-text.component';
import { AutomaticInvoiceComponent } from './features/automatic-invoice/automatic-invoice.component';
import { AutomaticScreeningComponent } from './features/automatic-screening/automatic-screening.component';
import { DefaultInstructionTextsComponent } from './features/default-instruction-texts/default-instruction-texts.component';
import { InternalResourceComponent } from './features/internal-resource/internal-resource.component';
import { MasterDataComponent } from './features/master-data/master-data.component';
import { PriceDefinitionsComponent } from './features/price-definitions/price-definitions.component';
import { WorkOrdersComponent } from './features/work-orders/work-orders.component';

const routes: Routes = [
    {
        path: 'automatic-invoice',
        component: AutomaticInvoiceComponent,
        canActivate: [isAuthenticatedAndAuthorized([Permissions.formConfiguration.read])],
        data: {
            reuse: true
        }
    },
    {
        path: 'master-data',
        component: MasterDataComponent,
        canActivate: [isAuthenticatedAndAuthorized([Permissions.formConfiguration.read])],
        data: {
            reuse: true
        }
    },
    {
        path: 'work-orders',
        component: WorkOrdersComponent,
        canActivate: [isAuthenticatedAndAuthorized([Permissions.formConfiguration.read])],
        data: {
            reuse: true
        }
    },
    {
        path: 'allow-automatization',
        component: AutomaticScreeningComponent,
        canActivate: [isAuthenticatedAndAuthorized([Permissions.formConfiguration.read])],
        data: {
            reuse: true
        }
    },
    {
        path: 'default-instruction-texts',
        component: DefaultInstructionTextsComponent,
        canActivate: [isAuthenticatedAndAuthorized([Permissions.formConfiguration.read])],
        data: {
            reuse: true
        }
    },
    {
        path: 'automatic-archiving',
        component: AutomaticArchivingComponent,
        canActivate: [isAuthenticatedAndAuthorized([Permissions.formConfiguration.read])],
        data: {
            reuse: true
        }
    },
    {
        path: 'automatic-instruction-texts',
        component: AutomaticInstructionTextComponent,
        canActivate: [isAuthenticatedAndAuthorized([Permissions.formConfiguration.read])],
        data: {
            reuse: true
        }
    },
    {
        path: 'automatic-emails',
        component: AutomaticEmailsComponent,
        canActivate: [isAuthenticatedAndAuthorized([Permissions.formConfiguration.read])],
        data: {
            reuse: true
        }
    },
    {
        path: 'price-definitions',
        component: PriceDefinitionsComponent,
        canActivate: [isAuthenticatedAndAuthorized([Permissions.formConfiguration.read])],
        data: {
            reuse: true
        }
    },
    {
        path: 'internal-resource',
        component: InternalResourceComponent,
        canActivate: [isAuthenticatedAndAuthorized([Permissions.formConfiguration.read])],
        data: {
            reuse: true
        }
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class InstallationFormsSettingsRoutingModule {}
