import { FormType } from 'src/app/api/installation-forms-client';

export const isAnyOfRequiredFormTypesSelected = (
    formTypes: FormType[],
    requiredTypes: readonly FormType[] | FormType[]
): boolean => {
    return formTypes.some((type) => requiredTypes.includes(type));
};

export const isAllOfRequiredFormTypesSelected = (
    formTypes: FormType[],
    requiredTypes: readonly FormType[] | FormType[]
): boolean => {
    return requiredTypes.every((type) => formTypes.includes(type));
};
