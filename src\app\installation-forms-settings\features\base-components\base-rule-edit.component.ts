import { Directive, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { multipleWhitespaceValidator } from 'src/app/core/utils/validators/multiple-whitespace-validator/multiple-whitespace-validator';
import { alreadyUsedDisplayNameValidator } from '../../../core/utils/validators/already-used-display-name-validator/already-used-display-name-validator';

@Directive()
export abstract class BaseRuleEditComponent<T> implements OnInit, OnDestroy {
    @Input() isProcessing!: boolean;

    private _alreadyUsedDisplayNames: string[] = [];

    @Input()
    set alreadyUsedDisplayNames(value: string[]) {
        this._alreadyUsedDisplayNames = value;
        if (this.form) {
            const control = this.form.get('displayName');
            if (control) {
                const currentValue = control.value;
                this._alreadyUsedDisplayNames = this._alreadyUsedDisplayNames.filter((name) => name !== currentValue);
                control.setValidators([
                    alreadyUsedDisplayNameValidator(this._alreadyUsedDisplayNames),
                    multipleWhitespaceValidator()
                ]);
                control.updateValueAndValidity();
            }
        }
    }

    get alreadyUsedDisplayNames(): string[] {
        return this._alreadyUsedDisplayNames;
    }

    @Input()
    set ruleInEdit(value: T) {
        this.setFormValue(value);
    }

    @Output() cancelEditing: EventEmitter<void> = new EventEmitter<void>();
    @Output() ruleEdited = new EventEmitter<T>();

    protected widgetTranslations: any;
    protected subscription: Subscription = new Subscription();

    form!: FormGroup;

    constructor(
        protected readonly translateService: TranslateService,
        protected readonly messageServiceHelper: MessageServiceHelper,
        protected readonly tabName: string,
        protected readonly translationPath: string
    ) {}

    ngOnInit(): void {
        this.loadTranslations();
        this.getEnumTranslations();
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    protected loadTranslations(): void {
        this.widgetTranslations = this.translateService.instant(this.translationPath);
    }

    cancelEditClick(): void {
        this.form.reset();
        this.cancelEditing.emit();
    }

    protected showSuccessMessage(): void {
        this.messageServiceHelper.showSuccess({
            key: this.tabName
        });
    }

    protected showErrorMessage(detail: string): void {
        this.messageServiceHelper.showError({
            key: this.tabName,
            detail: detail
        });
    }

    protected showWarningMessage(detail: string): void {
        this.messageServiceHelper.showWarning({
            key: this.tabName,
            detail: detail
        });
    }

    abstract initForm(): void;
    abstract setFormValue(rule: T): void;
    abstract getEnumTranslations(): void;
    abstract saveEditedClick(): void;
}
