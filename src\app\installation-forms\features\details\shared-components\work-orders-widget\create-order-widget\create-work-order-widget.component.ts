import { Component, <PERSON><PERSON><PERSON>ter, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { Subscription, combineLatest } from 'rxjs';
import { DeliveryOption, ValueListModel, ValueListType, WorkOrderType } from 'src/app/api/installation-forms-client';
import { FormDataService } from 'src/app/core/services/form-data/form-data.service';
import { ValueListsService } from 'src/app/core/services/value-lists/value-lists.service';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import { WidgetWithFormComponent } from '../../../base-components/widgets/widget-with-form.component';
import WorkOrder<PERSON><PERSON><PERSON><PERSON>erson from './work-order-contact-person.model';

@Component({
    selector: 'app-create-work-order-widget',
    templateUrl: './create-work-order-widget.component.html',
    standalone: false
})
export class CreateWorkOrderWidgetComponent extends WidgetWithFormComponent implements OnDestroy, OnInit {
    static readonly MainMeterFramePurposeId: string = '87d6d421-d7ba-4fa9-b1cb-936dda17676b';
    form!: FormGroup;
    showCreateWorkOrder: boolean = false;

    workOrderTypeOptions: SelectItem[] = [];
    subscription: Subscription = new Subscription();
    meterFrameOptions: SelectItem[] = [];

    meterFrameElectricityPurposeValueList?: ValueListModel;

    @Output() closeWidget = new EventEmitter();

    get workOrderType(): WorkOrderType | undefined {
        return this.form?.value?.workOrderType;
    }

    constructor(
        private readonly fb: FormBuilder,
        private readonly translateService: TranslateService,
        protected readonly formDataService: FormDataService,
        private readonly router: Router,
        private readonly valueListsService: ValueListsService
    ) {
        super(formDataService);
        this.form = this.fb.group({
            workOrderType: ['', Validators.required],
            meterFrameNumber: [null, []]
        });
    }

    ngOnInit(): void {
        const valueListObs$ = this.valueListsService.getValueList(ValueListType.MeterFrameElectricityPurpose);
        const masterDataProcessesLoaded$ = this.formDataService.masterDataProcessesLoaded$;

        this.subscription.add(
            combineLatest([valueListObs$, masterDataProcessesLoaded$]).subscribe(([valueList, _]) => {
                this.meterFrameElectricityPurposeValueList = valueList;
                this.setMeterFrameOptions();
                this.getEnumTranslations();
            })
        );
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    onCancelClicked() {
        this.cleanAndCloseWorkOrderForm();
    }

    onCreateClicked() {
        let contacts: WorkOrderContactPerson[] = [];
        const firstContactData = this.formDataService.installationContact;
        const secondContactData = this.formDataService.installerContact;

        if (firstContactData?.name) {
            contacts.push({
                type: 'Installation contact',
                name: firstContactData.name,
                phoneNumber: firstContactData.phoneNumber || '',
                otherDetails: firstContactData.email || ''
            });
        }

        if (secondContactData?.name) {
            contacts.push({
                type: 'Installer contact',
                name: secondContactData.name,
                phoneNumber: secondContactData.phoneNumber || '',
                otherDetails: secondContactData.email || ''
            });
        }

        this.router.navigate([`process-center/work-orders/start/ProcessStartWorkOrders${this.form.value.workOrderType}`], {
            queryParams: {
                objectId: this.canSelectMeterFrame() ? this.form.value.meterFrameNumber : this.formDataService.meterFrameNumber,
                objectType: 'MeterFrame',
                workDescriptionId: '',
                purposeId: '',
                comment: '',
                requestedAppointmentDate: this.formDataService.requestedConnectionDateEod?.toISOString(),
                requestedTechnician: '',
                meterDelivery_Type: this.mapDeliveryOptionToWorkOrderMeterDeliveryType(this.formDataService.meterDeliveryOption),
                meterDelivery_CarId: this.formDataService.meterDeliveryCarId,
                meterDelivery_Name: this.formDataService.meterDeliveryName,
                meterDelivery_Attention: this.formDataService.meterDeliveryAttention,
                installationFormReference: this.formDataService.formNumber,
                contactDetails_Type: contacts.map((x) => x.type),
                contactDetails_Name: contacts.map((x) => x.name),
                contactDetails_PhoneNo: contacts.map((x) => x.phoneNumber),
                contactDetails_OtherDetails: contacts.map((x) => x.otherDetails)
            }
        });
    }

    canSelectMeterFrame = () => this.workOrderType === WorkOrderType.RemoveMeter;

    private getEnumTranslations() {
        let workOrderTypeItems = Object.values(WorkOrderType);
        this.workOrderTypeOptions = enumMapper.map(this.translateService.instant('enums.workOrderType'), workOrderTypeItems);
    }

    private setMeterFrameOptions() {
        const defaultMfsList = this.formDataService.meterFrame
            ? [
                  {
                      label: `
                ${this.meterFrameElectricityPurposeValueList?.valueItems.find((x) => x.id === CreateWorkOrderWidgetComponent.MainMeterFramePurposeId)?.displayValue}
                ${this.formDataService.meterFrame.meterFrameNumber}`,
                      value: this.formDataService.meterFrame.meterFrameNumber
                  }
              ]
            : [];

        this.meterFrameOptions =
            (this.formDataService.meterFramesForRemoveMeter || []).length > 0
                ? this.formDataService.meterFramesForRemoveMeter?.map((mf) => ({
                      label: `
                ${this.meterFrameElectricityPurposeValueList?.valueItems.find((x) => x.id === mf.purposeId)?.displayValue}
                ${mf.meterFrameNumber}
                ${this.translateService.instant('common.from')}
                ${this.translateService.instant(
                    'enums.masterDataProcessType.' +
                        this.formDataService.masterDataProcessesLoaded$.value?.find(
                            (x) => x.masterDataProcessEntryId === mf.relatedMasterDataProcessId
                        )?.masterDataProcessType
                )}`,
                      value: mf.meterFrameNumber
                  })) || []
                : defaultMfsList;

        if (this.meterFrameOptions.length === 1) {
            this.form.patchValue({
                meterFrameNumber: this.meterFrameOptions[0].value
            });
        }
    }

    private cleanAndCloseWorkOrderForm() {
        this.showCreateWorkOrder = false;
        this.form.reset();
        this.closeWidget.emit();
    }

    private mapDeliveryOptionToWorkOrderMeterDeliveryType(option: DeliveryOption | undefined): string | undefined {
        switch (option) {
            case DeliveryOption.GridCompanyWillHandle:
                return 'InternalTechnician';
            case DeliveryOption.InstallerWillHandleSendMeter:
                return 'SendMeterToTechnician';
            case DeliveryOption.InstallerWillHandlePickUp:
                return 'TechnicianWillPickUpMeter';
            default:
                return undefined;
        }
    }
}
