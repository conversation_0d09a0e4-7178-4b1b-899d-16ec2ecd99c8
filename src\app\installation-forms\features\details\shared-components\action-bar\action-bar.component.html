<p-panel class="panel-without-header" *ngIf="canChangeScreeningStatus || (possibleStateChanges.length > 0 && canChangeState)">
    <div class="flex p-justify-between align-items-center">
        <div class="flex">
            <div *ngIf="this.formDataService.allowedScreeningStatusTransition">
                <button
                    type="button"
                    pButton
                    pRipple
                    label="{{
                        'actionBar.screeningStatusHandling.' + this.formDataService.allowedScreeningStatusTransition | translate
                    }}"
                    class="mr-2 p-button-outlined"
                    (click)="onScreeningStatusChangeButtonClick()"></button>
            </div>
            <div *ngIf="possibleStateChanges.length > 0 && canChangeState">
                <button
                    type="button"
                    pButton
                    pRipple
                    label="{{ 'actionBar.stateHandling.' + state | translate }}"
                    class="mr-2"
                    *ngFor="let state of possibleStateChanges"
                    (click)="onStateChangeButtonClick(state)"></button>
            </div>
        </div>
    </div>
</p-panel>

<!-- Dialog with state change and comment -->
<p-dialog
    header="{{ 'actionBar.stateHandling.' + stateChangeDialogState | translate }}"
    [formGroup]="form"
    [(visible)]="stateChangeDialogVisible"
    [modal]="true"
    [draggable]="false"
    [resizable]="false">
    <div class="p-dialog-body" fxLayout="column" fxLayoutGap="15px" fxLayoutAlign="space-between">
        <label [labelRequired]="form.controls['stateChangeComment']" for="stateChangeComment">
            {{ 'actionBar.stateHandling.stateChangeComment' | translate }}
        </label>
        <textarea id="stateChangeComment" formControlName="stateChangeComment" maxlength="1000" [autoResize]="true" pTextarea>
        </textarea>
        <small [controlValidationErrors]="form.controls['stateChangeComment']" class="p-error"></small>
        <div fxLayout="row" fxLayoutAlign="flex-end center">
            <button
                type="button"
                pButton
                pRipple
                [disabled]="!form.valid"
                label="{{ 'actionBar.stateHandling.' + stateChangeDialogState | translate }}"
                class="mr-2"
                (click)="onStateChangeDialogButtonClick()"></button>
        </div>
    </div>
</p-dialog>

<app-confirmation-dialog
    [headerKey]="'actionBar.screeningStatusHandling.screeningStatusDialogTitle'"
    [messageKey]="'actionBar.screeningStatusHandling.revertToHandleManuallyQuestion'"
    [visible]="screeningStatusRevertToHandleManuallyDialogVisible"
    (confirmEvent)="onScreeningStatusRevertConfirmationDialogYesButtonClick()"
    (cancelEvent)="onScreeningStatusRevertConfirmationDialogNoButtonClick()">
</app-confirmation-dialog>
