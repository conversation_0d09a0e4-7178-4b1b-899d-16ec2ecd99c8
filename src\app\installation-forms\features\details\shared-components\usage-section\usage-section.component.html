<div #overlayContainer></div>
<button
    pButton
    label="{{ 'usageSection.addApplication' | translate }}"
    (click)="createApplication()"
    class="p-button-outlined mb-20"
    *ngIf="!isAddingOpened && !isReadOnly"></button>

<div fxLayout="column" class="mb-20" [formGroup]="addApplicationForm" *ngIf="isAddingOpened">
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="16px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="addApplicationForm.controls['applicationType']" for="applicationType">
                {{ 'usageSection.applicationType' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="applicationType"
                [options]="applicationTypeOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                [showClear]="true"
                formControlName="applicationType">
            </p-select>
            <small [controlValidationErrors]="addApplicationForm.controls['applicationType']" class="p-error"></small>
        </div>
    </div>
    <ng-container *ngIf="this.addApplicationForm.get('applicationType')?.value">
        <div
            class="zebra-item"
            fxLayout="row"
            fxLayout.lt-sm="column"
            fxLayoutGap="15px"
            *ngIf="configurationForSelectedType?.hasEffect">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label [labelRequired]="addApplicationForm.controls['effect']" for="effect">
                    {{ 'usageSection.effect' | translate }}
                </label>
            </div>
            <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                <p-inputNumber
                    id="effect"
                    formControlName="effect"
                    [showClear]="true"
                    [useGrouping]="false"
                    [minFractionDigits]="0"
                    [maxFractionDigits]="5">
                </p-inputNumber>
                <small [controlValidationErrors]="addApplicationForm.controls['effect']" class="p-error"></small>
            </div>
        </div>
        <div
            class="zebra-item"
            fxLayout="row"
            fxLayout.lt-sm="column"
            fxLayoutGap="15px"
            *ngIf="configurationForSelectedType?.hasPhaseCount">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label [labelRequired]="addApplicationForm.controls['phaseCount']" for="phaseCount">
                    {{ 'usageSection.phaseCount' | translate }}
                </label>
            </div>
            <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                <p-select
                    id="phaseCount"
                    [options]="phaseCountOptions"
                    optionValue="value"
                    optionLabel="label"
                    [appendTo]="overlayContainer"
                    [placeholder]="'common.selectValue' | translate"
                    formControlName="phaseCount">
                </p-select>
                <small [controlValidationErrors]="addApplicationForm.controls['phaseCount']" class="p-error"></small>
            </div>
        </div>
        <div
            class="zebra-item"
            fxLayout="row"
            fxLayout.lt-sm="column"
            fxLayoutGap="15px"
            *ngIf="configurationForSelectedType?.hasRemark">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label [labelRequired]="addApplicationForm.controls['remark']" for="remark">
                    {{ 'usageSection.remark' | translate }}
                </label>
            </div>
            <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                <input id="remark" type="text" pInputText maxlength="100" formControlName="remark" />
                <small [controlValidationErrors]="addApplicationForm.controls['remark']" class="p-error"></small>
            </div>
        </div>
    </ng-container>
</div>
<button
    id="cancelButton"
    *ngIf="isAddingOpened"
    type="button"
    pButton
    pRipple
    class="mr-2 p-button-secondary mb-20"
    (click)="cancelCreateApplication()">
    {{ 'common.cancel' | translate | titlecase }}
</button>
<button id="saveButton" type="button" pButton pRipple (click)="saveApplication()" *ngIf="isAddingOpened" class="mb-20">
    {{ 'common.create' | translate | titlecase }}
</button>

<div [formGroup]="form" *ngIf="form.get('applications')?.value.length; else emptyMessage">
    <p-table
        [value]="form.get('applications')?.value"
        [paginator]="form.get('applications')?.value?.length > rowsPerPage"
        [rows]="rowsPerPage">
        <ng-template pTemplate="header">
            <tr>
                <th id="applitions-applicationType">{{ 'usageSection.applicationType' | translate }}</th>
                <th id="applitions-effect">{{ 'usageSection.effect' | translate }}</th>
                <th id="applitions-phaseCount">{{ 'usageSection.phaseCount' | translate }}</th>
                <th id="applitions-remark">{{ 'usageSection.remark' | translate }}</th>
                <th id="applitions-actions"></th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-item>
            <tr class="zebra-item table-row">
                <td>{{ 'enums.applicationType.' + item.type | translate }}</td>
                <td>{{ item.effect | noValue }}</td>
                <td>{{ item.phaseCount | noValue }}</td>
                <td>{{ item.remark | noValue }}</td>
                <td>
                    <button
                        [disabled]="isReadOnly"
                        pButton
                        icon="fa-solid fa-trash-can"
                        (click)="removeItem(item)"
                        class="p-button-outlined border-none"></button>
                </td>
            </tr>
        </ng-template>
    </p-table>
</div>

<ng-template #emptyMessage>
    <p>{{ 'usageSection.noApplicationsFound' | translate }}</p>
</ng-template>
