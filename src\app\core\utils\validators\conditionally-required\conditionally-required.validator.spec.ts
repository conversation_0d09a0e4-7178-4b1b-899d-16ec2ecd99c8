import { FormControl } from '@angular/forms';
import conditionallyRequiredValidator from './conditionally-required.validator';

describe('conditionallyRequiredValidator', () => {
    it('should validate when condition is true', () => {
        const shouldBeRequired = () => true;

        const validControl = new FormControl('value');
        expect(conditionallyRequiredValidator(validControl, shouldBeRequired)).toBeNull();

        const invalidControl = new FormControl('');
        expect(conditionallyRequiredValidator(invalidControl, shouldBeRequired)).toEqual({ required: true });

        const nullControl = new FormControl(null);
        expect(conditionallyRequiredValidator(nullControl, shouldBeRequired)).toEqual({ required: true });
    });

    it('should not validate when condition is false', () => {
        const shouldBeRequired = () => false;

        const validControl = new FormControl('value');
        expect(conditionallyRequiredValidator(validControl, shouldBeRequired)).toBeNull();

        const invalidControl = new FormControl('');
        expect(conditionallyRequiredValidator(invalidControl, shouldBeRequired)).toBeNull();

        const nullControl = new FormControl(null);
        expect(conditionallyRequiredValidator(nullControl, shouldBeRequired)).toBeNull();
    });

    it('should use complexObjectValueExtractionFunc when provided', () => {
        const shouldBeRequired = () => true;
        const extractValue = (control: any) => control.value?.text;

        const validComplexControl = new FormControl({ text: 'value' });
        expect(conditionallyRequiredValidator(validComplexControl, shouldBeRequired, extractValue)).toBeNull();

        const invalidComplexControl = new FormControl({ text: '' });
        expect(conditionallyRequiredValidator(invalidComplexControl, shouldBeRequired, extractValue)).toEqual({ required: true });

        const nullComplexControl = new FormControl(null);
        expect(conditionallyRequiredValidator(nullComplexControl, shouldBeRequired, extractValue)).toEqual({ required: true });
    });
});
