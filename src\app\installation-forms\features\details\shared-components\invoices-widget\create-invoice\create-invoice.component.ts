import { Component, <PERSON><PERSON><PERSON>ter, <PERSON><PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { finalize, Subscription } from 'rxjs';
import {
    FormType,
    InstallationFormsClient,
    InvoiceCreate,
    InvoiceType,
    Price,
    SupplyType
} from 'src/app/api/installation-forms-client';
import { WidgetNames } from 'src/app/core/constants/widget-names';
import { FormDataService } from 'src/app/core/services/form-data/form-data.service';
import { InvoicingPricesService } from 'src/app/core/services/invoicing-prices/invoicing-prices.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import conditionallyRequiredValidator from 'src/app/core/utils/validators/conditionally-required/conditionally-required.validator';
import { v4 as uuidv4 } from 'uuid';
import { WidgetWithFormComponent } from '../../../base-components/widgets/widget-with-form.component';

@Component({
    selector: 'app-create-invoice',
    templateUrl: './create-invoice.component.html',
    styleUrl: './create-invoice.component.scss',
    standalone: false
})
export class CreateInvoiceComponent extends WidgetWithFormComponent implements OnDestroy, OnInit {
    private commonTranslations: any;
    private subscription: Subscription = new Subscription();
    private widgetTranslations: any;
    public form!: FormGroup;
    public invoiceTypeOptions: SelectItem[] = [];
    public feeOptions: SelectItem[] = [];

    @Output() closeWidget = new EventEmitter();

    constructor(
        private readonly fb: FormBuilder,
        readonly formDataService: FormDataService,
        private readonly client: InstallationFormsClient,
        private readonly messageServiceHelper: MessageServiceHelper,
        private readonly translateService: TranslateService
    ) {
        super(formDataService);
        this.form = this.fb.group({
            invoiceType: [InvoiceType.ConnectionRights, Validators.required],
            feeId: [null, [(control: AbstractControl) => conditionallyRequiredValidator(control, this.canSelectFee)]]
        });
    }

    ngOnInit() {
        this.commonTranslations = this.translateService.instant('common');
        this.widgetTranslations = this.translateService.instant(WidgetNames.invoicesWidget);
        this.setEnumTranslations();
        this.getData();
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    getData() {
        this.subscription.add(
            this.client
                .getInvoicingPrices(uuidv4(), this.formDataService.supplyType as SupplyType)
                .pipe(finalize(() => this.processingFinished()))
                .subscribe({
                    next: (response) => {
                        this.feeOptions = response.result.results
                            .filter((r) => !!r.externalPriceId)
                            .map((price: Price) => {
                                return {
                                    label: price.displayName,
                                    value: price.id
                                };
                            });
                    },
                    error: (_) => {
                        this.messageServiceHelper.showError({
                            detail: this.widgetTranslations['loadPricesError'],
                            key: WidgetNames.invoicesWidget
                        });
                    }
                })
        );
    }

    onCreateClicked() {
        if (!this.form.valid) {
            this.messageServiceHelper.showWarning({
                detail: this.commonTranslations['allRequiredFieldsShouldBeCompleted'],
                key: this.formDataService.formId!
            });
            return;
        }
        this.processingStarted();

        this.subscription.add(
            this.client
                .createInvoice(
                    this.formDataService.formId!,
                    uuidv4(),
                    new InvoiceCreate({
                        type: this.form.get('invoiceType')?.value,
                        invoicingPriceId: this.form.get('feeId')?.value
                    })
                )
                .pipe(finalize(() => this.processingFinished()))
                .subscribe({
                    next: (_response) => {
                        this.messageServiceHelper.showSuccess({
                            detail: this.widgetTranslations['postInvoicesSuccess'],
                            key: this.formDataService.formId!
                        });
                        this.closeWidget.emit();
                    },
                    error: (_) => {
                        this.messageServiceHelper.showError({
                            detail: this.widgetTranslations['postInvoicesError'],
                            key: WidgetNames.invoicesWidget
                        });
                    }
                })
        );
    }

    onCancelClicked() {
        this.closeWidget.emit();
    }

    private setEnumTranslations() {
        this.invoiceTypeOptions = enumMapper.mapArray(
            this.translateService.instant('enums.invoiceType'),
            this.getAllowedInvoiceTypes()
        );
    }

    getAllowedInvoiceTypes() {
        var allowedTypes = [InvoiceType.StandardFee];
        if (this.formDataService.type === FormType.NewInstallation || this.formDataService.type === FormType.Extension) {
            allowedTypes.push(InvoiceType.ConnectionRights);
        }

        return allowedTypes;
    }

    protected canSelectFee = () => {
        return this.form?.get('invoiceType')?.value === InvoiceType.StandardFee;
    };
}
