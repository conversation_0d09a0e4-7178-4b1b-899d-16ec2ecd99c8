import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AuthorizationService } from '@kmd-elements-ui/authorization';
import {
    KeyActions,
    KeyboardShortcuts,
    KeyboardShortcutsService,
    KeyboardShortcutsSubscriptionManager
} from '@kmd-elements-ui/shortcuts';
import { TabsService } from '@kmd-elements-ui/tabs';
import { TranslateService } from '@ngx-translate/core';
import { TableRowReorderEvent } from 'primeng/table';
import { BehaviorSubject, catchError, EMPTY, filter, finalize, of, Subscription, switchMap, tap } from 'rxjs';
import { AutomaticInstructionTextRule, InstallationFormsClient, ReorderModel } from 'src/app/api/installation-forms-client';
import { Permissions } from 'src/app/core/constants/permissions';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { ValueListsService } from 'src/app/core/services/value-lists/value-lists.service';
import { v4 as uuidv4 } from 'uuid';
import { RulesListColumn } from '../models/rules-list-column';
import { AutomaticInstructionTextCreateComponent } from './automatic-instruction-text-create/automatic-instruction-text-create.component';
import { AutomaticInstructionTextEditComponent } from './automatic-instruction-text-edit/automatic-instruction-text-edit.component';
import {
    automaticInstructionTextMarkdownDaDK,
    automaticInstructionTextMarkdownEnUs,
    automaticInstructionTextTabName,
    automaticInstructionTextTranslationPath
} from './constants/automatic-instruction-text.consts';
import { AUTOMATIC_INSTRUCTION_TEXT_COLUMNS } from './constants/columns';

@Component({
    selector: 'app-automatic-instruction-text',
    templateUrl: './automatic-instruction-text.component.html',
    styleUrl: './automatic-instruction-text.component.scss',
    standalone: false
})
export class AutomaticInstructionTextComponent implements KeyboardShortcutsSubscriptionManager, OnInit, OnDestroy {
    private widgetTranslations: any;

    private _isCreating: boolean = false;
    private _ruleInEdit?: AutomaticInstructionTextRule;
    private _isReordered: boolean = false;

    get isCreating(): boolean {
        return this._isCreating;
    }

    set isCreating(value: boolean) {
        this._isCreating = value;
        this.recalculateDirty();
    }

    get ruleInEdit(): AutomaticInstructionTextRule | undefined {
        return this._ruleInEdit;
    }

    set ruleInEdit(value: AutomaticInstructionTextRule | undefined) {
        this._ruleInEdit = value;
        this.recalculateDirty();
    }

    get isReordered(): boolean {
        return this._isReordered;
    }

    set isReordered(value: boolean) {
        this._isReordered = value;
        this.recalculateDirty();
    }

    automaticInstructionTextRules: AutomaticInstructionTextRule[] = [];

    subscription: Subscription = new Subscription();

    automaticInstructionTextMarkdownEnUs = automaticInstructionTextMarkdownEnUs;
    automaticInstructionTextMarkdownDaDK = automaticInstructionTextMarkdownDaDK;

    isProcessing: boolean = false;

    dirty$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

    automaticInstructionTextTabName = automaticInstructionTextTabName;

    hasUserFormConfigurationWritePermission: boolean = false;

    private _tabId?: string;

    columns: RulesListColumn[] = AUTOMATIC_INSTRUCTION_TEXT_COLUMNS;

    private keyEventsSubscription: Subscription = new Subscription();

    @ViewChild('automaticInstructionTextsCreate') automaticInstructionTextCreate?: AutomaticInstructionTextCreateComponent;
    @ViewChild('automaticInstructionTextsEdit') automaticInstructionTextEdit?: AutomaticInstructionTextEditComponent;

    keyActions: KeyActions = {
        [KeyboardShortcuts.ShiftPlusN]: () => {
            this.startCreating();
        },
        [KeyboardShortcuts.Escape]: () => {
            this.tabsService.close();
        },
        [KeyboardShortcuts.Enter]: () => {
            if (this.ruleInEdit) {
                this.automaticInstructionTextEdit?.saveEditedClick();
                return;
            }

            if (this.isCreating) {
                this.automaticInstructionTextCreate?.createClick();
                return;
            }

            if (this.isReordered) {
                this.saveReorder();
            }
        }
    };
    attemptedAction: (() => void) | null = null;
    private ruleToDelete?: AutomaticInstructionTextRule;

    private originalRuleOrder: string[] = [];
    alreadyUsedDisplayNames: string[] = [];

    constructor(
        private readonly client: InstallationFormsClient,
        private readonly translateService: TranslateService,
        private readonly messageServiceHelper: MessageServiceHelper,
        protected tabsService: TabsService,
        private readonly activatedRoute: ActivatedRoute,
        private readonly authService: AuthorizationService,
        private readonly keyboardShortcutsService: KeyboardShortcutsService,
        private readonly valueListService: ValueListsService
    ) {
        this.addTab();
    }

    ngOnInit(): void {
        this.widgetTranslations = this.translateService.instant(automaticInstructionTextTranslationPath);

        this.authService
            .hasPermissions([Permissions.formConfiguration.write])
            .subscribe((x) => (this.hasUserFormConfigurationWritePermission = x));

        this.registerRefreshButtonListener();
        this.loadInstructionTextsWithTemplates();
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    subscribeKeyShortcuts(): void {
        this.keyEventsSubscription = this.keyboardShortcutsService.registerKeyboardShortcuts(this.keyActions).subscribe();
    }

    unsubscribeKeyShortcuts(): void {
        this.keyEventsSubscription.unsubscribe();
    }

    private loadInstructionTextsWithTemplates() {
        this.isProcessing = true;
        this.subscription.add(
            this.client
                .getAutomaticInstructionTextRules(uuidv4())
                .pipe(
                    catchError((_) => {
                        this.messageServiceHelper.showError({
                            key: automaticInstructionTextTabName,
                            detail: this.widgetTranslations['getAutomaticInstructionTextRulesError']
                        });
                        return EMPTY;
                    })
                )
                .pipe(finalize(() => (this.isProcessing = false)))
                .subscribe({
                    next: (response) => {
                        const sortedRules = [...response.result];
                        sortedRules.sort((a, b) => a.order - b.order);
                        this.automaticInstructionTextRules = sortedRules;
                        this.originalRuleOrder = this.automaticInstructionTextRules.map((x) => x.id);
                        this.alreadyUsedDisplayNames = this.automaticInstructionTextRules.map((x) => x.displayName);
                    }
                })
        );
    }

    protected editRule(event: MouseEvent, rule: AutomaticInstructionTextRule) {
        event.stopPropagation();
        this.ruleInEdit = this.automaticInstructionTextRules.find((r) => r.id === rule.id);
        this.isCreating = false;
    }

    protected startCreating() {
        this.isCreating = true;
        this.ruleInEdit = undefined;
    }

    protected cancelCreating() {
        this.isCreating = false;
    }

    protected cancelEditing() {
        this.ruleInEdit = undefined;
    }

    protected onRuleCreated(rule: AutomaticInstructionTextRule) {
        this.isCreating = false;
        this.automaticInstructionTextRules.push(rule);
        this.originalRuleOrder.push(rule.id);
        this.alreadyUsedDisplayNames.push(rule.displayName);
    }

    protected onRuleEdited(updatedRule: AutomaticInstructionTextRule) {
        const editedIndex = this.automaticInstructionTextRules.findIndex((r) => r.id === updatedRule.id);
        if (editedIndex === -1) {
            return;
        }

        this.alreadyUsedDisplayNames.splice(
            this.alreadyUsedDisplayNames.indexOf(this.ruleInEdit!.displayName),
            1,
            updatedRule.displayName
        );
        this.automaticInstructionTextRules[editedIndex] = updatedRule;
        this.ruleInEdit = undefined;
    }

    private addTab() {
        this._tabId = this.tabsService.addOrUpdateTab(this.activatedRoute, {
            icon$: of('fas fa-th-list'),
            areaLabel$: of(this.translateService.instant(`${automaticInstructionTextTranslationPath}.tabAreaLabel`)),
            label$: of(this.translateService.instant(`${automaticInstructionTextTranslationPath}.tabLabel`)),
            tooltip$: of(this.translateService.instant(`${automaticInstructionTextTranslationPath}.tabLabel`)),
            dirty$: this.dirty$,
            canReload$: of(true)
        });
    }

    registerRefreshButtonListener(): void {
        this.subscription.add(
            this.tabsService.reloadButtonClicked$
                .pipe(
                    filter((tabId) => tabId === this._tabId),
                    switchMap((tabId) => this.tabsService.reload(tabId)),
                    filter((canBeReloaded) => canBeReloaded),
                    tap(() => {
                        this.loadInstructionTextsWithTemplates();
                        this.valueListService.fetchValueLists();
                    })
                )
                .subscribe(() => {
                    this.tabsService.tabReloaded(this._tabId as string);
                    this.undoChanges();
                })
        );
    }

    undoChanges() {
        this.cancelCreating();
        this.cancelEditing();
        this.cancelReorder();
    }

    onRowReorder(_: TableRowReorderEvent) {
        this.setIsReordered();
    }

    setIsReordered() {
        this.isReordered = !this.automaticInstructionTextRules.every((rule, index) => rule.id === this.originalRuleOrder[index]);
    }

    cancelReorder() {
        this.automaticInstructionTextRules = this.originalRuleOrder.map(
            (id) => this.automaticInstructionTextRules.find((rule) => rule.id === id)!
        );
        this.setIsReordered();
    }

    protected deleteRule(event: MouseEvent, rule: AutomaticInstructionTextRule) {
        event.stopPropagation();
        this.ruleToDelete = rule;
        this.attemptedAction = () => this.performDelete();
    }

    private performDelete() {
        if (!this.ruleToDelete) {
            return;
        }

        this.isProcessing = true;
        this.subscription.add(
            this.client
                .deleteAutomaticInstructionTextRuleById(this.ruleToDelete.id, uuidv4())
                .pipe(
                    finalize(() => {
                        this.isProcessing = false;
                        this.ruleToDelete = undefined;
                    })
                )
                .subscribe({
                    complete: () => {
                        this.messageServiceHelper.showSuccess({
                            key: automaticInstructionTextTabName
                        });
                        this.automaticInstructionTextRules = this.automaticInstructionTextRules.filter(
                            (r) => r.id !== this.ruleToDelete?.id
                        );
                        this.originalRuleOrder = this.originalRuleOrder.filter((id) => id !== this.ruleToDelete?.id);
                        if (this.ruleInEdit?.id === this.ruleToDelete?.id) {
                            this.ruleInEdit = undefined;
                        }

                        this.alreadyUsedDisplayNames = this.alreadyUsedDisplayNames.filter(
                            (name) => name !== this.ruleToDelete?.displayName
                        );
                    },
                    error: (_error) => {
                        this.messageServiceHelper.showError({
                            key: automaticInstructionTextTabName,
                            detail: this.widgetTranslations['automaticInstructionTextRuleDeleteError']
                        });
                    }
                })
        );
    }

    onConfirmationDialogYesButtonClick() {
        if (this.attemptedAction) {
            this.attemptedAction();
        }
        this.attemptedAction = null;
    }

    onConfirmationDialogNoButtonClick() {
        this.attemptedAction = null;
        this.ruleToDelete = undefined;
    }

    saveReorder() {
        this.isProcessing = true;
        this.subscription.add(
            this.client
                .reorderAutomaticInstructionTextRules(
                    uuidv4(),
                    new ReorderModel({
                        reorderedIds: this.automaticInstructionTextRules.map((x) => x.id)
                    })
                )
                .pipe(finalize(() => (this.isProcessing = false)))
                .subscribe({
                    complete: () => {
                        this.messageServiceHelper.showSuccess({
                            key: automaticInstructionTextTabName
                        });
                        this.originalRuleOrder = this.automaticInstructionTextRules.map((x) => x.id);
                        this.isReordered = false;
                    },
                    error: (_error) => {
                        this.messageServiceHelper.showError({
                            key: automaticInstructionTextTabName,
                            detail: this.widgetTranslations['automaticInstructionTextRuleReorderError']
                        });
                    }
                })
        );
    }

    recalculateDirty() {
        this.dirty$.next(this.isCreating || this.isReordered || !!this.ruleInEdit);
    }
}
