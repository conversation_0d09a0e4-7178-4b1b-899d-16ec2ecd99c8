import { Pipe, PipeTransform } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Pipe({
    name: 'booleanToYesNo',
    standalone: false
})
export class BooleanToYesNoPipe implements PipeTransform {
    constructor(private readonly translateService: TranslateService) {}

    transform(value?: boolean): string {
        if (value === null || value === undefined) {
            return '';
        }
        const key = value ? 'yesOption' : 'noOption';
        return this.translateService.instant(`common.${key}`);
    }
}
