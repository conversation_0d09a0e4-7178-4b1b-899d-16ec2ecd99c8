import { TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { InstallationFormsClient } from 'src/app/api/installation-forms-client';
import { DefaultInstructionTextService } from './default-instruction-text.service';

describe('DefaultInstructionTextService', () => {
    let defaultInstructionTextService: DefaultInstructionTextService;
    let mockClient: InstallationFormsClient;

    const mockResponse = of([
        { id: 'item1', text: 'Text One', order: 1 },
        { id: 'item2', text: 'Text Two', order: 2 },
        { id: 'item3', text: 'Text Three', order: 3 }
    ]);

    beforeEach(() => {
        // Create a simple mock for the client
        mockClient = {
            getInstructionTexts: jest.fn().mockReturnValue(
                of([
                    { id: 'item1', text: 'Text One', order: 1 },
                    { id: 'item2', text: 'Text Two', order: 2 },
                    { id: 'item3', text: 'Text Three', order: 3 }
                ])
            )
        } as Partial<InstallationFormsClient> as InstallationFormsClient;

        TestBed.configureTestingModule({
            providers: [DefaultInstructionTextService, { provide: InstallationFormsClient, useValue: mockClient }]
        });

        defaultInstructionTextService = TestBed.inject(DefaultInstructionTextService);
        mockClient = TestBed.inject(InstallationFormsClient);
    });

    it('should be created', () => {
        expect(defaultInstructionTextService).toBeTruthy();
    });

    it('should call client with correct parameters', () => {
        defaultInstructionTextService.getInstructionTexts();

        expect(mockClient.getInstructionTexts).toHaveBeenCalledWith(expect.any(String));
    });

    it('should return instruction texts', () => {
        defaultInstructionTextService.getInstructionTexts().subscribe((instructionTexts) => {
            expect(instructionTexts).toEqual(mockResponse);
        });
    });

    it('should cache instruction texts', () => {
        // First call
        defaultInstructionTextService.getInstructionTexts().subscribe();
        expect(mockClient.getInstructionTexts).toHaveBeenCalledTimes(1);

        // Second call - should use cached result
        defaultInstructionTextService.getInstructionTexts().subscribe();
        expect(mockClient.getInstructionTexts).toHaveBeenCalledTimes(1);
    });

    it('should handle empty instruction texts', () => {
        (mockClient.getInstructionTexts as jest.Mock).mockReturnValue(of([]));
        defaultInstructionTextService.getInstructionTexts().subscribe((instructionTexts) => {
            expect(instructionTexts).toEqual(undefined);
        });
    });
});
