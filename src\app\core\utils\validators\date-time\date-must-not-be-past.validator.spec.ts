import { FormControl } from '@angular/forms';
import dateMustNotBePastValidator from './date-must-not-be-past.validator';

describe('dateMustNotBePastValidator', () => {
    let dateControl: FormControl;
    let todayDate: Date;
    let yesterdayDate: Date;
    let tomorrowDate: Date;

    beforeEach(() => {
        todayDate = new Date();
        yesterdayDate = new Date(todayDate);
        yesterdayDate.setDate(todayDate.getDate() - 1);
        tomorrowDate = new Date(todayDate);
        tomorrowDate.setDate(todayDate.getDate() + 1);
    });

    it('should return null when date is today', () => {
        dateControl = new FormControl(todayDate);
        const result = dateMustNotBePastValidator(dateControl);
        expect(result).toBeNull();
    });

    it('should return null when date is in the future', () => {
        dateControl = new FormControl(tomorrowDate);
        const result = dateMustNotBePastValidator(dateControl);
        expect(result).toBeNull();
    });

    it('should return error when date is in the past', () => {
        dateControl = new FormControl(yesterdayDate);
        const result = dateMustNotBePastValidator(dateControl);
        expect(result).toEqual({ dateMustNotBePast: true });
    });

    it('should return null when control value is null or undefined', () => {
        dateControl = new FormControl(null);
        expect(dateMustNotBePastValidator(dateControl)).toBeNull();

        dateControl = new FormControl(undefined);
        expect(dateMustNotBePastValidator(dateControl)).toBeNull();
    });
});
