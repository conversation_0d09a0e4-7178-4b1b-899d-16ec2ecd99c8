<div #overlayContainer></div>
<div fxLayout="column" class="mt-10 zebra-container" [formGroup]="form">
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['displayName']" for="displayName">
                {{ 'formsSettings.masterData.displayName' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input id="displayName" type="text" pInputText maxlength="100" formControlName="displayName" autoTrimWhitespaces />
            <small [controlValidationErrors]="form.controls['displayName']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['formTypes']" for="formTypes">
                {{ 'formsSettings.masterData.formTypes' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-multiSelect
                id="formTypes"
                [options]="formTypeOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="formTypes"
                [maxSelectedLabels]="formTypeOptions.length">
            </p-multiSelect>
            <small [controlValidationErrors]="form.controls['formTypes']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['formStates']" for="formStates">
                {{ 'formsSettings.masterData.formStates' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-multiSelect
                id="formStates"
                [options]="formStateOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="formStates"
                [maxSelectedLabels]="formStateOptions.length">
            </p-multiSelect>
            <small [controlValidationErrors]="form.controls['formStates']" class="p-error"></small>
        </div>
    </div>

    <div
        class="zebra-item"
        fxLayout="row"
        fxLayout.lt-sm="column"
        fxLayoutGap="15px"
        *ngIf="masterDataSettingsService.isNewInstallationOrExtensionSelected(form)">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['voltageLevels']" for="voltageLevels">
                {{ 'formsSettings.masterData.voltageLevels' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-multiSelect
                id="voltageLevels"
                [options]="voltageLevelOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="voltageLevels"
                [maxSelectedLabels]="voltageLevelOptions.length">
            </p-multiSelect>
            <small [controlValidationErrors]="form.controls['voltageLevels']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="masterDataSettingsService.isEnergyProductionFormSelected(form)">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['totalCapacityMin']" for="totalCapacityMin">
                {{ 'formsSettings.masterData.totalCapacityMin' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-inputNumber
                id="totalCapacityMin"
                formControlName="totalCapacityMin"
                [min]="0"
                [max]="2147483647"
                [useGrouping]="false"
                [minFractionDigits]="0"
                [maxFractionDigits]="1">
            </p-inputNumber>
            <small [controlValidationErrors]="form.controls['totalCapacityMin']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="masterDataSettingsService.isEnergyProductionFormSelected(form)">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['totalCapacityMax']" for="totalCapacityMax">
                {{ 'formsSettings.masterData.totalCapacityMax' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-inputNumber
                id="totalCapacityMax"
                formControlName="totalCapacityMax"
                [min]="0"
                [max]="2147483647"
                [useGrouping]="false"
                [minFractionDigits]="0"
                [maxFractionDigits]="1">
            </p-inputNumber>
            <small [controlValidationErrors]="form.controls['totalCapacityMax']" class="p-error"></small>
        </div>
    </div>

    <div
        class="zebra-item"
        fxLayout="row"
        fxLayout.lt-sm="column"
        fxLayoutGap="15px"
        *ngIf="masterDataSettingsService.isNewInstallationOrExtensionSelected(form)">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['scopeOfDeliveryMin']" for="scopeOfDeliveryMin">
                {{ 'formsSettings.masterData.scopeOfDeliveryMin' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-inputNumber
                id="scopeOfDeliveryMin"
                formControlName="scopeOfDeliveryMin"
                [min]="0"
                [max]="2147483647"
                [useGrouping]="false">
            </p-inputNumber>
            <small [controlValidationErrors]="form.controls['scopeOfDeliveryMin']" class="p-error"></small>
        </div>
    </div>

    <div
        class="zebra-item"
        fxLayout="row"
        fxLayout.lt-sm="column"
        fxLayoutGap="15px"
        *ngIf="masterDataSettingsService.isNewInstallationOrExtensionSelected(form)">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['scopeOfDeliveryMax']" for="scopeOfDeliveryMax">
                {{ 'formsSettings.masterData.scopeOfDeliveryMax' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-inputNumber
                id="scopeOfDeliveryMax"
                formControlName="scopeOfDeliveryMax"
                [min]="0"
                [max]="2147483647"
                [useGrouping]="false">
            </p-inputNumber>
            <small [controlValidationErrors]="form.controls['scopeOfDeliveryMax']" class="p-error"></small>
        </div>
    </div>

    <div
        class="zebra-item"
        fxLayout="row"
        fxLayout.lt-sm="column"
        fxLayoutGap="15px"
        *ngIf="masterDataSettingsService.isNewInstallationOrExtensionSelected(form)">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['scopeOfDeliveryUom']" for="scopeOfDeliveryUom">
                {{ 'formsSettings.masterData.scopeOfDeliveryUom' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="scopeOfDeliveryUom"
                [options]="scopeOfDeliveryUomOptions"
                optionValue="value"
                optionLabel="label"
                [showClear]="true"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="scopeOfDeliveryUom">
            </p-select>
            <small [controlValidationErrors]="form.controls['scopeOfDeliveryUom']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="masterDataSettingsService.isNewInstallationSelected(form)">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['startsAsConstruction']" for="startsAsConstruction">
                {{ 'formsSettings.masterData.startsAsConstruction' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="startsAsConstruction"
                formControlName="startsAsConstruction"
                [options]="(booleanOptionsService.getBooleanOptions() | async) ?? []"
                [appendTo]="overlayContainer"
                [showClear]="true"
                [placeholder]="'common.selectValue' | translate">
            </p-select>
            <small [controlValidationErrors]="form.controls['startsAsConstruction']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="masterDataSettingsService.isTerminationFormSelected(form)">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['terminationScope']" for="terminationScope">
                {{ 'formsSettings.masterData.terminationScope' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="terminationScope"
                [options]="terminationScopeOptions"
                optionValue="value"
                optionLabel="label"
                [showClear]="true"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="terminationScope">
            </p-select>
            <small [controlValidationErrors]="form.controls['terminationScope']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['masterDataProcessType']" for="masterDataProcessType">
                {{ 'formsSettings.masterData.masterDataProcessType' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="masterDataProcessType"
                [options]="masterDataProcessTypeOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="masterDataProcessType">
            </p-select>
            <small [controlValidationErrors]="form.controls['masterDataProcessType']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['masterDataProcessTemplateId']" for="masterDataProcessTemplateId">
                {{ 'formsSettings.masterData.masterDataProcessTemplate' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="masterDataProcessTemplateId"
                [options]="masterDataProcessTemplateOptions"
                optionValue="id"
                optionLabel="name"
                [showClear]="true"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="masterDataProcessTemplateId">
            </p-select>
            <small [controlValidationErrors]="form.controls['masterDataProcessTemplateId']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['masterDataProcessAutomationLevel']" for="masterDataProcessAutomationLevel">
                {{ 'formsSettings.masterData.masterDataProcessAutomationLevel' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="masterDataProcessAutomationLevel"
                [options]="masterDataProcessAutomationLevelOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="masterDataProcessAutomationLevel">
            </p-select>
            <small [controlValidationErrors]="form.controls['masterDataProcessAutomationLevel']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="masterDataSettingsService.isTerminationFormSelected(form)">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label
                [labelRequired]="form.controls['masterDataProcessWorkOrderAutomationLevel']"
                for="masterDataProcessWorkOrderAutomationLevel">
                {{ 'formsSettings.masterData.masterDataProcessWorkOrderAutomationLevel' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="masterDataProcessWorkOrderAutomationLevel"
                [options]="masterDataProcessWorkOrderAutomationLevelOptions"
                optionValue="value"
                optionLabel="label"
                [showClear]="true"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="masterDataProcessWorkOrderAutomationLevel">
            </p-select>
            <small [controlValidationErrors]="form.controls['masterDataProcessWorkOrderAutomationLevel']" class="p-error"></small>
        </div>
    </div>

    <ng-container *ngIf="masterDataSettingsService.isAnyWorkOrderTypeAvailable(workOrderTypeOptions)">
        <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label [labelRequired]="form.controls['workOrderType']" for="workOrderType">
                    {{ 'formsSettings.masterData.workOrderType' | translate }}
                </label>
            </div>
            <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                <p-select
                    id="workOrderType"
                    [options]="workOrderTypeOptions"
                    optionValue="value"
                    optionLabel="label"
                    [appendTo]="overlayContainer"
                    [placeholder]="'common.selectValue' | translate"
                    formControlName="workOrderType">
                </p-select>
                <small [controlValidationErrors]="form.controls['workOrderType']" class="p-error"></small>
            </div>
        </div>

        <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label [labelRequired]="form.controls['workOrderPurposeId']" for="workOrderPurposeId">
                    {{ 'formsSettings.masterData.workOrderPurposeId' | translate }}
                </label>
            </div>
            <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                <cmbs-value-list-dropdown
                    id="workOrderPurposeId"
                    formControlName="workOrderPurposeId"
                    [valueListType]="masterDataSettingsService.getWorkOrderPurposeValueListType()"
                    [messageKey]="this.tabName"
                    [valueList]="
                        valueListsService.getValueList(masterDataSettingsService.getWorkOrderPurposeValueListType()) | async
                    ">
                </cmbs-value-list-dropdown>
                <small [controlValidationErrors]="form.controls['workOrderPurposeId']" class="p-error"></small>
            </div>
        </div>

        <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="masterDataSettingsService.isWorkOrderTypeSelected(form)">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label [labelRequired]="form.controls['workOrderDescriptionId']" for="workOrderDescriptionId">
                    {{ 'formsSettings.masterData.workOrderDescriptionId' | translate }}
                </label>
            </div>
            <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                <cmbs-value-list-dropdown
                    id="workOrderDescriptionId"
                    formControlName="workOrderDescriptionId"
                    [valueListType]="masterDataSettingsService.getWorkOrderDescriptionValueListTypeForForm(form)"
                    [messageKey]="this.tabName"
                    [valueList]="valueListsService.getValueList(masterDataSettingsService.getWorkOrderDescriptionValueListTypeForForm(form)) | async">
                </cmbs-value-list-dropdown>
                <small [controlValidationErrors]="form.controls['workOrderDescriptionId']" class="p-error"></small>
            </div>
        </div>
    </ng-container>

    <div class="mb-2 mt-2 flex justify-content-end">
        <button
            id="cancelButton"
            [disabled]="isProcessing"
            type="button"
            pButton
            pRipple
            class="mr-2 p-button-secondary"
            (click)="cancelCreateClick()">
            {{ 'common.cancel' | translate }}
        </button>
        <button id="saveButton" type="button" pButton pRipple (click)="createClick()" [disabled]="form.invalid || isProcessing">
            {{ 'common.saveChanges' | translate }}
        </button>
    </div>
</div>
