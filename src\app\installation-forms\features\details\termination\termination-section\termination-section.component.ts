import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { Subscription } from 'rxjs';
import { TerminationScope } from 'src/app/api/installation-forms-client';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import { WidgetWithFormComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget-with-form.component';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';

@Component({
    selector: 'app-termination-section',
    templateUrl: './termination-section.component.html',
    styleUrls: ['./termination-section.component.scss'],
    standalone: false
})
export class TerminationSectionComponent extends WidgetWithFormComponent implements OnDestroy, OnInit {
    subscription: Subscription = new Subscription();
    terminationScope: SelectItem[] = [];

    @Input() form!: FormGroup;

    constructor(
        private readonly translateService: TranslateService,
        readonly formDataService: FormDataService
    ) {
        super(formDataService);
    }

    ngOnInit(): void {
        this.getEnumTranslations();
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    getEnumTranslations() {
        this.terminationScope = enumMapper.map(this.translateService.instant('enums.terminationScope'), TerminationScope);
    }
}
