import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { finalize } from 'rxjs';
import {
    AutomaticScreeningRule,
    AutomaticScreeningRuleCreateOrUpdate,
    AutomationVoltageLevel,
    ChangeMeterChangeReason,
    FormCategory,
    FormState,
    FormType,
    GroundingMethod,
    InstallationFormsClient,
    MeterReturnOption,
    MeterSize,
    ScopeOfDeliveryRange,
    TerminationScope,
    TransferConnectionRightUom,
    ValueListType
} from 'src/app/api/installation-forms-client';
import { SETTINGS_DISPLAY_NAME_MAX_LENGTH } from 'src/app/core/constants/constants';
import {
    energyProductionFormCategories,
    newInstallationFormCategories
} from 'src/app/core/constants/form-categories-per-form-type';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import conditionallyRequiredValidator from 'src/app/core/utils/validators/conditionally-required/conditionally-required.validator';
import { multipleWhitespaceValidator } from 'src/app/core/utils/validators/multiple-whitespace-validator/multiple-whitespace-validator';
import { v4 as uuidv4 } from 'uuid';
import { isAllOfRequiredFormTypesSelected, isAnyOfRequiredFormTypesSelected } from '../../../../core/utils/form-type-checkers';
import { alreadyUsedDisplayNameValidator } from '../../../../core/utils/validators/already-used-display-name-validator/already-used-display-name-validator';
import { BaseRuleCreateComponent } from '../../base-components/base-rule-create.components';
import { automaticScreeningTabName, automaticScreeningTranslationPath } from '../constants/automatic-screening.consts';
import {
    FORM_CATEGORY_FORM_TYPES,
    GROUNDING_METHODS_FORM_TYPES,
    METER_PLACEMENT_CODES_FORM_TYPES,
    METER_RETURN_OPTIONS_FORM_TYPES,
    METER_SIZE_FORM_TYPES,
    REASONS_FOR_CHANGE_FORM_TYPES,
    SCOPE_OF_DELIVERY_FORM_TYPES,
    TERMINATION_SCOPE_FORM_TYPES,
    VOLTAGE_LEVEL_FORM_TYPES
} from '../constants/properties-per-form-types';

@Component({
    selector: 'app-automatic-screening-create',
    templateUrl: './automatic-screening-create.component.html',
    styleUrl: './automatic-screening-create.component.scss',
    standalone: false
})
export class AutomaticScreeningCreateComponent extends BaseRuleCreateComponent<AutomaticScreeningRule> implements OnInit {
    formTypeOptions: SelectItem[] = [];
    formStateOptions: SelectItem[] = [];
    formCategoriesOptions: SelectItem[] = [];
    voltageLevelOptions: SelectItem[] = [];
    scopeOfDeliveryUomOptions: SelectItem[] = [];
    terminationScopeOptions: SelectItem[] = [];
    meterReturnOptionTypesOptions: SelectItem[] = [];
    meterSizeOptions: SelectItem[] = [];
    groundingMethodsOptions: SelectItem[] = [];
    reasonsForChangeOptions: SelectItem[] = [];

    readonly meterFramePlacementValueListType: ValueListType = ValueListType.MeterFramePlacementCode;

    constructor(
        private readonly client: InstallationFormsClient,
        protected readonly fb: FormBuilder,
        protected readonly translateService: TranslateService,
        protected readonly messageServiceHelper: MessageServiceHelper
    ) {
        super(fb, translateService, messageServiceHelper, automaticScreeningTranslationPath, automaticScreeningTabName);
    }

    ngOnInit(): void {
        super.ngOnInit();
        this.addFormControlsValueChanges();
    }

    override initForm(): void {
        this.form = this.fb.group({
            displayName: [
                null,
                [
                    Validators.required,
                    Validators.maxLength(SETTINGS_DISPLAY_NAME_MAX_LENGTH),
                    alreadyUsedDisplayNameValidator(this.alreadyUsedDisplayNames),
                    multipleWhitespaceValidator()
                ]
            ],
            formTypes: [null],
            formStates: [null],
            formCategories: [null],
            voltageLevels: [null],
            scopeOfDeliveryMin: [null, [Validators.min(0), Validators.max(2147483647)]],
            scopeOfDeliveryMax: [null, [Validators.min(0), Validators.max(2147483647)]],
            scopeOfDeliveryUom: [
                null,
                [(control: AbstractControl) => conditionallyRequiredValidator(control, this.isScopeOfDeliveryUomRequired)]
            ],
            terminationScope: [null],
            meterReturnOptionTypes: [null],
            meterPlacementCodes: [null],
            meterSize: [null],
            groundingMethods: [null],
            reasonsForChange: [null]
        });
    }

    override getEnumTranslations(): void {
        this.formTypeOptions = enumMapper.map(this.translateService.instant('enums.type'), FormType);
        this.formStateOptions = enumMapper.map(this.translateService.instant('enums.state'), [
            FormState.Registered,
            FormState.Instructed,
            FormState.Completed
        ]);
        this.voltageLevelOptions = enumMapper.map(
            this.translateService.instant('enums.automationVoltageLevel'),
            AutomationVoltageLevel
        );
        this.terminationScopeOptions = enumMapper.map(this.translateService.instant('enums.terminationScope'), TerminationScope);
        this.scopeOfDeliveryUomOptions = enumMapper.map(
            this.translateService.instant('enums.transferConnectionRightUom'),
            TransferConnectionRightUom
        );
        this.meterReturnOptionTypesOptions = enumMapper.map(
            this.translateService.instant('enums.meterReturnOption'),
            MeterReturnOption
        );
        this.meterSizeOptions = enumMapper.map(this.translateService.instant('enums.meterSize'), MeterSize);
        this.groundingMethodsOptions = enumMapper.map(
            this.translateService.instant('enums.connectionGroundingMethod'),
            GroundingMethod
        );
        this.reasonsForChangeOptions = enumMapper.map(
            this.translateService.instant('enums.reasonForChange'),
            ChangeMeterChangeReason
        );
    }

    override createClick() {
        if (this.form.invalid) {
            return;
        }

        const formValue = this.form.value;

        let hasValidationErrors = false;

        if (formValue.scopeOfDeliveryUom && formValue.scopeOfDeliveryMin === null && formValue.scopeOfDeliveryMax === null) {
            this.showWarningMessage(this.widgetTranslations['scopeOfDeliveryMinMaxEmptyWarning']);
            hasValidationErrors = true;
        }

        if (
            formValue.scopeOfDeliveryUom &&
            formValue.scopeOfDeliveryMin !== null &&
            formValue.scopeOfDeliveryMax !== null &&
            formValue.scopeOfDeliveryMin >= formValue.scopeOfDeliveryMax
        ) {
            this.showWarningMessage(this.widgetTranslations['scopeOfDeliveryMinGreaterThanMaxWarning']);
            hasValidationErrors = true;
        }

        if (hasValidationErrors) {
            return;
        }

        const rule: AutomaticScreeningRuleCreateOrUpdate = new AutomaticScreeningRuleCreateOrUpdate({
            displayName: formValue.displayName,
            formTypes: formValue.formTypes ?? [],
            formStates: formValue.formStates ?? [],
            formCategories: formValue.formCategories ?? [],
            scopeOfDeliveryRange: formValue.scopeOfDeliveryUom
                ? new ScopeOfDeliveryRange({
                      min: formValue.scopeOfDeliveryMin,
                      max: formValue.scopeOfDeliveryMax,
                      uom: formValue.scopeOfDeliveryUom
                  })
                : undefined,
            voltageLevels: formValue.voltageLevels ?? [],
            terminationScope: formValue.terminationScope ?? null,
            meterReturnOptionTypes: formValue.meterReturnOptionTypes ?? [],
            meterPlacementCodes: formValue.meterPlacementCodes ?? [],
            meterSize: formValue.meterSize ?? null,
            groundingMethods: formValue.groundingMethods ?? [],
            reasonsForChange: formValue.reasonsForChange ?? []
        });

        this.isProcessing = true;
        this.subscription.add(
            this.client
                .createAutomaticScreeningRule(uuidv4(), rule)
                .pipe(finalize(() => (this.isProcessing = false)))
                .subscribe({
                    next: (response) => {
                        this.ruleCreated.emit(response.result);
                        this.showSuccessMessage();
                    },
                    error: (_) => {
                        this.showErrorMessage(this.widgetTranslations['automaticScreeningRuleCreationError']);
                    }
                })
        );
    }

    addFormControlsValueChanges() {
        this.subscription.add(
            this.form.get('scopeOfDeliveryMin')?.valueChanges.subscribe((_) => {
                const uomControl = this.form.get('scopeOfDeliveryUom');
                if (uomControl) {
                    uomControl.updateValueAndValidity();
                }
            })
        );

        this.subscription.add(
            this.form.get('scopeOfDeliveryMax')?.valueChanges.subscribe((_) => {
                const uomControl = this.form.get('scopeOfDeliveryUom');
                if (uomControl) {
                    uomControl.updateValueAndValidity();
                }
            })
        );

        this.subscription.add(
            this.form.get('formTypes')?.valueChanges.subscribe(() => {
                this.updateScopeOfDeliveryValidation();
                this.updateFormCategories();
            })
        );
    }

    updateFormCategories() {
        const formTypes = this.form?.get('formTypes')?.value || [];
        let relevantCategories: FormCategory[] = [];

        if (isAllOfRequiredFormTypesSelected(formTypes, [FormType.NewInstallation, FormType.EnergyProduction])) {
            relevantCategories = Object.values(FormCategory);
        } else if (isAllOfRequiredFormTypesSelected(formTypes, [FormType.NewInstallation])) {
            relevantCategories = newInstallationFormCategories;
        } else if (isAllOfRequiredFormTypesSelected(formTypes, [FormType.EnergyProduction])) {
            relevantCategories = energyProductionFormCategories;
        } else {
            relevantCategories = [];
        }

        this.formCategoriesOptions = enumMapper.map(this.translateService.instant('enums.category'), relevantCategories);

        this.filterRelevantFormCategories(relevantCategories);
    }

    filterRelevantFormCategories(relevantFormCategories: FormCategory[]) {
        const selectedFormCategories = this.form.get('formCategories')?.value || [];
        const filteredCategories = selectedFormCategories.filter((category: FormCategory) =>
            relevantFormCategories.includes(category)
        );
        this.form.get('formCategories')?.setValue(filteredCategories);
    }

    isScopeOfDeliveryUomRequired = () => {
        const minValue = this.form?.get('scopeOfDeliveryMin')?.value;
        const maxValue = this.form?.get('scopeOfDeliveryMax')?.value;
        return minValue !== null || maxValue !== null;
    };

    isVoltageLevelRelevant = (): boolean => {
        const formTypes = this.form?.get('formTypes')?.value || [];
        return isAnyOfRequiredFormTypesSelected(formTypes, VOLTAGE_LEVEL_FORM_TYPES);
    };

    isScopeOfDeliveryRelevant = (): boolean => {
        const formTypes = this.form?.get('formTypes')?.value || [];
        return isAnyOfRequiredFormTypesSelected(formTypes, SCOPE_OF_DELIVERY_FORM_TYPES);
    };

    isFormCategoryRelevant = (): boolean => {
        const formTypes = this.form?.get('formTypes')?.value || [];
        return isAnyOfRequiredFormTypesSelected(formTypes, FORM_CATEGORY_FORM_TYPES);
    };

    isTerminationScopeRelevant = (): boolean => {
        const formTypes = this.form?.get('formTypes')?.value || [];
        return isAnyOfRequiredFormTypesSelected(formTypes, TERMINATION_SCOPE_FORM_TYPES);
    };

    isMeterReturnOptionsRelevant = (): boolean => {
        const formTypes = this.form?.get('formTypes')?.value || [];
        return isAnyOfRequiredFormTypesSelected(formTypes, METER_RETURN_OPTIONS_FORM_TYPES);
    };

    isMeterPlacementCodesRelevant = (): boolean => {
        const formTypes = this.form?.get('formTypes')?.value || [];
        return isAnyOfRequiredFormTypesSelected(formTypes, METER_PLACEMENT_CODES_FORM_TYPES);
    };

    isMeterSizeRelevant = (): boolean => {
        const formTypes = this.form?.get('formTypes')?.value || [];
        return isAnyOfRequiredFormTypesSelected(formTypes, METER_SIZE_FORM_TYPES);
    };

    isGroundingMethodsRelevant = (): boolean => {
        const formTypes = this.form?.get('formTypes')?.value || [];
        return isAnyOfRequiredFormTypesSelected(formTypes, GROUNDING_METHODS_FORM_TYPES);
    };

    isReasonsForChangeRelevant = (): boolean => {
        const formTypes = this.form?.get('formTypes')?.value || [];
        return isAnyOfRequiredFormTypesSelected(formTypes, REASONS_FOR_CHANGE_FORM_TYPES);
    };

    private updateScopeOfDeliveryValidation() {
        const scopeOfDeliveryMin = this.form.get('scopeOfDeliveryMin');
        const scopeOfDeliveryMax = this.form.get('scopeOfDeliveryMax');
        const scopeOfDeliveryUom = this.form.get('scopeOfDeliveryUom');

        if (!this.isScopeOfDeliveryRelevant()) {
            scopeOfDeliveryMin?.setValue(null);
            scopeOfDeliveryMax?.setValue(null);
            scopeOfDeliveryUom?.setValue(null);
        }
    }
}
