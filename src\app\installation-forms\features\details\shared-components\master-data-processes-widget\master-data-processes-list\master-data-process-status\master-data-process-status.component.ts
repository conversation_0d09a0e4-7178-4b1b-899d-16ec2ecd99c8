import { Component, Input } from '@angular/core';
import { MasterDataProcessEntryState } from 'src/app/api/installation-forms-client';
import { IconState } from 'src/app/core/enums/icon-state';

@Component({
    selector: 'app-master-data-process-status',
    templateUrl: './master-data-process-status.component.html',
    styleUrl: './master-data-process-status.component.scss',
    standalone: false
})
export class MasterDataProcessStatusComponent {
    @Input() masterDataProcessStatus?: MasterDataProcessEntryState;

    mapToIconState(): IconState {
        switch (this.masterDataProcessStatus) {
            case MasterDataProcessEntryState.Failed:
            case MasterDataProcessEntryState.FailedToCreateProcess:
            case MasterDataProcessEntryState.Cancelled:
                return IconState.Failed;
            case MasterDataProcessEntryState.Completed:
            case MasterDataProcessEntryState.ManuallyHandled:
                return IconState.Completed;
            default:
                return IconState.InProgress;
        }
    }
}
