import { ColumnTransformationType } from 'src/app/core/enums/column-transformation-type';
import { RulesListColumn } from '../../models/rules-list-column';
import { automaticInstructionTextTranslationPath } from './automatic-instruction-text.consts';

export const AUTOMATIC_INSTRUCTION_TEXT_COLUMNS: RulesListColumn[] = [
    {
        field: 'displayName',
        header: `${automaticInstructionTextTranslationPath}.displayName`,
        columnTransformationType: ColumnTransformationType.None,
        isDefault: true
    },
    {
        field: 'formTypes',
        header: `${automaticInstructionTextTranslationPath}.formTypes`,
        translationPath: 'enums.type.',
        columnTransformationType: ColumnTransformationType.TranslateEnumArray,
        isDefault: true
    },
    {
        field: 'formCategories',
        header: `${automaticInstructionTextTranslationPath}.formCategories`,
        translationPath: 'enums.category.',
        columnTransformationType: ColumnTransformationType.TranslateEnumArray,
        isDefault: true
    },
    {
        field: 'meterNeedsChange',
        header: `${automaticInstructionTextTranslationPath}.meterNeedsChange`,
        columnTransformationType: ColumnTransformationType.Boolean,
        isDefault: true
    },
    {
        field: 'meterNeedsReconfiguration',
        header: `${automaticInstructionTextTranslationPath}.meterNeedsReconfiguration`,
        columnTransformationType: ColumnTransformationType.Boolean,
        isDefault: true
    },
    {
        field: 'formIsScreened',
        header: `${automaticInstructionTextTranslationPath}.formIsScreened`,
        columnTransformationType: ColumnTransformationType.Boolean,
        isDefault: true
    },
    {
        field: 'defaultInstructionTexts',
        header: `${automaticInstructionTextTranslationPath}.defaultInstructionTexts`,
        columnTransformationType: ColumnTransformationType.InstructionTexts,
        isDefault: true
    }
];
