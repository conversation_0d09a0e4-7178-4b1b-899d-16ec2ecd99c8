import { ColumnTransformationType } from 'src/app/core/enums/column-transformation-type';
import { RulesListColumn } from '../../models/rules-list-column';
import { automaticScreeningTranslationPath } from './automatic-screening.consts';

export const AUTOMATIC_SCREENING_COLUMNS: RulesListColumn[] = [
    {
        field: 'displayName',
        header: `${automaticScreeningTranslationPath}.displayName`,
        columnTransformationType: ColumnTransformationType.None,
        isDefault: true
    },
    {
        field: 'formTypes',
        header: `${automaticScreeningTranslationPath}.formTypes`,
        translationPath: 'enums.type.',
        columnTransformationType: ColumnTransformationType.TranslateEnumArray,
        isDefault: true
    },
    {
        field: 'formStates',
        header: `${automaticScreeningTranslationPath}.formStates`,
        translationPath: 'enums.state.',
        columnTransformationType: ColumnTransformationType.TranslateEnumArray,
        isDefault: true
    },
    {
        field: 'formCategories',
        header: `${automaticScreeningTranslationPath}.formCategories`,
        translationPath: 'enums.category.',
        columnTransformationType: ColumnTransformationType.TranslateEnumArray,
        isDefault: true
    },
    {
        field: 'voltageLevels',
        header: `${automaticScreeningTranslationPath}.voltageLevels`,
        translationPath: 'enums.automationVoltageLevel.',
        columnTransformationType: ColumnTransformationType.TranslateEnumArray,
        isDefault: true
    },
    {
        field: 'scopeOfDelivery',
        header: `${automaticScreeningTranslationPath}.scopeOfDelivery`,
        columnTransformationType: ColumnTransformationType.None,
        isDefault: true
    },
    {
        field: 'terminationScope',
        header: `${automaticScreeningTranslationPath}.terminationScope`,
        translationPath: 'enums.terminationScope.',
        columnTransformationType: ColumnTransformationType.Translate,
        isDefault: true
    },
    {
        field: 'meterReturnOptionTypes',
        header: `${automaticScreeningTranslationPath}.meterReturnOptionTypes`,
        translationPath: 'enums.meterReturnOption.',
        columnTransformationType: ColumnTransformationType.TranslateEnumArray,
        isDefault: true
    },
    {
        field: 'meterPlacementCodes',
        header: `${automaticScreeningTranslationPath}.meterPlacementCodes`,
        columnTransformationType: ColumnTransformationType.ValueListMultiItem,
        isDefault: true
    },
    {
        field: 'meterSize',
        header: `${automaticScreeningTranslationPath}.meterSize`,
        translationPath: 'enums.meterSize.',
        columnTransformationType: ColumnTransformationType.Translate,
        isDefault: true
    },
    {
        field: 'groundingMethods',
        header: `${automaticScreeningTranslationPath}.groundingMethods`,
        translationPath: 'enums.connectionGroundingMethod.',
        columnTransformationType: ColumnTransformationType.TranslateEnumArray,
        isDefault: true
    },
    {
        field: 'reasonsForChange',
        header: `${automaticScreeningTranslationPath}.reasonsForChange`,
        translationPath: 'enums.reasonForChange.',
        columnTransformationType: ColumnTransformationType.TranslateEnumArray,
        isDefault: true
    }
];
