import { Component, Input } from '@angular/core';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';

@Component({
    selector: 'app-details-header',
    templateUrl: './details-header.component.html',
    styleUrls: ['./details-header.component.scss'],
    standalone: false
})
export class DetailsHeaderComponent {
    @Input() formNumber!: string;
    get supplyType(): string {
        return this.formDataService.supplyType!;
    }

    constructor(private formDataService: FormDataService) {}
}
