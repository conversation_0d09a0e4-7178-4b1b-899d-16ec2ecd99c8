<p-card>
    <app-markdown-viewer [markdownEnUs]="automaticScreeningMarkdownEnUs" [markdownDaDK]="automaticScreeningMarkdownDaDK">
    </app-markdown-viewer>
    <button
        pButton
        label="{{ 'formsSettings.automaticScreening.createRule' | translate }}"
        (click)="startCreating()"
        class="p-button-outlined mt-20 mb-20"
        *ngIf="!isCreating && hasUserFormConfigurationWritePermission"></button>
    <app-automatic-screening-create
        #automaticScreeningCreate
        *ngIf="isCreating"
        [isProcessing]="isProcessing"
        [alreadyUsedDisplayNames]="alreadyUsedDisplayNames"
        (cancelCreating)="cancelCreating()"
        (ruleCreated)="onRuleCreated($event)"></app-automatic-screening-create>
    <app-automatic-screening-edit
        #automaticScreeningEdit
        *ngIf="ruleInEdit"
        [isProcessing]="isProcessing"
        [ruleInEdit]="ruleInEdit"
        [alreadyUsedDisplayNames]="alreadyUsedDisplayNames"
        (cancelEditing)="cancelEditing()"
        (ruleEdited)="onRuleEdited($event)"></app-automatic-screening-edit>
    <p-table
        [value]="automaticScreeningRules"
        [columns]="columns"
        [reorderableColumns]="true"
        *ngIf="automaticScreeningRules.length > 0"
        (onRowReorder)="onRowReorder($event)"
        [scrollable]="true">
        <ng-template pTemplate="header">
            <tr>
                <th id="reorder-column" class="w-3rem" *ngIf="hasUserFormConfigurationWritePermission"></th>
                <th
                    *ngFor="let col of columns"
                    [id]="'automaticScreeningRules-' + col.field"
                    [pTooltip]="col.header | translate"
                    tooltipPosition="top">
                    {{ col.header | translate }}
                </th>
                <th id="automatic-screening-action-edit" pFrozenColumn></th>
                <th id="automatic-screening-action-delete" pFrozenColumn></th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-item let-index="rowIndex">
            <tr class="zebra-item table-row" [pReorderableRow]="index">
                <td *ngIf="hasUserFormConfigurationWritePermission">
                    <span class="pi pi-bars" pReorderableRowHandle></span>
                </td>
                <td *ngFor="let col of columns">
                    <ng-container [ngSwitch]="col.columnTransformationType">
                        <ng-container *ngSwitchCase="'None'">
                            {{ item[col.field] }}
                        </ng-container>
                        <ng-container *ngSwitchCase="'Translate'">
                            {{ !!item[col.field] ? (col.translationPath + item[col.field] | translate) : '' }}
                        </ng-container>
                        <ng-container *ngSwitchCase="'TranslateEnumArray'">
                            <div class="flex flex-column white-space-nowrap">
                                <ng-container *ngFor="let value of item[col.field]; let last = last">
                                    <span>{{ col.translationPath + value | translate }}{{ !last ? ',' : '' }}</span>
                                </ng-container>
                            </div>
                        </ng-container>
                        <ng-container *ngSwitchCase="'Boolean'">
                            <i class="fa-solid fa-circle-xmark color-error" *ngIf="item[col.field] === false"></i>
                            <i class="fa-regular fa-circle-check color-success" *ngIf="item[col.field] === true"></i>
                        </ng-container>
                        <ng-container *ngSwitchCase="'ValueListMultiItem'">
                            {{ item[col.field] | valueListMultiItem: getValueListType(col.field) | async }}
                        </ng-container>
                    </ng-container>
                </td>
                <td class="actions-column" pFrozenColumn alignFrozen="right">
                    <button
                        *ngIf="hasUserFormConfigurationWritePermission && (!ruleInEdit || ruleInEdit.id !== item.id)"
                        [disabled]="isProcessing"
                        pButton
                        icon="fa-solid fa-pen"
                        class="p-button-outlined border-none py-1"
                        (click)="editRule($event, item)"></button>
                </td>
                <td class="actions-column" pFrozenColumn alignFrozen="right">
                    <button
                        *ngIf="hasUserFormConfigurationWritePermission && (!ruleInEdit || ruleInEdit.id !== item.id)"
                        [disabled]="isProcessing"
                        pButton
                        icon="fa-solid fa-trash-can"
                        class="p-button-outlined border-none py-1"
                        (click)="deleteRule($event, item)"></button>
                </td>
            </tr>
        </ng-template>
    </p-table>
    <div class="flex justify-content-end mt-2" *ngIf="isReordered">
        <button
            id="cancelButton"
            type="button"
            pButton
            pRipple
            (click)="cancelReorder()"
            class="mr-2 p-button-secondary"
            [disabled]="isProcessing">
            {{ 'common.cancelChanges' | translate | titlecase }}
        </button>
        <button id="saveButton" type="button" pButton pRipple (click)="saveReorder()" [disabled]="isProcessing">
            {{ 'common.saveChanges' | translate }}
        </button>
    </div>
</p-card>

<app-confirmation-dialog
    [headerKey]="'formsSettings.automaticScreening.deleteConfirmationTitle'"
    [messageKey]="'formsSettings.automaticScreening.deleteConfirmationMessage'"
    [visible]="!!attemptedAction"
    (confirmEvent)="onConfirmationDialogYesButtonClick()"
    (cancelEvent)="onConfirmationDialogNoButtonClick()">
</app-confirmation-dialog>

<p-toast [key]="automaticScreeningTabName"></p-toast>

<p-toast #genericMessagesToast></p-toast>
