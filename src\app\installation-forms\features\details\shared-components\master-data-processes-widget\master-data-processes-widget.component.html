<div #overlayContainer></div>
<p-panel [header]="'masterDataProcessesWidget.title' | translate" [toggleable]="true">
    <ng-template pTemplate="icons">
        <button
            type="button"
            pButton
            pRipple
            class="p-button-outlined"
            *ngIf="showCreateMasterDataProcessButton()"
            [disabled]="isProcessing"
            (click)="createMasterDataProcessButtonClick()"
            size="small">
            {{ 'masterDataProcessesWidget.createMasterDataProcess' | translate }}
        </button>
    </ng-template>

    <app-create-master-data-process *ngIf="showCreateMasterDataProcess" (closeWidget)="closeCreateMasterDataProcess()">
    </app-create-master-data-process>

    <div *ngIf="!hasMasterDataReadPermission" fxFlex="nogrow" fxLayoutAlign="start center">
        <p-tag severity="warn" value="{{ 'common.lackOfPermissions' | translate }}"> </p-tag>
    </div>
    <app-master-data-processes-list *ngIf="hasMasterDataReadPermission"> </app-master-data-processes-list>
</p-panel>
