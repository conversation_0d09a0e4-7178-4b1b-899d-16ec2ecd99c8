import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { AutomaticScreeningRule, ScopeOfDeliveryRange, TransferConnectionRightUom } from 'src/app/api/installation-forms-client';
import { AutomaticScreeningListItem } from '../models/automatic-screening-list-item';

@Injectable({
    providedIn: 'root'
})
export class AutomaticScreeningMapper {
    constructor(private translateService: TranslateService) {}

    mapToAutomaticScreeningListItem = (rule: AutomaticScreeningRule): AutomaticScreeningListItem => ({
        id: rule.id,
        order: rule.order,
        displayName: rule.displayName,
        formTypes: rule.formTypes,
        formStates: rule.formStates,
        formCategories: rule.formCategories,
        voltageLevels: rule.voltageLevels,
        scopeOfDelivery: this.mapScopeOfDelivery(rule.scopeOfDeliveryRange),
        terminationScope: rule.terminationScope,
        meterReturnOptionTypes: rule.meterReturnOptionTypes,
        meterPlacementCodes: rule.meterPlacementCodes?.filter((x) => x !== undefined),
        meterSize: rule.meterSize,
        groundingMethods: rule.groundingMethods,
        reasonsForChange: rule.reasonsForChange?.filter((x) => x !== undefined)
    });

    mapScopeOfDelivery = (scopeOfDeliveryRange?: ScopeOfDeliveryRange) => {
        if (!scopeOfDeliveryRange) {
            return undefined;
        }
        return `${scopeOfDeliveryRange.min ?? '0'} - ${scopeOfDeliveryRange.max ?? '∞'} ${this.mapScopeOfDeliveryUom(scopeOfDeliveryRange.uom)}`;
    };

    mapScopeOfDeliveryUom = (uom: TransferConnectionRightUom) => {
        return this.translateService.instant(`enums.transferConnectionRightUom.${uom}`);
    };
}
