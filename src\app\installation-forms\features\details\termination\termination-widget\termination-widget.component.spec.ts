import { TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { of } from 'rxjs';
import { Termination } from 'src/app/api/installation-forms-client';
import { createGenericMock } from 'src/app/core/utils/mocks/create-generic-mock';
import { TerminationChanges } from './termination-changes.model';
import { TerminationWidgetComponent } from './termination-widget.component';

describe('TerminationWidgetComponent - Method Tests', () => {
    let component: TerminationWidgetComponent;
    let formBuilder: FormBuilder;

    const translateServiceMock = {
        instant: jest.fn().mockReturnValue({})
    };

    const messageMock = {
        showWarning: jest.fn(),
        showError: jest.fn(),
        showSuccess: jest.fn()
    };

    const clientMock = {
        updateFormPaymentDetails: jest.fn().mockReturnValue(of({})),
        updateTerminationFormData: jest.fn().mockReturnValue(of({}))
    };

    const formDataServiceMock = {
        editabilityChange$: of({}),
        canUpdatePaymentDetails: false
    };

    const automaticFormRefreshServiceMock = {
        notifyConflictingChangeOccurred: jest.fn(),
        forceRefresh: jest.fn(),
        forceRefreshRequested$: of({})
    };

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            declarations: [TerminationWidgetComponent],
            providers: [FormBuilder]
        }).compileComponents();

        // Get the FormBuilder
        formBuilder = TestBed.inject(FormBuilder);

        // Create component instance manually
        component = new TerminationWidgetComponent(
            formBuilder,
            messageMock as any,
            translateServiceMock as any,
            clientMock as any,
            formDataServiceMock as any,
            automaticFormRefreshServiceMock as any
        );

        component.formDetails = createGenericMock<Termination>({
            tags: [],
            relatedForms: [],
            flags: []
        });

        // Initialize component manually (equivalent to ngOnInit)
        // You might need to add this if component methods rely on initialization
        if (typeof component.ngOnInit === 'function') {
            component.ngOnInit();
        }
    });

    it('should have synchronized properties between TerminationChanges and comparison models', () => {
        // Get all property keys from TerminationChanges
        const changesModel = new TerminationChanges();
        const changesModelKeys = Object.keys(changesModel);

        // Get keys from both comparison models - with and without paymentDetailsOnly flag
        const formComparisonModelFullKeys = Object.keys(component.getFormComparisonModel(false));
        const modelComparisonFullKeys = Object.keys(component.convertToComparisonModel(component.formDetails, false));

        const paymentDetailsOnlyFormComparisonModelFullKeys = Object.keys(component.getFormComparisonModel(true));
        const paymentDetailsOnlyModelComparisonFullKeys = Object.keys(
            component.convertToComparisonModel(component.formDetails, true)
        );

        // When only payment details , then we just comparing objects (without changes model)
        const redundantInPaymentDetailsOnlyForm = paymentDetailsOnlyFormComparisonModelFullKeys.filter(
            (key) => !paymentDetailsOnlyModelComparisonFullKeys.includes(key)
        );
        const redundantInPaymentDetailsOnlyModel = paymentDetailsOnlyModelComparisonFullKeys.filter(
            (key) => !paymentDetailsOnlyFormComparisonModelFullKeys.includes(key)
        );

        // Comparing full details and comparing with changes model.
        const redundantInConvertToComparisonModel = formComparisonModelFullKeys.filter((key) => !changesModelKeys.includes(key));
        const redundantInGetFormComparisonModel = modelComparisonFullKeys.filter((key) => !changesModelKeys.includes(key));

        const missedInConvertToComparisonModel = changesModelKeys.filter((key) => !formComparisonModelFullKeys.includes(key));
        const missedInGetFormComparisonModel = changesModelKeys.filter((key) => !modelComparisonFullKeys.includes(key));

        // usefull for debugging
        if (redundantInPaymentDetailsOnlyForm.length > 0) {
            console.log('Redundant in paymentDetailsOnlyForm:', redundantInPaymentDetailsOnlyForm);
        }

        if (redundantInPaymentDetailsOnlyModel.length > 0) {
            console.log('Redundant in paymentDetailsOnlyModel:', redundantInPaymentDetailsOnlyModel);
        }
        if (missedInGetFormComparisonModel.length > 0) {
            console.log('Missing in getFormComparisonModel:', missedInGetFormComparisonModel);
        }

        if (missedInConvertToComparisonModel.length > 0) {
            console.log('Missing in convertToComparisonModel:', missedInConvertToComparisonModel);
        }

        if (redundantInConvertToComparisonModel.length > 0) {
            console.log('Redundant in convertToComparisonModel:', redundantInConvertToComparisonModel);
        }

        if (redundantInGetFormComparisonModel.length > 0) {
            console.log('Redundant in getFormComparisonModel:', redundantInGetFormComparisonModel);
        }

        // Assert
        expect(redundantInPaymentDetailsOnlyForm).toEqual([]);
        expect(redundantInPaymentDetailsOnlyModel).toEqual([]);
        expect(missedInGetFormComparisonModel).toEqual([]);
        expect(missedInConvertToComparisonModel).toEqual([]);
        expect(redundantInConvertToComparisonModel).toEqual([]);
        expect(redundantInGetFormComparisonModel).toEqual([]);
    });
});
