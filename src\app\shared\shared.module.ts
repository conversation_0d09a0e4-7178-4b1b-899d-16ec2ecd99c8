import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { MessageModule } from 'primeng/message';
import { MessagesModule } from 'primeng/messages';
import { MultiSelectModule } from 'primeng/multiselect';
import { SelectModule } from 'primeng/select';
import { TagModule } from 'primeng/tag';
import { ToggleSwitchModule } from 'primeng/toggleswitch';
import { TooltipModule } from 'primeng/tooltip';
import { FormDataService } from '../core/services/form-data/form-data.service';
import { AddressSearchComponent } from './components/address-search/address-search.component';
import { ConfirmationDialogComponent } from './components/confirmation-dialog/confirmation-dialog.component';
import { IconMasterDataPathComponent } from './components/icon-master-data-path/icon-master-data-path.component';
import { IconStateComponent } from './components/icon-state/icon-state.component';
import { IconTaskGroupComponent } from './components/icon-task-group/icon-task-group.component';
import { IconTaskIsManualComponent } from './components/icon-task-is-manual/icon-task-is-manual.component';
import { MasterDataCompareToggleComponent } from './components/master-data-compare-toggle/master-data-compare-toggle.component';
import { MeterFrameSearchComponent } from './components/meter-frame-search/meter-frame-search.component';
import { ValueListMultiselectComponent } from './components/value-list-multiselect/value-list-multiselect.component';
import { AutoTrimDirective } from './directives/auto-trim.directive';
import { ControlValidationErrorsDirective } from './directives/control-validation-errors/control-validation-errors.directive';
import { ValidationMessageProviderService } from './directives/control-validation-errors/validation-message-provider.service';
import { ValidationMessageTranslationKeysService } from './directives/control-validation-errors/validation-message-translation-keys.service';
import { LabelRequiredDirective } from './directives/label-required.directive';
import { BooleanToYesNoPipe } from './pipes/boolean-translate/boolean-translate.pipe';
import { FormatDatePipe } from './pipes/date-format/date-format.pipe';
import { FormatDateTimePipe } from './pipes/datetime-format/datetime-format.pipe';
import { InstructionTextsPipe } from './pipes/instruction-texts/instruction-text.pipe';
import { InvoicingPricePipe } from './pipes/invoicing-price/invoicing-price.pipe';
import { MasterDataPathTooltipPipe } from './pipes/master-data-path-tooltip/master-data-path-tooltip.pipe';
import { NoValueEnumTranslatePipe } from './pipes/no-value-enum-translate/no-value-enum-translate.pipe';
import { NoValuePipe } from './pipes/no-value/no-value.pipe';
import { TaskGroupPathTooltipPipe } from './pipes/task-group-path-tooltip/task-group-path-tooltip.pipe';
import { TranslateEnumArrayPipe } from './pipes/translate-enum-array/translate-enum-array.pipe';
import { ValueListMultiItemPipe } from './pipes/value-list-multi-item/value-list-multi-item.pipe';
import { ValueListSingleItemPipe } from './pipes/value-list-single-item/value-list-single-item.pipe';

@NgModule({
    declarations: [
        NoValuePipe,
        TranslateEnumArrayPipe,
        NoValueEnumTranslatePipe,
        ControlValidationErrorsDirective,
        LabelRequiredDirective,
        AutoTrimDirective,
        FormatDatePipe,
        FormatDateTimePipe,
        BooleanToYesNoPipe,
        ValueListMultiselectComponent,
        IconStateComponent,
        AddressSearchComponent,
        MeterFrameSearchComponent,
        ConfirmationDialogComponent,
        ValueListSingleItemPipe,
        ValueListMultiItemPipe,
        InstructionTextsPipe,
        MasterDataPathTooltipPipe,
        TaskGroupPathTooltipPipe,
        IconMasterDataPathComponent,
        MasterDataCompareToggleComponent,
        IconTaskGroupComponent,
        IconTaskIsManualComponent,
        InvoicingPricePipe
    ],
    imports: [
        CommonModule,
        FormsModule,
        MultiSelectModule,
        SelectModule,
        ReactiveFormsModule,
        TranslateModule,
        MessageModule,
        MessagesModule,
        AutoCompleteModule,
        FlexLayoutModule,
        DialogModule,
        ButtonModule,
        TooltipModule,
        ToggleSwitchModule,
        TagModule
    ],
    exports: [
        NoValuePipe,
        TranslateEnumArrayPipe,
        NoValueEnumTranslatePipe,
        ControlValidationErrorsDirective,
        LabelRequiredDirective,
        AutoTrimDirective,
        FormatDatePipe,
        FormatDateTimePipe,
        BooleanToYesNoPipe,
        ValueListMultiselectComponent,
        IconStateComponent,
        AddressSearchComponent,
        MeterFrameSearchComponent,
        ConfirmationDialogComponent,
        ValueListSingleItemPipe,
        ValueListMultiItemPipe,
        InstructionTextsPipe,
        MasterDataPathTooltipPipe,
        TaskGroupPathTooltipPipe,
        IconMasterDataPathComponent,
        MasterDataCompareToggleComponent,
        IconTaskGroupComponent,
        IconTaskIsManualComponent,
        InvoicingPricePipe
    ],
    providers: [ValidationMessageProviderService, ValidationMessageTranslationKeysService, FormDataService],
    bootstrap: []
})
export class SharedModule {}
