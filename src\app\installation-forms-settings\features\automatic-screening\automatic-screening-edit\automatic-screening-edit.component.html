<div #overlayContainer></div>
<div fxLayout="column" class="mt-10 zebra-container" [formGroup]="form">
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['displayName']" for="displayName">
                {{ 'formsSettings.automaticScreening.displayName' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input id="displayName" type="text" pInputText maxlength="100" formControlName="displayName" autoTrimWhitespaces />
            <small [controlValidationErrors]="form.controls['displayName']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['formTypes']" for="formTypes">
                {{ 'formsSettings.automaticScreening.formTypes' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-multiSelect
                id="formTypes"
                [options]="formTypeOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="formTypes"
                [maxSelectedLabels]="formTypeOptions.length">
            </p-multiSelect>
            <small [controlValidationErrors]="form.controls['formTypes']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['formStates']" for="formStates">
                {{ 'formsSettings.automaticScreening.formStates' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-multiSelect
                id="formStates"
                [options]="formStateOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="formStates"
                [maxSelectedLabels]="formStateOptions.length">
            </p-multiSelect>
            <small [controlValidationErrors]="form.controls['formStates']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isFormCategoryRelevant()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['formCategories']" for="formCategories">
                {{ 'formsSettings.automaticScreening.formCategories' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-multiSelect
                id="formCategories"
                [options]="formCategoriesOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="formCategories"
                [maxSelectedLabels]="formCategoriesOptions.length">
            </p-multiSelect>
            <small [controlValidationErrors]="form.controls['formCategories']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isVoltageLevelRelevant()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['voltageLevels']" for="voltageLevels">
                {{ 'formsSettings.automaticScreening.voltageLevels' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-multiSelect
                id="voltageLevels"
                [options]="voltageLevelOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="voltageLevels"
                [maxSelectedLabels]="voltageLevelOptions.length">
            </p-multiSelect>
            <small [controlValidationErrors]="form.controls['voltageLevels']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isScopeOfDeliveryRelevant()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['scopeOfDeliveryMin']" for="scopeOfDeliveryMin">
                {{ 'formsSettings.automaticScreening.scopeOfDeliveryMin' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-inputNumber
                id="scopeOfDeliveryMin"
                formControlName="scopeOfDeliveryMin"
                [min]="0"
                [max]="2147483647"
                [useGrouping]="false">
            </p-inputNumber>
            <small [controlValidationErrors]="form.controls['scopeOfDeliveryMin']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isScopeOfDeliveryRelevant()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['scopeOfDeliveryMax']" for="scopeOfDeliveryMax">
                {{ 'formsSettings.automaticScreening.scopeOfDeliveryMax' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-inputNumber
                id="scopeOfDeliveryMax"
                formControlName="scopeOfDeliveryMax"
                [min]="0"
                [max]="2147483647"
                [useGrouping]="false">
            </p-inputNumber>
            <small [controlValidationErrors]="form.controls['scopeOfDeliveryMax']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isScopeOfDeliveryRelevant()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['scopeOfDeliveryUom']" for="scopeOfDeliveryUom">
                {{ 'formsSettings.automaticScreening.scopeOfDeliveryUom' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="scopeOfDeliveryUom"
                [options]="scopeOfDeliveryUomOptions"
                optionValue="value"
                optionLabel="label"
                [showClear]="true"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="scopeOfDeliveryUom">
            </p-select>
            <small [controlValidationErrors]="form.controls['scopeOfDeliveryUom']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isTerminationScopeRelevant()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['terminationScope']" for="terminationScope">
                {{ 'formsSettings.automaticScreening.terminationScope' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="terminationScope"
                [options]="terminationScopeOptions"
                optionValue="value"
                optionLabel="label"
                [showClear]="true"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="terminationScope">
            </p-select>
            <small [controlValidationErrors]="form.controls['terminationScope']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isMeterReturnOptionsRelevant()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['meterReturnOptionTypes']" for="meterReturnOptionTypes">
                {{ 'formsSettings.automaticScreening.meterReturnOptionTypes' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-multiSelect
                id="meterReturnOptionTypes"
                [options]="meterReturnOptionTypesOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="meterReturnOptionTypes"
                [maxSelectedLabels]="meterReturnOptionTypesOptions.length">
            </p-multiSelect>
            <small [controlValidationErrors]="form.controls['meterReturnOptionTypes']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isMeterPlacementCodesRelevant()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['meterPlacementCodes']" for="meterPlacementCodes">
                {{ 'formsSettings.automaticScreening.meterPlacementCodes' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <app-value-list-multiselect
                id="meterPlacementCodes"
                formControlName="meterPlacementCodes"
                [valueListType]="meterFramePlacementValueListType"
                [appendTo]="overlayContainer">
            </app-value-list-multiselect>
            <small [controlValidationErrors]="form.controls['meterPlacementCodes']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isMeterSizeRelevant()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['meterSize']" for="meterSize">
                {{ 'formsSettings.automaticScreening.meterSize' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="meterSize"
                [options]="meterSizeOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="meterSize">
            </p-select>
            <small [controlValidationErrors]="form.controls['meterSize']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isGroundingMethodsRelevant()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['groundingMethods']" for="groundingMethods">
                {{ 'formsSettings.automaticScreening.groundingMethods' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-multiSelect
                id="groundingMethods"
                [options]="groundingMethodsOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="groundingMethods"
                [maxSelectedLabels]="groundingMethodsOptions.length">
            </p-multiSelect>
            <small [controlValidationErrors]="form.controls['groundingMethods']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isReasonsForChangeRelevant()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['reasonsForChange']" for="reasonsForChange">
                {{ 'formsSettings.automaticScreening.reasonsForChange' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-multiSelect
                id="reasonsForChange"
                [options]="reasonsForChangeOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="reasonsForChange"
                [maxSelectedLabels]="reasonsForChangeOptions.length">
            </p-multiSelect>
            <small [controlValidationErrors]="form.controls['reasonsForChange']" class="p-error"></small>
        </div>
    </div>

    <div class="mb-2 mt-2 flex justify-content-end">
        <button
            id="cancelButton"
            [disabled]="isProcessing"
            type="button"
            pButton
            pRipple
            class="mr-2 p-button-secondary"
            (click)="cancelEditClick()">
            {{ 'common.cancel' | translate }}
        </button>
        <button
            id="saveButton"
            type="button"
            pButton
            pRipple
            (click)="saveEditedClick()"
            [disabled]="form.invalid || isProcessing">
            {{ 'common.saveChanges' | translate }}
        </button>
    </div>
</div>
