import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormDataService } from 'src/app/core/services/form-data/form-data.service';
import { TranslatedFormProblem } from '../translated-form-problem.model';

@Component({
    selector: 'app-problems-list',
    templateUrl: './problems-list.component.html',
    standalone: false
})
export class ProblemsListComponent {
    @Input() problems: TranslatedFormProblem[] = [];
    @Input() type: 'error' | 'warning' | 'info' | 'acknowledged' = 'info';
    @Input() isProcessing = false;

    @Output() markAsAcknowledged = new EventEmitter<string>();

    constructor(protected readonly formDataService: FormDataService) {}

    get colorClass(): string {
        return `color-${this.type}`;
    }

    get icon(): string {
        switch (this.type) {
            case 'error':
                return 'pi pi-times-circle';
            case 'warning':
                return 'pi pi-exclamation-triangle';
            case 'info':
                return 'pi pi-exclamation-circle';
            case 'acknowledged':
                return 'pi pi-check-circle';
            default:
                return '';
        }
    }

    onMarkAsAcknowledged(id: string): void {
        this.markAsAcknowledged.emit(id);
    }
}
