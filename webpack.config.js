﻿const {
    shareAll,
    withModuleFederationPlugin,
} = require("@angular-architects/module-federation/webpack");

module.exports = withModuleFederationPlugin({
    name: "installation-forms",

    exposes: {
        './mf/installation-forms': './src/app/installation-forms/installation-forms.module.ts',
        './mf/installation-forms-settings': './src/app/installation-forms-settings/installation-forms-settings.module.ts'
    },

    shared: {
        ...shareAll({ singleton: true, requiredVersion: 'auto' }),
    },
    sharedMappings: ['paths']
});
