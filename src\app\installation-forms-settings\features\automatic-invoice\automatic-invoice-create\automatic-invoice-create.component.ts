import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import {
    AutomaticInvoiceRule,
    AutomaticInvoiceRuleCreateOrUpdate,
    AutomationVoltageLevel,
    FormType,
    InstallationFormsClient,
    Price,
    SupplyType,
    ValueRange
} from 'src/app/api/installation-forms-client';
import { SETTINGS_DISPLAY_NAME_MAX_LENGTH } from 'src/app/core/constants/constants';
import { WidgetNames } from 'src/app/core/constants/widget-names';
import { BooleanOptionsService } from 'src/app/core/services/boolean-options/boolean-options.service';
import { InvoicingPricesService } from 'src/app/core/services/invoicing-prices/invoicing-prices.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import conditionallyRequiredValidator from 'src/app/core/utils/validators/conditionally-required/conditionally-required.validator';
import { multipleWhitespaceValidator } from 'src/app/core/utils/validators/multiple-whitespace-validator/multiple-whitespace-validator';
import { v4 as uuidv4 } from 'uuid';
import { alreadyUsedDisplayNameValidator } from '../../../../core/utils/validators/already-used-display-name-validator/already-used-display-name-validator';
import { BaseRuleCreateComponent } from '../../base-components/base-rule-create.components';
import { automaticInvoiceTabName, automaticInvoiceTranslationPath } from '../automatic-invoice.consts';
import { AutomaticInvoiceSettingsService } from '../services/automatic-invoice-settings.service';

@Component({
    selector: 'app-automatic-invoice-create',
    templateUrl: './automatic-invoice-create.component.html',
    standalone: false
})
export class AutomaticInvoiceCreateComponent extends BaseRuleCreateComponent<AutomaticInvoiceRule> implements OnInit {
    formTypeOptions: SelectItem[] = [];
    voltageLevelOptions: SelectItem[] = [];
    formCategoriesOptions: SelectItem[] = [];
    invoiceTypeOptions: SelectItem[] = [];
    tariffOptions: SelectItem[] = [];

    constructor(
        private readonly client: InstallationFormsClient,
        private readonly invoicingPricesService: InvoicingPricesService,
        private readonly fb: FormBuilder,
        protected readonly translateService: TranslateService,
        protected readonly messageServiceHelper: MessageServiceHelper,
        protected readonly booleanOptionsService: BooleanOptionsService,
        protected readonly settingsService: AutomaticInvoiceSettingsService
    ) {
        super(translateService, messageServiceHelper, automaticInvoiceTranslationPath, automaticInvoiceTabName);
    }

    ngOnInit(): void {
        super.ngOnInit();
        this.addFormControlsValueChanges();
        this.loadExistingTariffs();
    }

    override getEnumTranslations() {
        this.formTypeOptions = enumMapper.map(this.translateService.instant('enums.type'), FormType);
        this.voltageLevelOptions = enumMapper.map(
            this.translateService.instant('enums.automationVoltageLevel'),
            AutomationVoltageLevel
        );

        this.invoiceTypeOptions = this.settingsService.updateInvoiceTypes(this.form);
    }

    loadExistingTariffs() {
        this.subscription.add(
            this.invoicingPricesService.getPricesBySupplyType(SupplyType.Electricity).subscribe({
                next: (response) => {
                    this.tariffOptions =
                        response?.map((price: Price) => {
                            return {
                                label: price.displayName,
                                value: price.id
                            };
                        }) || [];
                },
                error: (_) => {
                    this.messageServiceHelper.showError({
                        detail: this.widgetTranslations['loadPricesError'],
                        key: WidgetNames.invoicesWidget
                    });
                }
            })
        );
    }

    override initForm(): void {
        this.form = this.fb.group({
            displayName: [
                null,
                [
                    Validators.required,
                    Validators.maxLength(SETTINGS_DISPLAY_NAME_MAX_LENGTH),
                    alreadyUsedDisplayNameValidator(this.alreadyUsedDisplayNames),
                    multipleWhitespaceValidator()
                ]
            ],
            voltageLevels: [null, Validators.required],
            formTypes: [null, Validators.required],
            formCategories: [null],
            scopeOfDeliveryMin: [null, [Validators.min(0), Validators.max(2147483647)]],
            scopeOfDeliveryMax: [null, [Validators.min(0), Validators.max(2147483647)]],
            canTransferInvoiceAutomatically: [false, Validators.required],
            productionCapacityMin: [null, [Validators.min(0), Validators.max(2147483647)]],
            productionCapacityMax: [null, [Validators.min(0), Validators.max(2147483647)]],
            hadExistingProduction: [null],
            invoiceType: [null, Validators.required],
            tariff: [
                null,
                (control: AbstractControl) =>
                    conditionallyRequiredValidator(control, () => this.settingsService.showTariff(this.form))
            ]
        });
    }

    override createClick() {
        if (this.form.invalid) {
            return;
        }
        const rule: AutomaticInvoiceRuleCreateOrUpdate = new AutomaticInvoiceRuleCreateOrUpdate({
            displayName: this.form.controls['displayName'].value,
            voltageLevels: this.form.controls['voltageLevels'].value,
            formTypes: this.form.controls['formTypes'].value,
            formCategories: this.form.controls['formCategories'].value,
            scopeOfDeliveryMin: this.form.controls['scopeOfDeliveryMin'].value,
            scopeOfDeliveryMax: this.form.controls['scopeOfDeliveryMax'].value,
            canTransferInvoiceAutomatically: this.form.controls['canTransferInvoiceAutomatically'].value,
            productionCapacityRange:
                this.form.controls['productionCapacityMin'].value || this.form.controls['productionCapacityMax'].value
                    ? new ValueRange({
                          min: this.form.controls['productionCapacityMin'].value,
                          max: this.form.controls['productionCapacityMax'].value
                      })
                    : undefined,
            hadExistingProduction: this.form.controls['hadExistingProduction'].value,
            invoiceType: this.form.controls['invoiceType'].value,
            tariff: this.form.controls['tariff'].value
        });

        if (
            rule.scopeOfDeliveryMin !== undefined &&
            rule.scopeOfDeliveryMax !== undefined &&
            rule.scopeOfDeliveryMin > rule.scopeOfDeliveryMax
        ) {
            this.showWarningMessage(this.widgetTranslations['invoiceRuleWarningMinGreaterThanMax']);
            return;
        }

        this.isProcessing = true;
        this.subscription.add(
            this.client.createAutomaticInvoicesRule(uuidv4(), rule).subscribe({
                next: (response) => {
                    this.ruleCreated.emit(response.result);
                    this.showSuccessMessage();
                    this.isProcessing = false;
                },

                error: (_) => {
                    this.showErrorMessage(this.widgetTranslations['invoiceRuleCreationError']);
                    this.isProcessing = false;
                }
            })
        );
    }

    addFormControlsValueChanges() {
        this.subscription.add(
            this.form.get('formTypes')?.valueChanges.subscribe(() => {
                this.settingsService.updateHadExistingProductionValidation(this.form);
                this.settingsService.updateProductionCapacityValidation(this.form);
                this.formCategoriesOptions = this.settingsService.updateFormCategories(this.form);
                this.settingsService.updateShowTariffValidation(this.form);
                this.invoiceTypeOptions = this.settingsService.updateInvoiceTypes(this.form);
            })
        );
    }
}
