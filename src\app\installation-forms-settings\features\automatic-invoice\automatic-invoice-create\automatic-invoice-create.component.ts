import { Component } from '@angular/core';
import { Form<PERSON><PERSON>er, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import {
    AutomaticInvoiceRule,
    AutomaticInvoiceRuleCreateOrUpdate,
    AutomationVoltageLevel,
    FormType,
    InstallationFormsClient
} from 'src/app/api/installation-forms-client';
import { SETTINGS_DISPLAY_NAME_MAX_LENGTH } from 'src/app/core/constants/constants';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import { multipleWhitespaceValidator } from 'src/app/core/utils/validators/multiple-whitespace-validator/multiple-whitespace-validator';
import { v4 as uuidv4 } from 'uuid';
import { alreadyUsedDisplayNameValidator } from '../../../../core/utils/validators/already-used-display-name-validator/already-used-display-name-validator';
import { BaseRuleCreateComponent } from '../../base-components/base-rule-create.components';
import { automaticInvoiceTabName, automaticInvoiceTranslationPath } from '../automatic-invoice.consts';

@Component({
    selector: 'app-automatic-invoice-create',
    templateUrl: './automatic-invoice-create.component.html',
    styleUrl: './automatic-invoice-create.component.scss',
    standalone: false
})
export class AutomaticInvoiceCreateComponent extends BaseRuleCreateComponent<AutomaticInvoiceRule> {
    formTypeOptions: SelectItem[] = [];
    voltageLevelOptions: SelectItem[] = [];

    constructor(
        private readonly client: InstallationFormsClient,
        protected readonly fb: FormBuilder,
        protected readonly translateService: TranslateService,
        protected readonly messageServiceHelper: MessageServiceHelper
    ) {
        super(fb, translateService, messageServiceHelper, automaticInvoiceTranslationPath, automaticInvoiceTabName);
    }

    override initForm(): void {
        this.form = this.fb.group({
            displayName: [
                null,
                [
                    Validators.required,
                    Validators.maxLength(SETTINGS_DISPLAY_NAME_MAX_LENGTH),
                    alreadyUsedDisplayNameValidator(this.alreadyUsedDisplayNames),
                    multipleWhitespaceValidator()
                ]
            ],
            voltageLevels: [null, Validators.required],
            formTypes: [null, Validators.required],
            scopeOfDeliveryMin: [null, [Validators.min(0), Validators.max(2147483647)]],
            scopeOfDeliveryMax: [null, [Validators.min(0), Validators.max(2147483647)]],
            canTransferInvoiceAutomatically: [false, Validators.required]
        });
    }

    override getEnumTranslations() {
        this.formTypeOptions = enumMapper.map(this.translateService.instant('enums.type'), [
            FormType.NewInstallation,
            FormType.Extension,
            FormType.Termination
        ]);
        this.voltageLevelOptions = enumMapper.map(
            this.translateService.instant('enums.automationVoltageLevel'),
            AutomationVoltageLevel
        );
    }

    override createClick() {
        if (this.form.invalid) {
            return;
        }
        const rule: AutomaticInvoiceRuleCreateOrUpdate = new AutomaticInvoiceRuleCreateOrUpdate({
            displayName: this.form.controls['displayName'].value,
            voltageLevels: this.form.controls['voltageLevels'].value,
            formTypes: this.form.controls['formTypes'].value,
            scopeOfDeliveryMin: this.form.controls['scopeOfDeliveryMin'].value,
            scopeOfDeliveryMax: this.form.controls['scopeOfDeliveryMax'].value,
            canTransferInvoiceAutomatically: this.form.controls['canTransferInvoiceAutomatically'].value
        });

        if (
            rule.scopeOfDeliveryMin !== undefined &&
            rule.scopeOfDeliveryMax !== undefined &&
            rule.scopeOfDeliveryMin > rule.scopeOfDeliveryMax
        ) {
            this.showWarningMessage(this.widgetTranslations['invoiceRuleWarningMinGreaterThanMax']);
            return;
        }

        this.isProcessing = true;
        this.subscription.add(
            this.client.createAutomaticInvoicesRule(uuidv4(), rule).subscribe({
                next: (response) => {
                    this.ruleCreated.emit(response.result);
                    this.showSuccessMessage();
                    this.isProcessing = false;
                },
                error: (_) => {
                    this.showErrorMessage(this.widgetTranslations['invoiceRuleCreationError']);
                    this.isProcessing = false;
                }
            })
        );
    }
}
