import { Component } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { Observable } from 'rxjs';
import {
    <PERSON><PERSON>erson,
    FormsRelationType,
    InstallationFormsApiResponse,
    InstallationFormsClient,
    InstallationInformationUpdate,
    PayerType,
    PayerUpdate,
    PaymentDetailsUpdate,
    ReasonForChange,
    RelatedForm,
    RelatedFormUpdate,
    ResponsibleForSeal,
    SealBreach,
    SealBreachSealInformationUpdate,
    SealBreachUpdate
} from 'src/app/api/installation-forms-client';
import { ChangesModel } from 'src/app/core/constants/changes-details';
import { AutomaticFormRefreshService } from 'src/app/core/services/automatic-form-refresh/automatic-form-refresh.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { createMeteringPoint } from 'src/app/core/utils/create-metering-point';
import { getValueOrDefault } from 'src/app/core/utils/get-value-or-default';
import addressLookupValidator from 'src/app/core/utils/validators/address-lookup/address-lookup.validator';
import { EmailValidator } from 'src/app/core/utils/validators/email/email.validator';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';
import { CoreWidgetComponent } from '../../base-components/widgets/core-widget.component';
import { SealBreachChanges } from './seal-breach-changes.model';

@Component({
    selector: 'app-seal-breach-widget',
    templateUrl: './seal-breach-widget.component.html',
    styleUrls: ['./seal-breach-widget.component.scss'],
    standalone: false
})
export class SealBreachWidgetComponent extends CoreWidgetComponent<SealBreach, SealBreachUpdate> {
    override accordionActiveIndexes: number[] = [0, 1];
    override payerPanelIndex: number = 3;

    get sealBreachSealInformationForm(): FormGroup {
        return this.form.get('sealBreachSealInformation') as FormGroup;
    }

    constructor(
        protected fb: FormBuilder,
        protected readonly messageServiceHelper: MessageServiceHelper,
        protected readonly translateService: TranslateService,
        protected readonly client: InstallationFormsClient,
        protected readonly formDataService: FormDataService,
        protected readonly automaticFormRefreshService: AutomaticFormRefreshService
    ) {
        super(formDataService, automaticFormRefreshService, translateService, messageServiceHelper, client);
        this.initForm();
        this.setChangesModel();
    }

    protected override initForm() {
        this.form = this.fb.group({
            installationInformation: this.fb.group({
                installationAddress: [{ id: '', text: '' }, [addressLookupValidator()]],
                remarksToInstallation: ['', [Validators.maxLength(1000)]],
                connectionPoint: [{ id: '', connectionPointNumber: '' }],
                meterFrame: [{ id: '', meterFrameNumber: '' }],
                consumptionMeteringPoint: [{ meteringPointId: '', meteringPointVersionId: '' }],
                tags: []
            }),
            payer: this.fb.group({
                payerName: ['', [Validators.maxLength(100)]],
                payerType: [''],
                payerEmail: ['', [EmailValidator()]],
                payerContactPersonName: ['', [Validators.maxLength(100)]],
                payerContactPersonEmail: ['', [EmailValidator()]],
                payerContactPersonPhoneNumber: ['', [Validators.maxLength(20)]],
                requisition: '',
                cvrOrSeNumber: ['', [Validators.pattern('^[0-9]{8}$')]],
                eanNumber: ['', [Validators.pattern('^[0-9]{13}$')]],
                payerAddress: [{ id: '', text: '' }]
            }),
            contactPerson: this.fb.group({
                contactPersonCompanyName: ['', [Validators.maxLength(100)]],
                contactPersonName: ['', [Validators.maxLength(100)]],
                contactPersonEmail: ['', [EmailValidator()]],
                contactPersonPhoneNumber: ['', [Validators.maxLength(20)]]
            }),
            sealBreachSealInformation: this.fb.group({
                meterPlacementId: ['', [Validators.maxLength(100), Validators.required]],
                responsibleForSeal: [''],
                reasonForChange: ['']
            }),
            relatedForms: this.fb.group({
                relatedForms: [],
                hasRelatedFormsChanged: false
            })
        });
    }

    protected override setChangesModel() {
        this.changesModel = new SealBreachChanges() as unknown as ChangesModel;
    }

    protected override supplyFormData() {
        this.form.setValue({
            installationInformation: {
                installationAddress: {
                    id: this.formDetails.installationAddress?.carId || '',
                    text: this.formDetails.installationAddress?.formattedAddress || ''
                },
                remarksToInstallation: this.formDetails.remarksToInstallation || '',
                connectionPoint: {
                    id: this.formDetails.connectionPoint?.id || '',
                    connectionPointNumber: this.formDetails.connectionPoint?.connectionPointNumber || ''
                },
                meterFrame: {
                    id: this.formDetails.meterFrame?.id || '',
                    meterFrameNumber: this.formDetails.meterFrame?.meterFrameNumber || ''
                },
                consumptionMeteringPoint: {
                    meteringPointId: this.formDetails.consumptionMeteringPoint?.meteringPointId || '',
                    meteringPointVersionId: this.formDetails.consumptionMeteringPoint?.meteringPointVersionId || ''
                },
                tags: this.formDetails.tags.map((t) => t.id) || []
            },
            contactPerson: {
                contactPersonCompanyName: this.formDetails.contactPerson?.companyName || '',
                contactPersonName: this.formDetails.contactPerson?.name || '',
                contactPersonEmail: this.formDetails.contactPerson?.email || '',
                contactPersonPhoneNumber: this.formDetails.contactPerson?.phoneNumber || ''
            },
            payer: {
                payerName: this.formDetails.payer?.name || '',
                payerEmail: this.formDetails.payer?.email || '',
                payerType: this.formDetails.payer?.type || PayerType.Private,
                payerContactPersonName: this.formDetails.payer?.contactPerson?.name || '',
                payerContactPersonEmail: this.formDetails.payer?.contactPerson?.email || '',
                payerContactPersonPhoneNumber: this.formDetails.payer?.contactPerson?.phoneNumber || '',
                requisition: this.formDetails.payer?.requisition || '',
                cvrOrSeNumber: this.formDetails.payer?.cvrOrSeNumber || '',
                eanNumber: this.formDetails.payer?.eanNumber || '',
                payerAddress: {
                    id: this.formDetails.payer?.address?.carId || '',
                    text: this.formDetails.payer?.address?.formattedAddress || ''
                }
            },
            sealBreachSealInformation: {
                meterPlacementId: this.formDetails.sealBreachSealInformation?.meterPlacementId || '',
                responsibleForSeal: this.formDetails.sealBreachSealInformation?.responsibleForSeal || '',
                reasonForChange: this.formDetails.sealBreachSealInformation?.reasonForChange || ''
            },
            relatedForms: {
                relatedForms: [...this.formDetails.relatedForms],
                hasRelatedFormsChanged: false
            }
        });

        if (this.formDetails.payer) {
            this.isPayerDataVisible = true;
        }

        if (this.formDetails.contactPerson) {
            this.isInstallationContactPersonVisible = true;
        }

        this.initDropDownOptions();
    }

    getFormComparisonModel(paymentDetailsOnly: boolean): any {
        return paymentDetailsOnly
            ? {
                  // Contact person
                  contactPersonCompanyName: this.contactPersonForm.get('contactPersonCompanyName')?.value,
                  contactPersonName: this.contactPersonForm.get('contactPersonName')?.value,
                  contactPersonEmail: this.contactPersonForm.get('contactPersonEmail')?.value,
                  contactPersonPhoneNumber: this.contactPersonForm.get('contactPersonPhoneNumber')?.value,

                  //  Payer
                  payerName: this.getPayerFieldForComparison(this.payerForm.get('payerName')?.value),
                  payerEmail: this.getPayerFieldForComparison(this.payerForm.get('payerEmail')?.value),
                  payerType: this.getPayerFieldForComparison(this.payerForm.get('payerType')?.value, null),
                  payerContactPersonName: this.getPayerFieldForComparison(this.payerForm.get('payerContactPersonName')?.value),
                  payerContactPersonEmail: this.getPayerFieldForComparison(this.payerForm.get('payerContactPersonEmail')?.value),
                  payerContactPersonPhoneNumber: this.getPayerFieldForComparison(
                      this.payerForm.get('payerContactPersonPhoneNumber')?.value
                  ),
                  requisition: this.getPayerFieldForComparison(this.payerForm.get('requisition')?.value),
                  cvrOrSeNumber: this.getPayerFieldForComparison(this.payerForm.get('cvrOrSeNumber')?.value),
                  eanNumber: this.getPayerFieldForComparison(this.payerForm.get('eanNumber')?.value),
                  payerAddress: [
                      this.getPayerFieldForComparison(this.payerForm.get('payerAddress')?.value?.id),
                      this.getPayerFieldForComparison(this.payerForm.get('payerAddress')?.value?.text)
                  ]
              }
            : {
                  installationAddress: [
                      this.installationInformationForm.get('installationAddress')?.value?.id,
                      this.installationInformationForm.get('installationAddress')?.value?.text
                  ],
                  connectionPoint: [
                      this.installationInformationForm.get('connectionPoint')?.value?.id || '',
                      this.installationInformationForm.get('connectionPoint')?.value?.connectionPointNumber || ''
                  ],
                  meterFrame: [
                      this.installationInformationForm.get('meterFrame')?.value?.id || '',
                      this.installationInformationForm.get('meterFrame')?.value?.meterFrameNumber || ''
                  ],
                  consumptionMeteringPoint: [
                      this.installationInformationForm.get('consumptionMeteringPoint')?.value?.meteringPointId || '',
                      this.installationInformationForm.get('consumptionMeteringPoint')?.value?.meteringPointVersionId || ''
                  ],
                  contactPersonCompanyName: this.isInstallationContactPersonVisible
                      ? this.contactPersonForm.get('contactPersonCompanyName')?.value
                      : null,
                  contactPersonName: this.isInstallationContactPersonVisible
                      ? this.contactPersonForm.get('contactPersonName')?.value
                      : null,
                  contactPersonEmail: this.isInstallationContactPersonVisible
                      ? this.contactPersonForm.get('contactPersonEmail')?.value
                      : null,
                  contactPersonPhoneNumber: this.isInstallationContactPersonVisible
                      ? this.contactPersonForm.get('contactPersonPhoneNumber')?.value
                      : null,
                  remarksToInstallation: this.installationInformationForm.get('remarksToInstallation')?.value,
                  tags: this.installationInformationForm.get('tags')?.value,

                  payerName: this.getPayerFieldForComparison(this.payerForm.get('payerName')?.value),
                  payerEmail: this.getPayerFieldForComparison(this.payerForm.get('payerEmail')?.value),
                  payerType: this.getPayerFieldForComparison(this.payerForm.get('payerType')?.value),
                  payerContactPersonName: this.getPayerFieldForComparison(this.payerForm.get('payerContactPersonName')?.value),
                  payerContactPersonEmail: this.getPayerFieldForComparison(this.payerForm.get('payerContactPersonEmail')?.value),
                  payerContactPersonPhoneNumber: this.getPayerFieldForComparison(
                      this.payerForm.get('payerContactPersonPhoneNumber')?.value
                  ),
                  requisition: this.getPayerFieldForComparison(this.payerForm.get('requisition')?.value),
                  cvrOrSeNumber: this.getPayerFieldForComparison(this.payerForm.get('cvrOrSeNumber')?.value),
                  eanNumber: this.getPayerFieldForComparison(this.payerForm.get('eanNumber')?.value),
                  payerAddress: [
                      this.getPayerFieldForComparison(this.payerForm.get('payerAddress')?.value?.id),
                      this.getPayerFieldForComparison(this.payerForm.get('payerAddress')?.value?.text)
                  ],

                  sealBreachSealInformationMeterPlacementId:
                      this.sealBreachSealInformationForm.get('meterPlacementId')?.value || null,
                  sealBreachSealInformationResponsibleForSeal:
                      this.sealBreachSealInformationForm.get('responsibleForSeal')?.value || null,
                  sealBreachSealInformationReasonForChange: this.sealBreachSealInformationForm.get('reasonForChange')?.value,

                  //related forms
                  hasRelatedFormsChanged: this.relatedFormsForm.get('hasRelatedFormsChanged')?.value || false
              };
    }

    convertToComparisonModel(model: SealBreach, paymentDetailsOnly: boolean): any {
        return paymentDetailsOnly
            ? {
                  // Contact person
                  contactPersonCompanyName: model.contactPerson?.companyName || '',
                  contactPersonName: model.contactPerson?.name || '',
                  contactPersonEmail: model.contactPerson?.email || '',
                  contactPersonPhoneNumber: model.contactPerson?.phoneNumber || '',

                  //  Payer
                  payerName: model.payer?.name || '',
                  payerType: model.payer?.type || '',
                  payerEmail: model.payer?.email || '',
                  payerContactPersonName: model.payer?.contactPerson?.name || '',
                  payerContactPersonEmail: model.payer?.contactPerson?.email || '',
                  payerContactPersonPhoneNumber: model.payer?.contactPerson?.phoneNumber || '',
                  requisition: model.payer?.requisition || '',
                  cvrOrSeNumber: model.payer?.cvrOrSeNumber || '',
                  eanNumber: model.payer?.eanNumber || '',
                  payerAddress: [model.payer?.address?.carId || '', model.payer?.address?.formattedAddress || '']
              }
            : {
                  installationAddress: [
                      model.installationAddress?.carId || '',
                      model.installationAddress?.formattedAddress || ''
                  ],
                  connectionPoint: [model.connectionPoint?.id || '', model.connectionPoint?.connectionPointNumber || ''],
                  meterFrame: [model.meterFrame?.id || '', model.meterFrame?.meterFrameNumber || ''],
                  consumptionMeteringPoint: [
                      model.consumptionMeteringPoint?.meteringPointId || '',
                      model.consumptionMeteringPoint?.meteringPointVersionId || ''
                  ],
                  contactPersonCompanyName: model.contactPerson?.companyName || '',
                  contactPersonName: model.contactPerson?.name || '',
                  contactPersonEmail: model.contactPerson?.email || '',
                  contactPersonPhoneNumber: model.contactPerson?.phoneNumber || '',
                  remarksToInstallation: model.remarksToInstallation || '',
                  tags: model.tags.map((t) => t.id),

                  payerName: model.payer?.name || '',
                  payerType: model.payer?.type || '',
                  payerEmail: model.payer?.email || '',
                  payerContactPersonName: model.payer?.contactPerson?.name || '',
                  payerContactPersonEmail: model.payer?.contactPerson?.email || '',
                  payerContactPersonPhoneNumber: model.payer?.contactPerson?.phoneNumber || '',
                  requisition: model.payer?.requisition || '',
                  cvrOrSeNumber: model.payer?.cvrOrSeNumber || '',
                  eanNumber: model.payer?.eanNumber || '',
                  payerAddress: [model.payer?.address?.carId || '', model.payer?.address?.formattedAddress || ''],

                  sealBreachSealInformationMeterPlacementId: model.sealBreachSealInformation?.meterPlacementId || null,
                  sealBreachSealInformationResponsibleForSeal: model.sealBreachSealInformation?.responsibleForSeal || null,
                  sealBreachSealInformationReasonForChange: model.sealBreachSealInformation?.reasonForChange || null,

                  // related forms
                  hasRelatedFormsChanged: false
              };
    }

    protected override updateFormData(
        installationFormId: string,
        es_message_id: string,
        row_version: string,
        body: SealBreachUpdate
    ): Observable<InstallationFormsApiResponse<void>> {
        return this.client.updateSealBreachFormData(installationFormId, es_message_id, row_version, body);
    }

    protected override createFormDataUpdate(): SealBreachUpdate {
        const update = new SealBreachUpdate({
            installationInformationUpdate: this.createInstallationInformationUpdate(),
            payerUpdate: this.isPayerDataVisible ? this.createPayerUpdate() : undefined,
            sealBreachSealInformationUpdate: this.createSealBreachSealInformationUpdate(),
            relatedFormsUpdate: this.relatedFormsForm
                .get('relatedForms')
                ?.value.filter((x: RelatedForm) => x.relationType === FormsRelationType.Manual)
                .map((x: RelatedForm) => new RelatedFormUpdate({ formId: x.formId }))
        });

        return update;
    }

    protected override createPaymentDetailsUpdate(): PaymentDetailsUpdate {
        const update = new PaymentDetailsUpdate({
            contactPerson: this.isInstallationContactPersonVisible ? this.createContactPersonUpdate() : undefined,
            payer: this.isPayerDataVisible ? this.createPayerUpdate() : undefined
        });
        return update;
    }

    private createContactPersonUpdate(): ContactPerson {
        const contactPersonFormValue = this.contactPersonForm.value;
        return new ContactPerson({
            companyName: getValueOrDefault(contactPersonFormValue.contactPersonCompanyName),
            name: getValueOrDefault(contactPersonFormValue.contactPersonName),
            email: getValueOrDefault(contactPersonFormValue.contactPersonEmail),
            phoneNumber: getValueOrDefault(contactPersonFormValue.contactPersonPhoneNumber)
        });
    }

    private createInstallationInformationUpdate(): InstallationInformationUpdate {
        const installationInformationFormValue = this.installationInformationForm.value;
        const contactPersonFormValue = this.contactPersonForm.value;
        return new InstallationInformationUpdate({
            contactPerson: this.isInstallationContactPersonVisible
                ? new ContactPerson({
                      companyName: getValueOrDefault(contactPersonFormValue.contactPersonCompanyName),
                      name: getValueOrDefault(contactPersonFormValue.contactPersonName),
                      email: getValueOrDefault(contactPersonFormValue.contactPersonEmail),
                      phoneNumber: getValueOrDefault(contactPersonFormValue.contactPersonPhoneNumber)
                  })
                : undefined,
            installationInformationCarId: getValueOrDefault(installationInformationFormValue.installationAddress?.id),
            remarksToInstallation: getValueOrDefault(installationInformationFormValue.remarksToInstallation),
            connectionPointId: getValueOrDefault(installationInformationFormValue.connectionPoint?.id),
            meterFrameId: getValueOrDefault(installationInformationFormValue.meterFrame?.id),
            consumptionMeteringPoint: createMeteringPoint(installationInformationFormValue.consumptionMeteringPoint),
            tags: installationInformationFormValue.tags || []
        });
    }

    private createPayerUpdate(): PayerUpdate {
        const payerFormValue = this.payerForm.value;
        return new PayerUpdate({
            contactPerson: new ContactPerson({
                email: getValueOrDefault(payerFormValue.payerContactPersonEmail),
                name: getValueOrDefault(payerFormValue.payerContactPersonName),
                phoneNumber: getValueOrDefault(payerFormValue.payerContactPersonPhoneNumber)
            }),
            payerType: payerFormValue.payerType,
            payerCarId: getValueOrDefault(payerFormValue.payerAddress?.id),
            name: getValueOrDefault(payerFormValue.payerName),
            email: getValueOrDefault(payerFormValue.payerEmail),
            requisition: getValueOrDefault(payerFormValue.requisition),
            cvrOrSeNumber: getValueOrDefault(payerFormValue.cvrOrSeNumber),
            eanNumber: getValueOrDefault(payerFormValue.eanNumber)
        });
    }

    private createSealBreachSealInformationUpdate(): SealBreachSealInformationUpdate {
        const sealBreachFormValue = this.sealBreachSealInformationForm.value;
        return new SealBreachSealInformationUpdate({
            meterPlacementId: sealBreachFormValue.meterPlacementId,
            responsibleForSeal: sealBreachFormValue.responsibleForSeal as ResponsibleForSeal,
            reasonForChange: sealBreachFormValue.reasonForChange as ReasonForChange
        });
    }
}
