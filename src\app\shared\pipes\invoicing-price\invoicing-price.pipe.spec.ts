import { TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { SupplyType } from 'src/app/api/installation-forms-client';
import { InvoicingPricesService } from 'src/app/core/services/invoicing-prices/invoicing-prices.service';
import { InvoicingPricePipe } from './invoicing-price.pipe';

describe('InvoicingPricePipe', () => {
    let pipe: InvoicingPricePipe;
    let invoicingPricesService: InvoicingPricesService;
    let getAllPricesSpy: jest.SpyInstance;

    // Using type assertion to simplify our mock
    const mockPrices = [
        { id: 'item1', displayName: 'Text One', externalPriceId: 1, supplyType: SupplyType.Electricity },
        { id: 'item2', displayName: 'Text Two', externalPriceId: 2, supplyType: SupplyType.Electricity },
        { id: 'item3', displayName: 'Text Three', externalPriceId: 3, supplyType: SupplyType.Heating }
    ];

    beforeEach(() => {
        const invoicingPricesServiceMock = {
            getAllPrices: jest.fn().mockReturnValue(of(mockPrices))
        };

        TestBed.configureTestingModule({
            providers: [InvoicingPricePipe, { provide: InvoicingPricesService, useValue: invoicingPricesServiceMock }]
        });

        pipe = TestBed.inject(InvoicingPricePipe);
        invoicingPricesService = TestBed.inject(InvoicingPricesService);
        getAllPricesSpy = jest.spyOn(invoicingPricesService, 'getAllPrices');
    });

    it('should create an instance', () => {
        expect(pipe).toBeTruthy();
    });

    it('should return empty string when id is undefined', (done) => {
        pipe.transform(undefined as any).subscribe((result) => {
            expect(result).toBe('');
            expect(invoicingPricesService.getAllPrices).not.toHaveBeenCalled();
            done();
        });
    });

    it('should return empty string when there are no invoicing prices', (done) => {
        getAllPricesSpy.mockReturnValueOnce(of([]));

        pipe.transform('item1').subscribe((result) => {
            expect(result).toBe('');
            expect(invoicingPricesService.getAllPrices).toHaveBeenCalledWith();
            done();
        });
    });

    it('should return value when valid id is found', (done) => {
        pipe.transform('item2').subscribe((result) => {
            expect(result).toBe('Text Two');
            done();
        });
    });
});
