import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { MessageService, ToastMessageOptions } from 'primeng/api';
import { ProblemDetails } from 'src/app/api/installation-forms-client';
import { TranslateServiceHelper } from '../translate/translate.service';

@Injectable({
    providedIn: 'root'
})
export class MessageServiceHelper {
    constructor(
        private readonly messageService: MessageService,
        private readonly translateService: TranslateService,
        private readonly translateServiceHelper: TranslateServiceHelper
    ) {}

    showSuccess(message: ToastMessageOptions) {
        this.messageService.add({
            ...message,
            severity: MessageServiceType.SUCCESS,
            summary: this.translateService.instant('common.successTitle')
        });
    }

    showWarning(message: ToastMessageOptions) {
        this.messageService.add({
            ...message,
            severity: MessageServiceType.WARNING,
            summary: this.translateService.instant('common.validationTitle')
        });
    }

    showError(message: ToastMessageOptions) {
        this.messageService.add({
            ...message,
            severity: MessageServiceType.ERROR,
            summary: this.translateService.instant('common.errorTitle')
        });
    }

    addTranslated(
        error: any,
        formId: string,
        widgetTranslationKey: string,
        ...substitutionsTranslationPath: { key: string; path: string }[]
    ) {
        let problemDetails: ProblemDetails;
        try {
            problemDetails = JSON.parse(error.response);
        } catch (e) {
            this.showError({
                summary: this.translateService.instant('common.errorTitle'),
                detail: `Error occurred while trying to display a message: ${e instanceof Error ? e.message : String(e)}`
            });
            return;
        }

        const substitutions = this.translateSubstitutions(problemDetails.substitutions || {}, substitutionsTranslationPath);
        const severity = MessageServiceType.parseType(problemDetails.severity);

        this.messageService.add({
            severity: severity,
            summary: this.translateService.instant(
                severity === MessageServiceType.WARNING ? 'common.validationTitle' : 'common.errorTitle'
            ),
            detail:
                this.translateServiceHelper.getTranslationOrNull(
                    widgetTranslationKey + '.' + problemDetails.resultCode,
                    substitutions
                ) ??
                this.translateServiceHelper.getTranslationOrNull('common.' + problemDetails.resultCode, substitutions) ??
                problemDetails.detail ??
                problemDetails.title,
            key: formId
        });
    }

    private translateSubstitutions(
        substitutions: { [key: string]: string },
        substitutionsTranslationPath: { key: string; path: string }[]
    ) {
        substitutionsTranslationPath?.forEach((substitution) => {
            const translationKey = substitution.path + '.' + substitutions[substitution.key];

            if (substitution.key in substitutions && this.translateServiceHelper.hasTranslation(translationKey)) {
                substitutions[substitution.key] = this.translateService.instant(translationKey);
            }
        });

        return substitutions;
    }
}

export class MessageServiceType {
    static readonly INFO = 'info';
    static readonly ERROR = 'error';
    static readonly WARNING = 'warn';
    static readonly SUCCESS = 'success';

    static parseType(type: string | undefined): string {
        switch (type) {
            case 'Info':
                return MessageServiceType.INFO;
            case 'Warning':
                return MessageServiceType.WARNING;
            default:
                return MessageServiceType.ERROR;
        }
    }
}
