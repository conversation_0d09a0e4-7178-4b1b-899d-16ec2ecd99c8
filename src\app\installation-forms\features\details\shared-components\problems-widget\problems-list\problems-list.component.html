<ul>
    <li *ngFor="let problem of problems; let i = index">
        <div *ngIf="type !== 'acknowledged'" class="flex pb-2 align-items-center">
            <h2 [class]="colorClass">
                <i *ngIf="icon" [class]="icon"></i>
                &nbsp;{{ problem.translatedProblem }}
            </h2>
            <button
                *ngIf="formDataService.canCurrentUserEditData"
                pButton
                icon="pi pi-times"
                [disabled]="isProcessing"
                [pTooltip]="'problemsWidget.markAsAcknowledged' | translate"
                (click)="onMarkAsAcknowledged(problem.id)"
                class="p-button-outlined border-none p-0"></button>
        </div>

        <div *ngIf="type === 'acknowledged'" class="pb-2 align-items-center">
            <span class="pb-2 color-acknowledged">
                <i class="pi pi-check-circle"></i>
                &nbsp;{{ problem.acknowledgedBy }} | {{ problem.acknowledgedTimestamp | formatDateTime }}
            </span>
            <h2 class="pt-2 pl-4">{{ problem.translatedProblem }}</h2>
        </div>
    </li>
</ul>
