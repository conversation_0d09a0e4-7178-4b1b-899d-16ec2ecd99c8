import { Injectable } from '@angular/core';
import { ConfigurationService, TenantConfiguration } from '@kmd-elements-ui/configuration';

@Injectable({
    providedIn: 'root'
})
export class DateTimeService {
    // This locale is uses desired format yyyy-mm-dd hh:mm:ss
    private readonly locale: string = 'sv-SE';
    private tenantConfig!: TenantConfiguration;

    constructor(private readonly configurationService: ConfigurationService) {
        this.configurationService.tenantConfiguration$.subscribe(
            (tenantConfig: TenantConfiguration) => (this.tenantConfig = tenantConfig)
        );
    }

    transformDateTime(date: Date | undefined): string {
        if (!date?.getDate()) {
            return '';
        }

        return date.toLocaleString(this.locale, {
            timeZone: this.tenantConfig.ianaTimezoneId,
            timeStyle: 'short',
            dateStyle: 'short'
        });
    }

    transformDate(date: Date | undefined): string {
        if (!date?.getDate()) {
            return '';
        }

        return date.toLocaleString(this.locale, {
            timeZone: this.tenantConfig.ianaTimezoneId,
            timeStyle: undefined,
            dateStyle: 'short'
        });
    }
}
