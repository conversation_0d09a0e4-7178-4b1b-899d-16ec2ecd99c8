import { Component, Input } from '@angular/core';
import { AbstractControl, FormBuilder, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { finalize } from 'rxjs/operators';
import {
    AutomaticMasterDataProcessRule,
    AutomaticMasterDataProcessRuleCreateOrUpdate,
    AutomationVoltageLevel,
    ConnectionPointTemplateDetails,
    FormState,
    FormType,
    InstallationFormsClient,
    MasterDataProcessAutomationLevel,
    MasterDataProcessWorkOrderAutomationLevel,
    ScopeOfDeliveryRange,
    TerminationScope,
    TotalCapacityRange,
    TransferConnectionRightUom,
    ValueListType
} from 'src/app/api/installation-forms-client';
import { SETTINGS_DISPLAY_NAME_MAX_LENGTH } from 'src/app/core/constants/constants';
import { BooleanOptionsService } from 'src/app/core/services/boolean-options/boolean-options.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { ValueListsService } from 'src/app/core/services/value-lists/value-lists.service';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import { getAvailableMasterDataProcessTypes } from 'src/app/core/utils/master-data-processes-per-form-types';
import conditionallyRequiredValidator from 'src/app/core/utils/validators/conditionally-required/conditionally-required.validator';
import { v4 as uuidv4 } from 'uuid';
import { BaseRuleEditComponent } from '../../base-components/base-rule-edit.component';
import { masterDataTabName, masterDataTranslationPath } from '../constants/master-data.consts';
import { findMatchingWorkOrderTypes } from '../constants/properties-per-for-type';
import { MasterDataOptionsService } from '../services/master-data-options.service';

@Component({
    selector: 'app-master-data-edit',
    templateUrl: './master-data-edit.component.html',
    styleUrls: ['./master-data-edit.component.scss'],
    standalone: false
})
export class MasterDataEditComponent extends BaseRuleEditComponent<AutomaticMasterDataProcessRule> {
    @Input() masterDataProcessTemplateOptions: ConnectionPointTemplateDetails[] = [];

    formTypeOptions: SelectItem[] = [];
    formStateOptions: SelectItem[] = [];
    voltageLevelOptions: SelectItem[] = [];
    masterDataProcessTypeOptions: SelectItem[] = [];
    masterDataProcessAutomationLevelOptions: SelectItem[] = [];
    masterDataProcessWorkOrderAutomationLevelOptions: SelectItem[] = [];
    terminationScopeOptions: SelectItem[] = [];
    scopeOfDeliveryUomOptions: SelectItem[] = [];
    workOrderTypeOptions: SelectItem[] = [];

    constructor(
        protected readonly fb: FormBuilder,
        protected readonly translateService: TranslateService,
        protected readonly client: InstallationFormsClient,
        protected readonly messageServiceHelper: MessageServiceHelper,
        protected readonly booleanOptionsService: BooleanOptionsService,
        protected readonly masterDataOptionsService: MasterDataOptionsService,
        protected readonly valueListsService: ValueListsService
    ) {
        super(fb, translateService, messageServiceHelper, masterDataTabName, masterDataTranslationPath);
        this.initForm();
        this.addFormControlsValueChanges();
    }

    override initForm(): void {
        this.form = this.fb.group({
            id: [null, Validators.required],
            displayName: [null, [Validators.required, Validators.maxLength(SETTINGS_DISPLAY_NAME_MAX_LENGTH)]],
            formTypes: [null, Validators.required],
            formStates: [null, Validators.required],
            voltageLevels: [null, Validators.required],
            totalCapacityMin: [null, [Validators.min(0), Validators.max(2147483647)]],
            totalCapacityMax: [null, [Validators.min(0), Validators.max(2147483647)]],
            scopeOfDeliveryMin: [null, [Validators.min(0), Validators.max(2147483647)]],
            scopeOfDeliveryMax: [null, [Validators.min(0), Validators.max(2147483647)]],
            scopeOfDeliveryUom: [
                null,
                [(control: AbstractControl) => conditionallyRequiredValidator(control, this.isScopeOfDeliveryUomRequired)]
            ],
            startsAsConstruction: [false],
            terminationScope: [null],
            masterDataProcessType: [null, Validators.required],
            masterDataProcessTemplateId: [
                null,
                [(control: AbstractControl) => conditionallyRequiredValidator(control, this.isProcessTemplateRequired)]
            ],
            masterDataProcessAutomationLevel: [null, Validators.required],
            masterDataProcessWorkOrderAutomationLevel: [null],
            workOrderType: [
                null,
                [(control: AbstractControl) => conditionallyRequiredValidator(control, this.isAnyWorkOrderTypeAvailable)]
            ],
            workOrderDescriptionId: [
                null,
                [(control: AbstractControl) => conditionallyRequiredValidator(control, this.isAnyWorkOrderTypeAvailable)]
            ],
            workOrderPurposeId: [
                null,
                [(control: AbstractControl) => conditionallyRequiredValidator(control, this.isAnyWorkOrderTypeAvailable)]
            ]
        });
    }

    override getEnumTranslations() {
        this.formTypeOptions = enumMapper.map(this.translateService.instant('enums.type'), FormType);
        this.formStateOptions = enumMapper.mapArray(this.translateService.instant('enums.state'), [
            FormState.Registered,
            FormState.Instructed,
            FormState.Completed
        ]);
        this.voltageLevelOptions = enumMapper.map(
            this.translateService.instant('enums.automationVoltageLevel'),
            AutomationVoltageLevel
        );
        this.masterDataProcessAutomationLevelOptions = enumMapper.map(
            this.translateService.instant('enums.masterDataProcessAutomationLevel'),
            MasterDataProcessAutomationLevel
        );
        this.masterDataProcessWorkOrderAutomationLevelOptions = enumMapper.map(
            this.translateService.instant('enums.masterDataProcessWorkOrderAutomationLevel'),
            MasterDataProcessWorkOrderAutomationLevel
        );
        this.terminationScopeOptions = enumMapper.map(this.translateService.instant('enums.terminationScope'), TerminationScope);
        this.scopeOfDeliveryUomOptions = enumMapper.map(
            this.translateService.instant('enums.transferConnectionRightUom'),
            TransferConnectionRightUom
        );
    }

    override setFormValue(rule: AutomaticMasterDataProcessRule) {
        this.form.patchValue({
            id: rule.id,
            displayName: rule.displayName,
            formTypes: rule.formTypes,
            formStates: rule.formStates,
            voltageLevels: rule.voltageLevels,
            totalCapacityMin: rule.totalCapacityRange?.min ?? null,
            totalCapacityMax: rule.totalCapacityRange?.max ?? null,
            scopeOfDeliveryMin: rule.scopeOfDeliveryRange?.min ?? null,
            scopeOfDeliveryMax: rule.scopeOfDeliveryRange?.max ?? null,
            scopeOfDeliveryUom: rule.scopeOfDeliveryRange?.uom,
            startsAsConstruction: rule.startsAsConstruction,
            terminationScope: rule.terminationScope,
            masterDataProcessType: rule.masterDataProcessType,
            masterDataProcessTemplateId: rule.masterDataProcessTemplateId,
            masterDataProcessAutomationLevel: rule.masterDataProcessAutomationLevel,
            masterDataProcessWorkOrderAutomationLevel: rule.masterDataProcessWorkOrderAutomationLevel,
            workOrderType: rule.workOrderType,
            workOrderDescriptionId: rule.workOrderDescriptionId,
            workOrderPurposeId: rule.workOrderPurposeId
        });
    }

    override saveEditedClick() {
        if (this.form.invalid) {
            return;
        }

        const formValue = this.form.getRawValue();

        let hasValidationErrors = false;

        if (
            formValue.totalCapacityMin &&
            formValue.totalCapacityMax &&
            formValue.totalCapacityMin >= formValue.totalCapacityMax
        ) {
            this.showWarningMessage(this.widgetTranslations['totalCapacityMinGreaterThanMaxWarning']);
            hasValidationErrors = true;
        }

        if (formValue.scopeOfDeliveryUom && formValue.scopeOfDeliveryMin === null && formValue.scopeOfDeliveryMax === null) {
            this.showWarningMessage(this.widgetTranslations['scopeOfDeliveryMinMaxEmptyWarning']);
            hasValidationErrors = true;
        }

        if (
            formValue.scopeOfDeliveryUom &&
            formValue.scopeOfDeliveryMin !== null &&
            formValue.scopeOfDeliveryMax !== null &&
            formValue.scopeOfDeliveryMin >= formValue.scopeOfDeliveryMax
        ) {
            this.showWarningMessage(this.widgetTranslations['scopeOfDeliveryMinGreaterThanMaxWarning']);
            hasValidationErrors = true;
        }

        if (hasValidationErrors) {
            return;
        }

        const rule: AutomaticMasterDataProcessRuleCreateOrUpdate = new AutomaticMasterDataProcessRuleCreateOrUpdate({
            displayName: formValue.displayName,
            formTypes: formValue.formTypes,
            formStates: formValue.formStates,
            scopeOfDeliveryRange: formValue.scopeOfDeliveryUom
                ? new ScopeOfDeliveryRange({
                      min: formValue.scopeOfDeliveryMin,
                      max: formValue.scopeOfDeliveryMax,
                      uom: formValue.scopeOfDeliveryUom
                  })
                : undefined,
            voltageLevels: formValue.voltageLevels ?? [],
            totalCapacityRange:
                formValue.totalCapacityMin || formValue.totalCapacityMax
                    ? new TotalCapacityRange({
                          min: formValue.totalCapacityMin,
                          max: formValue.totalCapacityMax
                      })
                    : undefined,
            startsAsConstruction: formValue.startsAsConstruction ?? null,
            terminationScope: formValue.terminationScope ?? null,
            masterDataProcessType: formValue.masterDataProcessType,
            masterDataProcessTemplateId: formValue.masterDataProcessTemplateId ?? null,
            masterDataProcessAutomationLevel: formValue.masterDataProcessAutomationLevel,
            masterDataProcessWorkOrderAutomationLevel: formValue.masterDataProcessWorkOrderAutomationLevel ?? null,
            workOrderType: formValue.workOrderType ?? null,
            workOrderDescriptionId: formValue.workOrderDescriptionId ?? null,
            workOrderPurposeId: formValue.workOrderPurposeId ?? null
        });

        this.isProcessing = true;
        this.subscription.add(
            this.client
                .updateAutomaticMasterDataRuleById(formValue.id, uuidv4(), rule)
                .pipe(finalize(() => (this.isProcessing = false)))
                .subscribe({
                    next: (response) => {
                        this.ruleEdited.emit(response.result);
                        this.showSuccessMessage();
                    },
                    error: (_) => {
                        this.showErrorMessage(this.widgetTranslations['masterDataRuleEditError']);
                    }
                })
        );
    }

    addFormControlsValueChanges() {
        this.subscription.add(
            this.form.get('masterDataProcessType')?.valueChanges.subscribe((_) => {
                const controlToReValidate = this.form.get('masterDataProcessTemplateId');
                if (controlToReValidate) {
                    controlToReValidate.updateValueAndValidity();
                }
            })
        );

        this.subscription.add(
            this.form.get('scopeOfDeliveryMin')?.valueChanges.subscribe((_) => {
                const uomControl = this.form.get('scopeOfDeliveryUom');
                if (uomControl) {
                    uomControl.updateValueAndValidity();
                }
            })
        );

        this.subscription.add(
            this.form.get('scopeOfDeliveryMax')?.valueChanges.subscribe((_) => {
                const uomControl = this.form.get('scopeOfDeliveryUom');
                if (uomControl) {
                    uomControl.updateValueAndValidity();
                }
            })
        );

        this.subscription.add(
            this.form.get('formTypes')?.valueChanges.subscribe(() => {
                this.updateTerminationScopeValidation();
                this.updateVoltageLevelsValidation();
                this.updateTotalCapacityValidation();
                this.updateScopeOfDeliveryValidation();
                this.updateStartsAsConstructionValidation();
                this.updateWorkOrderAutomationLevelValidation();
                this.updateMasterDataProcessTypesOptions();
                this.setWorkOrderTypeOptions();
            })
        );

        this.subscription.add(
            this.form.get('masterDataProcessWorkOrderAutomationLevel')?.valueChanges.subscribe(() => {
                this.setWorkOrderTypeOptions();
            })
        );

        this.subscription.add(
            this.form.get('terminationScope')?.valueChanges.subscribe(() => {
                this.updateMasterDataProcessTypesOptions();
                if (!this.masterDataProcessTypeOptions.some((x) => x.value === this.form.get('masterDataProcessType')?.value)) {
                    this.form.get('masterDataProcessType')?.setValue(null);
                }
            })
        );
    }

    updateMasterDataProcessTypesOptions = () => {
        const formTypes = this.form?.get('formTypes')?.value || [];
        this.masterDataProcessTypeOptions = enumMapper.mapArray(
            this.translateService.instant('enums.masterDataProcessType'),
            getAvailableMasterDataProcessTypes(formTypes, this.form.get('terminationScope')?.value)
        );
        if (!this.masterDataProcessTypeOptions.includes(this.form?.get('masterDataProcessType')?.value)) {
            this.form?.get('masterDataProcessType')?.setValue(null);
        }
    };

    isProcessTemplateRequired = () => {
        const value = this.form?.get('masterDataProcessType')?.value;
        return this.masterDataOptionsService.isProcessTemplateRequired(value);
    };

    isScopeOfDeliveryUomRequired = () => {
        const minValue = this.form?.get('scopeOfDeliveryMin')?.value;
        const maxValue = this.form?.get('scopeOfDeliveryMax')?.value;
        return minValue !== null || maxValue !== null;
    };

    isNewInstallationSelected(): boolean {
        const selectedTypes = this.form?.get('formTypes')?.value || [];
        return selectedTypes.includes(FormType.NewInstallation);
    }

    isTerminationFormSelected(): boolean {
        const selectedTypes = this.form?.get('formTypes')?.value || [];
        return selectedTypes.includes(FormType.Termination);
    }

    isOnlyTerminationFormSelected(): boolean {
        const selectedTypes = this.form?.get('formTypes')?.value || [];
        return selectedTypes.length === 1 && selectedTypes.includes(FormType.Termination);
    }

    isNewInstallationOrExtensionSelected(): boolean {
        const selectedTypes = this.form?.get('formTypes')?.value || [];
        return selectedTypes.some((type: FormType) => type === FormType.NewInstallation || type === FormType.Extension);
    }

    isEnergyProductionFormSelected(): boolean {
        const selectedTypes = this.form?.get('formTypes')?.value || [];
        return selectedTypes.includes(FormType.EnergyProduction);
    }

    private updateTerminationScopeValidation() {
        const terminationScope = this.form.get('terminationScope');
        if (this.isTerminationFormSelected()) {
            terminationScope?.setValidators(Validators.required);
        } else {
            terminationScope?.clearValidators();
            terminationScope?.setValue(null);
        }
        terminationScope?.updateValueAndValidity();
    }

    private updateVoltageLevelsValidation() {
        const voltageLevels = this.form.get('voltageLevels');
        if (this.isNewInstallationOrExtensionSelected()) {
            voltageLevels?.setValidators(Validators.required);
        } else {
            voltageLevels?.clearValidators();
            voltageLevels?.setValue(null);
        }
        voltageLevels?.updateValueAndValidity();
    }

    private updateTotalCapacityValidation() {
        const totalCapacityMin = this.form.get('totalCapacityMin');
        const totalCapacityMax = this.form.get('totalCapacityMax');

        if (!this.isEnergyProductionFormSelected()) {
            totalCapacityMin?.setValue(null);
            totalCapacityMax?.setValue(null);
        }
    }

    private updateScopeOfDeliveryValidation() {
        const scopeOfDeliveryMin = this.form.get('scopeOfDeliveryMin');
        const scopeOfDeliveryMax = this.form.get('scopeOfDeliveryMax');
        const scopeOfDeliveryUom = this.form.get('scopeOfDeliveryUom');

        if (!this.isNewInstallationOrExtensionSelected()) {
            scopeOfDeliveryMin?.setValue(null);
            scopeOfDeliveryMax?.setValue(null);
            scopeOfDeliveryUom?.setValue(null);
        }
    }

    private updateStartsAsConstructionValidation() {
        const startsAsConstruction = this.form.get('startsAsConstruction');
        if (!this.isNewInstallationSelected()) {
            startsAsConstruction?.setValue(null);
        }
    }

    private updateWorkOrderAutomationLevelValidation() {
        const workOrderAutomationLevel = this.form.get('masterDataProcessWorkOrderAutomationLevel');
        if (this.isOnlyTerminationFormSelected()) {
            workOrderAutomationLevel?.enable();
            workOrderAutomationLevel?.setValidators(Validators.required);
        } else {
            workOrderAutomationLevel?.clearValidators();
            workOrderAutomationLevel?.setValue(null);
        }

        const selectedFormTypes = this.form?.get('formTypes')?.value;

        if (selectedFormTypes?.length > 1 && selectedFormTypes.includes(FormType.Termination)) {
            workOrderAutomationLevel?.clearValidators();
            workOrderAutomationLevel?.setValue(MasterDataProcessWorkOrderAutomationLevel.Manual);
            workOrderAutomationLevel?.disable();
        }

        workOrderAutomationLevel?.updateValueAndValidity();
    }

    isWorkOrderTypeSelected = (): boolean => {
        return this.form?.get('workOrderType')?.value !== null;
    };

    getWorkOrderDescriptionValueListType = (): ValueListType => {
        return this.masterDataOptionsService.getWorkOrderDescriptionValueListType(this.form?.get('workOrderType')?.value);
    };

    setWorkOrderTypeOptions(): void {
        const automationLevel = this.form?.get('masterDataProcessWorkOrderAutomationLevel')?.value;
        if (!automationLevel || automationLevel === MasterDataProcessWorkOrderAutomationLevel.Manual) {
            this.workOrderTypeOptions = [];
            this.form.get('workOrderType')?.setValue(null);
            this.form.get('workOrderDescriptionId')?.setValue(null);
            this.form.get('workOrderPurposeId')?.setValue(null);
            return;
        }

        const selectedFormTypes = this.form?.get('formTypes')?.value;
        const workOrderTypes = findMatchingWorkOrderTypes(selectedFormTypes);
        if (workOrderTypes) {
            this.workOrderTypeOptions = enumMapper.map(this.translateService.instant('enums.workOrderType'), workOrderTypes);
            if (this.workOrderTypeOptions.length === 1) {
                this.form.get('workOrderType')?.setValue(this.workOrderTypeOptions[0].value);
                this.form.get('workOrderType')?.disable();
            }
            if (this.workOrderTypeOptions.length > 1) {
                this.form.get('workOrderType')?.enable();
            }
        } else {
            this.workOrderTypeOptions = [];
            this.form.get('workOrderType')?.setValue(null);
            this.form.get('workOrderDescriptionId')?.setValue(null);
            this.form.get('workOrderPurposeId')?.setValue(null);
        }
    }

    isAnyWorkOrderTypeAvailable = () => {
        return this.workOrderTypeOptions.length > 0;
    };
}
