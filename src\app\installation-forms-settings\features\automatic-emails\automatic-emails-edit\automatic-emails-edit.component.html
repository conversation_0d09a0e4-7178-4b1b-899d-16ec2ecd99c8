<div #overlayContainer></div>
<div fxLayout="column" class="mt-10 zebra-container" [formGroup]="form">
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['displayName']" for="displayName">
                {{ 'formsSettings.automaticEmails.displayName' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input id="displayName" type="text" pInputText maxlength="100" formControlName="displayName" autoTrimWhitespaces />
            <small [controlValidationErrors]="form.controls['displayName']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['formTypes']" for="formTypes">
                {{ 'formsSettings.automaticEmails.formTypes' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-multiSelect
                id="formTypes"
                [options]="formTypeOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="formTypes"
                [maxSelectedLabels]="formTypeOptions.length">
            </p-multiSelect>
            <small [controlValidationErrors]="form.controls['formTypes']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['formStates']" for="formStates">
                {{ 'formsSettings.automaticEmails.formStates' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-multiSelect
                id="formStates"
                [options]="formStateOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="formStates"
                [maxSelectedLabels]="formStateOptions.length">
            </p-multiSelect>
            <small [controlValidationErrors]="form.controls['formStates']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isFormCategoryRelevant()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['formCategories']" for="formCategories">
                {{ 'formsSettings.automaticEmails.formCategories' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-multiSelect
                id="formCategories"
                [options]="formCategoriesOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="formCategories"
                [maxSelectedLabels]="formCategoriesOptions.length">
            </p-multiSelect>
            <small [controlValidationErrors]="form.controls['formCategories']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['emailTemplateId']" for="emailTemplateId">
                {{ 'formsSettings.automaticEmails.emailTemplateName' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="emailTemplateId"
                [options]="emailTemplateOptions"
                optionValue="id"
                optionLabel="name"
                [showClear]="true"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="emailTemplateId">
            </p-select>
            <small [controlValidationErrors]="form.controls['emailTemplateId']" class="p-error"></small>
        </div>
    </div>

    <div class="mb-2 mt-2 flex justify-content-end">
        <button
            id="cancelButton"
            [disabled]="isProcessing"
            type="button"
            pButton
            pRipple
            class="mr-2 p-button-secondary"
            (click)="cancelEditClick()">
            {{ 'common.cancel' | translate }}
        </button>
        <button
            id="saveButton"
            type="button"
            pButton
            pRipple
            (click)="saveEditedClick()"
            [disabled]="form.invalid || isProcessing">
            {{ 'common.saveChanges' | translate }}
        </button>
    </div>
</div>
