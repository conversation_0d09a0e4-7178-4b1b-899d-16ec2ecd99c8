import { enumMapper } from './enum-to-select-items.mapper';

describe('EnumToSelectItemsMapper', () => {
    // Sample enum for testing
    enum TestEnum {
        OPTION_ONE = 'OPTION_ONE',
        OPTION_TWO = 'OPTION_TWO',
        OPTION_THREE = 'OPTION_THREE'
    }

    // Translation object for testing
    const translations = {
        OPTION_ONE: 'First Option',
        OPTION_TWO: 'Second Option'
        // Deliberately missing OPTION_THREE to test fallback
    };

    describe('map', () => {
        it('should map enum values to select items with translations', () => {
            const result = enumMapper.map(translations, TestEnum);

            expect(result).toEqual([
                { value: 'OPTION_ONE', label: 'First Option' },
                { value: 'OPTION_TWO', label: 'Second Option' },
                { value: 'OPTION_THREE', label: 'OPTION_THREE' } // Uses value as fallback
            ]);
        });

        it('should handle empty enum', () => {
            enum EmptyEnum {}

            const result = enumMapper.map(translations, EmptyEnum);

            expect(result).toEqual([]);
        });

        it('should use enum value as label when no translation is provided', () => {
            const result = enumMapper.map({}, TestEnum);

            expect(result).toEqual([
                { value: 'OPTION_ONE', label: 'OPTION_ONE' },
                { value: 'OPTION_TWO', label: 'OPTION_TWO' },
                { value: 'OPTION_THREE', label: 'OPTION_THREE' }
            ]);
        });
    });

    describe('mapArray', () => {
        it('should map array of values to select items with translations', () => {
            const items = ['OPTION_ONE', 'OPTION_TWO', 'OPTION_THREE'];

            const result = enumMapper.mapArray(translations, items);

            expect(result).toEqual([
                { value: 'OPTION_ONE', label: 'First Option' },
                { value: 'OPTION_TWO', label: 'Second Option' },
                { value: 'OPTION_THREE', label: 'OPTION_THREE' } // Uses value as fallback
            ]);
        });

        it('should handle empty array', () => {
            const result = enumMapper.mapArray(translations, []);

            expect(result).toEqual([]);
        });

        it('should use item value as label when no translation is provided', () => {
            const items = ['ITEM_ONE', 'ITEM_TWO'];

            const result = enumMapper.mapArray({}, items);

            expect(result).toEqual([
                { value: 'ITEM_ONE', label: 'ITEM_ONE' },
                { value: 'ITEM_TWO', label: 'ITEM_TWO' }
            ]);
        });

        it('should work with non-string array items', () => {
            const items = [1, 2, 3];

            const translations = {
                '1': 'One',
                '2': 'Two',
                '3': 'Three'
            };

            const result = enumMapper.mapArray(translations, items);

            expect(result).toEqual([
                { value: 1, label: 'One' },
                { value: 2, label: 'Two' },
                { value: 3, label: 'Three' }
            ]);
        });

        it('should handle mixed array items', () => {
            const items = ['STRING', 123, true];

            const translations = {
                STRING: 'String Value',
                '123': 'Number Value',
                true: 'Boolean Value'
            };

            const result = enumMapper.mapArray(translations, items);

            expect(result).toEqual([
                { value: 'STRING', label: 'String Value' },
                { value: 123, label: 'Number Value' },
                { value: true, label: 'Boolean Value' }
            ]);
        });
    });
});
