import { FormControl } from '@angular/forms';
import { multipleWhitespaceValidator } from './multiple-whitespace-validator';

describe('multipleWhitespaceValidator', () => {
    it('should return null when display name has single spaces between words', () => {
        const validator = multipleWhitespaceValidator();
        
        const control = new FormControl('This is a valid name');
        const result = validator(control);
        
        expect(result).toBeNull();
    });

    it('should return error when display name has multiple spaces between words', () => {
        const validator = multipleWhitespaceValidator();
        
        const control = new FormControl('This  is  a name with  multiple spaces');
        const result = validator(control);
        
        expect(result).toEqual({ multipleWhitespaces: true });
    });

    it('should return null when control value is null or empty', () => {
        const validator = multipleWhitespaceValidator();
        
        const nullControl = new FormControl(null);
        const emptyControl = new FormControl('');
        
        expect(validator(nullControl)).toBeNull();
        expect(validator(emptyControl)).toBeNull();
    });

    it('should return error when display name has tabs or multiple newlines', () => {
        const validator = multipleWhitespaceValidator();
        
        const tabControl = new FormControl('This\t\thas tabs');
        const newlineControl = new FormControl('This has\n\nmultiple newlines');
        
        expect(validator(tabControl)).toEqual({ multipleWhitespaces: true });
        expect(validator(newlineControl)).toEqual({ multipleWhitespaces: true });
    });
});