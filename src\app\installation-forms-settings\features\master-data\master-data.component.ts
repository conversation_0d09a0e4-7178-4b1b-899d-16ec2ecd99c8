import { Component, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AuthorizationService } from '@kmd-elements-ui/authorization';
import {
    KeyActions,
    KeyboardShortcuts,
    KeyboardShortcutsService,
    KeyboardShortcutsSubscriptionManager
} from '@kmd-elements-ui/shortcuts';
import { TabsService } from '@kmd-elements-ui/tabs';
import { TranslateService } from '@ngx-translate/core';
import { TableRowReorderEvent } from 'primeng/table';
import { BehaviorSubject, catchError, EMPTY, filter, finalize, forkJoin, of, Subscription, switchMap, tap } from 'rxjs';
import {
    AutomaticMasterDataProcessRule,
    ConnectionPointTemplateDetails,
    InstallationFormsClient,
    ReorderModel,
    SupplyTypes,
    ValueListType,
    WorkOrderType
} from 'src/app/api/installation-forms-client';
import { Permissions } from 'src/app/core/constants/permissions';
import { v4 as uuidv4 } from 'uuid';
import { MessageServiceHelper } from '../../../core/services/message/message.service';
import { ValueListsService } from '../../../core/services/value-lists/value-lists.service';
import { RulesListColumn } from '../models/rules-list-column';
import { MASTER_DATA_COLUMNS } from './constants/columns';
import {
    masterDataMarkdownDaDK,
    masterDataMarkdownEnUs,
    masterDataTabName,
    masterDataTranslationPath
} from './constants/master-data.consts';
import { MasterDataCreateComponent } from './master-data-create/master-data-create.component';
import { MasterDataEditComponent } from './master-data-edit/master-data-edit.component';
import { MasterDataListItem } from './models/master-data-list-item';
import { MasterDataMapper } from './services/master-data.mapper';

@Component({
    selector: 'app-master-data',
    templateUrl: './master-data.component.html',
    styleUrls: ['./master-data.component.scss'],
    standalone: false
})
export class MasterDataComponent implements KeyboardShortcutsSubscriptionManager, OnInit, OnDestroy {
    private commonTranslations: any;
    private widgetTranslations: any;

    private _isCreating: boolean = false;
    private _ruleInEdit?: AutomaticMasterDataProcessRule;
    private _isReordered: boolean = false;

    get isCreating(): boolean {
        return this._isCreating;
    }

    set isCreating(value: boolean) {
        this._isCreating = value;
        this.recalculateDirty();
    }

    get ruleInEdit(): AutomaticMasterDataProcessRule | undefined {
        return this._ruleInEdit;
    }

    set ruleInEdit(value: AutomaticMasterDataProcessRule | undefined) {
        this._ruleInEdit = value;
        this.recalculateDirty();
    }

    get isReordered(): boolean {
        return this._isReordered;
    }

    set isReordered(value: boolean) {
        this._isReordered = value;
        this.recalculateDirty();
    }

    masterDataRules: MasterDataListItem[] = [];
    rawMasterDataRules: AutomaticMasterDataProcessRule[] = [];

    subscription: Subscription = new Subscription();

    masterDataMarkdownEnUs = masterDataMarkdownEnUs;
    masterDataMarkdownDaDK = masterDataMarkdownDaDK;

    isProcessing: boolean = false;

    dirty$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

    masterDataTabName = masterDataTabName;

    hasUserFormConfigurationWritePermission: boolean = false;

    private _tabId?: string;

    columns: RulesListColumn[] = MASTER_DATA_COLUMNS;

    public masterDataProcessTemplateOptions: ConnectionPointTemplateDetails[] = [];

    private keyEventsSubscription: Subscription = new Subscription();

    @ViewChild('masterDataCreate') masterDataCreate?: MasterDataCreateComponent;
    @ViewChild('masterDataEdit') masterDataEdit?: MasterDataEditComponent;

    keyActions: KeyActions = {
        [KeyboardShortcuts.ShiftPlusN]: () => {
            this.startCreating();
        },
        [KeyboardShortcuts.Escape]: () => {
            this.tabsService.close();
        },
        [KeyboardShortcuts.Enter]: () => {
            if (this.ruleInEdit) {
                this.masterDataEdit?.saveEditedClick();
                return;
            }

            if (this.isCreating) {
                this.masterDataCreate?.createClick();
                return;
            }

            if (this.isReordered) {
                this.saveReorder();
                return;
            }
        }
    };
    attemptedAction: (() => void) | null = null;
    private ruleToDelete?: MasterDataListItem;

    private originalRuleOrder: string[] = [];
    public alreadyUsedDisplayNames: string[] = [];

    constructor(
        private readonly client: InstallationFormsClient,
        private readonly translateService: TranslateService,
        private readonly messageServiceHelper: MessageServiceHelper,
        protected tabsService: TabsService,
        private readonly activatedRoute: ActivatedRoute,
        private readonly authService: AuthorizationService,
        private readonly keyboardShortcutsService: KeyboardShortcutsService,
        private readonly masterDataMapper: MasterDataMapper,
        private readonly valueListService: ValueListsService
    ) {
        this.addTab();
    }

    ngOnInit(): void {
        this.widgetTranslations = this.translateService.instant(masterDataTranslationPath);
        this.commonTranslations = this.translateService.instant('common');

        this.authService
            .hasPermissions([Permissions.formConfiguration.write])
            .subscribe((x) => (this.hasUserFormConfigurationWritePermission = x));

        this.registerRefreshButtonListener();
        this.loadMasterDataWithTemplates();
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    subscribeKeyShortcuts(): void {
        this.keyEventsSubscription = this.keyboardShortcutsService.registerKeyboardShortcuts(this.keyActions).subscribe();
    }

    unsubscribeKeyShortcuts(): void {
        this.keyEventsSubscription.unsubscribe();
    }

    private loadMasterDataWithTemplates() {
        this.isProcessing = true;
        this.subscription.add(
            forkJoin({
                rules: this.client.getAutomaticMasterDataRules(uuidv4()).pipe(
                    catchError((_) => {
                        this.messageServiceHelper.showError({
                            key: masterDataTabName,
                            detail: this.widgetTranslations['getMasterDataRulesError']
                        });
                        return EMPTY;
                    })
                ),
                templates: this.client.getConnectionPointTemplatesBySupplyType(uuidv4(), SupplyTypes.Electricity).pipe(
                    catchError((_) => {
                        this.messageServiceHelper.showError({
                            key: masterDataTabName,
                            detail: this.commonTranslations['loadMasterDataProcessTemplatesError']
                        });
                        return EMPTY;
                    })
                )
            })
                .pipe(finalize(() => (this.isProcessing = false)))
                .subscribe({
                    next: (response) => {
                        this.masterDataProcessTemplateOptions = response.templates.result.connectionPointTemplatesDetails;
                        const sortedRules = [...response.rules.result];
                        sortedRules.sort((a, b) => a.order - b.order);
                        this.rawMasterDataRules = sortedRules;
                        this.masterDataRules = sortedRules.map((rule) =>
                            this.masterDataMapper.mapToMasterDataListItem(rule, this.masterDataProcessTemplateOptions)
                        );
                        this.originalRuleOrder = this.masterDataRules.map((x) => x.id);
                        this.alreadyUsedDisplayNames = this.masterDataRules.map((x) => x.displayName);
                    }
                })
        );
    }

    protected editRule(event: MouseEvent, rule: MasterDataListItem) {
        event.stopPropagation();
        this.ruleInEdit = this.rawMasterDataRules.find((r) => r.id === rule.id);
        this.isCreating = false;
    }

    protected startCreating() {
        this.isCreating = true;
        this.ruleInEdit = undefined;
    }

    protected cancelCreating() {
        this.isCreating = false;
    }

    protected cancelEditing() {
        this.ruleInEdit = undefined;
    }

    protected onRuleCreated(rule: AutomaticMasterDataProcessRule) {
        this.isCreating = false;
        this.rawMasterDataRules.push(rule);
        this.masterDataRules.push(this.masterDataMapper.mapToMasterDataListItem(rule, this.masterDataProcessTemplateOptions));
        this.originalRuleOrder.push(rule.id);
        this.alreadyUsedDisplayNames.push(rule.displayName);
    }

    protected onRuleEdited(updatedRule: AutomaticMasterDataProcessRule) {
        const editedIndex = this.masterDataRules.findIndex((r) => r.id === updatedRule.id);
        if (editedIndex === -1) {
            return;
        }

        this.alreadyUsedDisplayNames.splice(
            this.alreadyUsedDisplayNames.indexOf(this.ruleInEdit!.displayName),
            1,
            updatedRule.displayName
        );
        this.rawMasterDataRules[editedIndex] = updatedRule;
        this.masterDataRules[editedIndex] = this.masterDataMapper.mapToMasterDataListItem(
            updatedRule,
            this.masterDataProcessTemplateOptions
        );
        this.ruleInEdit = undefined;
    }

    private addTab() {
        this._tabId = this.tabsService.addOrUpdateTab(this.activatedRoute, {
            icon$: of('fas fa-th-list'),
            areaLabel$: of(this.translateService.instant(`${masterDataTranslationPath}.tabAreaLabel`)),
            label$: of(this.translateService.instant(`${masterDataTranslationPath}.tabLabel`)),
            tooltip$: of(this.translateService.instant(`${masterDataTranslationPath}.tabLabel`)),
            dirty$: this.dirty$,
            canReload$: of(true)
        });
    }

    registerRefreshButtonListener(): void {
        this.subscription.add(
            this.tabsService.reloadButtonClicked$
                .pipe(
                    filter((tabId) => tabId === this._tabId),
                    switchMap((tabId) => this.tabsService.reload(tabId)),
                    filter((canBeReloaded) => canBeReloaded),
                    tap(() => {
                        this.loadMasterDataWithTemplates();
                        this.valueListService.fetchValueLists();
                    })
                )
                .subscribe(() => {
                    this.tabsService.tabReloaded(this._tabId as string);
                })
        );
    }

    onRowReorder(_: TableRowReorderEvent) {
        this.setIsReordered();
    }

    setIsReordered() {
        this.isReordered = !this.masterDataRules.every((rule, index) => rule.id === this.originalRuleOrder[index]);
    }

    cancelReorder() {
        this.masterDataRules = this.originalRuleOrder.map((id) => this.masterDataRules.find((rule) => rule.id === id)!);
        this.setIsReordered();
    }

    protected deleteRule(event: MouseEvent, rule: MasterDataListItem) {
        event.stopPropagation();
        this.ruleToDelete = rule;
        this.attemptedAction = () => this.performDelete();
    }

    private performDelete() {
        if (!this.ruleToDelete) {
            return;
        }

        this.isProcessing = true;
        this.subscription.add(
            this.client
                .deleteAutomaticMasterDataRuleById(this.ruleToDelete.id, uuidv4())
                .pipe(
                    finalize(() => {
                        this.isProcessing = false;
                        this.ruleToDelete = undefined;
                    })
                )
                .subscribe({
                    complete: () => {
                        this.messageServiceHelper.showSuccess({
                            key: masterDataTabName
                        });
                        this.masterDataRules = this.masterDataRules.filter((r) => r.id !== this.ruleToDelete?.id);
                        this.originalRuleOrder = this.originalRuleOrder.filter((id) => id !== this.ruleToDelete?.id);
                        this.rawMasterDataRules = this.rawMasterDataRules.filter((r) => r.id !== this.ruleToDelete?.id);
                        if (this.ruleInEdit?.id === this.ruleToDelete?.id) {
                            this.ruleInEdit = undefined;
                        }

                        this.alreadyUsedDisplayNames = this.alreadyUsedDisplayNames.filter(
                            (name) => name !== this.ruleToDelete?.displayName
                        );
                    },
                    error: (_error) => {
                        this.messageServiceHelper.showError({
                            key: masterDataTabName,
                            detail: this.widgetTranslations['masterDataRuleDeleteError']
                        });
                    }
                })
        );
    }

    onConfirmationDialogYesButtonClick() {
        if (this.attemptedAction) {
            this.attemptedAction();
        }
        this.attemptedAction = null;
    }

    onConfirmationDialogNoButtonClick() {
        this.attemptedAction = null;
        this.ruleToDelete = undefined;
    }

    saveReorder() {
        this.isProcessing = true;
        this.subscription.add(
            this.client
                .reorderAutomaticMasterDataRules(
                    uuidv4(),
                    new ReorderModel({
                        reorderedIds: this.masterDataRules.map((x) => x.id)
                    })
                )
                .pipe(finalize(() => (this.isProcessing = false)))
                .subscribe({
                    complete: () => {
                        this.messageServiceHelper.showSuccess({
                            key: masterDataTabName
                        });
                        this.originalRuleOrder = this.masterDataRules.map((x) => x.id);
                        this.isReordered = false;
                    },
                    error: (_error) => {
                        this.messageServiceHelper.showError({
                            key: masterDataTabName,
                            detail: this.widgetTranslations['masterDataRuleReorderError']
                        });
                    }
                })
        );
    }

    recalculateDirty() {
        this.dirty$.next(this.isCreating || this.isReordered || !!this.ruleInEdit);
    }

    // Supply type will be included
    getValueListType(colName: string, workOrderType: WorkOrderType): ValueListType | undefined {
        switch (colName) {
            case 'workOrderDescriptionId':
                if (workOrderType === WorkOrderType.General) {
                    return ValueListType.WorkOrderGeneralElectricityWorkDescriptionList;
                }
                if (workOrderType === WorkOrderType.InstallMeter) {
                    return ValueListType.WorkOrderInstallMeterElectricityWorkDescriptionList;
                }
                if (workOrderType === WorkOrderType.ReplaceMeter) {
                    return ValueListType.WorkOrderReplaceMeterElectricityWorkDescriptionList;
                }
                if (workOrderType === WorkOrderType.RemoveMeter) {
                    return ValueListType.WorkOrderRemoveMeterElectricityWorkDescriptionList;
                }
                return undefined;
            case 'workOrderPurposeId':
                return ValueListType.WorkOrderElectricityPurposeList;
            default:
                return undefined;
        }
    }
}
