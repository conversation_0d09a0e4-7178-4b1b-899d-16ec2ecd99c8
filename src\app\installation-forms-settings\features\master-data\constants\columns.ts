import { ColumnTransformationType } from 'src/app/core/enums/column-transformation-type';
import { RulesListColumn } from '../../models/rules-list-column';
import { masterDataTranslationPath } from './master-data.consts';

export const MASTER_DATA_COLUMNS: RulesListColumn[] = [
    {
        field: 'displayName',
        header: `${masterDataTranslationPath}.displayName`,
        columnTransformationType: ColumnTransformationType.None,
        isDefault: true
    },
    {
        field: 'formTypes',
        header: `${masterDataTranslationPath}.formTypes`,
        translationPath: 'enums.type.',
        columnTransformationType: ColumnTransformationType.TranslateEnumArray,
        isDefault: true
    },
    {
        field: 'formStates',
        header: `${masterDataTranslationPath}.formStates`,
        translationPath: 'enums.state.',
        columnTransformationType: ColumnTransformationType.TranslateEnumArray,
        isDefault: true
    },
    {
        field: 'voltageLevels',
        header: `${masterDataTranslationPath}.voltageLevels`,
        translationPath: 'enums.automationVoltageLevel.',
        columnTransformationType: ColumnTransformationType.TranslateEnumArray,
        isDefault: true
    },
    {
        field: 'totalCapacity',
        header: `${masterDataTranslationPath}.totalCapacity`,
        columnTransformationType: ColumnTransformationType.None,
        isDefault: true
    },
    {
        field: 'scopeOfDelivery',
        header: `${masterDataTranslationPath}.scopeOfDelivery`,
        columnTransformationType: ColumnTransformationType.None,
        isDefault: true
    },
    {
        field: 'startsAsConstruction',
        header: `${masterDataTranslationPath}.startsAsConstruction`,
        columnTransformationType: ColumnTransformationType.Boolean,
        isDefault: true
    },
    {
        field: 'terminationScope',
        header: `${masterDataTranslationPath}.terminationScope`,
        translationPath: 'enums.terminationScope.',
        columnTransformationType: ColumnTransformationType.Translate,
        isDefault: true
    },
    {
        field: 'masterDataProcessType',
        header: `${masterDataTranslationPath}.masterDataProcessType`,
        translationPath: 'enums.masterDataProcessType.',
        columnTransformationType: ColumnTransformationType.Translate,
        isDefault: true
    },
    {
        field: 'masterDataProcessTemplate',
        header: `${masterDataTranslationPath}.masterDataProcessTemplate`,
        columnTransformationType: ColumnTransformationType.None,
        isDefault: true
    },
    {
        field: 'masterDataProcessAutomationLevel',
        header: `${masterDataTranslationPath}.masterDataProcessAutomationLevel`,
        translationPath: 'enums.masterDataProcessAutomationLevel.',
        columnTransformationType: ColumnTransformationType.Translate,
        isDefault: true
    },
    {
        field: 'masterDataProcessWorkOrderAutomationLevel',
        header: `${masterDataTranslationPath}.masterDataProcessWorkOrderAutomationLevel`,
        translationPath: 'enums.masterDataProcessWorkOrderAutomationLevel.',
        columnTransformationType: ColumnTransformationType.Translate,
        isDefault: true
    },
    {
        field: 'workOrderType',
        header: `${masterDataTranslationPath}.workOrderType`,
        translationPath: 'enums.workOrderType.',
        columnTransformationType: ColumnTransformationType.Translate,
        isDefault: true
    },
    {
        field: 'workOrderPurposeId',
        header: `${masterDataTranslationPath}.workOrderPurposeId`,
        columnTransformationType: ColumnTransformationType.ValueListSingleItem,
        isDefault: true
    },
    {
        field: 'workOrderDescriptionId',
        header: `${masterDataTranslationPath}.workOrderDescriptionId`,
        columnTransformationType: ColumnTransformationType.ValueListSingleItem,
        isDefault: true
    },
    {
        field: 'hadExistingProduction',
        header: `${masterDataTranslationPath}.hadExistingProduction`,
        columnTransformationType: ColumnTransformationType.Boolean,
        isDefault: true
    }
];
