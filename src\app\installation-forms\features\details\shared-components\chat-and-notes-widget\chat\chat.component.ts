import { AfterViewInit, Component, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { ScrollPanel } from 'primeng/scrollpanel';
import { Subscription } from 'rxjs';
import {
    ChatMessage,
    CreateChatMessage,
    InstallationFormsClient,
    PendingUpdateAreaType
} from 'src/app/api/installation-forms-client';
import { AutomaticFormRefreshService } from 'src/app/core/services/automatic-form-refresh/automatic-form-refresh.service';
import { FormDataService } from 'src/app/core/services/form-data/form-data.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { WidgetWithFormComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget-with-form.component';
import { v4 as uuidv4 } from 'uuid';
import { FormState } from '../../../../../../api/installation-forms-client';

@Component({
    selector: 'app-chat',
    templateUrl: './chat.component.html',
    styleUrl: './chat.component.scss',
    standalone: false
})
export class ChatComponent extends WidgetWithFormComponent implements OnInit, OnDestroy, AfterViewInit {
    public messages: ChatMessage[] = [];

    private widgetTranslations: any;

    subscription: Subscription = new Subscription();

    form: FormGroup;

    @ViewChild('scrollPanel') private scrollPanel!: ScrollPanel;

    get canCreateChatMessage(): boolean {
        return this.formDataService.state !== FormState.Archived && this.formDataService.state !== FormState.Cancelled;
    }

    constructor(
        private readonly client: InstallationFormsClient,
        private readonly messageServiceHelper: MessageServiceHelper,
        private readonly translateService: TranslateService,
        private readonly fb: FormBuilder,
        readonly formDataService: FormDataService,
        private readonly automaticFormRefreshService: AutomaticFormRefreshService
    ) {
        super(formDataService);
        this.form = this.fb.group({
            message: ['']
        });
    }

    ngOnInit(): void {
        this.widgetTranslations = this.translateService.instant('chatAndNotesWidget.chat');

        this.automaticFormRefreshService.subscribeForRefresh(PendingUpdateAreaType.ChatMessages, this.getChatMessages);
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
        this.automaticFormRefreshService.unsubscribeForRefresh(PendingUpdateAreaType.ChatMessages);
    }

    ngAfterViewInit() {
        this.scrollToBottom();
    }

    private getChatMessages = () => {
        this.subscription.add(
            this.client.getChatMessages(this.formDataService.formId!, uuidv4()).subscribe({
                error: (_) => {
                    this.messageServiceHelper.showError({
                        detail: this.widgetTranslations['getChatMessagesError'],
                        key: this.formDataService.formId!
                    });
                },
                next: (x) => {
                    this.messages = x.result;
                    this.formDataService.hasUnreadMessages = this.messages.some((x) => !!x.isUnread);
                    setTimeout(() => this.scrollToBottom(), 0);
                }
            })
        );
    };

    protected createNewChatMessage() {
        this.processingStarted();
        this.subscription.add(
            this.client
                .createNewChatMessage(
                    this.formDataService.formId!,
                    uuidv4(),
                    this.formDataService.rowVersion,
                    new CreateChatMessage({
                        message: this.form.get('message')?.value?.trim()
                    })
                )
                .subscribe({
                    error: (_) => {
                        this.processingFinished();
                        this.messageServiceHelper.showError({
                            detail: this.widgetTranslations['createNewChatMessageError'],
                            key: this.formDataService.formId!
                        });
                    },
                    next: (x) => {
                        this.messages.push(x.result);
                        this.processingFinished();
                        this.form.reset();
                        this.getChatMessages();
                    }
                })
        );
    }

    protected markAllMessagesAsRead() {
        this.processingStarted();
        this.subscription.add(
            this.client.markAllMessagesAsRead(this.formDataService.formId!, uuidv4(), this.formDataService.rowVersion).subscribe({
                error: (_) => {
                    this.processingFinished();
                    this.messageServiceHelper.showError({
                        detail: this.widgetTranslations['markAllAsReadError'],
                        key: this.formDataService.formId!
                    });
                },
                complete: () => {
                    this.processingFinished();
                    this.messageServiceHelper.showSuccess({
                        detail: this.widgetTranslations['markAllAsReadSuccess'],
                        key: this.formDataService.formId!
                    });
                    this.getChatMessages();
                }
            })
        );
    }

    private scrollToBottom(): void {
        this.scrollPanel.scrollTop(this.scrollPanel.contentViewChild?.nativeElement.scrollHeight);
    }

    protected hasUnreadMessages = () => this.formDataService.hasUnreadMessages;
    protected formMessageIsEmpty = () => this.form.get('message')?.value?.length === 0;
}
