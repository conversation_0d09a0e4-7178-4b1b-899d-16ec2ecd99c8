import { CommonModule, DatePipe } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { InjectionToken, NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ConfigurationService } from '@kmd-elements-ui/configuration';
import { ATTACHMENTS_MESSAGE_SERVICE, ATTACHMENTSBFF_BASE_URL, AttachmentsModule } from '@kmd-elements/attachments';
import {
    CmbsCalendarComponent,
    CmbsValueListDropdownComponent,
    DisplayColumnsSelectorAdapterDirective,
    DisplayColumnsSelectorComponent,
    PersistableSearchButtonsComponent,
    PersistableSettingsRootDirective,
    PTableAdapterDirective,
    USER_PREFERENCES_BFF_BASE_URL
} from '@kmd-elements/core-kit';
import { NextZebraContainerModule } from '@kmd-elements/utilities';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { AccordionModule } from 'primeng/accordion';
import { MessageService } from 'primeng/api';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { CardModule } from 'primeng/card';
import { CheckboxModule } from 'primeng/checkbox';
import { DialogModule } from 'primeng/dialog';
import { DividerModule } from 'primeng/divider';
import { InputGroupModule } from 'primeng/inputgroup';
import { InputGroupAddonModule } from 'primeng/inputgroupaddon';
import { InputNumberModule } from 'primeng/inputnumber';
import { InputTextModule } from 'primeng/inputtext';
import { MessageModule } from 'primeng/message';
import { MessagesModule } from 'primeng/messages';
import { MultiSelectModule } from 'primeng/multiselect';
import { PaginatorModule } from 'primeng/paginator';
import { PanelModule } from 'primeng/panel';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { RippleModule } from 'primeng/ripple';
import { ScrollPanelModule } from 'primeng/scrollpanel';
import { SelectModule } from 'primeng/select';
import { SelectButtonModule } from 'primeng/selectbutton';
import { SkeletonModule } from 'primeng/skeleton';
import { TableModule } from 'primeng/table';
import { TabsModule } from 'primeng/tabs';
import { TagModule } from 'primeng/tag';
import { TextareaModule } from 'primeng/textarea';
import { TieredMenuModule } from 'primeng/tieredmenu';
import { ToastModule } from 'primeng/toast';
import { ToggleButtonModule } from 'primeng/togglebutton';
import { ToggleSwitchModule } from 'primeng/toggleswitch';
import { TooltipModule } from 'primeng/tooltip';
import { environment } from 'src/environments/environment';
import daTranslations from '../../assets/i18n/da-DK.json';
import enTranslations from '../../assets/i18n/en-US.json';
import { INSTALLATIONFORMS_BASE_URL, InstallationFormsClient } from '../api/installation-forms-client';
import { ApplicationsConfigService } from '../core/services/applications-config/applications-config.service';
import { DefaultInstructionTextService } from '../core/services/default-instruction-text/default-instruction-text.service';
import { EmailTemplatesService } from '../core/services/email-templates/email-templates.service';
import { MessageServiceHelper } from '../core/services/message/message.service';
import { TranslateServiceHelper } from '../core/services/translate/translate.service';
import { ValueListsService } from '../core/services/value-lists/value-lists.service';
import { SharedModule } from '../shared/shared.module';
import { ChangeBranchLineDetailsComponent } from './features/details/change-branch-line/change-branch-line-details.component';
import { ChangeBranchLineSectionComponent } from './features/details/change-branch-line/change-branch-line-section/change-branch-line-section.component';
import { ChangeBranchLineWidgetComponent } from './features/details/change-branch-line/change-branch-line-widget/change-branch-line-widget.component';
import { ChangeOfMeterDetailsComponent } from './features/details/change-of-meter/change-of-meter-details.component';
import { ChangeOfMeterSectionComponent } from './features/details/change-of-meter/change-of-meter-section/change-of-meter-section.component';
import { ChangeOfMeterWidgetComponent } from './features/details/change-of-meter/change-of-meter-widget/change-of-meter-widget.component';
import { EnergyProductionDetailsComponent } from './features/details/energy-production/energy-production-details.component';
import { EnergyProductionCommonSectionComponent } from './features/details/energy-production/energy-production-widget/energy-production-common-section/energy-production-common-section.component';
import { EnergyProductionWidgetComponent } from './features/details/energy-production/energy-production-widget/energy-production-widget.component';
import { ConnectionAddressSectionComponent } from './features/details/energy-production/energy-production-widget/type-specific-sections/connection-address-section/connection-address-section.component';
import { PlantsSectionComponent } from './features/details/energy-production/energy-production-widget/type-specific-sections/plants-section/plants-section.component';
import { PowerPlantSectionComponent } from './features/details/energy-production/energy-production-widget/type-specific-sections/power-plant-section/power-plant-section.component';
import { WindPlantSectionComponent } from './features/details/energy-production/energy-production-widget/type-specific-sections/wind-plant-section/wind-plant-section.component';
import { ExtensionDetailsComponent } from './features/details/extension/extension-details.component';
import { ExtensionSectionComponent } from './features/details/extension/extension-section/extension-section.component';
import { ExtensionWidgetComponent } from './features/details/extension/extension-widget/extension-widget.component';
import { MoveMeterDetailsComponent } from './features/details/move-meter/move-meter-details.component';
import { MoveMeterSectionComponent } from './features/details/move-meter/move-meter-section/move-meter-section.component';
import { MoveMeterWidgetComponent } from './features/details/move-meter/move-meter-widget/move-meter-widget.component';
import { NewInstallationDetailsComponent } from './features/details/new-installation/new-installation-details.component';
import { NewInstallationWidgetComponent } from './features/details/new-installation/new-installation-widget/new-installation-widget.component';
import { SealBreachDetailsComponent } from './features/details/seal-breach/seal-breach-details.component';
import { SealBreachWidgetComponent } from './features/details/seal-breach/seal-breach-widget/seal-breach-widget.component';
import { SealBreachSealSectionComponent } from './features/details/seal-breach/seal-section/seal-section.component';
import { ActionBarComponent } from './features/details/shared-components/action-bar/action-bar.component';
import { CaseWorkerWidgetComponent } from './features/details/shared-components/case-worker-widget/case-worker-widget.component';
import { ChatAndNotesWidgetComponent } from './features/details/shared-components/chat-and-notes-widget/chat-and-notes-widget.component';
import { ChatComponent } from './features/details/shared-components/chat-and-notes-widget/chat/chat.component';
import { NotesComponent } from './features/details/shared-components/chat-and-notes-widget/notes/notes.component';
import { CommonReadingsComponent } from './features/details/shared-components/common-readings/common-readings.component';
import { ConnectionRightsSectionComponent } from './features/details/shared-components/connection-rights-section/connection-rights-section.component';
import { DetailsHeaderComponent } from './features/details/shared-components/details-header/details-header.component';
import { EmailStatusComponent } from './features/details/shared-components/emails/emails-list-widget/email-status/email-status.component';
import { EmailsListWidgetComponent } from './features/details/shared-components/emails/emails-list-widget/emails-list-widget.component';
import { EmailsWidgetComponent } from './features/details/shared-components/emails/emails-widget/emails-widget.component';
import { SendEmailComponent } from './features/details/shared-components/emails/send-email/send-email.component';
import { FlagsWidgetComponent } from './features/details/shared-components/flags-widget/flags-widget.component';
import { DefaultFlagTemplateComponent } from './features/details/shared-components/flags-widget/templates/default-flag-template/default-flag-template.component';
import { ReadyForMeterFlagTemplateComponent } from './features/details/shared-components/flags-widget/templates/ready-for-meter-flag-template/ready-for-meter-flag-template.component';
import { IdentificationBarComponent } from './features/details/shared-components/identification-bar/identification-bar.component';
import { InstallationContactPersonSectionComponent } from './features/details/shared-components/installation-contact-person-section/installation-contact-person-section.component';
import { InstallationSectionComponent } from './features/details/shared-components/installation-section/installation-section.component';
import { InstallerWidgetComponent } from './features/details/shared-components/installer-widget/installer-widget.component';
import { InstructionDataSectionComponent } from './features/details/shared-components/instruction-data-section/instruction-data-section.component';
import { InvoicesWidgetComponent } from './features/details/shared-components/invoices-widget/invoices-widget.component';
import { CreateMasterDataProcessComponent } from './features/details/shared-components/master-data-processes-widget/create-master-data-process/create-master-data-process.component';
import { MasterDataProcessStatusComponent } from './features/details/shared-components/master-data-processes-widget/master-data-processes-list/master-data-process-status/master-data-process-status.component';
import { MasterDataProcessesListComponent } from './features/details/shared-components/master-data-processes-widget/master-data-processes-list/master-data-processes-list.component';
import { MasterDataProcessesWidgetComponent } from './features/details/shared-components/master-data-processes-widget/master-data-processes-widget.component';
import { MeterDeliverySectionComponent } from './features/details/shared-components/meter-delivery-section/meter-delivery-section.component';
import { MeterReturnSectionComponent } from './features/details/shared-components/meter-return-section/meter-return-section.component';
import { MeterTypeSectionComponent } from './features/details/shared-components/meter-type-section/meter-type-section.component';
import { PayerSectionComponent } from './features/details/shared-components/payer-section/payer-section.component';
import { ProblemsListComponent } from './features/details/shared-components/problems-widget/problems-list/problems-list.component';
import { ProblemsWidgetComponent } from './features/details/shared-components/problems-widget/problems-widget.component';
import { RelatedFormsSectionComponent } from './features/details/shared-components/related-forms-section/related-forms-section.component';
import { TasksWidgetComponent } from './features/details/shared-components/tasks-widget/tasks-widget.component';
import { TechnicalInformationSectionComponent } from './features/details/shared-components/technical-information-section/technical-information-section.component';
import { UsageSectionComponent } from './features/details/shared-components/usage-section/usage-section.component';
import { VoltageSectionComponent } from './features/details/shared-components/voltage-section/voltage-section.component';
import { CreateWorkOrderWidgetComponent } from './features/details/shared-components/work-orders-widget/create-order-widget/create-work-order-widget.component';
import { WorkOrderStatusComponent } from './features/details/shared-components/work-orders-widget/work-orders-list-widget/work-order-status/work-order-status.component';
import { WorkOrdersListWidgetComponent } from './features/details/shared-components/work-orders-widget/work-orders-list-widget/work-orders-list-widget.component';
import { WorkOrdersWidgetComponent } from './features/details/shared-components/work-orders-widget/work-orders-widget.component';
import { TerminationDetailsComponent } from './features/details/termination/termination-details.component';
import { TerminationSectionComponent } from './features/details/termination/termination-section/termination-section.component';
import { TerminationWidgetComponent } from './features/details/termination/termination-widget/termination-widget.component';
import { InstallationFormsListComponent } from './features/list/list.component';
import { InstallationFormsColumnsService } from './features/list/utils/columns.service';
import { InstallationFormsRoutingModule } from './installation-forms-routing.module';

export function HttpLoaderFactory(httpClient: HttpClient, host: string) {
    return new TranslateHttpLoader(httpClient, `${host}/assets/i18n/`, '.json');
}

export const INSTALLATIONFORMS_UI_BASE_URL = new InjectionToken<string>('INSTALLATIONFORMS_UI_BASE_URL');

const components = [
    InstallationFormsListComponent,
    NewInstallationDetailsComponent,
    ActionBarComponent,
    DetailsHeaderComponent,
    InstallerWidgetComponent,
    IdentificationBarComponent,
    CaseWorkerWidgetComponent,
    WorkOrdersWidgetComponent,
    CreateWorkOrderWidgetComponent,
    WorkOrdersListWidgetComponent,
    InstallationSectionComponent,
    PayerSectionComponent,
    TechnicalInformationSectionComponent,
    InstructionDataSectionComponent,
    MeterDeliverySectionComponent,
    ConnectionRightsSectionComponent,
    CommonReadingsComponent,
    MeterTypeSectionComponent,
    SealBreachDetailsComponent,
    SealBreachSealSectionComponent,
    TerminationDetailsComponent,
    TerminationWidgetComponent,
    TerminationSectionComponent,
    MoveMeterDetailsComponent,
    MoveMeterWidgetComponent,
    MoveMeterSectionComponent,
    MeterReturnSectionComponent,
    ChangeOfMeterDetailsComponent,
    ChangeOfMeterWidgetComponent,
    ChangeOfMeterSectionComponent,
    FlagsWidgetComponent,
    DefaultFlagTemplateComponent,
    ReadyForMeterFlagTemplateComponent,
    NewInstallationWidgetComponent,
    SealBreachWidgetComponent,
    InstallationContactPersonSectionComponent,
    ProblemsWidgetComponent,
    EmailsWidgetComponent,
    ChangeBranchLineDetailsComponent,
    ChangeBranchLineWidgetComponent,
    ChangeBranchLineSectionComponent,
    EmailsListWidgetComponent,
    SendEmailComponent,
    ExtensionDetailsComponent,
    ExtensionWidgetComponent,
    ExtensionSectionComponent,
    UsageSectionComponent,
    VoltageSectionComponent,
    EmailStatusComponent,
    WorkOrderStatusComponent,
    EnergyProductionDetailsComponent,
    EnergyProductionWidgetComponent,
    EnergyProductionCommonSectionComponent,
    WindPlantSectionComponent,
    ConnectionAddressSectionComponent,
    PowerPlantSectionComponent,
    PlantsSectionComponent,
    InvoicesWidgetComponent,
    RelatedFormsSectionComponent,
    MasterDataProcessesWidgetComponent,
    MasterDataProcessesListComponent,
    MasterDataProcessStatusComponent,
    CreateMasterDataProcessComponent,
    ChatAndNotesWidgetComponent,
    ChatComponent,
    NotesComponent,
    TasksWidgetComponent,
    ProblemsListComponent
];

const imports = [
    CommonModule,
    TranslateModule.forChild({
        extend: false
    }),
    InstallationFormsRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    PanelModule,
    FlexLayoutModule,
    InputTextModule,
    InputNumberModule,
    TextareaModule,
    AccordionModule,
    ButtonModule,
    CheckboxModule,
    RippleModule,
    TableModule,
    PaginatorModule,
    SkeletonModule,
    ProgressSpinnerModule,
    CardModule,
    DialogModule,
    DividerModule,
    TieredMenuModule,
    ToastModule,
    MessagesModule,
    MessageModule,
    CalendarModule,
    AutoCompleteModule,
    ToggleSwitchModule,
    SelectButtonModule,
    InputGroupModule,
    InputGroupAddonModule,
    SharedModule,
    AttachmentsModule,
    NextZebraContainerModule,
    ScrollPanelModule,
    TooltipModule,
    MultiSelectModule,
    ToggleButtonModule,
    CmbsCalendarComponent,
    CmbsValueListDropdownComponent,
    PersistableSearchButtonsComponent,
    PersistableSettingsRootDirective,
    PTableAdapterDirective,
    DisplayColumnsSelectorAdapterDirective,
    DisplayColumnsSelectorComponent,
    SelectModule,
    TabsModule,
    TagModule
];

const providers = [
    MessageService,
    ConfigurationService,
    InstallationFormsClient,
    DatePipe,
    {
        provide: INSTALLATIONFORMS_BASE_URL,
        useValue: environment.backendBasePath
    },
    {
        provide: INSTALLATIONFORMS_UI_BASE_URL,
        useValue: environment.ui
    },
    {
        provide: ATTACHMENTSBFF_BASE_URL,
        useValue: environment.attachmentsApiBasePath
    },
    {
        provide: ATTACHMENTS_MESSAGE_SERVICE,
        useExisting: MessageService
    },
    { provide: USER_PREFERENCES_BFF_BASE_URL, useValue: environment.userPreferencesBffUrl },
    ApplicationsConfigService,
    EmailTemplatesService,
    MessageServiceHelper,
    TranslateServiceHelper,
    ValueListsService,
    InstallationFormsColumnsService,
    DefaultInstructionTextService
];

@NgModule({
    declarations: [...components],
    imports: [...imports],
    providers: [...providers]
})
export class InstallationFormsModule {
    constructor(private readonly translateService: TranslateService) {
        this.translateService.setTranslation('da-DK', daTranslations, true);
        this.translateService.setTranslation('en-US', enTranslations, true);
    }
}
