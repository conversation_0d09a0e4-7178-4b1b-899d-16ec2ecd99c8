<div #overlayContainer></div>
<div fxLayout="column" class="mt-10 zebra-container" [formGroup]="form">
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['displayName']" for="displayName">
                {{ 'formsSettings.automaticInstructionTexts.displayName' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input id="displayName" type="text" pInputText maxlength="100" formControlName="displayName" autoTrimWhitespaces />
            <small [controlValidationErrors]="form.controls['displayName']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['formTypes']" for="formTypes">
                {{ 'formsSettings.automaticInstructionTexts.formTypes' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-multiSelect
                id="formTypes"
                [options]="formTypeOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="formTypes"
                [maxSelectedLabels]="formTypeOptions.length">
            </p-multiSelect>
            <small [controlValidationErrors]="form.controls['formTypes']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isFormCategoryRelevant()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['formCategories']" for="formCategories">
                {{ 'formsSettings.automaticInstructionTexts.formCategories' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-multiSelect
                id="formCategories"
                [options]="formCategoriesOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="formCategories"
                [maxSelectedLabels]="formCategoriesOptions.length">
            </p-multiSelect>
            <small [controlValidationErrors]="form.controls['formCategories']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['meterNeedsChange']" for="meterNeedsChange">
                {{ 'formsSettings.automaticInstructionTexts.meterNeedsChange' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="meterNeedsChange"
                formControlName="meterNeedsChange"
                [options]="(booleanOptionsService.getBooleanOptions() | async) ?? []"
                [appendTo]="overlayContainer"
                [showClear]="true"
                [placeholder]="'common.selectValue' | translate">
            </p-select>
            <small [controlValidationErrors]="form.controls['meterNeedsChange']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['meterNeedsReconfiguration']" for="meterNeedsReconfiguration">
                {{ 'formsSettings.automaticInstructionTexts.meterNeedsReconfiguration' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="meterNeedsReconfiguration"
                formControlName="meterNeedsReconfiguration"
                [options]="(booleanOptionsService.getBooleanOptions() | async) ?? []"
                [appendTo]="overlayContainer"
                [showClear]="true"
                [placeholder]="'common.selectValue' | translate">
            </p-select>
            <small [controlValidationErrors]="form.controls['meterNeedsReconfiguration']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['formIsScreened']" for="formIsScreened">
                {{ 'formsSettings.automaticInstructionTexts.formIsScreened' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="formIsScreened"
                formControlName="formIsScreened"
                [options]="(booleanOptionsService.getBooleanOptions() | async) ?? []"
                [appendTo]="overlayContainer"
                [showClear]="true"
                [placeholder]="'common.selectValue' | translate">
            </p-select>
            <small [controlValidationErrors]="form.controls['formIsScreened']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['defaultInstructionTexts']" for="defaultInstructionTexts">
                {{ 'formsSettings.automaticInstructionTexts.defaultInstructionTexts' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-multiSelect
                id="defaultInstructionTexts"
                [options]="defaultInstructionTextsOptions"
                [loading]="isDuringInstructionTextLoading"
                optionValue="id"
                optionLabel="text"
                [appendTo]="overlayContainer"
                [showClear]="true"
                [placeholder]="'common.selectValue' | translate"
                formControlName="defaultInstructionTexts">
            </p-multiSelect>
            <small [controlValidationErrors]="form.controls['defaultInstructionTexts']" class="p-error"></small>
        </div>
    </div>

    <div class="mb-2 mt-2 flex justify-content-end">
        <button
            id="cancelButton"
            [disabled]="isProcessing"
            type="button"
            pButton
            pRipple
            class="mr-2 p-button-secondary"
            (click)="cancelEditClick()">
            {{ 'common.cancel' | translate }}
        </button>
        <button
            id="saveButton"
            type="button"
            pButton
            pRipple
            (click)="saveEditedClick()"
            [disabled]="form.invalid || isProcessing">
            {{ 'common.saveChanges' | translate }}
        </button>
    </div>
</div>
