import { Component, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription, retry, take } from 'rxjs';
import { Email, InstallationFormsClient, PendingUpdateAreaType } from 'src/app/api/installation-forms-client';
import { SMALL_TABLE_ROWS_PER_PAGE } from 'src/app/core/constants/constants';
import { AutomaticFormRefreshService } from 'src/app/core/services/automatic-form-refresh/automatic-form-refresh.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { v4 as uuidv4 } from 'uuid';
import { FormDataService } from '../../../../../../core/services/form-data/form-data.service';

@Component({
    selector: 'app-emails-list-widget',
    templateUrl: './emails-list-widget.component.html',
    styleUrls: ['./emails-list-widget.component.scss'],
    standalone: false
})
export class EmailsListWidgetComponent implements OnInit, OnDestroy {
    loading = true;
    columns = ['templateName', 'type', 'recipientAddress', 'status', 'createdDate'];
    rowsPerPage = SMALL_TABLE_ROWS_PER_PAGE;

    emails: Email[] = [];

    subscription: Subscription = new Subscription();

    constructor(
        private readonly client: InstallationFormsClient,
        private readonly messageServiceHelper: MessageServiceHelper,
        private readonly router: Router,
        private readonly formDataService: FormDataService,
        private readonly automaticFormRefreshService: AutomaticFormRefreshService
    ) {}

    isRowClickable = (row: Email) => {
        return row.processId !== null;
    };

    async handleRowClick(row: Email) {
        const selection = window.getSelection();
        if (selection && selection.toString().length > 0) {
            return;
        }
        if (!this.isRowClickable(row)) {
            return;
        }

        if (row.processId) {
            await this.router.navigate([
                `process-center/customer-communication/details/EmailCommunicationProcess/${row.processId}`
            ]);
        }
    }

    ngOnInit(): void {
        this.automaticFormRefreshService.subscribeForRefresh(PendingUpdateAreaType.Emails, this.getEmails);
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
        this.automaticFormRefreshService.unsubscribeForRefresh(PendingUpdateAreaType.Emails);
    }

    getEmails = () => {
        this.subscription.add(
            this.client
                .getEmails(this.formDataService.formId!, uuidv4())
                .pipe(retry({ count: 3, delay: 250 }), take(1))
                .subscribe({
                    next: (response) => {
                        this.emails = response.result;
                        this.loading = false;
                    },
                    error: (_) => {
                        this.messageServiceHelper.showError({
                            key: this.formDataService.formId!
                        });
                    }
                })
        );
    };
}
