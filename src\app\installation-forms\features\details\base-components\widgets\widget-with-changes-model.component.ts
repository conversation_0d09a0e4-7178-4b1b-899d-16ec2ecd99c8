import { Component, EventEmitter, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { Subscription, debounceTime } from 'rxjs';
import { distinctUntilChanged } from 'rxjs/operators';
import { AutomaticFormRefreshService } from 'src/app/core/services/automatic-form-refresh/automatic-form-refresh.service';
import { ChangesModel } from '../../../../../core/constants/changes-details';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';
import { ModelComparer } from '../../../../../core/utils/model.comparer';
import { WidgetWithFormComponent } from './widget-with-form.component';

@Component({
    template: '',
    standalone: false
})
export abstract class WidgetWithChangesModelComponent extends WidgetWithFormComponent implements OnInit, OnDestroy {
    changesModel!: ChangesModel;
    changesAmount = 0;

    subscription: Subscription = new Subscription();
    protected abstract onChange(attemptedValue: any): void;

    @Output() refreshRequested = new EventEmitter<void>();

    get canSupplyFormData(): boolean {
        return this.changesAmount === 0 || this.isReadOnly;
    }

    constructor(
        protected readonly formDataService: FormDataService,
        protected readonly automaticFormRefreshService: AutomaticFormRefreshService
    ) {
        super(formDataService);
    }

    ngOnInit(): void {
        this.subscription.add(
            this.form.valueChanges.pipe(distinctUntilChanged(), debounceTime(100)).subscribe({
                next: (attemptedValue) => {
                    this.onChange(attemptedValue);
                }
            })
        );

        this.subscription.add(
            this.automaticFormRefreshService.forceRefreshRequested$.subscribe(() => {
                this.forceRefreshStarted();
            })
        );
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    public shouldShowChangesPanel(): boolean {
        return (
            !this.formDataService.isProcessing && !this.isDuringForceRefresh && (!this.isReadOnly || this.canUpdatePaymentDetails)
        );
    }

    public setFormEditability(shouldBeEditable: boolean = false): void {
        if (this.isReadOnly && !shouldBeEditable) {
            this.form.disable();
            this.setSubFormEditability();
        } else {
            this.form.enable();
        }
    }

    public setSubFormEditability(): void {
        // to be optionally overridden by children
    }

    public recalculateChanges(initialValue: any, currentValue: any, compareOnly: boolean = false): boolean {
        const compareResult = new ModelComparer(this.changesModel).compareModels(initialValue, currentValue);
        if (!compareOnly) {
            if (!this.shouldShowChangesPanel()) {
                this.changesAmount = 0;
                Object.keys(this.changesModel).forEach((key) => (this.changesModel[key].change = false));
            } else {
                this.changesModel = compareResult.changesModel;
                this.changesAmount = compareResult.changesAmount;
            }
        }
        return compareResult.changesAmount > 0;
    }

    public hasConflictingChanges(initialValue: any, currentFormValue: any, remoteValue: any): boolean {
        return (
            this.changesAmount > 0 &&
            this.recalculateChanges(this.formDataService.isProcessing ? currentFormValue : initialValue, remoteValue, true)
        );
    }

    public dataLoaded() {
        if (this.isDuringForceRefresh) {
            this.forceRefreshFinished();
        }
        this.form.markAsPristine();
    }
}
