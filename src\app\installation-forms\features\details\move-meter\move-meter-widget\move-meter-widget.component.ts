import { Component } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { Observable } from 'rxjs';
import {
    BranchLineResponsible,
    ConnectionFee,
    Contact<PERSON>erson,
    DeliveryInformationUpdate,
    DeliveryOption,
    FormsRelationType,
    InstallationFormsApiResponse,
    InstallationFormsClient,
    InstallationInformationUpdate,
    InstructionDataUpdate,
    MeterDeliveryOptionsUpdate,
    MeterResponsible,
    MeterSize,
    MeterTypeUpdate,
    MoveMeter,
    MoveMeterChangeReason,
    MoveMeterUpdate,
    PayerType,
    PayerUpdate,
    PaymentDetailsUpdate,
    RelatedForm,
    RelatedFormUpdate
} from 'src/app/api/installation-forms-client';
import { ChangesModel } from 'src/app/core/constants/changes-details';
import { METER_TRANSFORMER_OTHER_VALUE_ID } from 'src/app/core/constants/constants';
import { RemarkLength } from 'src/app/core/constants/field-lengths';
import { AutomaticFormRefreshService } from 'src/app/core/services/automatic-form-refresh/automatic-form-refresh.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { createMeteringPoint } from 'src/app/core/utils/create-metering-point';
import { getValueOrDefault } from 'src/app/core/utils/get-value-or-default';
import addressLookupValidator from 'src/app/core/utils/validators/address-lookup/address-lookup.validator';
import conditionallyRequiredAddressLookupValidator from 'src/app/core/utils/validators/address-lookup/conditionally-required-address-lookup.validator';
import conditionallyRequiredValidator from 'src/app/core/utils/validators/conditionally-required/conditionally-required.validator';
import dateMustNotBePastValidator from 'src/app/core/utils/validators/date-time/date-must-not-be-past.validator';
import { EmailValidator } from 'src/app/core/utils/validators/email/email.validator';
import { MoveMeterInformationUpdate } from '../../../../../api/installation-forms-client';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';
import { CoreWidgetComponent } from '../../base-components/widgets/core-widget.component';
import { MoveMeterChanges } from './move-meter-changes.model';
@Component({
    selector: 'app-move-meter-widget',
    templateUrl: './move-meter-widget.component.html',
    styleUrls: ['./move-meter-widget.component.scss'],
    standalone: false
})
export class MoveMeterWidgetComponent extends CoreWidgetComponent<MoveMeter, MoveMeterUpdate> {
    override accordionActiveIndexes: number[] = [0, 1, 2, 3, 4];
    override payerPanelIndex: number = 5;

    get moveMeterInformationForm(): FormGroup {
        return this.form?.get('moveMeterInformation') as FormGroup;
    }

    constructor(
        protected fb: FormBuilder,
        protected readonly messageServiceHelper: MessageServiceHelper,
        protected readonly translateService: TranslateService,
        protected readonly client: InstallationFormsClient,
        protected readonly formDataService: FormDataService,
        protected readonly automaticFormRefreshService: AutomaticFormRefreshService
    ) {
        super(formDataService, automaticFormRefreshService, translateService, messageServiceHelper, client);
        this.initForm();
        this.setChangesModel();
    }

    protected override initForm() {
        this.form = this.fb.group({
            installationInformation: this.fb.group({
                installationAddress: [{ id: '', text: '' }, [addressLookupValidator()]],
                remarksToInstallation: ['', [Validators.maxLength(1000)]],
                connectionPoint: [{ id: '', connectionPointNumber: '' }],
                meterFrame: [{ id: '', meterFrameNumber: '' }],
                consumptionMeteringPoint: [{ meteringPointId: '', meteringPointVersionId: '' }],
                tags: []
            }),
            payer: this.fb.group({
                payerName: ['', [Validators.maxLength(100)]],
                payerType: [''],
                payerEmail: ['', [EmailValidator()]],
                payerContactPersonName: ['', [Validators.maxLength(100)]],
                payerContactPersonEmail: ['', [EmailValidator()]],
                payerContactPersonPhoneNumber: ['', [Validators.maxLength(20)]],
                requisition: '',
                cvrOrSeNumber: ['', [Validators.pattern('^[0-9]{8}$')]],
                eanNumber: ['', [Validators.pattern('^[0-9]{13}$')]],
                payerAddress: [{ id: '', text: '' }]
            }),
            contactPerson: this.fb.group({
                contactPersonCompanyName: ['', [Validators.maxLength(100)]],
                contactPersonName: ['', [Validators.maxLength(100)]],
                contactPersonEmail: ['', [EmailValidator()]],
                contactPersonPhoneNumber: ['', [Validators.maxLength(20)]]
            }),
            instructionData: this.fb.group({
                meterResponsible: '',
                branchLineResponsible: '',
                connectionFeeValidUntilEod: ['', [(control: AbstractControl) => dateMustNotBePastValidator(control)]],
                connectionFeeFee: [null, [Validators.min(-*********), Validators.max(*********)]], // Connection fee can be negative, that's not a mistake
                remark: ['', [Validators.maxLength(RemarkLength)]]
            }),
            meterDeliveryOptions: this.fb.group({
                requestedConnectionDateEod: [
                    null,
                    [Validators.required, (control: AbstractControl) => dateMustNotBePastValidator(control)]
                ],
                deliveryOption: [null, [Validators.required]],
                name: [
                    '',
                    [
                        (control: AbstractControl) => conditionallyRequiredValidator(control, this.shouldShowDelivery),
                        Validators.maxLength(100)
                    ]
                ],
                attention: [
                    '',
                    [
                        (control: AbstractControl) => conditionallyRequiredValidator(control, this.shouldShowDelivery),
                        Validators.maxLength(100)
                    ]
                ],
                meterDeliveryAddress: [
                    { id: '', text: '' },
                    [conditionallyRequiredAddressLookupValidator(this.shouldShowDelivery)]
                ]
            }),
            moveMeterInformation: this.fb.group({
                reasonForChange: [''],
                meterType: this.fb.group({
                    meterSize: [null, [Validators.required]],
                    connectionTypeId: [
                        null,
                        [(control: AbstractControl) => conditionallyRequiredValidator(control, this.isConnectionTypeVisible)]
                    ],
                    meterTransformerId: [
                        null,
                        [(control: AbstractControl) => conditionallyRequiredValidator(control, this.isMeterTransformerVisible)]
                    ],
                    meterTransformerRemark: [
                        null,
                        [
                            (control: AbstractControl) =>
                                conditionallyRequiredValidator(control, this.isMeterTransformerRemarkVisible)
                        ]
                    ]
                }),
                meterPlacementId: ['', [Validators.maxLength(100), Validators.required]],
                responsibleForSeal: ['', [Validators.required]]
            }),
            relatedForms: this.fb.group({
                relatedForms: [],
                hasRelatedFormsChanged: false
            })
        });
    }

    protected override setChangesModel() {
        this.changesModel = new MoveMeterChanges() as unknown as ChangesModel;
    }

    isConnectionTypeVisible = () => {
        return this.getMeterSize() === MeterSize.Below63A;
    };

    isMeterTransformerVisible = () => {
        return this.getMeterSize() === MeterSize.Above63A;
    };

    getMeterSize() {
        return this.moveMeterInformationForm?.get('meterType.meterSize')?.value;
    }

    isMeterTransformerRemarkVisible = () => {
        return this.isMeterTransformerVisible() && this.getMeterTransformer() === METER_TRANSFORMER_OTHER_VALUE_ID;
    };

    getMeterTransformer() {
        return this.moveMeterInformationForm?.get('meterType.meterTransformerId')?.value;
    }

    protected override supplyFormData() {
        this.form.setValue({
            installationInformation: {
                installationAddress: {
                    id: this.formDetails.installationAddress?.carId || '',
                    text: this.formDetails.installationAddress?.formattedAddress || ''
                },
                remarksToInstallation: this.formDetails.remarksToInstallation || '',
                connectionPoint: {
                    id: this.formDetails.connectionPoint?.id || '',
                    connectionPointNumber: this.formDetails.connectionPoint?.connectionPointNumber || ''
                },
                meterFrame: {
                    id: this.formDetails.meterFrame?.id || '',
                    meterFrameNumber: this.formDetails.meterFrame?.meterFrameNumber || ''
                },
                consumptionMeteringPoint: {
                    meteringPointId: this.formDetails.consumptionMeteringPoint?.meteringPointId || '',
                    meteringPointVersionId: this.formDetails.consumptionMeteringPoint?.meteringPointVersionId || ''
                },
                tags: this.formDetails.tags.map((t) => t.id) || []
            },
            contactPerson: {
                contactPersonCompanyName: this.formDetails.contactPerson?.companyName || '',
                contactPersonName: this.formDetails.contactPerson?.name || '',
                contactPersonEmail: this.formDetails.contactPerson?.email || '',
                contactPersonPhoneNumber: this.formDetails.contactPerson?.phoneNumber || ''
            },
            payer: {
                payerName: this.formDetails.payer?.name || '',
                payerEmail: this.formDetails.payer?.email || '',
                payerType: this.formDetails.payer?.type || PayerType.Private,
                payerContactPersonName: this.formDetails.payer?.contactPerson?.name || '',
                payerContactPersonEmail: this.formDetails.payer?.contactPerson?.email || '',
                payerContactPersonPhoneNumber: this.formDetails.payer?.contactPerson?.phoneNumber || '',
                requisition: this.formDetails.payer?.requisition || '',
                cvrOrSeNumber: this.formDetails.payer?.cvrOrSeNumber || '',
                eanNumber: this.formDetails.payer?.eanNumber || '',
                payerAddress: {
                    id: this.formDetails.payer?.address?.carId || '',
                    text: this.formDetails.payer?.address?.formattedAddress || ''
                }
            },
            instructionData: {
                meterResponsible: this.formDetails.instructionData?.meterResponsible || null,
                branchLineResponsible: this.formDetails.instructionData?.branchLineResponsible || null,
                connectionFeeFee: this.formDetails.instructionData?.connectionFee?.fee || null,
                connectionFeeValidUntilEod: this.formDetails.instructionData?.connectionFee?.validUntilEod || null,
                remark: this.formDetails.instructionData?.remark || null
            },
            meterDeliveryOptions: {
                requestedConnectionDateEod: this.formDetails.meterDelivery?.requestedConnectionDateEod || '',
                deliveryOption: this.formDetails.meterDelivery?.deliveryOption || '',
                name: this.formDetails.meterDelivery?.deliveryInformation?.name || '',
                attention: this.formDetails.meterDelivery?.deliveryInformation?.attention || '',
                meterDeliveryAddress: {
                    id: this.formDetails.meterDelivery?.deliveryInformation?.address?.carId || '',
                    text: this.formDetails.meterDelivery?.deliveryInformation?.address?.formattedAddress || ''
                }
            },
            moveMeterInformation: {
                reasonForChange: this.formDetails.moveMeterInformation?.changeReason || '',
                meterType: {
                    meterSize: this.formDetails.moveMeterInformation?.meterType?.meterSize || null,
                    connectionTypeId: this.formDetails.moveMeterInformation?.meterType?.connectionTypeId || null,
                    meterTransformerId: this.formDetails.moveMeterInformation?.meterType?.meterTransformerId || null,
                    meterTransformerRemark: this.formDetails.moveMeterInformation?.meterType?.meterTransformerRemark || null
                },
                meterPlacementId: this.formDetails.moveMeterInformation?.meterPlacementId || '',
                responsibleForSeal: this.formDetails.moveMeterInformation?.responsibleForSeal || ''
            },
            relatedForms: {
                relatedForms: [...this.formDetails.relatedForms],
                hasRelatedFormsChanged: false
            }
        });

        if (this.formDetails.payer) {
            this.isPayerDataVisible = true;
        }

        if (this.formDetails.contactPerson) {
            this.isInstallationContactPersonVisible = true;
        }

        this.initDropDownOptions();
    }

    getFormComparisonModel(paymentDetailsOnly: boolean): any {
        return paymentDetailsOnly
            ? {
                  // Contact person
                  contactPersonCompanyName: this.contactPersonForm.get('contactPersonCompanyName')?.value,
                  contactPersonName: this.contactPersonForm.get('contactPersonName')?.value,
                  contactPersonEmail: this.contactPersonForm.get('contactPersonEmail')?.value,
                  contactPersonPhoneNumber: this.contactPersonForm.get('contactPersonPhoneNumber')?.value,

                  //  Payer
                  payerName: this.getPayerFieldForComparison(this.payerForm.get('payerName')?.value),
                  payerEmail: this.getPayerFieldForComparison(this.payerForm.get('payerEmail')?.value),
                  payerType: this.getPayerFieldForComparison(this.payerForm.get('payerType')?.value, null),
                  payerContactPersonName: this.getPayerFieldForComparison(this.payerForm.get('payerContactPersonName')?.value),
                  payerContactPersonEmail: this.getPayerFieldForComparison(this.payerForm.get('payerContactPersonEmail')?.value),
                  payerContactPersonPhoneNumber: this.getPayerFieldForComparison(
                      this.payerForm.get('payerContactPersonPhoneNumber')?.value
                  ),
                  requisition: this.getPayerFieldForComparison(this.payerForm.get('requisition')?.value),
                  cvrOrSeNumber: this.getPayerFieldForComparison(this.payerForm.get('cvrOrSeNumber')?.value),
                  eanNumber: this.getPayerFieldForComparison(this.payerForm.get('eanNumber')?.value),
                  payerAddress: [
                      this.getPayerFieldForComparison(this.payerForm.get('payerAddress')?.value?.id),
                      this.getPayerFieldForComparison(this.payerForm.get('payerAddress')?.value?.text)
                  ]
              }
            : {
                  installationAddress: [
                      this.installationInformationForm.get('installationAddress')?.value?.id,
                      this.installationInformationForm.get('installationAddress')?.value?.text
                  ],
                  connectionPoint: [
                      this.installationInformationForm.get('connectionPoint')?.value?.id || '',
                      this.installationInformationForm.get('connectionPoint')?.value?.connectionPointNumber || ''
                  ],
                  meterFrame: [
                      this.installationInformationForm.get('meterFrame')?.value?.id || '',
                      this.installationInformationForm.get('meterFrame')?.value?.meterFrameNumber || ''
                  ],
                  consumptionMeteringPoint: [
                      this.consumptionMeteringPoint?.value?.meteringPointId || '',
                      this.consumptionMeteringPoint?.value?.meteringPointVersionId || ''
                  ],
                  contactPersonCompanyName: this.isInstallationContactPersonVisible
                      ? this.contactPersonForm.get('contactPersonCompanyName')?.value
                      : null,
                  contactPersonName: this.isInstallationContactPersonVisible
                      ? this.contactPersonForm.get('contactPersonName')?.value
                      : null,
                  contactPersonEmail: this.isInstallationContactPersonVisible
                      ? this.contactPersonForm.get('contactPersonEmail')?.value
                      : null,
                  contactPersonPhoneNumber: this.isInstallationContactPersonVisible
                      ? this.contactPersonForm.get('contactPersonPhoneNumber')?.value
                      : null,
                  remarksToInstallation: this.installationInformationForm.get('remarksToInstallation')?.value,
                  tags: this.installationInformationForm.get('tags')?.value,

                  payerName: this.getPayerFieldForComparison(this.payerForm.get('payerName')?.value),
                  payerEmail: this.getPayerFieldForComparison(this.payerForm.get('payerEmail')?.value),
                  payerType: this.getPayerFieldForComparison(this.payerForm.get('payerType')?.value),
                  payerContactPersonName: this.getPayerFieldForComparison(this.payerForm.get('payerContactPersonName')?.value),
                  payerContactPersonEmail: this.getPayerFieldForComparison(this.payerForm.get('payerContactPersonEmail')?.value),
                  payerContactPersonPhoneNumber: this.getPayerFieldForComparison(
                      this.payerForm.get('payerContactPersonPhoneNumber')?.value
                  ),
                  requisition: this.getPayerFieldForComparison(this.payerForm.get('requisition')?.value),
                  cvrOrSeNumber: this.getPayerFieldForComparison(this.payerForm.get('cvrOrSeNumber')?.value),
                  eanNumber: this.getPayerFieldForComparison(this.payerForm.get('eanNumber')?.value),
                  payerAddress: [
                      this.getPayerFieldForComparison(this.payerForm.get('payerAddress')?.value?.id),
                      this.getPayerFieldForComparison(this.payerForm.get('payerAddress')?.value?.text)
                  ],

                  // instructionData
                  instructionDataMeterResponsible: this.instructionDataForm.get('meterResponsible')?.value || null,
                  instructionDataBranchLineResponsible: this.instructionDataForm.get('branchLineResponsible')?.value || null,
                  instructionDataConnectionFeeFee: this.instructionDataForm.get('connectionFeeFee')?.value || null,
                  instructionDataConnectionFeeValidUntilEod:
                      this.instructionDataForm.get('connectionFeeValidUntilEod')?.value || null,
                  instructionDataRemark: this.instructionDataForm.get('remark')?.value || null,

                  // meterDeliveryOptions
                  meterDeliveryOptionsRequestedConnectionDateEod:
                      this.meterDeliveryOptionsForm.get('requestedConnectionDateEod')?.value || null,
                  meterDeliveryOptionsDeliveryOption: this.meterDeliveryOptionsForm.get('deliveryOption')?.value || null,
                  meterDeliveryOptionsName: this.meterDeliveryOptionsForm.get('name')?.value || null,
                  meterDeliveryOptionsAttention: this.meterDeliveryOptionsForm.get('attention')?.value || null,
                  meterDeliveryOptionsMeterDeliveryAddress: [
                      this.meterDeliveryOptionsForm.get('meterDeliveryAddress')?.value?.id || null,
                      this.meterDeliveryOptionsForm.get('meterDeliveryAddress')?.value?.text || null
                  ],

                  // Move meter information
                  moveMeterInformationReasonForChange: this.moveMeterInformationForm?.get('reasonForChange')?.value || null,
                  moveMeterInformationMeterTypeMeterSize:
                      this.moveMeterInformationForm?.get('meterType.meterSize')?.value || null,
                  moveMeterInformationMeterTypeConnectionType:
                      this.moveMeterInformationForm?.get('meterType.connectionTypeId')?.value || null,
                  moveMeterInformationMeterTypeMeterTransformerId:
                      this.moveMeterInformationForm?.get('meterType.meterTransformerId')?.value || null,
                  moveMeterInformationMeterTypeMeterTransformerRemark:
                      this.moveMeterInformationForm.get('meterType.meterTransformerRemark')?.value || null,
                  sealInformationMeterPlacementId: this.moveMeterInformationForm?.get('meterPlacementId')?.value || null,
                  sealInformationResponsibleForSeal: this.moveMeterInformationForm?.get('responsibleForSeal')?.value || null,

                  //related forms
                  hasRelatedFormsChanged: this.relatedFormsForm.get('hasRelatedFormsChanged')?.value || false
              };
    }

    convertToComparisonModel(model: MoveMeter, paymentDetailsOnly: boolean): any {
        return paymentDetailsOnly
            ? {
                  // Contact person
                  contactPersonCompanyName: model.contactPerson?.companyName || '',
                  contactPersonName: model.contactPerson?.name || '',
                  contactPersonEmail: model.contactPerson?.email || '',
                  contactPersonPhoneNumber: model.contactPerson?.phoneNumber || '',

                  //  Payer
                  payerName: model.payer?.name || '',
                  payerType: model.payer?.type || '',
                  payerEmail: model.payer?.email || '',
                  payerContactPersonName: model.payer?.contactPerson?.name || '',
                  payerContactPersonEmail: model.payer?.contactPerson?.email || '',
                  payerContactPersonPhoneNumber: model.payer?.contactPerson?.phoneNumber || '',
                  requisition: model.payer?.requisition || '',
                  cvrOrSeNumber: model.payer?.cvrOrSeNumber || '',
                  eanNumber: model.payer?.eanNumber || '',
                  payerAddress: [model.payer?.address?.carId || '', model.payer?.address?.formattedAddress || '']
              }
            : {
                  installationAddress: [
                      model.installationAddress?.carId || '',
                      model.installationAddress?.formattedAddress || ''
                  ],
                  connectionPoint: [model.connectionPoint?.id || '', model.connectionPoint?.connectionPointNumber || ''],
                  meterFrame: [model.meterFrame?.id || '', model.meterFrame?.meterFrameNumber || ''],
                  consumptionMeteringPoint: [
                      model.consumptionMeteringPoint?.meteringPointId || '',
                      model.consumptionMeteringPoint?.meteringPointVersionId || ''
                  ],
                  contactPersonCompanyName: model.contactPerson?.companyName || '',
                  contactPersonName: model.contactPerson?.name || '',
                  contactPersonEmail: model.contactPerson?.email || '',
                  contactPersonPhoneNumber: model.contactPerson?.phoneNumber || '',
                  remarksToInstallation: model.remarksToInstallation || '',
                  tags: model.tags.map((t) => t.id),

                  //  Payer
                  payerName: model.payer?.name || '',
                  payerType: model.payer?.type || '',
                  payerEmail: model.payer?.email || '',
                  payerContactPersonName: model.payer?.contactPerson?.name || '',
                  payerContactPersonEmail: model.payer?.contactPerson?.email || '',
                  payerContactPersonPhoneNumber: model.payer?.contactPerson?.phoneNumber || '',
                  requisition: model.payer?.requisition || '',
                  cvrOrSeNumber: model.payer?.cvrOrSeNumber || '',
                  eanNumber: model.payer?.eanNumber || '',
                  payerAddress: [model.payer?.address?.carId || '', model.payer?.address?.formattedAddress || ''],

                  // instructionData
                  instructionDataMeterResponsible: model.instructionData?.meterResponsible || null,
                  instructionDataBranchLineResponsible: model.instructionData?.branchLineResponsible || null,
                  instructionDataConnectionFeeFee: model.instructionData?.connectionFee?.fee || null,
                  instructionDataConnectionFeeValidUntilEod: model.instructionData?.connectionFee?.validUntilEod || null,
                  instructionDataRemark: model.instructionData?.remark || null,

                  // meterDeliveryOptions
                  meterDeliveryOptionsRequestedConnectionDateEod: model.meterDelivery?.requestedConnectionDateEod || null,
                  meterDeliveryOptionsDeliveryOption: model.meterDelivery?.deliveryOption || null,
                  meterDeliveryOptionsName: model.meterDelivery?.deliveryInformation?.name || null,
                  meterDeliveryOptionsAttention: model.meterDelivery?.deliveryInformation?.attention || null,
                  meterDeliveryOptionsMeterDeliveryAddress: [
                      model.meterDelivery?.deliveryInformation?.address?.carId || null,
                      model.meterDelivery?.deliveryInformation?.address?.formattedAddress || null
                  ],

                  // // Move meter information
                  moveMeterInformationReasonForChange: model.moveMeterInformation?.changeReason || null,
                  moveMeterInformationMeterTypeMeterSize: model.moveMeterInformation?.meterType?.meterSize || null,
                  moveMeterInformationMeterTypeConnectionType: model.moveMeterInformation?.meterType?.connectionTypeId || null,
                  moveMeterInformationMeterTypeMeterTransformerId:
                      model.moveMeterInformation?.meterType?.meterTransformerId || null,
                  moveMeterInformationMeterTypeMeterTransformerRemark:
                      model.moveMeterInformation?.meterType?.meterTransformerRemark || null,
                  sealInformationMeterPlacementId: model.moveMeterInformation?.meterPlacementId || null,
                  sealInformationResponsibleForSeal: model.moveMeterInformation?.responsibleForSeal || null,

                  // related forms
                  hasRelatedFormsChanged: false
              };
    }

    protected override updateFormData(
        installationFormId: string,
        es_message_id: string,
        row_version: string,
        body: MoveMeterUpdate
    ): Observable<InstallationFormsApiResponse<void>> {
        return this.client.updateMoveMeterFormData(installationFormId, es_message_id, row_version, body);
    }

    protected override createFormDataUpdate(): MoveMeterUpdate {
        return new MoveMeterUpdate({
            installationInformationUpdate: this.createInstallationInformationUpdate(),
            payerUpdate: this.isPayerDataVisible ? this.createPayerUpdate() : undefined,
            meterDeliveryOptionsUpdate: this.createMeterDeliveryOptionsUpdate(),
            instructionDataUpdate: this.createInstructionDataUpdate(),
            moveMeterInformationUpdate: this.createMoveMeterInformationUpdate(),
            relatedFormsUpdate: this.relatedFormsForm
                .get('relatedForms')
                ?.value.filter((x: RelatedForm) => x.relationType === FormsRelationType.Manual)
                .map((x: RelatedForm) => new RelatedFormUpdate({ formId: x.formId }))
        });
    }

    protected override createPaymentDetailsUpdate(): PaymentDetailsUpdate {
        const update = new PaymentDetailsUpdate({
            contactPerson: this.isInstallationContactPersonVisible ? this.createContactPersonUpdate() : undefined,
            payer: this.isPayerDataVisible ? this.createPayerUpdate() : undefined
        });
        return update;
    }

    private createContactPersonUpdate(): ContactPerson {
        const contactPersonFormValue = this.contactPersonForm.value;
        return new ContactPerson({
            companyName: getValueOrDefault(contactPersonFormValue.contactPersonCompanyName),
            name: getValueOrDefault(contactPersonFormValue.contactPersonName),
            email: getValueOrDefault(contactPersonFormValue.contactPersonEmail),
            phoneNumber: getValueOrDefault(contactPersonFormValue.contactPersonPhoneNumber)
        });
    }

    private createInstallationInformationUpdate(): InstallationInformationUpdate {
        const installationInformationFormValue = this.installationInformationForm.value;
        const contactPersonFormValue = this.contactPersonForm.value;
        return new InstallationInformationUpdate({
            contactPerson: this.isInstallationContactPersonVisible
                ? new ContactPerson({
                      companyName: getValueOrDefault(contactPersonFormValue.contactPersonCompanyName),
                      name: getValueOrDefault(contactPersonFormValue.contactPersonName),
                      email: getValueOrDefault(contactPersonFormValue.contactPersonEmail),
                      phoneNumber: getValueOrDefault(contactPersonFormValue.contactPersonPhoneNumber)
                  })
                : undefined,
            installationInformationCarId: getValueOrDefault(installationInformationFormValue.installationAddress?.id),
            remarksToInstallation: getValueOrDefault(installationInformationFormValue.remarksToInstallation),
            connectionPointId: getValueOrDefault(installationInformationFormValue.connectionPoint?.id),
            meterFrameId: getValueOrDefault(installationInformationFormValue.meterFrame?.id),
            consumptionMeteringPoint: createMeteringPoint(installationInformationFormValue.consumptionMeteringPoint),
            tags: installationInformationFormValue.tags || []
        });
    }

    private createPayerUpdate(): PayerUpdate {
        const payerFormValue = this.payerForm.value;
        return new PayerUpdate({
            contactPerson: new ContactPerson({
                email: getValueOrDefault(payerFormValue.payerContactPersonEmail),
                name: getValueOrDefault(payerFormValue.payerContactPersonName),
                phoneNumber: getValueOrDefault(payerFormValue.payerContactPersonPhoneNumber)
            }),
            payerType: payerFormValue.payerType,
            payerCarId: getValueOrDefault(payerFormValue.payerAddress?.id),
            name: getValueOrDefault(payerFormValue.payerName),
            email: getValueOrDefault(payerFormValue.payerEmail),
            requisition: getValueOrDefault(payerFormValue.requisition),
            cvrOrSeNumber: getValueOrDefault(payerFormValue.cvrOrSeNumber),
            eanNumber: getValueOrDefault(payerFormValue.eanNumber)
        });
    }

    private createInstructionDataUpdate(): InstructionDataUpdate {
        const instructionDataFormValue = this.instructionDataForm.value;

        return new InstructionDataUpdate({
            meterResponsible: instructionDataFormValue.meterResponsible as MeterResponsible,
            branchLineResponsible: instructionDataFormValue.branchLineResponsible as BranchLineResponsible,
            connectionFee: !!instructionDataFormValue.connectionFeeFee
                ? new ConnectionFee({
                      currency: 'DKK', // So far there's no support for different currencies. Hardcoded DKK
                      fee: instructionDataFormValue.connectionFeeFee,
                      validUntilEod: instructionDataFormValue.connectionFeeValidUntilEod
                  })
                : undefined,
            remark: getValueOrDefault(instructionDataFormValue.remark)
        });
    }

    private createMeterDeliveryOptionsUpdate(): MeterDeliveryOptionsUpdate {
        const meterDeliveryOptionsFormValue = this.meterDeliveryOptionsForm.value;
        return new MeterDeliveryOptionsUpdate({
            requestedConnectionDateEod: meterDeliveryOptionsFormValue.requestedConnectionDateEod,
            deliveryOption: meterDeliveryOptionsFormValue.deliveryOption,
            deliveryInformation: this.shouldShowDelivery()
                ? new DeliveryInformationUpdate({
                      name: getValueOrDefault(meterDeliveryOptionsFormValue.name),
                      attention: getValueOrDefault(meterDeliveryOptionsFormValue.attention),
                      deliveryInformationCarId: getValueOrDefault(meterDeliveryOptionsFormValue.meterDeliveryAddress?.id)
                  })
                : undefined
        });
    }

    private createMoveMeterInformationUpdate(): MoveMeterInformationUpdate {
        const moveMeterInformationFormValue = this.moveMeterInformationForm.value;

        return new MoveMeterInformationUpdate({
            changeReason: moveMeterInformationFormValue.reasonForChange as MoveMeterChangeReason,
            meterType: new MeterTypeUpdate({
                meterSize: moveMeterInformationFormValue.meterType.meterSize,
                connectionTypeId: moveMeterInformationFormValue.meterType.connectionTypeId,
                meterTransformerId: moveMeterInformationFormValue.meterType.meterTransformerId,
                meterTransformerRemark: moveMeterInformationFormValue.meterType.meterTransformerRemark
            }),
            meterPlacementId: moveMeterInformationFormValue.meterPlacementId,
            responsibleForSeal: moveMeterInformationFormValue.responsibleForSeal
        });
    }

    shouldShowDelivery = () => {
        return this.form?.get('meterDeliveryOptions.deliveryOption')?.value === DeliveryOption.InstallerWillHandleSendMeter;
    };
}
