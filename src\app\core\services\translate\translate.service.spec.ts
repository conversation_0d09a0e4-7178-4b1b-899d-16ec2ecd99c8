import { TestBed } from '@angular/core/testing';
import { TranslateService } from '@ngx-translate/core';
import { MockService } from 'ng-mocks';
import { TranslateServiceHelper } from './translate.service';

describe('TranslateServiceHelper', () => {
    let service: TranslateServiceHelper;
    let translateService: TranslateService;

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [TranslateServiceHelper, { provide: TranslateService, useValue: MockService(TranslateService) }]
        });

        service = TestBed.inject(TranslateServiceHelper);
        translateService = TestBed.inject(TranslateService);
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    describe('getTranslationOrNull', () => {
        it('should return translation if key exists', () => {
            const translationKey = 'common.hello';
            const translationValue = 'Hello';
            jest.spyOn(service, 'hasTranslation').mockReturnValue(true);
            jest.spyOn(translateService, 'instant').mockReturnValue(translationValue);

            const result = service.getTranslationOrNull(translationKey);

            expect(result).toBe(translationValue);
            expect(translateService.instant).toHaveBeenCalledWith(translationKey, undefined);
        });

        it('should return null if key does not exist', () => {
            const translationKey = 'common.hello';
            jest.spyOn(service, 'hasTranslation').mockReturnValue(false);
            jest.spyOn(translateService, 'instant').mockReturnValue(null);

            const result = service.getTranslationOrNull(translationKey);

            expect(result).toBeNull();
            expect(translateService.instant).not.toHaveBeenCalled();
        });
    });

    describe('hasTranslation', () => {
        it('should return true if translation exists', () => {
            const translationKey = 'common.hello';
            translateService.translations = { en: { common: { hello: 'Hello' } } };
            translateService.currentLang = 'en';

            const result = service.hasTranslation(translationKey);

            expect(result).toBe(true);
        });

        it('should return false if translation does not exist', () => {
            const translationKey = 'common.hello';
            translateService.translations = { en: { common: {} } };
            translateService.currentLang = 'en';

            const result = service.hasTranslation(translationKey);

            expect(result).toBe(false);
        });
    });
});
