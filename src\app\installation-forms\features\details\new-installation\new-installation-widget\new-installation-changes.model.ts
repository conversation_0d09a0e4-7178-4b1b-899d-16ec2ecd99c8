import { ChangeField, ChangeFieldType } from 'src/app/core/constants/changes-details';

export class NewInstallationChanges {
    installationAddress: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };
    gridAreaId: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    connectionPoint: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };
    meterFrame: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };
    consumptionMeteringPoint: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };
    contactPersonCompanyName: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    contactPersonName: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    contactPersonEmail: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    contactPersonPhoneNumber: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    remarksToInstallation: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    tags: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };

    payerName: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    payerType: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    payerEmail: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    payerContactPersonName: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    payerContactPersonEmail: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    payerContactPersonPhoneNumber: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    payerAddress: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };
    requisition: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    cvrOrSeNumber: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    eanNumber: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };

    voltageLevelId: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    voltageLevelCurrentTransformerId: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    voltageLevelVoltageTransformerId: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };

    meterPlacementId: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    installInConstructionPhase: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    meterSize: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    connectionTypeId: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    meterTransformerId: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    meterTransformerRemark: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    transformerStationNumber: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    cabinetNumber: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    groundingMethod: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    fuseSizeOfPreProtection: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    protectionTypeOfPreProtection: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    phaseCount: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    type: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    branchLineMeterFrameId: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    fuseTypeOfMainProtection: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    fuseSizeOfMainProtection: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    protectionTypeOfMainProtection: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    numberOfPairs: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    protectiveEarthingDimensionId: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    cableDimensionId: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    containerId: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    nodeId: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    gisStatus: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    gisDescription: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };

    // instructionData
    instructionDataMeterResponsible: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    instructionDataBranchLineResponsible: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    instructionDataConnectionFeeFee: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    instructionDataConnectionFeeValidUntilEod: ChangeField = {
        change: false,
        type: ChangeFieldType.Date,
        dtoFieldName: undefined
    };
    instructionDataRemark: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };

    // meterDeliveryOptions
    meterDeliveryOptionsRequestedConnectionDateEod: ChangeField = {
        change: false,
        type: ChangeFieldType.Date,
        dtoFieldName: undefined
    };
    meterDeliveryOptionsDeliveryOption: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    meterDeliveryOptionsName: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    meterDeliveryOptionsAttention: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    meterDeliveryOptionsMeterDeliveryAddress: ChangeField = {
        change: false,
        type: ChangeFieldType.Array,
        dtoFieldName: undefined
    };

    //usage
    hasApplicationsChanged: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };

    // related forms
    hasRelatedFormsChanged: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };

    // connection rights
    scopeOfDeliverySize: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    hasTransfersChanged: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
}
