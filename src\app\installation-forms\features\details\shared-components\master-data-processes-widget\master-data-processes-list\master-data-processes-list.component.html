<ng-container *ngIf="masterDataProcesses && masterDataProcesses.length; else emptyMessage">
    <p-table
        [columns]="columns"
        [value]="masterDataProcesses"
        [rowHover]="true"
        responsiveLayout="scroll"
        [paginator]="masterDataProcesses.length > rowsPerPage"
        [rows]="rowsPerPage"
        [loading]="loading"
        [customSort]="false"
        [sortField]="'masterDataProcessStarted'"
        [sortOrder]="-1">
        <ng-template pTemplate="header" let-columns>
            <tr>
                <th *ngFor="let col of columns" [pSortableColumn]="col" [id]="col">
                    <div fxLayout="row">
                        {{ 'masterDataProcessesWidget.columnNames.' + col | translate }}
                    </div>
                </th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rowData let-columns="columns">
            <tr class="zebra-item" (click)="handleRowClick(rowData)">
                <td>
                    {{ 'enums.masterDataProcessType.' + rowData['masterDataProcessType'] | translate }}
                </td>
                <td>
                    <app-master-data-process-status [masterDataProcessStatus]="rowData['state']"></app-master-data-process-status>
                </td>
                <td>{{ rowData['masterDataProcessStarted'] | formatDateTime }}</td>
            </tr>
        </ng-template>
        <ng-template pTemplate="paginatorright"> </ng-template>
    </p-table>
</ng-container>

<ng-template #emptyMessage>
    <p>{{ 'masterDataProcessesWidget.noMasterDataProcessesFound' | translate }}</p>
</ng-template>
