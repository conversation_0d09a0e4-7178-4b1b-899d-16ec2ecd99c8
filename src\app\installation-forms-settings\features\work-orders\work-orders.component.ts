import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AuthorizationService } from '@kmd-elements-ui/authorization';
import {
    KeyActions,
    KeyboardShortcuts,
    KeyboardShortcutsService,
    KeyboardShortcutsSubscriptionManager
} from '@kmd-elements-ui/shortcuts';
import { TabsService } from '@kmd-elements-ui/tabs';
import { TranslateService } from '@ngx-translate/core';
import { TableRowReorderEvent } from 'primeng/table';
import { BehaviorSubject, Subscription, filter, finalize, of, switchMap, tap } from 'rxjs';
import {
    AutomaticWorkOrderRule,
    InstallationFormsClient,
    ReorderModel,
    ValueListType,
    WorkOrderType
} from 'src/app/api/installation-forms-client';
import { Permissions } from 'src/app/core/constants/permissions';
import { v4 as uuidv4 } from 'uuid';
import { MessageServiceHelper } from '../../../core/services/message/message.service';
import { ValueListsService } from '../../../core/services/value-lists/value-lists.service';
import { RulesListColumn } from '../models/rules-list-column';
import { WORK_ORDERS_COLUMNS } from './constants/columns';
import { WorkOrdersCreateComponent } from './work-orders-create/work-orders-create.component';
import { WorkOrdersEditComponent } from './work-orders-edit/work-orders-edit.component';
import { workOrderMarkdownDaDK, workOrderMarkdownEnUs, workOrderTabName, workOrderTranslationPath } from './work-orders.consts';

@Component({
    selector: 'app-work-orders',
    templateUrl: './work-orders.component.html',
    styleUrls: ['./work-orders.component.scss'],
    standalone: false
})
export class WorkOrdersComponent implements KeyboardShortcutsSubscriptionManager, OnInit, OnDestroy {
    private widgetTranslations: any;

    private _isCreating: boolean = false;
    private _ruleInEdit?: AutomaticWorkOrderRule;
    private _isReordered: boolean = false;

    get isCreating(): boolean {
        return this._isCreating;
    }

    set isCreating(value: boolean) {
        this._isCreating = value;
        this.recalculateDirty();
    }

    get ruleInEdit(): AutomaticWorkOrderRule | undefined {
        return this._ruleInEdit;
    }

    set ruleInEdit(value: AutomaticWorkOrderRule | undefined) {
        this._ruleInEdit = value;
        this.recalculateDirty();
    }

    get isReordered(): boolean {
        return this._isReordered;
    }

    set isReordered(value: boolean) {
        this._isReordered = value;
        this.recalculateDirty();
    }

    workOrderRules: AutomaticWorkOrderRule[] = [];
    originalRuleOrder: string[] = [];
    subscription: Subscription = new Subscription();

    workOrderMarkdownEnUs = workOrderMarkdownEnUs;
    workOrderMarkdownDaDK = workOrderMarkdownDaDK;

    isProcessing: boolean = false;

    dirty$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

    workOrderTabName = workOrderTabName;

    hasUserFormConfigurationWritePermission: boolean = false;

    private _tabId?: string;

    columns: RulesListColumn[] = WORK_ORDERS_COLUMNS;

    private keyEventsSubscription: Subscription = new Subscription();

    @ViewChild('workOrdersCreate') workOrdersCreate?: WorkOrdersCreateComponent;
    @ViewChild('workOrdersEdit') workOrdersEdit?: WorkOrdersEditComponent;

    keyActions: KeyActions = {
        [KeyboardShortcuts.ShiftPlusN]: () => {
            this.startCreating();
        },
        [KeyboardShortcuts.Escape]: () => {
            this.tabsService.close();
        },
        [KeyboardShortcuts.Enter]: () => {
            if (this.ruleInEdit) {
                this.workOrdersEdit?.saveEditedClick();
                return;
            }

            if (this.isCreating) {
                this.workOrdersCreate?.createClick();
                return;
            }

            if (this.isReordered) {
                this.saveReorder();
                return;
            }
        }
    };

    attemptedAction: (() => void) | null = null;
    private ruleToDelete?: AutomaticWorkOrderRule;
    public alreadyUsedDisplayNames: string[] = [];

    constructor(
        private readonly client: InstallationFormsClient,
        private readonly translateService: TranslateService,
        private readonly messageServiceHelper: MessageServiceHelper,
        protected tabsService: TabsService,
        private readonly activatedRoute: ActivatedRoute,
        private readonly authService: AuthorizationService,
        private readonly keyboardShortcutsService: KeyboardShortcutsService,
        private readonly valueListsService: ValueListsService
    ) {
        this.addTab();
    }

    ngOnInit(): void {
        this.widgetTranslations = this.translateService.instant(workOrderTranslationPath);

        this.authService
            .hasPermissions([Permissions.formConfiguration.write])
            .subscribe((x) => (this.hasUserFormConfigurationWritePermission = x));

        this.registerRefreshButtonListener();
        this.getWorkOrderRules();
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    subscribeKeyShortcuts(): void {
        this.keyEventsSubscription = this.keyboardShortcutsService.registerKeyboardShortcuts(this.keyActions).subscribe();
    }

    unsubscribeKeyShortcuts(): void {
        this.keyEventsSubscription.unsubscribe();
    }

    getWorkOrderRules() {
        this.isProcessing = true;
        this.subscription.add(
            this.client
                .getAutomaticWorkOrderRules(uuidv4())
                .pipe(finalize(() => (this.isProcessing = false)))
                .subscribe({
                    next: (response) => {
                        this.workOrderRules = response.result.sort((a, b) => a.order - b.order);
                        this.originalRuleOrder = this.workOrderRules.map((x) => x.id);
                        this.alreadyUsedDisplayNames = this.workOrderRules.map((x) => x.displayName);
                    },
                    error: (_error) => {
                        this.messageServiceHelper.showError({
                            key: workOrderTabName,
                            detail: this.widgetTranslations['getWorkOrderRulesError']
                        });
                    }
                })
        );
    }

    protected deleteRule(event: MouseEvent, rule: AutomaticWorkOrderRule) {
        event.stopPropagation();
        this.ruleToDelete = rule;
        this.attemptedAction = () => this.performDelete();
    }

    private performDelete() {
        if (!this.ruleToDelete) {
            return;
        }

        this.isProcessing = true;
        this.subscription.add(
            this.client
                .deleteAutomaticWorkOrderRuleById(this.ruleToDelete.id, uuidv4())
                .pipe(
                    finalize(() => {
                        this.isProcessing = false;
                        this.ruleToDelete = undefined;
                    })
                )
                .subscribe({
                    complete: () => {
                        this.messageServiceHelper.showSuccess({
                            key: workOrderTabName
                        });
                        this.workOrderRules = this.workOrderRules.filter((r) => r.id !== this.ruleToDelete?.id);
                        this.originalRuleOrder = this.originalRuleOrder.filter((id) => id !== this.ruleToDelete?.id);
                        if (this.workOrderRules.length === 0) {
                            this.isReordered = false;
                        }

                        this.alreadyUsedDisplayNames = this.alreadyUsedDisplayNames.filter(
                            (name) => name !== this.ruleToDelete?.displayName
                        );
                    },
                    error: (_error) => {
                        this.messageServiceHelper.showError({
                            key: workOrderTabName,
                            detail: this.widgetTranslations['workOrderRuleDeleteError']
                        });
                    }
                })
        );
    }

    onConfirmationDialogYesButtonClick() {
        if (this.attemptedAction) {
            this.attemptedAction();
        }
        this.attemptedAction = null;
    }

    onConfirmationDialogNoButtonClick() {
        this.attemptedAction = null;
        this.ruleToDelete = undefined;
    }

    protected editRule(event: MouseEvent, rule: AutomaticWorkOrderRule) {
        event.stopPropagation();
        this.ruleInEdit = rule;
        this.isCreating = false;
    }

    protected startCreating() {
        this.isCreating = true;
        this.ruleInEdit = undefined;
    }

    protected cancelCreating() {
        this.isCreating = false;
    }

    protected cancelEditing() {
        this.ruleInEdit = undefined;
    }

    protected onRuleCreated(rule: AutomaticWorkOrderRule) {
        this.isCreating = false;
        this.workOrderRules.push(rule);
        this.originalRuleOrder.push(rule.id);
        this.alreadyUsedDisplayNames.push(rule.displayName);
    }

    protected onRuleEdited(updatedRule: AutomaticWorkOrderRule) {
        const editedIndex = this.workOrderRules.findIndex((r) => r.id === updatedRule.id);
        if (editedIndex === -1) {
            return;
        }

        this.alreadyUsedDisplayNames.splice(
            this.alreadyUsedDisplayNames.indexOf(this.ruleInEdit!.displayName),
            1,
            updatedRule.displayName
        );
        this.workOrderRules[editedIndex] = updatedRule;
        this.ruleInEdit = undefined;
    }

    private addTab() {
        this._tabId = this.tabsService.addOrUpdateTab(this.activatedRoute, {
            icon$: of('fas fa-th-list'),
            areaLabel$: of(this.translateService.instant(`${workOrderTranslationPath}.tabAreaLabel`)),
            label$: of(this.translateService.instant(`${workOrderTranslationPath}.tabLabel`)),
            tooltip$: of(this.translateService.instant(`${workOrderTranslationPath}.tabLabel`)),
            dirty$: this.dirty$,
            canReload$: of(true)
        });
    }

    registerRefreshButtonListener(): void {
        this.subscription.add(
            this.tabsService.reloadButtonClicked$
                .pipe(
                    filter((tabId) => tabId === this._tabId),
                    switchMap((tabId) => this.tabsService.reload(tabId)),
                    filter((canBeReloaded) => canBeReloaded),
                    tap(() => {
                        this.getWorkOrderRules();
                        this.valueListsService.fetchValueLists();
                    })
                )
                .subscribe(() => {
                    this.tabsService.tabReloaded(this._tabId as string);
                    this.undoChanges();
                })
        );
    }

    undoChanges() {
        this.cancelCreating();
        this.cancelEditing();
        this.cancelReorder();
    }

    onRowReorder(_: TableRowReorderEvent) {
        this.setIsReordered();
    }

    setIsReordered() {
        this.isReordered = !this.workOrderRules.every((rule, index) => rule.id === this.originalRuleOrder[index]);
    }

    cancelReorder() {
        this.workOrderRules = this.originalRuleOrder.map((id) => this.workOrderRules.find((rule) => rule.id === id)!);
        this.setIsReordered();
    }

    saveReorder() {
        this.isProcessing = true;
        this.subscription.add(
            this.client
                .reorderAutomaticWorkOrderRules(
                    uuidv4(),
                    new ReorderModel({
                        reorderedIds: this.workOrderRules.map((x) => x.id)
                    })
                )
                .pipe(finalize(() => (this.isProcessing = false)))
                .subscribe({
                    complete: () => {
                        this.messageServiceHelper.showSuccess({
                            key: workOrderTabName
                        });
                        this.originalRuleOrder = this.workOrderRules.map((x) => x.id);
                        this.isReordered = false;
                    },
                    error: (_error) => {
                        this.messageServiceHelper.showError({
                            key: workOrderTabName,
                            detail: this.widgetTranslations['workOrderRuleReorderError']
                        });
                    }
                })
        );
    }

    recalculateDirty() {
        this.dirty$.next(this.isCreating || this.isReordered || !!this.ruleInEdit);
    }

    // Supply type will be included
    getValueListType(colName: string, workOrderType: WorkOrderType): ValueListType | undefined {
        switch (colName) {
            case 'workOrderDescriptionId':
                if (workOrderType === WorkOrderType.General) {
                    return ValueListType.WorkOrderGeneralElectricityWorkDescriptionList;
                }
                if (workOrderType === WorkOrderType.InstallMeter) {
                    return ValueListType.WorkOrderInstallMeterElectricityWorkDescriptionList;
                }
                if (workOrderType === WorkOrderType.ReplaceMeter) {
                    return ValueListType.WorkOrderReplaceMeterElectricityWorkDescriptionList;
                }
                if (workOrderType === WorkOrderType.RemoveMeter) {
                    return ValueListType.WorkOrderRemoveMeterElectricityWorkDescriptionList;
                }
                return undefined;
            case 'workOrderPurposeId':
                return ValueListType.WorkOrderElectricityPurposeList;
            default:
                return undefined;
        }
    }
}
