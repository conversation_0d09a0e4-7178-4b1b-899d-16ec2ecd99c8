import { Component } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { finalize } from 'rxjs/operators';
import {
    AutomaticWorkOrderRule,
    AutomaticWorkOrderRuleCreateOrUpdate,
    FormState,
    FormType,
    InstallationFormsClient,
    ResponsibleForMeter,
    ResponsibleForSeal,
    ValueListType,
    WorkOrderType
} from 'src/app/api/installation-forms-client';
import { SETTINGS_DISPLAY_NAME_MAX_LENGTH } from 'src/app/core/constants/constants';
import { BooleanOptionsService } from 'src/app/core/services/boolean-options/boolean-options.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { ValueListsService } from 'src/app/core/services/value-lists/value-lists.service';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import { mapWorkOrderTypeToWorkDescriptionValueList } from 'src/app/core/utils/mappers/work-orders/work-order-type-to-value-list.mapper';
import { v4 as uuidv4 } from 'uuid';
import { isAnyOfRequiredFormTypesSelected } from '../../../../core/utils/form-type-checkers';
import { BaseRuleEditComponent } from '../../base-components/base-rule-edit.component';
import {
    INVOICE_PAID_FORM_TYPES,
    METER_NEEDS_CHANGE_FORM_TYPES,
    METER_READY_FORM_TYPES,
    METER_RESPONSIBLE_FORM_TYPES,
    SEAL_RESPONSIBLE_FORM_TYPES,
    STARTS_AS_CONSTRUCTION_FORM_TYPES,
    SUPPLIER_SELECTED_FORM_TYPES
} from '../constants/properties-per-form-types';
import { workOrderTabName, workOrderTranslationPath } from '../work-orders.consts';

@Component({
    selector: 'app-work-orders-edit',
    templateUrl: './work-orders-edit.component.html',
    standalone: false
})
export class WorkOrdersEditComponent extends BaseRuleEditComponent<AutomaticWorkOrderRule> {
    formTypeOptions: SelectItem[] = [];
    formStateOptions: SelectItem[] = [];
    workOrderTypeOptions: SelectItem[] = [];
    responsibleForSealOptions: SelectItem[] = [];
    responsibleForMeterOptions: SelectItem[] = [];

    constructor(
        private readonly client: InstallationFormsClient,
        private readonly fb: FormBuilder,
        protected readonly translateService: TranslateService,
        protected readonly messageServiceHelper: MessageServiceHelper,
        protected readonly valueListsService: ValueListsService,
        protected readonly booleanOptionsService: BooleanOptionsService
    ) {
        super(translateService, messageServiceHelper, workOrderTabName, workOrderTranslationPath);
        this.initForm();
        this.addFormControlsValueChanges();
    }

    override initForm() {
        this.form = this.fb.group({
            id: [null],
            displayName: [null, [Validators.required, Validators.maxLength(SETTINGS_DISPLAY_NAME_MAX_LENGTH)]],
            formTypes: [null, Validators.required],
            formStates: [null, Validators.required],
            sealResponsibles: [[]],
            meterResponsibles: [[]],
            meterNeedsChange: [null],
            startsAsConstruction: [null],
            invoicePaid: [null],
            readyForMeter: [null],
            supplierSelected: [null],
            workOrderType: [null, Validators.required],
            workOrderDescriptionId: [null, Validators.required],
            workOrderPurposeId: [null, Validators.required]
        });
    }

    override getEnumTranslations() {
        this.formTypeOptions = enumMapper.map(this.translateService.instant('enums.type'), FormType);
        this.formStateOptions = enumMapper.map(this.translateService.instant('enums.state'), [
            FormState.Registered,
            FormState.Instructed,
            FormState.Completed
        ]);
        this.workOrderTypeOptions = enumMapper.map(this.translateService.instant('enums.workOrderType'), WorkOrderType);
        this.responsibleForSealOptions = enumMapper.map(
            this.translateService.instant('enums.responsibleForSeal'),
            ResponsibleForSeal
        );
        this.responsibleForMeterOptions = enumMapper.map(
            this.translateService.instant('enums.responsibleForMeter'),
            ResponsibleForMeter
        );
    }

    override setFormValue(rule: AutomaticWorkOrderRule) {
        this.form.patchValue({
            id: rule.id,
            displayName: rule.displayName,
            formTypes: rule.formTypes,
            formStates: rule.formStates,
            sealResponsibles: rule.sealResponsibles,
            meterResponsibles: rule.meterResponsibles,
            meterNeedsChange: rule.meterNeedsChange,
            startsAsConstruction: rule.startsAsConstruction,
            invoicePaid: rule.invoicePaid,
            readyForMeter: rule.readyForMeter,
            supplierSelected: rule.supplierSelected,
            workOrderType: rule.workOrderType,
            workOrderDescriptionId: rule.workOrderDescriptionId,
            workOrderPurposeId: rule.workOrderPurposeId
        });
    }

    override saveEditedClick() {
        if (this.form.invalid) {
            return;
        }

        const formValue = this.form.value;
        const rule = new AutomaticWorkOrderRuleCreateOrUpdate({
            displayName: formValue.displayName,
            formTypes: formValue.formTypes,
            formStates: formValue.formStates,
            sealResponsibles: formValue.sealResponsibles,
            meterResponsibles: formValue.meterResponsibles,
            meterNeedsChange: formValue.meterNeedsChange,
            startsAsConstruction: formValue.startsAsConstruction,
            invoicePaid: formValue.invoicePaid,
            readyForMeter: formValue.readyForMeter,
            supplierSelected: formValue.supplierSelected,
            workOrderType: formValue.workOrderType,
            workOrderDescriptionId: formValue.workOrderDescriptionId,
            workOrderPurposeId: formValue.workOrderPurposeId
        });

        this.isProcessing = true;
        this.subscription.add(
            this.client
                .updateAutomaticWorkOrderRuleById(formValue.id, uuidv4(), rule)
                .pipe(finalize(() => (this.isProcessing = false)))
                .subscribe({
                    next: (response) => {
                        this.ruleEdited.emit(response.result);
                        this.showSuccessMessage();
                    },
                    error: (_) => {
                        this.showErrorMessage(this.widgetTranslations['workOrderRuleEditError']);
                    }
                })
        );
    }

    isSealResponsiblesRequired = (): boolean => {
        const formTypes = this.form?.get('formTypes')?.value || [];
        return isAnyOfRequiredFormTypesSelected(formTypes, SEAL_RESPONSIBLE_FORM_TYPES);
    };

    isMeterResponsiblesRequired = (): boolean => {
        const formTypes = this.form?.get('formTypes')?.value || [];
        return isAnyOfRequiredFormTypesSelected(formTypes, METER_RESPONSIBLE_FORM_TYPES);
    };

    isMeterNeedsChangeRequired = (): boolean => {
        const formTypes = this.form?.get('formTypes')?.value || [];
        return isAnyOfRequiredFormTypesSelected(formTypes, METER_NEEDS_CHANGE_FORM_TYPES);
    };

    isSupplierSelectedRequired = (): boolean => {
        const formTypes = this.form?.get('formTypes')?.value || [];
        return isAnyOfRequiredFormTypesSelected(formTypes, SUPPLIER_SELECTED_FORM_TYPES);
    };

    isReadyForMeterRequired = (): boolean => {
        const formTypes = this.form?.get('formTypes')?.value || [];
        return isAnyOfRequiredFormTypesSelected(formTypes, METER_READY_FORM_TYPES);
    };

    isInvoicePaidRequired = (): boolean => {
        const formTypes = this.form?.get('formTypes')?.value || [];
        return isAnyOfRequiredFormTypesSelected(formTypes, INVOICE_PAID_FORM_TYPES);
    };

    isStartsAsConstructionRequired = (): boolean => {
        const formTypes = this.form?.get('formTypes')?.value || [];
        return isAnyOfRequiredFormTypesSelected(formTypes, STARTS_AS_CONSTRUCTION_FORM_TYPES);
    };

    isWorkOrderTypeSelected = (): boolean => {
        return this.form?.get('workOrderType')?.value !== null;
    };

    addFormControlsValueChanges() {
        this.subscription.add(
            this.form.get('formTypes')?.valueChanges.subscribe((_) => {
                if (!this.isSealResponsiblesRequired()) {
                    this.form.get('sealResponsibles')?.setValue([]);
                }

                if (!this.isMeterResponsiblesRequired()) {
                    this.form.get('meterResponsibles')?.setValue([]);
                }

                if (!this.isMeterNeedsChangeRequired()) {
                    this.form.get('meterNeedsChange')?.setValue(null);
                }

                if (!this.isStartsAsConstructionRequired()) {
                    this.form.get('startsAsConstruction')?.setValue(null);
                }

                if (!this.isInvoicePaidRequired()) {
                    this.form.get('invoicePaid')?.setValue(null);
                }

                if (!this.isReadyForMeterRequired()) {
                    this.form.get('readyForMeter')?.setValue(null);
                }

                if (!this.isSupplierSelectedRequired()) {
                    this.form.get('supplierSelected')?.setValue(null);
                }
            })
        );

        this.subscription.add(
            this.form.get('workOrderType')?.valueChanges.subscribe((_) => {
                this.form.get('workOrderDescriptionId')?.setValue(null);
            })
        );
    }

    // Supply type will be included
    getWorkOrderDescriptionValueListType = (): ValueListType => {
        return mapWorkOrderTypeToWorkDescriptionValueList(this.form?.get('workOrderType')?.value ?? WorkOrderType.General);
    };

    // Supply type will be included
    getWorkOrderPurposeValueListType = (): ValueListType => {
        return ValueListType.WorkOrderElectricityPurposeList;
    };
}
