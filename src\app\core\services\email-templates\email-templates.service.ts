import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map, shareReplay } from 'rxjs/operators';
import { CommunicationTemplatesListModel, InstallationFormsClient, SupplyType } from 'src/app/api/installation-forms-client';
import { v4 as uuidv4 } from 'uuid';

@Injectable({
    providedIn: 'root'
})
export class EmailTemplatesService {
    private readonly emailTemplatesDictionary: { [key in SupplyType]?: Observable<CommunicationTemplatesListModel> } = {};

    constructor(private readonly client: InstallationFormsClient) {}

    getEmailTemplates(supplyType: SupplyType): Observable<CommunicationTemplatesListModel> {
        this.emailTemplatesDictionary[supplyType] ??= this.client.getEmailTemplatesList(uuidv4(), supplyType).pipe(
            map((response) => response.result),
            shareReplay(1)
        );
        return this.emailTemplatesDictionary[supplyType];
    }
}
