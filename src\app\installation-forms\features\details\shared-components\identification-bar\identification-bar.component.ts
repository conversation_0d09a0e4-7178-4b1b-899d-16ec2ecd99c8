import { Component, Input } from '@angular/core';
import {
    AddressLookup,
    ConnectionPoint,
    FormCategory,
    FormScreeningStatus,
    FormState,
    FormType
} from 'src/app/api/installation-forms-client';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';

@Component({
    selector: 'app-identification-bar',
    templateUrl: './identification-bar.component.html',
    styleUrls: ['./identification-bar.component.scss'],
    standalone: false
})
export class IdentificationBarComponent {
    private _installationAddress!: AddressLookup;

    constructor(private readonly formDataService: FormDataService) {}

    @Input()
    set installationAddress(value: AddressLookup | null | undefined) {
        this._installationAddress = value || new AddressLookup();
    }

    get installationAddress(): AddressLookup {
        return this._installationAddress;
    }

    @Input() connectionPoint?: ConnectionPoint;

    @Input() meterNumber?: string;

    get formId(): string {
        return this.formDataService.formId!;
    }

    get supplyType(): string {
        return this.formDataService.supplyType!;
    }

    get type(): FormType {
        return this.formDataService.type!;
    }

    get state(): FormState {
        return this.formDataService.state!;
    }

    get category(): FormCategory {
        return this.formDataService.category!;
    }

    get creationDateTime(): Date | undefined {
        return this.formDataService.creationDateTime;
    }

    get lastChangedDateTime(): Date | undefined {
        return this.formDataService.lastChangedDateTime;
    }

    get requestedConnectionDateEodTime(): Date | undefined {
        return this.formDataService.requestedConnectionDateEodTime;
    }
    get hasCategory(): boolean {
        return !!this.formDataService.category;
    }

    get screeningStatus(): FormScreeningStatus {
        return this.formDataService.screeningStatus!;
    }
}
