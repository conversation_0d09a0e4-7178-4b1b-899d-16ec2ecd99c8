import { MeteringPoint } from 'src/app/api/installation-forms-client';

export function createMeteringPoint(meteringPoint: any): MeteringPoint | undefined {
    const meteringPointIdValue = meteringPoint?.meteringPointId;
    const meteringPointVersionIdValue = meteringPoint?.meteringPointVersionId;

    return meteringPointIdValue || meteringPointVersionIdValue
        ? new MeteringPoint({
              meteringPointId: meteringPointIdValue ?? '',
              meteringPointVersionId: meteringPointVersionIdValue ?? ''
          })
        : undefined;
}
