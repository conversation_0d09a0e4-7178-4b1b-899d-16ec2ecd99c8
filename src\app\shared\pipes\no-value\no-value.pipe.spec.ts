import { NO_VALUE_STRING } from 'src/app/core/constants/constants';
import { NoValuePipe } from './no-value.pipe';

describe('NoValuePipe', () => {
    let pipe: NoValuePipe;

    beforeEach(() => {
        pipe = new NoValuePipe();
    });

    it('should create an instance', () => {
        expect(pipe).toBeTruthy();
    });

    it('should return string representation of truthy string values', () => {
        expect(pipe.transform('test')).toBe('test');
        expect(pipe.transform('0')).toBe('0');
    });

    it('should return string representation of truthy number values', () => {
        expect(pipe.transform(123)).toBe('123');
        expect(pipe.transform(0.5)).toBe('0.5');
    });

    it('should return string representation of Date objects', () => {
        const date = new Date(2023, 0, 15); // January 15, 2023
        expect(pipe.transform(date)).toBe(date.toString());
    });

    it('should return NO_VALUE_STRING for null', () => {
        expect(pipe.transform(null)).toBe(NO_VALUE_STRING);
    });

    it('should return NO_VALUE_STRING for undefined', () => {
        expect(pipe.transform(undefined as any)).toBe(NO_VALUE_STRING);
    });

    it('should return NO_VALUE_STRING for empty string', () => {
        expect(pipe.transform('')).toBe(NO_VALUE_STRING);
    });

    it('should return NO_VALUE_STRING for zero', () => {
        expect(pipe.transform(0)).toBe(NO_VALUE_STRING);
    });

    it('should handle any additional arguments without affecting the result', () => {
        expect(pipe.transform('test', 'arg1', 'arg2')).toBe('test');
        expect(pipe.transform(null, 'arg1', 'arg2')).toBe(NO_VALUE_STRING);
    });
});
