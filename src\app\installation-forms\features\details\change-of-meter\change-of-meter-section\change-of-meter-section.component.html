<div #overlayContainer></div>
<div [formGroup]="form">
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label for="reasonForChange">
                {{ 'changeOfMeterWidget.reasonForChange' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="reasonForChange"
                [options]="reasonForChangeOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                [showClear]="true"
                formControlName="reasonForChange">
            </p-select>
        </div>
    </div>
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['responsibleForSeal']" for="responsibleForSeal">
                {{ 'changeOfMeterWidget.responsibleForSeal' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="responsibleForSeal"
                [options]="responsibleForSealOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="responsibleForSeal">
            </p-select>
            <small [controlValidationErrors]="form.controls['responsibleForSeal']" class="p-error"></small>
        </div>
    </div>
    <app-meter-type-section [form]="meterTypeForm" [showMeterPlacement]="true" nextZebraContainer> </app-meter-type-section>
</div>
