import { CommonModule, DatePipe } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { InjectionToken, NgModule } from '@angular/core';
import { FlexLayoutModule } from '@angular/flex-layout';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ConfigurationService } from '@kmd-elements-ui/configuration';
import { CmbsCalendarComponent, CmbsValueListDropdownComponent } from '@kmd-elements/core-kit';
import { NextZebraContainerModule } from '@kmd-elements/utilities';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { MarkdownModule } from 'ngx-markdown';
import { AccordionModule } from 'primeng/accordion';
import { MessageService } from 'primeng/api';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { CardModule } from 'primeng/card';
import { CheckboxModule } from 'primeng/checkbox';
import { DialogModule } from 'primeng/dialog';
import { DividerModule } from 'primeng/divider';
import { InputGroupModule } from 'primeng/inputgroup';
import { InputGroupAddonModule } from 'primeng/inputgroupaddon';
import { InputNumberModule } from 'primeng/inputnumber';
import { InputTextModule } from 'primeng/inputtext';
import { MessageModule } from 'primeng/message';
import { MessagesModule } from 'primeng/messages';
import { MultiSelectModule } from 'primeng/multiselect';
import { PaginatorModule } from 'primeng/paginator';
import { PanelModule } from 'primeng/panel';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { RippleModule } from 'primeng/ripple';
import { ScrollPanelModule } from 'primeng/scrollpanel';
import { SelectModule } from 'primeng/select';
import { SelectButtonModule } from 'primeng/selectbutton';
import { SkeletonModule } from 'primeng/skeleton';
import { TableModule } from 'primeng/table';
import { TabsModule } from 'primeng/tabs';
import { TagModule } from 'primeng/tag';
import { TextareaModule } from 'primeng/textarea';
import { TieredMenuModule } from 'primeng/tieredmenu';
import { ToastModule } from 'primeng/toast';
import { ToggleButtonModule } from 'primeng/togglebutton';
import { ToggleSwitchModule } from 'primeng/toggleswitch';
import { TooltipModule } from 'primeng/tooltip';
import { InstallationFormsClient } from 'src/app/api/installation-forms-client';
import { environment } from 'src/environments/environment';
import daTranslations from '../../assets/i18n/da-DK.json';
import enTranslations from '../../assets/i18n/en-US.json';
import { INSTALLATIONFORMS_BASE_URL } from '../api/installation-forms-client';
import { DefaultInstructionTextService } from '../core/services/default-instruction-text/default-instruction-text.service';
import { EmailTemplatesService } from '../core/services/email-templates/email-templates.service';
import { MessageServiceHelper } from '../core/services/message/message.service';
import { TranslateServiceHelper } from '../core/services/translate/translate.service';
import { ValueListsService } from '../core/services/value-lists/value-lists.service';
import { SharedModule } from '../shared/shared.module';
import { AutomaticArchivingCreateComponent } from './features/automatic-archiving/automatic-archiving-create/automatic-archiving-create.component';
import { AutomaticArchivingEditComponent } from './features/automatic-archiving/automatic-archiving-edit/automatic-archiving-edit.component';
import { AutomaticArchivingComponent } from './features/automatic-archiving/automatic-archiving.component';
import { AutomaticArchivingMapper } from './features/automatic-archiving/services/automatic-archiving-mapper';
import { AutomaticEmailsCreateComponent } from './features/automatic-emails/automatic-emails-create/automatic-emails-create.component';
import { AutomaticEmailsEditComponent } from './features/automatic-emails/automatic-emails-edit/automatic-emails-edit.component';
import { AutomaticEmailsComponent } from './features/automatic-emails/automatic-emails.component';
import { AutomaticInstructionTextCreateComponent } from './features/automatic-instruction-text/automatic-instruction-text-create/automatic-instruction-text-create.component';
import { AutomaticInstructionTextEditComponent } from './features/automatic-instruction-text/automatic-instruction-text-edit/automatic-instruction-text-edit.component';
import { AutomaticInstructionTextComponent } from './features/automatic-instruction-text/automatic-instruction-text.component';
import { AutomaticInvoiceCreateComponent } from './features/automatic-invoice/automatic-invoice-create/automatic-invoice-create.component';
import { AutomaticInvoiceEditComponent } from './features/automatic-invoice/automatic-invoice-edit/automatic-invoice-edit.component';
import { AutomaticInvoiceComponent } from './features/automatic-invoice/automatic-invoice.component';
import { AutomaticScreeningCreateComponent } from './features/automatic-screening/automatic-screening-create/automatic-screening-create.component';
import { AutomaticScreeningEditComponent } from './features/automatic-screening/automatic-screening-edit/automatic-screening-edit.component';
import { AutomaticScreeningComponent } from './features/automatic-screening/automatic-screening.component';
import { AutomaticScreeningMapper } from './features/automatic-screening/services/automatic-screening-mapper';
import { DefaultInstructionTextsCreateComponent } from './features/default-instruction-texts/default-instruction-texts-create/default-instruction-texts-create.component';
import { DefaultInstructionTextsEditComponent } from './features/default-instruction-texts/default-instruction-texts-edit/default-instruction-texts-edit.component';
import { DefaultInstructionTextsComponent } from './features/default-instruction-texts/default-instruction-texts.component';
import { MarkdownViewerComponent } from './features/markdown-viewer/markdown-viewer.component';
import { MasterDataCreateComponent } from './features/master-data/master-data-create/master-data-create.component';
import { MasterDataEditComponent } from './features/master-data/master-data-edit/master-data-edit.component';
import { MasterDataComponent } from './features/master-data/master-data.component';
import { MasterDataMapper } from './features/master-data/services/master-data.mapper';
import { PriceDefinitionsComponent } from './features/price-definitions/price-definitions.component';
import { WorkOrdersCreateComponent } from './features/work-orders/work-orders-create/work-orders-create.component';
import { WorkOrdersEditComponent } from './features/work-orders/work-orders-edit/work-orders-edit.component';
import { WorkOrdersComponent } from './features/work-orders/work-orders.component';
import { InstallationFormsSettingsRoutingModule } from './installation-forms-settings-routing.module';
import { AutomaticInvoiceMapper } from './features/automatic-invoice/services/automatic-invoice.mapper';

export function HttpLoaderFactory(httpClient: HttpClient, host: string) {
    return new TranslateHttpLoader(httpClient, `${host}/assets/i18n/`, '.json');
}

export const INSTALLATIONFORMS_UI_BASE_URL = new InjectionToken<string>('INSTALLATIONFORMS_UI_BASE_URL');

const components = [
    MarkdownViewerComponent,
    AutomaticInvoiceComponent,
    AutomaticInvoiceCreateComponent,
    AutomaticInvoiceEditComponent,
    MasterDataComponent,
    MasterDataCreateComponent,
    MasterDataEditComponent,
    WorkOrdersComponent,
    WorkOrdersCreateComponent,
    WorkOrdersEditComponent,
    AutomaticScreeningComponent,
    AutomaticScreeningCreateComponent,
    AutomaticScreeningEditComponent,
    AutomaticArchivingComponent,
    AutomaticArchivingCreateComponent,
    AutomaticArchivingEditComponent,
    AutomaticScreeningEditComponent,
    DefaultInstructionTextsComponent,
    DefaultInstructionTextsCreateComponent,
    DefaultInstructionTextsEditComponent,
    AutomaticInstructionTextComponent,
    AutomaticInstructionTextCreateComponent,
    AutomaticInstructionTextEditComponent,
    AutomaticEmailsCreateComponent,
    AutomaticEmailsEditComponent,
    AutomaticEmailsComponent,
    PriceDefinitionsComponent
];

const imports = [
    CommonModule,
    TranslateModule.forChild({
        extend: false
    }),
    InstallationFormsSettingsRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    PanelModule,
    FlexLayoutModule,
    InputTextModule,
    InputNumberModule,
    TextareaModule,
    AccordionModule,
    ButtonModule,
    CheckboxModule,
    RippleModule,
    TableModule,
    PaginatorModule,
    SkeletonModule,
    ProgressSpinnerModule,
    CardModule,
    DialogModule,
    DividerModule,
    TieredMenuModule,
    ToastModule,
    MessagesModule,
    MessageModule,
    CalendarModule,
    AutoCompleteModule,
    ToggleSwitchModule,
    SelectButtonModule,
    InputGroupModule,
    InputGroupAddonModule,
    SharedModule,
    NextZebraContainerModule,
    ScrollPanelModule,
    TooltipModule,
    MultiSelectModule,
    ToggleButtonModule,
    CmbsCalendarComponent,
    CmbsValueListDropdownComponent,
    MarkdownModule.forRoot(),
    SelectModule,
    TabsModule,
    TagModule
];

const providers = [
    MessageService,
    ConfigurationService,
    InstallationFormsClient,
    DatePipe,
    {
        provide: INSTALLATIONFORMS_BASE_URL,
        useValue: environment.backendBasePath
    },
    {
        provide: INSTALLATIONFORMS_UI_BASE_URL,
        useValue: environment.ui
    },
    ValueListsService,
    MasterDataMapper,
    AutomaticScreeningMapper,
    AutomaticArchivingMapper,
    DefaultInstructionTextService,
    EmailTemplatesService,
    MessageServiceHelper,
    TranslateServiceHelper,
    AutomaticInvoiceMapper
];

@NgModule({
    declarations: [...components],
    imports: [...imports],
    providers: [...providers]
})
export class InstallationFormsSettingsModule {
    constructor(private readonly translateService: TranslateService) {
        this.translateService.setTranslation('da-DK', daTranslations, true);
        this.translateService.setTranslation('en-US', enTranslations, true);
    }
}
