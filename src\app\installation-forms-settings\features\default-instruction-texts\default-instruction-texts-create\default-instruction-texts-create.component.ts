import { Component, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { finalize } from 'rxjs';
import { InstallationFormsClient, InstructionText, InstructionTextCreateOrUpdate } from 'src/app/api/installation-forms-client';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { v4 as uuidv4 } from 'uuid';
import { BaseRuleCreateComponent } from '../../base-components/base-rule-create.components';
import {
    defaultInstructionTextsTabName,
    defaultInstructionTextsTranslationPath,
    instructionTextMaxLength
} from '../constants/default-instruction-texts.consts';

@Component({
    selector: 'app-default-instruction-texts-create',
    templateUrl: './default-instruction-texts-create.component.html',
    standalone: false
})
export class DefaultInstructionTextsCreateComponent extends BaseRuleCreateComponent<InstructionText> implements OnInit {
    constructor(
        private readonly client: InstallationFormsClient,
        private readonly fb: FormBuilder,
        protected readonly translateService: TranslateService,
        protected readonly messageServiceHelper: MessageServiceHelper
    ) {
        super(translateService, messageServiceHelper, defaultInstructionTextsTranslationPath, defaultInstructionTextsTabName);
    }

    ngOnInit(): void {
        super.ngOnInit();
    }

    override initForm(): void {
        this.form = this.fb.group({
            text: [null, [Validators.required, Validators.maxLength(instructionTextMaxLength)]]
        });
    }

    override createClick() {
        if (this.form.invalid) {
            return;
        }

        const formValue = this.form.value;

        let hasValidationErrors = false;

        if (hasValidationErrors) {
            return;
        }

        const rule: InstructionTextCreateOrUpdate = new InstructionTextCreateOrUpdate({
            text: formValue.text
        });

        this.isProcessing = true;
        this.subscription.add(
            this.client
                .createInstructionText(uuidv4(), rule)
                .pipe(finalize(() => (this.isProcessing = false)))
                .subscribe({
                    next: (response) => {
                        this.ruleCreated.emit(response.result);
                        this.showSuccessMessage();
                    },
                    error: (_) => {
                        this.showErrorMessage(this.widgetTranslations['instructionTextCreationError']);
                    }
                })
        );
    }

    getEnumTranslations() {
        // no options to select
    }
}
