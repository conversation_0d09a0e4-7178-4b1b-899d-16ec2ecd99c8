import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
    {
        path: '',
        loadChildren: () => import('./installation-forms/installation-forms.module').then((m) => m.InstallationFormsModule)
    },
    {
        path: 'settings',
        loadChildren: () =>
            import('./installation-forms-settings/installation-forms-settings.module').then(
                (m) => m.InstallationFormsSettingsModule
            )
    }
];

@NgModule({
    imports: [RouterModule.forRoot(routes)],
    exports: [RouterModule]
})
export class AppRoutingModule {}
