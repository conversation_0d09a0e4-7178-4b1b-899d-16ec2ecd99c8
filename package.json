{"name": "installation-forms", "version": "0.0.3", "scripts": {"ng": "ng", "start": "ng serve", "start-proxy": "ng serve --configuration proxy --port 4207", "build": "ng build", "build-ci-debug": "node --max-old-space-size=8192 node_modules/@angular/cli/bin/ng build --configuration=ci-debug", "watch": "ng build --watch --configuration development", "test": "ng test", "test:pr": "ng test --coverage", "lint": "ng lint", "run:all": "node node_modules/@angular-architects/module-federation/src/server/mf-dev-server.js", "configure-husky": "npx husky install && npx husky add .husky/pre-commit \"npx --no-install lint-staged\"", "generate-client": "pwsh -File ./scripts/open-api-tools/generate.ps1"}, "private": true, "dependencies": {"@angular-architects/module-federation": "^19.0.3", "@angular/animations": "^19.2.4", "@angular/cdk": "^19.2.7", "@angular/common": "^19.2.4", "@angular/compiler": "^19.2.4", "@angular/core": "^19.2.4", "@angular/flex-layout": "^15.0.0-beta.42", "@angular/forms": "^19.2.4", "@angular/platform-browser": "^19.2.4", "@angular/platform-browser-dynamic": "^19.2.4", "@angular/router": "^19.2.4", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.25.1", "@kmd-elements-ui/authorization": "^19.0.16", "@kmd-elements-ui/configuration": "^19.0.8", "@kmd-elements-ui/feature-toggle": "^19.0.0", "@kmd-elements-ui/shortcuts": "^19.0.0", "@kmd-elements-ui/tabs": "^19.0.0", "@kmd-elements-ui/topology-selector": "^19.0.1", "@kmd-elements/attachments": "19.0.0", "@kmd-elements/core-kit": "^19.1.0", "@kmd-elements/send-message": "^19.2.0", "@kmd-elements/utilities": "19.0.0", "@ngrx/component-store": "^19.1.0", "@ngrx/effects": "^19.1.0", "@ngrx/operators": "^19.1.0", "@ngrx/store": "^19.1.0", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "@primeng/themes": "^19.0.10", "@types/uuid": "^10.0.0", "ngx-markdown": "^19.1.1", "primeflex": "^4.0.0", "primeicons": "^7.0.0", "primeng": "^19.1.2", "rxjs": "~7.8.2", "tslib": "^2.8.1", "uuid": "^11.1.0", "zone.js": "^0.15.0"}, "devDependencies": {"@angular-builders/jest": "^19.0.1", "@angular-devkit/build-angular": "^19.0.0", "@angular-eslint/builder": "^19.3.0", "@angular-eslint/eslint-plugin": "^19.3.0", "@angular-eslint/eslint-plugin-template": "^19.3.0", "@angular-eslint/schematics": "^19.3.0", "@angular-eslint/template-parser": "^19.3.0", "@angular/build": "^19.2.6", "@angular/cli": "^19.2.6", "@angular/compiler-cli": "^19.2.5", "@happy-dom/jest-environment": "^17.4.4", "@kmd-elements-ui/authentication": "^19.0.7", "@kmd-elements-ui/host-sdk": "^19.0.13", "@kmd-elements/kmd-elements-theme": "^4.0.30", "@types/file-saver": "^2.0.7", "@types/jest": "^29.5.14", "@types/node": "^22.14.0", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.29.1", "core-js": "^3.41.0", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-html-reporter": "^4.1.0", "jest-junit": "^16.0.0", "jest-preset-angular": "^14.5.4", "ng-mocks": "^14.13.4", "ngx-build-plus": "^19.0.0", "prettier": "^3.5.3", "typescript": "^5.8.3"}, "lint-staged": {"src/**/*.{js,ts,html}": ["eslint --cache --fix", "prettier --write --ignore-unknown"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}}