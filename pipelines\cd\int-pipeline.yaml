name: Int_$(date:yyyyMMdd)$(rev:.r)

trigger: none

pool:
  name: "kmd-platform-build-agents-internal"

variables:
  - template: ../global-variables-template.yaml

resources:
  pipelines:
    - pipeline: "_app_image"
      source: "[INT] InstallationFormsUI - build container image"
      branch: master
      trigger:
        branches:
          include:
            - refs/heads/master
          exclude:
            - refs/heads/temp-release/*
            - refs/heads/release/*
            - refs/heads/feature/*
    - pipeline: '_Scripts'
      source: 'Scripts'
      branch: master
      trigger: none
  repositories:
    - repository: commonPipelines
      type: git
      name: COMBAS/KMD.Elements.Pipelines
      ref: "master"
    - repository: helmCharts
      type: git
      name: COMBAS/KMD.Elements.HelmCharts.Elements
      ref: "master"

extends:
  template: cd/int/int-template.yaml@commonPipelines
  parameters:
    aksApplicationName: ${{ variables.applicationName }}
    pathToImageInfoFile: ${{ variables.pathToImageInfoFile }}
