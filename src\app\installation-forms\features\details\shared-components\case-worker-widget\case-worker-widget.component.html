<div #overlayContainer></div>
<p-panel [header]="'caseWorkerWidget.title' | translate" [toggleable]="true">
    <ng-template pTemplate="icons">
        <button
            type="button"
            pButton
            pRipple
            class="p-button-outlined"
            *ngIf="hasUserWritePermission && !isCurrentUserAssignedAsCaseWorker"
            [disabled]="isProcessing"
            (click)="confirmationAssignToMe()"
            size="small">
            {{ 'caseWorkerWidget.assignMe' | translate }}
        </button>
        <div *ngIf="hasVisibleMenuItems()">
            <button pbutton class="p-element p-panel-header-icon p-link p-button p-component" (click)="menu.toggle($event)">
                <span class="fa-regular fa-ellipsis-vertical"></span>
            </button>
            <p-tieredMenu [appendTo]="overlayContainer" #menu [model]="menuItems" [popup]="true"> </p-tieredMenu>
        </div>
    </ng-template>

    <div *ngIf="!isCurrentUserAssignedAsCaseWorker">
        <p-tag severity="warn" value="{{ 'caseWorkerWidget.onlyAssignedUserCanEditFormInformation' | translate }}"> </p-tag>
    </div>

    <div fxLayout="column" class="mt-10 zebra-container" [formGroup]="form">
        <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label for="name">{{ 'caseWorkerWidget.name' | translate }}</label>
            </div>
            <div class="zebra-edit" fxFlex>
                <p-autoComplete
                    formControlName="name"
                    [showEmptyMessage]="true"
                    [suggestions]="filteredUsers"
                    [forceSelection]="true"
                    field="text"
                    [appendTo]="overlayContainer"
                    [minLength]="2"
                    (completeMethod)="search($event)"
                    (onSelect)="onSelectUser($event)"
                    (onClear)="onClear()">
                </p-autoComplete>
                <small [controlValidationErrors]="form.controls['name']" class="p-error"></small>
            </div>
        </div>

        <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label for="email">{{ 'caseWorkerWidget.email' | translate }}</label>
            </div>
            <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                <input
                    id="email"
                    type="text"
                    maxlength="320"
                    pInputText
                    [disabled]="true"
                    value="{{ form.get('email')?.value }}" />
                <small [controlValidationErrors]="form.controls['email']" class="p-error"></small>
            </div>
        </div>
    </div>

    <ng-template [ngIf]="hasUserWritePermission && changesAmount > 0">
        <div class="mt-10" fxLayout="row" fxLayoutAlign="space-between center">
            <div class="app-updated-text">
                <ng-container *ngIf="changesAmount === 1; else elseTemplate">
                    {{ 'common.singleChangedFieldInformation' | translate: { amount: changesAmount } }}
                </ng-container>
                <ng-template #elseTemplate>
                    {{ 'common.pluralChangedFieldsInformation' | translate: { amount: changesAmount } }}
                </ng-template>
            </div>
            <div>
                <button
                    id="cancelButton"
                    [disabled]="isProcessing"
                    type="button"
                    pButton
                    pRipple
                    class="mr-2 p-button-secondary"
                    (click)="onCancelChangesClicked()">
                    {{ 'common.cancelChanges' | translate | titlecase }}
                </button>
                <button id="saveButton" type="button" pButton pRipple (click)="onSaveClicked()">
                    <span *ngIf="isProcessing" class="fa fa-spin fa-spinner"></span>
                    {{ 'common.saveChanges' | translate | titlecase }}
                </button>
            </div>
        </div>
    </ng-template>
</p-panel>
<app-confirmation-dialog
    [headerKey]="'common.saveChanges'"
    [messageKey]="'caseWorkerWidget.takeOverFormQuestion'"
    [visible]="!!attemptedAction"
    (confirmEvent)="onConfirmationDialogYesButtonClick()"
    (cancelEvent)="onConfirmationDialogNoButtonClick()">
</app-confirmation-dialog>
