<div #overlayContainer></div>

<div fxLayout="column" class="mt-10 zebra-container" [formGroup]="form">
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['templateId']" for="templateId">
                {{ 'emailsWidget.template' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="emailTemplateOptions"
                [options]="templates"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="templateId">
            </p-select>
            <small [controlValidationErrors]="form.controls['templateId']" class="p-error"></small>
        </div>
    </div>
    <div class="mt-10 mb-10" fxLayout="row" fxLayoutAlign="end center">
        <button
            id="cancelButton"
            type="button"
            pButton
            pRipple
            [disabled]="isProcessing"
            class="mr-2 p-button-secondary"
            label="{{ 'emailsWidget.cancel' | translate }}"
            (click)="cancelSend()"></button>
        <button id="saveButton" type="button" class="mr-10" pButton pRipple (click)="sendEmail()">
            <span *ngIf="isProcessing" class="fa fa-spin fa-spinner"></span>
            {{ 'emailsWidget.sendEmail' | translate | titlecase }}
        </button>
    </div>
</div>
