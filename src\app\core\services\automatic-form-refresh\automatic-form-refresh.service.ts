import { Injectable } from '@angular/core';
import { TabsService } from '@kmd-elements-ui/tabs';
import { TranslateService } from '@ngx-translate/core';
import { Subject, Subscription, filter, interval, takeWhile, tap } from 'rxjs';
import { InstallationFormsClient, PendingUpdateAreaType } from 'src/app/api/installation-forms-client';
import { environment } from 'src/environments/environment';
import { v4 as uuidv4 } from 'uuid';
import { FormDataService } from '../form-data/form-data.service';
import { MessageServiceHelper } from '../message/message.service';

type RefreshCallback = () => void;

@Injectable({
    providedIn: 'root'
})
export class AutomaticFormRefreshService {
    static readonly MAX_FAILURES_UNTIL_REFRESH_STOPS = 10;
    static readonly CONFLICTING_CHANGE_MESSAGE_LIFE_IN_SECONDS = 10;

    private secondsCounter = 0;
    private failuresCounter = 0;

    private readonly refreshCallbacks = new Map<PendingUpdateAreaType, RefreshCallback>();
    subscription: Subscription = new Subscription();
    forceRefreshRequested$ = new Subject<void>();

    commonTranslations: any;

    private formId?: string;
    private pollingEnabled = false;
    private latestChangeTimestamp?: Date;

    constructor(
        private readonly client: InstallationFormsClient,
        private readonly messageServiceHelper: MessageServiceHelper,
        private readonly translateService: TranslateService,
        private readonly tabsService: TabsService,
        private readonly formDataService: FormDataService
    ) {
        this.commonTranslations = this.translateService.instant('common');
    }

    tabActiveStateChanged = (isTabActive: boolean) => {
        if (isTabActive) {
            this.restartPolling();
        } else {
            this.pollingEnabled = false;
        }
    };

    startRefreshingForm(formId: string, tabId: string) {
        this.stopRefreshingForm();
        this.formId = formId;
        this.latestChangeTimestamp = undefined;
        this.restartPolling();

        this.subscription.add(this.tabsService.isTabActiveListener(tabId).subscribe(this.tabActiveStateChanged));
        this.subscription.add(
            interval(1000)
                .pipe(
                    tap(() => this.secondsCounter++),
                    filter(() => {
                        if (!this.pollingEnabled || this.formDataService.isProcessing) {
                            return false;
                        }
                        let currentRefreshIntervalInSeconds = environment.automaticRefresh.initialRefreshIntervalInSeconds;
                        if (environment.automaticRefresh.intervalExtensions.length > 0) {
                            for (let i = environment.automaticRefresh.intervalExtensions.length - 1; i >= 0; i--) {
                                if (this.secondsCounter > environment.automaticRefresh.intervalExtensions[i].afterSeconds) {
                                    currentRefreshIntervalInSeconds =
                                        environment.automaticRefresh.intervalExtensions[i].newIntervalInSeconds;
                                }
                            }
                        }

                        return this.secondsCounter % currentRefreshIntervalInSeconds === 0;
                    }),
                    takeWhile(() => this.failuresCounter < AutomaticFormRefreshService.MAX_FAILURES_UNTIL_REFRESH_STOPS)
                )
                .subscribe(() => this.fetchData())
        );
    }

    stopRefreshingForm() {
        this.subscription.unsubscribe();
        this.subscription = new Subscription();
    }

    subscribeForRefresh(area: PendingUpdateAreaType, callback: RefreshCallback) {
        this.refreshCallbacks.set(area, callback);
    }

    unsubscribeForRefresh(area: PendingUpdateAreaType) {
        this.refreshCallbacks.delete(area);
    }

    restartPolling() {
        //-1 means next second we're getting data refreshed
        this.secondsCounter = -1;
        this.failuresCounter = 0;
        this.pollingEnabled = true;
    }

    forceRefresh = () => {
        this.forceRefreshRequested$.next();
        this.latestChangeTimestamp = undefined;
        this.restartPolling();
    };

    areasChanged(changedAreas: PendingUpdateAreaType[]) {
        for (let changedArea of changedAreas) {
            const changedAreaHandler = this.refreshCallbacks.get(changedArea);
            if (changedAreaHandler) {
                changedAreaHandler();
            }
        }
    }

    fetchData() {
        this.subscription.add(
            this.client.getPendingUpdates(this.formId!, uuidv4(), undefined, this.latestChangeTimestamp).subscribe({
                next: (response) => {
                    this.failuresCounter = 0;
                    this.latestChangeTimestamp = response.result.latestChangeTimestamp;
                    if (response.result.pendingUpdateAreas.length > 0) {
                        this.areasChanged(response.result.pendingUpdateAreas);
                    }
                },
                error: (_error) => {
                    ++this.failuresCounter;
                    if (this.failuresCounter === AutomaticFormRefreshService.MAX_FAILURES_UNTIL_REFRESH_STOPS) {
                        this.messageServiceHelper.showError({
                            detail: this.commonTranslations['errorRefreshingDetail'],
                            key: this.formId!
                        });
                    }
                }
            })
        );
    }

    notifyConflictingChangeOccurred() {
        this.messageServiceHelper.showWarning({
            detail: this.commonTranslations['staleDataDetail'],
            key: this.formId!,
            life: AutomaticFormRefreshService.CONFLICTING_CHANGE_MESSAGE_LIFE_IN_SECONDS * 1000
        });
        //polling disabled since form requires reloading
        this.pollingEnabled = false;
    }
}
