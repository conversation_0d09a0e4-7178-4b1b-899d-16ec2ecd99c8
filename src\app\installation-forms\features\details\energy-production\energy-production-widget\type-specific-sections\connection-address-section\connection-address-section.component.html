<div #overlayContainer></div>
<ng-container [formGroup]="form">
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['connectionAddress']" for="connectionAddress">
                {{ 'energyProductionSection.connectionAddress.address' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <app-address-search id="connectionAddress" formControlName="connectionAddress"> </app-address-search>
            <small [controlValidationErrors]="form.controls['connectionAddress']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="type === 'Consumption'">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['consumptionUnitType']" for="consumptionUnitType">
                {{ 'energyProductionSection.connectionAddress.consumptionUnitType' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input id="consumptionUnitType" type="text" pInputText maxlength="50" formControlName="consumptionUnitType" />
            <small [controlValidationErrors]="form.controls['consumptionUnitType']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="type === 'Wind'">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['locationType']" for="locationType">
                {{ 'energyProductionSection.connectionAddress.locationType' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="locationType"
                [options]="locationTypeOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="locationType">
            </p-select>
            <small [controlValidationErrors]="form.controls['locationType']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="type === 'Wind'">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['cadastralDistrictIdentifier']" for="cadastralDistrictIdentifier">
                {{ 'energyProductionSection.connectionAddress.cadastralDistrictIdentifier' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input
                id="cadastralDistrictIdentifier"
                type="text"
                pInputText
                maxlength="50"
                formControlName="cadastralDistrictIdentifier" />
            <small [controlValidationErrors]="form.controls['cadastralDistrictIdentifier']" class="p-error"></small>
        </div>
    </div>

    <div
        class="zebra-item"
        fxLayout="row"
        fxLayout.lt-sm="column"
        fxLayoutGap="15px"
        *ngIf="isCadastralRegistrationNumberRequired()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['cadastralRegistrationNumber']" for="cadastralRegistrationNumber">
                {{ 'energyProductionSection.connectionAddress.cadastralRegistrationNumber' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input
                id="cadastralRegistrationNumber"
                type="text"
                pInputText
                maxlength="50"
                formControlName="cadastralRegistrationNumber" />
            <small [controlValidationErrors]="form.controls['cadastralRegistrationNumber']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="type === 'Wind'">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['locationUtmX']" for="locationUtmX">
                {{ 'energyProductionSection.connectionAddress.locationUtmX' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-inputNumber
                id="locationUtmX"
                formControlName="locationUtmX"
                [min]="1"
                [max]="999999999"
                [useGrouping]="true"
                [minFractionDigits]="0"
                [maxFractionDigits]="2"></p-inputNumber>
            <small [controlValidationErrors]="form.controls['locationUtmX']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="type === 'Wind'">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['locationUtmY']" for="locationUtmY">
                {{ 'energyProductionSection.connectionAddress.locationUtmY' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-inputNumber
                id="locationUtmY"
                formControlName="locationUtmY"
                [min]="1"
                [max]="999999999"
                [useGrouping]="true"
                [minFractionDigits]="0"
                [maxFractionDigits]="2"></p-inputNumber>
            <small [controlValidationErrors]="form.controls['locationUtmY']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="type === 'Wind'">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['foundationElevation']" for="foundationElevation">
                {{ 'energyProductionSection.connectionAddress.foundationElevation' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input id="foundationElevation" type="text" pInputText maxlength="50" formControlName="foundationElevation" />
            <small [controlValidationErrors]="form.controls['foundationElevation']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="type === 'PowerPlant'">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['buildingNumber']" for="buildingNumber">
                {{ 'energyProductionSection.connectionAddress.buildingNumber' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input id="buildingNumber" type="text" pInputText maxlength="50" formControlName="buildingNumber" />
            <small [controlValidationErrors]="form.controls['buildingNumber']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="type === 'PowerPlant'">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['propertyNumber']" for="propertyNumber">
                {{ 'energyProductionSection.connectionAddress.propertyNumber' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input id="propertyNumber" type="text" pInputText maxlength="50" formControlName="propertyNumber" />
            <small [controlValidationErrors]="form.controls['propertyNumber']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="type === 'Solar'">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['connectionPointUtmX']" for="connectionPointUtmX">{{
                'energyProductionSection.connectionAddress.connectionPointUtmX' | translate
            }}</label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-inputNumber
                id="connectionPointUtmX"
                formControlName="connectionPointUtmX"
                [min]="1"
                [max]="999999999"
                [useGrouping]="true"
                [minFractionDigits]="0"
                [maxFractionDigits]="2"></p-inputNumber>
            <small [controlValidationErrors]="form.controls['connectionPointUtmX']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="type === 'Solar'">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['connectionPointUtmY']" for="connectionPointUtmY">{{
                'energyProductionSection.connectionAddress.connectionPointUtmY' | translate
            }}</label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-inputNumber
                id="connectionPointUtmY"
                formControlName="connectionPointUtmY"
                [min]="1"
                [max]="999999999"
                [useGrouping]="true"
                [minFractionDigits]="0"
                [maxFractionDigits]="2"></p-inputNumber>
            <small [controlValidationErrors]="form.controls['connectionPointUtmY']" class="p-error"></small>
        </div>
    </div>
</ng-container>
