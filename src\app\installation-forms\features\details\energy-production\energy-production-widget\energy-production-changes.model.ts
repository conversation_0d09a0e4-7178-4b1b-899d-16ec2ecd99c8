import { ChangeField, ChangeFieldType } from 'src/app/core/constants/changes-details';

export class EnergyProductionChanges {
    installationAddress: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };
    connectionPoint: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };
    meterFrame: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };
    consumptionMeteringPoint: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };
    productionMeteringPoint: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };
    contactPersonCompanyName: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    contactPersonName: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    contactPersonEmail: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    contactPersonPhoneNumber: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    remarksToInstallation: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    tags: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };

    payerName: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    payerType: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    payerEmail: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    payerContactPersonName: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    payerContactPersonEmail: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    payerContactPersonPhoneNumber: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    payerAddress: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };
    requisition: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    cvrOrSeNumber: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    eanNumber: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };

    // instructionData
    instructionDataMeterResponsible: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    instructionDataBranchLineResponsible: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    instructionDataConnectionFeeFee: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    instructionDataConnectionFeeValidUntilEod: ChangeField = {
        change: false,
        type: ChangeFieldType.Date,
        dtoFieldName: undefined
    };
    instructionDataRemark: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };

    //energy production common
    energyProductionConnectionType: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    ownerName: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    ownerIdentificationType: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    ownerIdentifier: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    ownerAddress: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };

    moreThan125kW: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    includesStorage: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    commissioningDateSod: ChangeField = { change: false, type: ChangeFieldType.Date, dtoFieldName: undefined };

    //connectionAddress
    connectionAddress: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };
    consumptionUnitType: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    locationType: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    cadastralDistrictIdentifier: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    cadastralRegistrationNumber: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    locationUtmX: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    locationUtmY: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    foundationElevation: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    buildingNumber: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    propertyNumber: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    connectionPointUtmX: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    connectionPointUtmY: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };

    //plants
    hasPlantsChanged: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };

    // related forms
    hasRelatedFormsChanged: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
}
