import { Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { Subscription } from 'rxjs';
import { ConnectionTypeChange, MeterSize, ValueListType } from 'src/app/api/installation-forms-client';
import { METER_TRANSFORMER_OTHER_VALUE_ID } from 'src/app/core/constants/constants';
import { ValueListsService } from 'src/app/core/services/value-lists/value-lists.service';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import { WidgetWithFormComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget-with-form.component';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';

@Component({
    selector: 'app-meter-type-section',
    templateUrl: './meter-type-section.component.html',
    styleUrls: ['./meter-type-section.component.scss'],
    standalone: false
})
export class MeterTypeSectionComponent extends WidgetWithFormComponent implements OnInit, OnDestroy {
    @Input()
    override form!: FormGroup;

    @Input()
    showMeterPlacement: boolean = false;

    subscription: Subscription = new Subscription();
    translations: any;

    connectionTypeChangeOptions: SelectItem[] = [];
    meterSizeOptions: SelectItem[] = [];

    readonly meterFramePlacementValueListType: ValueListType = ValueListType.MeterFramePlacementCode;
    readonly meterFrameMeterTransformerCodeValueListType: ValueListType = ValueListType.MeterFrameElectricityAttributesRatioCT;
    readonly meterFrameConnectionTypeValueListType: ValueListType = ValueListType.MeterFrameElectricityConnectionType;

    constructor(
        private translateService: TranslateService,
        readonly formDataService: FormDataService,
        protected readonly valueListsService: ValueListsService
    ) {
        super(formDataService);
    }

    ngOnInit(): void {
        this.translations = this.translateService.instant('technicalInformationWidget');

        this.getEnumTranslations();
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    onMeterSizeChange() {
        if (!this.isConnectionTypeVisible()) {
            this.form.get('connectionTypeId')?.reset();
            this.form.get('connectionTypeId')?.disable();
            this.form.get('meterTransformerId')?.enable();
        } else {
            this.form.get('meterTransformerId')?.reset();
            this.form.get('meterTransformerId')?.disable();
            this.form.get('connectionTypeId')?.enable();
        }
    }

    isConnectionTypeVisible = () => {
        return this.getMeterSize() === MeterSize.Below63A;
    };

    isConnectionTypeChangeVisible = () => {
        return this.form.get('connectionTypeChange') != null; // Only show if defined in FormGroup.
    };

    isMeterTransformerVisible = () => {
        return this.getMeterSize() === MeterSize.Above63A;
    };

    getMeterSize() {
        return this.form?.get('meterSize')?.value;
    }

    isMeterTransformerRemarkVisible = () => {
        return this.isMeterTransformerVisible() && this.getMeterTransformer() === METER_TRANSFORMER_OTHER_VALUE_ID;
    };

    getMeterTransformer() {
        return this.form?.get('meterTransformerId')?.value;
    }

    private getEnumTranslations() {
        this.connectionTypeChangeOptions = enumMapper.map(
            this.translateService.instant('enums.meterConnectionTypeChange'),
            ConnectionTypeChange
        );
        this.meterSizeOptions = enumMapper.map(this.translateService.instant('enums.meterSize'), MeterSize);
    }
}
