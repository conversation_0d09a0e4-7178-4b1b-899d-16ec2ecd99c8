<p-dialog
    [header]="headerKey | translate"
    [visible]="visible"
    [modal]="true"
    [draggable]="false"
    [resizable]="false"
    [closable]="false">
    <div class="flex flex-column gap-4 mt-2">
        <span>
            {{ messageKey | translate }}
        </span>
        <div class="flex justify-content-end">
            <button
                type="button"
                pButton
                pRipple
                label="{{ 'common.yesOption' | translate }}"
                class="mr-2"
                (click)="onYesClick()"></button>
            <button
                type="button"
                pButton
                pRipple
                label="{{ 'common.noOption' | translate }}"
                class="mr-2"
                (click)="onNoClick()"></button>
        </div>
    </div>
</p-dialog>
