import { TestBed } from '@angular/core/testing';
import { ValueListItemModel, ValueListModel } from '@kmd-elements/core-kit';
import { of } from 'rxjs';
import { ValueListType } from 'src/app/api/installation-forms-client';
import { ValueListsService } from 'src/app/core/services/value-lists/value-lists.service';
import { ValueListMultiItemPipe } from './value-list-multi-item.pipe';

describe('ValueListMultiItemPipe', () => {
    let pipe: ValueListMultiItemPipe;
    let valueListsService: ValueListsService;
    let getValueListSpy: jest.SpyInstance;

    // Using type assertion to simplify our mock
    const mockValueList: ValueListModel = {
        id: 'mock-list',
        valueItems: [
            { id: 'item1', displayValue: 'Item One', code: 'I1' } as unknown as ValueListItemModel,
            { id: 'item2', displayValue: 'Item Two', code: 'I2' } as unknown as ValueListItemModel,
            { id: 'item3', displayValue: 'Item Three', code: 'I3' } as unknown as ValueListItemModel
        ]
    } as unknown as ValueListModel;

    beforeEach(() => {
        const valueListsServiceMock = {
            getValueList: jest.fn().mockReturnValue(of(mockValueList))
        };

        TestBed.configureTestingModule({
            providers: [ValueListMultiItemPipe, { provide: ValueListsService, useValue: valueListsServiceMock }]
        });

        pipe = TestBed.inject(ValueListMultiItemPipe);
        valueListsService = TestBed.inject(ValueListsService);
        getValueListSpy = jest.spyOn(valueListsService, 'getValueList');
    });

    it('should create an instance', () => {
        expect(pipe).toBeTruthy();
    });

    it('should return empty string when type is undefined', (done) => {
        pipe.transform(['item1'], undefined).subscribe((result) => {
            expect(result).toBe('');
            expect(valueListsService.getValueList).not.toHaveBeenCalled();
            done();
        });
    });

    it('should return empty string when ids array is undefined', (done) => {
        pipe.transform(undefined as any, ValueListType.BranchLineElectricityCableDimension).subscribe((result) => {
            expect(result).toBe('');
            expect(valueListsService.getValueList).not.toHaveBeenCalled();
            done();
        });
    });

    it('should return empty string when ids array is empty', (done) => {
        pipe.transform([], ValueListType.BranchLineElectricityCableDimension).subscribe((result) => {
            expect(result).toBe('');
            expect(valueListsService.getValueList).not.toHaveBeenCalled();
            done();
        });
    });

    it('should return empty string when valueList is undefined', (done) => {
        getValueListSpy.mockReturnValueOnce(of(undefined));

        pipe.transform(['item1'], ValueListType.BranchLineElectricityCableDimension).subscribe((result) => {
            expect(result).toBe('');
            expect(valueListsService.getValueList).toHaveBeenCalledWith(ValueListType.BranchLineElectricityCableDimension);
            done();
        });
    });

    it('should return empty string when valueList.valueItems is undefined', (done) => {
        const listWithoutItems = { id: 'list-without-items' } as ValueListModel;
        getValueListSpy.mockReturnValueOnce(of(listWithoutItems));

        pipe.transform(['item1'], ValueListType.BranchLineElectricityCableDimension).subscribe((result) => {
            expect(result).toBe('');
            expect(valueListsService.getValueList).toHaveBeenCalledWith(ValueListType.BranchLineElectricityCableDimension);
            done();
        });
    });

    it('should map ids to display values and join them with commas', (done) => {
        pipe.transform(['item1', 'item3'], ValueListType.BranchLineElectricityCableDimension).subscribe((result) => {
            expect(result).toBe('Item One, Item Three');
            expect(valueListsService.getValueList).toHaveBeenCalledWith(ValueListType.BranchLineElectricityCableDimension);
            done();
        });
    });

    it('should handle ids that do not exist in the value list', (done) => {
        pipe.transform(['item1', 'nonExistent', 'item3'], ValueListType.BranchLineElectricityCableDimension).subscribe(
            (result) => {
                expect(result).toBe('Item One, Item Three');
                expect(valueListsService.getValueList).toHaveBeenCalledWith(ValueListType.BranchLineElectricityCableDimension);
                done();
            }
        );
    });

    it('should filter out empty values', (done) => {
        const listWithEmptyValue = {
            id: 'list-with-empty',
            valueItems: [
                { id: 'item1', displayValue: 'Item One', code: 'I1' } as unknown as ValueListItemModel,
                { id: 'item2', displayValue: '', code: 'I2' } as unknown as ValueListItemModel,
                { id: 'item3', displayValue: 'Item Three', code: 'I3' } as unknown as ValueListItemModel
            ]
        };
        getValueListSpy.mockReturnValueOnce(of(listWithEmptyValue));

        pipe.transform(['item1', 'item2', 'item3'], ValueListType.BranchLineElectricityCableDimension).subscribe((result) => {
            expect(result).toBe('Item One, Item Three');
            done();
        });
    });

    it('should return single value without comma when only one valid id is found', (done) => {
        pipe.transform(['item2'], ValueListType.BranchLineElectricityCableDimension).subscribe((result) => {
            expect(result).toBe('Item Two');
            done();
        });
    });
});
