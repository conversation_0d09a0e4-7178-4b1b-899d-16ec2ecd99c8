import { Component } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { Observable } from 'rxjs';
import {
    BranchLineResponsible,
    BranchLineType,
    BranchLineUpdate,
    ChangeBranchLine,
    ChangeBranchLineInformationUpdate,
    ChangeBranchLineUpdate,
    Connection,
    ConnectionFee,
    ContactPerson,
    FormsRelationType,
    InstallationFormsApiResponse,
    InstallationFormsClient,
    InstallationInformationUpdate,
    InstructionDataUpdate,
    MainProtection,
    MainProtectionType,
    MeterResponsible,
    MeterSize,
    MeterTypeUpdate,
    PayerType,
    PayerUpdate,
    PaymentDetailsUpdate,
    PreProtection,
    PreProtectionType,
    RelatedForm,
    RelatedFormUpdate,
    TechnicalInformationUpdate
} from 'src/app/api/installation-forms-client';
import { ChangesModel } from 'src/app/core/constants/changes-details';
import { METER_TRANSFORMER_OTHER_VALUE_ID } from 'src/app/core/constants/constants';
import { RemarkLength } from 'src/app/core/constants/field-lengths';
import { AutomaticFormRefreshService } from 'src/app/core/services/automatic-form-refresh/automatic-form-refresh.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { createMeteringPoint } from 'src/app/core/utils/create-metering-point';
import { getValueOrDefault } from 'src/app/core/utils/get-value-or-default';
import addressLookupValidator from 'src/app/core/utils/validators/address-lookup/address-lookup.validator';
import conditionallyRequiredValidator from 'src/app/core/utils/validators/conditionally-required/conditionally-required.validator';
import dateMustNotBePastValidator from 'src/app/core/utils/validators/date-time/date-must-not-be-past.validator';
import { EmailValidator } from 'src/app/core/utils/validators/email/email.validator';
import { MeterFrameSearchItemModel } from 'src/app/shared/components/meter-frame-search/meter-frame-search-item.model';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';
import { CoreWidgetComponent } from '../../base-components/widgets/core-widget.component';
import { ChangeBranchLineChanges } from './change-branch-line-changes.model';

@Component({
    selector: 'app-change-branch-line-widget',
    templateUrl: './change-branch-line-widget.component.html',
    styleUrls: ['./change-branch-line-widget.component.scss'],
    standalone: false
})
export class ChangeBranchLineWidgetComponent extends CoreWidgetComponent<ChangeBranchLine, ChangeBranchLineUpdate> {
    override accordionActiveIndexes: number[] = [0, 1, 2, 3, 4, 5];
    override payerPanelIndex: number = 6;

    get meterTypeForm(): FormGroup {
        return this.form.get('technicalInformation.meterType') as FormGroup;
    }
    get connectionForm(): FormGroup {
        return this.form.get('technicalInformation.connection') as FormGroup;
    }
    get branchLineForm(): FormGroup {
        return this.form.get('technicalInformation.branchLine') as FormGroup;
    }
    get changeBranchLineInformationForm(): FormGroup {
        return this.form?.get('changeBranchLineInformation') as FormGroup;
    }

    constructor(
        protected fb: FormBuilder,
        protected readonly messageServiceHelper: MessageServiceHelper,
        protected readonly translateService: TranslateService,
        protected readonly client: InstallationFormsClient,
        protected readonly formDataService: FormDataService,
        protected readonly automaticFormRefreshService: AutomaticFormRefreshService
    ) {
        super(formDataService, automaticFormRefreshService, translateService, messageServiceHelper, client);
        this.initForm();
        this.setChangesModel();
    }

    protected override initForm() {
        this.form = this.fb.group({
            installationInformation: this.fb.group({
                installationAddress: [{ id: '', text: '' }, [addressLookupValidator()]],
                remarksToInstallation: ['', [Validators.maxLength(1000)]],
                connectionPoint: [{ id: '', connectionPointNumber: '', electricityConnectionStatusI: '' }],
                meterFrame: [{ id: '', meterFrameNumber: '' }],
                consumptionMeteringPoint: [{ meteringPointId: '', meteringPointVersionId: '' }],
                tags: []
            }),
            payer: this.fb.group({
                payerName: ['', [Validators.maxLength(100)]],
                payerType: [''],
                payerEmail: ['', EmailValidator()],
                payerContactPersonName: ['', [Validators.maxLength(100)]],
                payerContactPersonEmail: ['', [EmailValidator()]],
                payerContactPersonPhoneNumber: ['', [Validators.maxLength(20)]],
                requisition: '',
                cvrOrSeNumber: ['', Validators.pattern('^[0-9]{8}$')],
                eanNumber: ['', Validators.pattern('^[0-9]{13}$')],
                payerAddress: [{ id: '', text: '' }]
            }),
            contactPerson: this.fb.group({
                contactPersonCompanyName: ['', [Validators.maxLength(100)]],
                contactPersonName: ['', [Validators.maxLength(100)]],
                contactPersonEmail: ['', [EmailValidator()]],
                contactPersonPhoneNumber: ['', [Validators.maxLength(20)]]
            }),
            technicalInformation: this.fb.group({
                meterType: this.fb.group({
                    meterSize: [null, [Validators.required]],
                    connectionTypeId: [
                        null,
                        [(control: AbstractControl) => conditionallyRequiredValidator(control, this.isConnectionTypeVisible)]
                    ],
                    connectionTypeChange: [null],
                    meterTransformerId: [
                        null,
                        [(control: AbstractControl) => conditionallyRequiredValidator(control, this.isMeterTransformerVisible)]
                    ],
                    meterTransformerRemark: [
                        null,
                        [
                            (control: AbstractControl) =>
                                conditionallyRequiredValidator(control, this.isMeterTransformerRemarkVisible)
                        ]
                    ]
                }),
                connection: this.fb.group({
                    transformerStationNumber: ['', [Validators.maxLength(100)]],
                    cabinetNumber: ['', [Validators.maxLength(100)]],
                    groundingMethod: [null, [Validators.required]],
                    protectionTypeOfPreProtection: [null, [Validators.required]],
                    fuseSizeOfPreProtection: [
                        null,
                        [
                            (control: AbstractControl) =>
                                conditionallyRequiredValidator(control, this.isPreProtectionFuseSizeRequired),
                            Validators.min(1),
                            Validators.max(999999999)
                        ]
                    ]
                }),
                branchLine: this.fb.group({
                    type: [null, [Validators.required]],
                    branchLineMeterFrame: [null, []],
                    protectionTypeOfMainProtection: [null, [Validators.required]],
                    fuseSizeOfMainProtection: [null, [Validators.required, Validators.min(1), Validators.max(999999999)]],
                    fuseTypeOfMainProtection: [
                        null,
                        [
                            (control: AbstractControl) =>
                                conditionallyRequiredValidator(control, this.isFuseTypeOfMainProtectionRequired)
                        ]
                    ],
                    numberOfPairs: [null, [Validators.min(1), Validators.max(1000)]],
                    protectiveEarthingDimension: [null, []],
                    cableDimensionId: [null, []]
                })
            }),
            instructionData: this.fb.group({
                meterResponsible: '',
                branchLineResponsible: '',
                connectionFeeValidUntilEod: ['', [(control: AbstractControl) => dateMustNotBePastValidator(control)]],
                connectionFeeFee: [null, [Validators.min(-999999999), Validators.max(999999999)]], // Connection fee can be negative, that's not a mistake
                remark: ['', [Validators.maxLength(RemarkLength)]]
            }),
            changeBranchLineInformation: this.fb.group({
                meterPlacementId: ['', [Validators.maxLength(100), Validators.required]],
                responsibleForSeal: ['', [Validators.required]],
                newMeterRequested: false
            }),
            applications: this.fb.group({
                applications: [],
                hasApplicationsChanged: false
            }),
            relatedForms: this.fb.group({
                relatedForms: [],
                hasRelatedFormsChanged: false
            })
        });
    }

    protected override setChangesModel() {
        this.changesModel = new ChangeBranchLineChanges() as unknown as ChangesModel;
    }

    protected override supplyFormData() {
        this.form.setValue({
            installationInformation: {
                installationAddress: {
                    id: this.formDetails.installationAddress?.carId || '',
                    text: this.formDetails.installationAddress?.formattedAddress || ''
                },
                remarksToInstallation: this.formDetails.remarksToInstallation || '',
                connectionPoint: {
                    id: this.formDetails.connectionPoint?.id || '',
                    connectionPointNumber: this.formDetails.connectionPoint?.connectionPointNumber || '',
                    electricityConnectionStatusId: this.formDetails.connectionPoint?.electricityConnectionStatusId || ''
                },
                meterFrame: {
                    id: this.formDetails.meterFrame?.id || '',
                    meterFrameNumber: this.formDetails.meterFrame?.meterFrameNumber || ''
                },
                consumptionMeteringPoint: {
                    meteringPointId: this.formDetails.consumptionMeteringPoint?.meteringPointId || '',
                    meteringPointVersionId: this.formDetails.consumptionMeteringPoint?.meteringPointVersionId || ''
                },
                tags: this.formDetails.tags.map((t) => t.id) || []
            },
            contactPerson: {
                contactPersonCompanyName: this.formDetails.contactPerson?.companyName || '',
                contactPersonName: this.formDetails.contactPerson?.name || '',
                contactPersonEmail: this.formDetails.contactPerson?.email || '',
                contactPersonPhoneNumber: this.formDetails.contactPerson?.phoneNumber || ''
            },
            payer: {
                payerName: this.formDetails.payer?.name || '',
                payerEmail: this.formDetails.payer?.email || '',
                payerType: this.formDetails.payer?.type || PayerType.Private,
                payerContactPersonName: this.formDetails.payer?.contactPerson?.name || '',
                payerContactPersonEmail: this.formDetails.payer?.contactPerson?.email || '',
                payerContactPersonPhoneNumber: this.formDetails.payer?.contactPerson?.phoneNumber || '',
                requisition: this.formDetails.payer?.requisition || '',
                cvrOrSeNumber: this.formDetails.payer?.cvrOrSeNumber || '',
                eanNumber: this.formDetails.payer?.eanNumber || '',
                payerAddress: {
                    id: this.formDetails.payer?.address?.carId || '',
                    text: this.formDetails.payer?.address?.formattedAddress || ''
                }
            },
            instructionData: {
                meterResponsible: this.formDetails.instructionData?.meterResponsible || null,
                branchLineResponsible: this.formDetails.instructionData?.branchLineResponsible || null,
                connectionFeeFee: this.formDetails.instructionData?.connectionFee?.fee || null,
                connectionFeeValidUntilEod: this.formDetails.instructionData?.connectionFee?.validUntilEod || null,
                remark: this.formDetails.instructionData?.remark || null
            },
            technicalInformation: {
                meterType: {
                    meterSize: this.formDetails.technicalInformation?.meterType?.meterSize || null,
                    connectionTypeId: this.formDetails.technicalInformation?.meterType?.connectionTypeId || null,
                    connectionTypeChange: this.formDetails.technicalInformation?.meterType?.connectionTypeChange || null,
                    meterTransformerId: this.formDetails.technicalInformation?.meterType?.meterTransformerId || null,
                    meterTransformerRemark: this.formDetails.technicalInformation?.meterType?.meterTransformerRemark || null
                },
                connection: {
                    transformerStationNumber: this.formDetails.technicalInformation?.connection?.transformerStationNumber || null,
                    cabinetNumber: this.formDetails.technicalInformation?.connection?.cabinetNumber || null,
                    groundingMethod: this.formDetails.technicalInformation?.connection?.groundingMethod || null,
                    fuseSizeOfPreProtection: this.formDetails.technicalInformation?.connection?.preProtection?.size || null,
                    protectionTypeOfPreProtection:
                        this.formDetails.technicalInformation?.connection?.preProtection?.protectionType || null
                },
                branchLine: {
                    type: this.formDetails.technicalInformation?.branchLine?.type || null,
                    branchLineMeterFrame: this.formDetails.technicalInformation?.branchLine?.meterFrame
                        ? {
                              id: this.formDetails.technicalInformation.branchLine.meterFrame.id,
                              meterFrameNumber: this.formDetails.technicalInformation.branchLine.meterFrame.meterFrameNumber,
                              connectionPointId: this.formDetails.technicalInformation?.branchLine?.connectionPointId || null
                          }
                        : null,
                    fuseTypeOfMainProtection: this.formDetails.technicalInformation?.branchLine?.mainProtection?.fuseType || null,
                    fuseSizeOfMainProtection: this.formDetails.technicalInformation?.branchLine?.mainProtection?.size || null,
                    protectionTypeOfMainProtection:
                        this.formDetails.technicalInformation?.branchLine?.mainProtection?.protectionType || null,
                    numberOfPairs: this.formDetails.technicalInformation?.branchLine?.numberOfPairs || null,
                    protectiveEarthingDimension:
                        this.formDetails.technicalInformation?.branchLine?.protectiveEarthingDimension || null,
                    cableDimensionId: this.formDetails.technicalInformation?.branchLine?.cableDimensionId || null
                }
            },
            changeBranchLineInformation: {
                meterPlacementId: this.formDetails.changeBranchLineInformation?.meterPlacementId || '',
                responsibleForSeal: this.formDetails.changeBranchLineInformation?.responsibleForSeal || '',
                newMeterRequested: this.formDetails.changeBranchLineInformation?.newMeterRequested || false
            },
            applications: {
                applications: [...this.formDetails.applications],
                hasApplicationsChanged: false
            },
            relatedForms: {
                relatedForms: [...this.formDetails.relatedForms],
                hasRelatedFormsChanged: false
            }
        });

        if (this.formDetails.payer) {
            this.isPayerDataVisible = true;
        }

        if (this.formDetails.contactPerson) {
            this.isInstallationContactPersonVisible = true;
        }

        this.initDropDownOptions();
    }

    getFormComparisonModel(paymentDetailsOnly: boolean): any {
        return paymentDetailsOnly
            ? {
                  // Contact person
                  contactPersonCompanyName: this.isInstallationContactPersonVisible
                      ? this.contactPersonForm.get('contactPersonCompanyName')?.value
                      : null,
                  contactPersonName: this.isInstallationContactPersonVisible
                      ? this.contactPersonForm.get('contactPersonName')?.value
                      : null,
                  contactPersonEmail: this.isInstallationContactPersonVisible
                      ? this.contactPersonForm.get('contactPersonEmail')?.value
                      : null,
                  contactPersonPhoneNumber: this.isInstallationContactPersonVisible
                      ? this.contactPersonForm.get('contactPersonPhoneNumber')?.value
                      : null,

                  //  Payer
                  payerName: this.getPayerFieldForComparison(this.payerForm.get('payerName')?.value),
                  payerEmail: this.getPayerFieldForComparison(this.payerForm.get('payerEmail')?.value),
                  payerType: this.getPayerFieldForComparison(this.payerForm.get('payerType')?.value, null),
                  payerContactPersonName: this.getPayerFieldForComparison(this.payerForm.get('payerContactPersonName')?.value),
                  payerContactPersonEmail: this.getPayerFieldForComparison(this.payerForm.get('payerContactPersonEmail')?.value),
                  payerContactPersonPhoneNumber: this.getPayerFieldForComparison(
                      this.payerForm.get('payerContactPersonPhoneNumber')?.value
                  ),
                  requisition: this.getPayerFieldForComparison(this.payerForm.get('requisition')?.value),
                  cvrOrSeNumber: this.getPayerFieldForComparison(this.payerForm.get('cvrOrSeNumber')?.value),
                  eanNumber: this.getPayerFieldForComparison(this.payerForm.get('eanNumber')?.value),
                  payerAddress: [
                      this.getPayerFieldForComparison(this.payerForm.get('payerAddress')?.value?.id),
                      this.getPayerFieldForComparison(this.payerForm.get('payerAddress')?.value?.text)
                  ]
              }
            : {
                  installationAddress: [
                      this.installationInformationForm.get('installationAddress')?.value?.id,
                      this.installationInformationForm.get('installationAddress')?.value?.text
                  ],
                  connectionPoint: [
                      this.installationInformationForm.get('connectionPoint')?.value?.id || '',
                      this.installationInformationForm.get('connectionPoint')?.value?.connectionPointNumber || '',
                      this.installationInformationForm.get('connectionPoint')?.value?.electricityConnectionStatusId || ''
                  ],
                  meterFrame: [
                      this.installationInformationForm.get('meterFrame')?.value?.id || '',
                      this.installationInformationForm.get('meterFrame')?.value?.meterFrameNumber || ''
                  ],
                  consumptionMeteringPoint: [
                      this.consumptionMeteringPoint?.value?.meteringPointId || '',
                      this.consumptionMeteringPoint?.value?.meteringPointVersionId || ''
                  ],
                  contactPersonCompanyName: this.isInstallationContactPersonVisible
                      ? this.contactPersonForm.get('contactPersonCompanyName')?.value
                      : null,
                  contactPersonName: this.isInstallationContactPersonVisible
                      ? this.contactPersonForm.get('contactPersonName')?.value
                      : null,
                  contactPersonEmail: this.isInstallationContactPersonVisible
                      ? this.contactPersonForm.get('contactPersonEmail')?.value
                      : null,
                  contactPersonPhoneNumber: this.isInstallationContactPersonVisible
                      ? this.contactPersonForm.get('contactPersonPhoneNumber')?.value
                      : null,
                  remarksToInstallation: this.installationInformationForm.get('remarksToInstallation')?.value,
                  tags: this.installationInformationForm.get('tags')?.value,

                  payerName: this.getPayerFieldForComparison(this.payerForm.get('payerName')?.value),
                  payerEmail: this.getPayerFieldForComparison(this.payerForm.get('payerEmail')?.value),
                  payerType: this.getPayerFieldForComparison(this.payerForm.get('payerType')?.value),
                  payerContactPersonName: this.getPayerFieldForComparison(this.payerForm.get('payerContactPersonName')?.value),
                  payerContactPersonEmail: this.getPayerFieldForComparison(this.payerForm.get('payerContactPersonEmail')?.value),
                  payerContactPersonPhoneNumber: this.getPayerFieldForComparison(
                      this.payerForm.get('payerContactPersonPhoneNumber')?.value
                  ),
                  requisition: this.getPayerFieldForComparison(this.payerForm.get('requisition')?.value),
                  cvrOrSeNumber: this.getPayerFieldForComparison(this.payerForm.get('cvrOrSeNumber')?.value),
                  eanNumber: this.getPayerFieldForComparison(this.payerForm.get('eanNumber')?.value),
                  payerAddress: [
                      this.getPayerFieldForComparison(this.payerForm.get('payerAddress')?.value?.id),
                      this.getPayerFieldForComparison(this.payerForm.get('payerAddress')?.value?.text)
                  ],

                  // instructionData
                  instructionDataMeterResponsible: this.instructionDataForm.get('meterResponsible')?.value || null,
                  instructionDataBranchLineResponsible: this.instructionDataForm.get('branchLineResponsible')?.value || null,
                  instructionDataConnectionFeeFee: this.instructionDataForm.get('connectionFeeFee')?.value || null,
                  instructionDataConnectionFeeValidUntilEod:
                      this.instructionDataForm.get('connectionFeeValidUntilEod')?.value || null,
                  instructionDataRemark: this.instructionDataForm.get('remark')?.value || null,

                  //technical Information
                  ...this.meterTypeForm.value,
                  ...this.connectionForm.value,
                  type: this.branchLineForm.value?.type,
                  protectionTypeOfMainProtection: this.branchLineForm.value?.protectionTypeOfMainProtection,
                  fuseSizeOfMainProtection: this.branchLineForm.value?.fuseSizeOfMainProtection,
                  fuseTypeOfMainProtection: this.branchLineForm.value?.fuseTypeOfMainProtection,
                  numberOfPairs: this.branchLineForm.value?.numberOfPairs,
                  protectiveEarthingDimension: this.branchLineForm.value?.protectiveEarthingDimension,
                  cableDimensionId: this.branchLineForm.value?.cableDimensionId,
                  branchLineMeterFrameId: this.branchLineForm.value?.branchLineMeterFrame?.id,

                  // Change Branch Line Information
                  sealInformationMeterPlacementId: this.changeBranchLineInformationForm?.get('meterPlacementId')?.value || null,
                  sealInformationResponsibleForSeal:
                      this.changeBranchLineInformationForm?.get('responsibleForSeal')?.value || null,
                  newMeterRequestedNewMeterRequested:
                      this.changeBranchLineInformationForm?.get('newMeterRequested')?.value || null,

                  //applications
                  hasApplicationsChanged: this.applicationsForm.get('hasApplicationsChanged')?.value || false,

                  //related forms
                  hasRelatedFormsChanged: this.relatedFormsForm.get('hasRelatedFormsChanged')?.value || false
              };
    }

    convertToComparisonModel(model: ChangeBranchLine, paymentDetailsOnly: boolean): any {
        return paymentDetailsOnly
            ? {
                  // Contact person
                  contactPersonCompanyName: this.formDetails.contactPerson?.companyName || '',
                  contactPersonName: this.formDetails.contactPerson?.name || '',
                  contactPersonEmail: this.formDetails.contactPerson?.email || '',
                  contactPersonPhoneNumber: this.formDetails.contactPerson?.phoneNumber || '',

                  //  Payer
                  payerName: this.formDetails.payer?.name || '',
                  payerType: this.formDetails.payer?.type || '',
                  payerEmail: this.formDetails.payer?.email || '',
                  payerContactPersonName: this.formDetails.payer?.contactPerson?.name || '',
                  payerContactPersonEmail: this.formDetails.payer?.contactPerson?.email || '',
                  payerContactPersonPhoneNumber: this.formDetails.payer?.contactPerson?.phoneNumber || '',
                  requisition: this.formDetails.payer?.requisition || '',
                  cvrOrSeNumber: this.formDetails.payer?.cvrOrSeNumber || '',
                  eanNumber: this.formDetails.payer?.eanNumber || '',
                  payerAddress: [
                      this.formDetails.payer?.address?.carId || '',
                      this.formDetails.payer?.address?.formattedAddress || ''
                  ]
              }
            : {
                  installationAddress: [
                      model.installationAddress?.carId || '',
                      model.installationAddress?.formattedAddress || ''
                  ],
                  connectionPoint: [model.connectionPoint?.id || '', model.connectionPoint?.connectionPointNumber || '', model.connectionPoint?.electricityConnectionStatusId || ''],
                  meterFrame: [model.meterFrame?.id || '', model.meterFrame?.meterFrameNumber || ''],
                  consumptionMeteringPoint: [
                      model.consumptionMeteringPoint?.meteringPointId || '',
                      model.consumptionMeteringPoint?.meteringPointVersionId || ''
                  ],
                  contactPersonCompanyName: model.contactPerson?.companyName || '',
                  contactPersonName: model.contactPerson?.name || '',
                  contactPersonEmail: model.contactPerson?.email || '',
                  contactPersonPhoneNumber: model.contactPerson?.phoneNumber || '',
                  remarksToInstallation: model.remarksToInstallation || '',
                  tags: model.tags.map((t) => t.id),

                  //  Payer
                  payerName: model.payer?.name || '',
                  payerType: model.payer?.type || '',
                  payerEmail: model.payer?.email || '',
                  payerContactPersonName: model.payer?.contactPerson?.name || '',
                  payerContactPersonEmail: model.payer?.contactPerson?.email || '',
                  payerContactPersonPhoneNumber: model.payer?.contactPerson?.phoneNumber || '',
                  requisition: model.payer?.requisition || '',
                  cvrOrSeNumber: model.payer?.cvrOrSeNumber || '',
                  eanNumber: model.payer?.eanNumber || '',
                  payerAddress: [model.payer?.address?.carId || '', model.payer?.address?.formattedAddress || ''],

                  // instructionData
                  instructionDataMeterResponsible: model.instructionData?.meterResponsible || null,
                  instructionDataBranchLineResponsible: model.instructionData?.branchLineResponsible || null,
                  instructionDataConnectionFeeFee: model.instructionData?.connectionFee?.fee || null,
                  instructionDataConnectionFeeValidUntilEod: model.instructionData?.connectionFee?.validUntilEod || null,
                  instructionDataRemark: model.instructionData?.remark || null,

                  // Technical information
                  meterSize: model.technicalInformation?.meterType?.meterSize || null,
                  connectionTypeId: model.technicalInformation?.meterType?.connectionTypeId || null,
                  connectionTypeChange: model.technicalInformation?.meterType?.connectionTypeChange || null,
                  meterTransformerId: model.technicalInformation?.meterType?.meterTransformerId || null,
                  meterTransformerRemark: model.technicalInformation?.meterType?.meterTransformerRemark || null,
                  transformerStationNumber: model.technicalInformation?.connection?.transformerStationNumber || null,
                  cabinetNumber: model.technicalInformation?.connection?.cabinetNumber || null,
                  groundingMethod: model.technicalInformation?.connection?.groundingMethod || null,
                  fuseSizeOfPreProtection: model.technicalInformation?.connection?.preProtection?.size || null,
                  protectionTypeOfPreProtection: model.technicalInformation?.connection?.preProtection?.protectionType || null,
                  type: model.technicalInformation?.branchLine?.type || null,
                  branchLineMeterFrameId: model.technicalInformation?.branchLine?.meterFrame?.id || null,
                  fuseTypeOfMainProtection: model.technicalInformation?.branchLine?.mainProtection?.fuseType || null,
                  fuseSizeOfMainProtection: model.technicalInformation?.branchLine?.mainProtection?.size || null,
                  protectionTypeOfMainProtection: model.technicalInformation?.branchLine?.mainProtection?.protectionType || null,
                  numberOfPairs: model.technicalInformation?.branchLine?.numberOfPairs || null,
                  protectiveEarthingDimension: model.technicalInformation?.branchLine?.protectiveEarthingDimension || null,
                  cableDimensionId: model.technicalInformation?.branchLine?.cableDimensionId || null,

                  // Change Branch Line Information
                  sealInformationMeterPlacementId: model.changeBranchLineInformation?.meterPlacementId || null,
                  sealInformationResponsibleForSeal: model.changeBranchLineInformation?.responsibleForSeal || null,
                  newMeterRequestedNewMeterRequested: model.changeBranchLineInformation?.newMeterRequested || null,

                  //applications
                  hasApplicationsChanged: false,

                  // related forms
                  hasRelatedFormsChanged: false
              };
    }

    protected override initDropDownOptions() {
        super.initDropDownOptions();
        this.initBranchLineMeterFrameOptions();
    }

    initBranchLineMeterFrameOptions() {
        this.branchLineMeterFrameOptions =
            this.formDetails.technicalInformation?.branchLine?.meterFrame &&
            this.formDetails.technicalInformation?.branchLine?.connectionPointId
                ? [
                      <MeterFrameSearchItemModel>{
                          id: this.formDetails.technicalInformation.branchLine.meterFrame.id,
                          meterFrameNumber: this.formDetails.technicalInformation.branchLine.meterFrame.meterFrameNumber,
                          connectionPointId: this.formDetails.technicalInformation.branchLine.connectionPointId
                      }
                  ]
                : [];
    }

    protected override updateFormData(
        installationFormId: string,
        es_message_id: string,
        row_version: string,
        body: ChangeBranchLineUpdate
    ): Observable<InstallationFormsApiResponse<void>> {
        return this.client.updateChangeBranchLineFormData(installationFormId, es_message_id, row_version, body);
    }

    protected override createFormDataUpdate(): ChangeBranchLineUpdate {
        return new ChangeBranchLineUpdate({
            changeBranchLineInformationUpdate: this.createChangeBranchLineInformationUpdate(),
            installationInformationUpdate: this.createInstallationInformationUpdate(),
            payerUpdate: this.isPayerDataVisible ? this.createPayerUpdate() : undefined,
            technicalInformationUpdate: this.createTechnicalInformationUpdate(),
            instructionDataUpdate: this.createInstructionDataUpdate(),
            applicationsUpdate: this.applicationsForm.get('applications')?.value,
            relatedFormsUpdate: this.relatedFormsForm
                .get('relatedForms')
                ?.value.filter((x: RelatedForm) => x.relationType === FormsRelationType.Manual)
                .map((x: RelatedForm) => new RelatedFormUpdate({ formId: x.formId }))
        });
    }

    protected override createPaymentDetailsUpdate(): PaymentDetailsUpdate {
        const update = new PaymentDetailsUpdate({
            contactPerson: this.isInstallationContactPersonVisible ? this.createContactPersonUpdate() : undefined,
            payer: this.isPayerDataVisible ? this.createPayerUpdate() : undefined
        });
        return update;
    }

    private createContactPersonUpdate(): ContactPerson {
        const contactPersonFormValue = this.contactPersonForm.value;
        return new ContactPerson({
            companyName: getValueOrDefault(contactPersonFormValue.contactPersonCompanyName),
            name: getValueOrDefault(contactPersonFormValue.contactPersonName),
            email: getValueOrDefault(contactPersonFormValue.contactPersonEmail),
            phoneNumber: getValueOrDefault(contactPersonFormValue.contactPersonPhoneNumber)
        });
    }

    private createInstallationInformationUpdate(): InstallationInformationUpdate {
        const installationInformationFormValue = this.installationInformationForm.value;
        const contactPersonFormValue = this.contactPersonForm.value;
        return new InstallationInformationUpdate({
            contactPerson: this.isInstallationContactPersonVisible
                ? new ContactPerson({
                      companyName: getValueOrDefault(contactPersonFormValue.contactPersonCompanyName),
                      name: getValueOrDefault(contactPersonFormValue.contactPersonName),
                      email: getValueOrDefault(contactPersonFormValue.contactPersonEmail),
                      phoneNumber: getValueOrDefault(contactPersonFormValue.contactPersonPhoneNumber)
                  })
                : undefined,
            installationInformationCarId: getValueOrDefault(installationInformationFormValue.installationAddress?.id),
            remarksToInstallation: getValueOrDefault(installationInformationFormValue.remarksToInstallation),
            connectionPointId: getValueOrDefault(installationInformationFormValue.connectionPoint?.id),
            meterFrameId: getValueOrDefault(installationInformationFormValue.meterFrame?.id),
            consumptionMeteringPoint: createMeteringPoint(installationInformationFormValue.consumptionMeteringPoint),
            tags: installationInformationFormValue.tags || []
        });
    }

    private createPayerUpdate(): PayerUpdate {
        const payerFormValue = this.payerForm.value;
        return new PayerUpdate({
            contactPerson: new ContactPerson({
                email: getValueOrDefault(payerFormValue.payerContactPersonEmail),
                name: getValueOrDefault(payerFormValue.payerContactPersonName),
                phoneNumber: getValueOrDefault(payerFormValue.payerContactPersonPhoneNumber)
            }),
            payerType: payerFormValue.payerType,
            payerCarId: getValueOrDefault(payerFormValue.payerAddress?.id),
            name: getValueOrDefault(payerFormValue.payerName),
            email: getValueOrDefault(payerFormValue.payerEmail),
            requisition: getValueOrDefault(payerFormValue.requisition),
            cvrOrSeNumber: getValueOrDefault(payerFormValue.cvrOrSeNumber),
            eanNumber: getValueOrDefault(payerFormValue.eanNumber)
        });
    }

    private createInstructionDataUpdate(): InstructionDataUpdate {
        const instructionDataFormValue = this.instructionDataForm.value;

        return new InstructionDataUpdate({
            meterResponsible: instructionDataFormValue.meterResponsible as MeterResponsible,
            branchLineResponsible: instructionDataFormValue.branchLineResponsible as BranchLineResponsible,
            connectionFee: !!instructionDataFormValue.connectionFeeFee
                ? new ConnectionFee({
                      currency: 'DKK', // So far there's no support for different currencies. Hardcoded DKK
                      fee: instructionDataFormValue.connectionFeeFee,
                      validUntilEod: instructionDataFormValue.connectionFeeValidUntilEod
                  })
                : undefined,
            remark: getValueOrDefault(instructionDataFormValue.remark)
        });
    }

    private createTechnicalInformationUpdate(): TechnicalInformationUpdate {
        const technicalInformationFormValue = this.technicalInformationForm.value;
        return new TechnicalInformationUpdate({
            meterTypeUpdate: new MeterTypeUpdate({
                meterSize: technicalInformationFormValue.meterType.meterSize,
                connectionTypeId: technicalInformationFormValue.meterType.connectionTypeId,
                connectionTypeChange: technicalInformationFormValue.meterType.connectionTypeChange,
                meterTransformerId: technicalInformationFormValue.meterType.meterTransformerId,
                meterTransformerRemark: technicalInformationFormValue.meterType.meterTransformerRemark
            }),
            connectionUpdate: new Connection({
                transformerStationNumber: getValueOrDefault(technicalInformationFormValue.connection.transformerStationNumber),
                cabinetNumber: getValueOrDefault(technicalInformationFormValue.connection.cabinetNumber),
                groundingMethod: technicalInformationFormValue.connection.groundingMethod,
                preProtection: technicalInformationFormValue.connection.protectionTypeOfPreProtection
                    ? new PreProtection({
                          protectionType: technicalInformationFormValue.connection.protectionTypeOfPreProtection,
                          size: technicalInformationFormValue.connection.fuseSizeOfPreProtection
                      })
                    : undefined
            }),
            branchLineUpdate: new BranchLineUpdate({
                type: technicalInformationFormValue.branchLine.type,
                meterFrameId: getValueOrDefault(technicalInformationFormValue.branchLine.branchLineMeterFrame?.id),
                mainProtection: technicalInformationFormValue.branchLine.protectionTypeOfMainProtection
                    ? new MainProtection({
                          protectionType: technicalInformationFormValue.branchLine.protectionTypeOfMainProtection,
                          fuseType: getValueOrDefault(technicalInformationFormValue.branchLine.fuseTypeOfMainProtection),
                          size: technicalInformationFormValue.branchLine.fuseSizeOfMainProtection
                      })
                    : undefined,
                numberOfPairs: technicalInformationFormValue.branchLine.numberOfPairs,
                protectiveEarthingDimension: technicalInformationFormValue.branchLine.protectiveEarthingDimension,
                cableDimensionId: technicalInformationFormValue.branchLine.cableDimensionId
            })
        });
    }

    private createChangeBranchLineInformationUpdate(): ChangeBranchLineInformationUpdate {
        const sealInformationFormValue = this.changeBranchLineInformationForm.value;
        return new ChangeBranchLineInformationUpdate({
            meterPlacementId: sealInformationFormValue.meterPlacementId,
            responsibleForSeal: sealInformationFormValue.responsibleForSeal,
            newMeterRequested: sealInformationFormValue.newMeterRequested
        });
    }

    areBranchLineTypeIdentifiersVisible = () => {
        return this.form?.get('technicalInformation.branchLine.type')?.value === BranchLineType.Existing;
    };

    isConnectionTypeVisible = () => {
        return this.getMeterSize() === MeterSize.Below63A;
    };

    isMeterTransformerVisible = () => {
        return this.getMeterSize() === MeterSize.Above63A;
    };

    getMeterSize() {
        return this.form?.get('technicalInformation.meterType.meterSize')?.value;
    }

    isMeterTransformerRemarkVisible = () => {
        return this.isMeterTransformerVisible() && this.getMeterTransformer() === METER_TRANSFORMER_OTHER_VALUE_ID;
    };

    getMeterTransformer() {
        return this.form?.get('technicalInformation.meterType.meterTransformerId')?.value;
    }

    isPreProtectionFuseSizeRequired = () => {
        const value = this.form?.get('technicalInformation.connection.protectionTypeOfPreProtection')?.value;
        return value === PreProtectionType.MaxBreaker || value === PreProtectionType.Fuse;
    };

    isFuseTypeOfMainProtectionRequired = () => {
        const value = this.form?.get('technicalInformation.branchLine.protectionTypeOfMainProtection')?.value;
        return value === MainProtectionType.Fuse;
    };
}
