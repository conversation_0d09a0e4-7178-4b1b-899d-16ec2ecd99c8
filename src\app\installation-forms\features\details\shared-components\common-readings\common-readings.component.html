<p-table
    [columns]="columns"
    responsiveLayout="scroll"
    [value]="items"
    [(first)]="first"
    [(rows)]="maxRowsPerPage"
    [totalRecords]="items.length"
    [paginator]="items.length > maxRowsPerPage"
    currentPageReportTemplate="{first} - {last} {{ 'common.of' | translate }} {totalRecords}"
    [showCurrentPageReport]="true"
    dataKey="id"
    [autoLayout]="true">
    <ng-template pTemplate="header" let-columns>
        <tr>
            <th id="columnHeader" *ngFor="let col of columns" class="columnHeader">
                <div fxLayout="row">
                    {{ col.header | translate }}
                </div>
            </th>
        </tr>
    </ng-template>

    <ng-template pTemplate="body" let-rowData let-columns="columns">
        <ng-container class="zebra-container">
            <tr>
                <td *ngFor="let col of columns">
                    <div fxLayout="row">
                        {{
                            col.valueTranslationKeyPath
                                ? (col.valueTranslationKeyPath + '.' + rowData[col.field] | translate)
                                : rowData[col.field]
                        }}
                    </div>
                </td>
            </tr>
        </ng-container>
    </ng-template>
</p-table>
