import { Route } from '@angular/router';
import { LocalDevelopmentComponent, provideElementsHostRouting } from '@kmd-elements-ui/host-sdk';

export const appRoutes: Route[] = provideElementsHostRouting(
    [
        {
            path: '',
            loadChildren: () => import('./installation-forms/installation-forms.module').then((m) => m.InstallationFormsModule)
        },
        {
            path: 'settings',
            loadChildren: () =>
                import('./installation-forms-settings/installation-forms-settings.module').then(
                    (m) => m.InstallationFormsSettingsModule
                )
        }
    ],
    {
        layoutComponent: LocalDevelopmentComponent
    }
);
