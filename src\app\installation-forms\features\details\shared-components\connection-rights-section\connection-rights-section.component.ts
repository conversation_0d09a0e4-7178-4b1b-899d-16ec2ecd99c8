import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { Subscription, take } from 'rxjs';
import { AddConnectionRights, InstallationFormsClient } from 'src/app/api/installation-forms-client';
import { SMALL_TABLE_ROWS_PER_PAGE } from 'src/app/core/constants/constants';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { MeterFrameSearchItemModel } from 'src/app/shared/components/meter-frame-search/meter-frame-search-item.model';
import { v4 as uuidv4 } from 'uuid';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';
import { WidgetWithFormComponent } from '../../base-components/widgets/widget-with-form.component';
import { TransferConnectionRightFormArrayItem } from './transfer-connection-right-form-array-item.model';

@Component({
    selector: 'app-connection-rights-section',
    templateUrl: './connection-rights-section.component.html',
    styleUrls: ['./connection-rights-section.component.scss'],
    standalone: false
})
export class ConnectionRightsSectionComponent extends WidgetWithFormComponent implements OnInit, OnDestroy {
    private _form!: FormGroup;

    @Input()
    get form(): FormGroup {
        return this._form;
    }

    set form(value: FormGroup) {
        this._form = value;
        this.setAccordionActiveValues();
    }
    @Input() formHasChanges!: boolean;

    @Output() refreshRequested = new EventEmitter<void>();

    gridItems: any[] = [];

    columns: { field: string; header: string }[] = [
        {
            field: 'meterFrameNumber',
            header: 'connectionRights.columnHeaders.meterFrameNumber'
        },
        {
            field: 'value',
            header: 'connectionRights.columnHeaders.value'
        }
    ];

    accordionActiveValues: string = '';
    transferConnectionRightsTabName: string = 'connectionRights';

    updateConnectionRightsButtonDisabled: boolean = true;
    totalConnectionRightsTransfer: number = 0;
    isAddingOpened: boolean = false;
    maxRowsPerPage = SMALL_TABLE_ROWS_PER_PAGE;
    addTransferForm: FormGroup;
    meterFrameOptions: MeterFrameSearchItemModel[] = [];
    private commonTranslations: any;
    private widgetTranslations: any;

    subscription: Subscription = new Subscription();

    constructor(
        private fb: FormBuilder,
        private readonly messageServiceHelper: MessageServiceHelper,
        readonly formDataService: FormDataService,
        private readonly router: Router,
        private readonly translateService: TranslateService,
        private readonly client: InstallationFormsClient
    ) {
        super(formDataService);
        this.addTransferForm = this.fb.group({
            meterFrame: [null, Validators.required],
            value: [null, [Validators.required]]
        });
    }

    ngOnInit(): void {
        this.commonTranslations = this.translateService.instant('common');
        this.widgetTranslations = this.translateService.instant('connectionRights');

        this.form.get('transferConnectionRights')?.valueChanges.subscribe(() => {
            this.recalculateGridItems();
            this.updateTotalConnectionRightsTransfer();
        });
        this.recalculateGridItems();
        this.updateTotalConnectionRightsTransfer();
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    canUpdateConnectionRights(): boolean {
        return !(this.formHasChanges || this.form.disabled || this.isProcessing);
    }

    onUpdateConnectionRightsClicked(): void {
        if (!this.canUpdateConnectionRights()) {
            return;
        }

        this.updateConnectionRightsButtonDisabled = true;
        const scopeOfDelivery = this.form.get('scopeOfDeliverySize');
        if (scopeOfDelivery?.valid) {
            this.client
                .addConnectionRights(
                    this.formDataService.formId!,
                    uuidv4(),
                    this.formDataService.rowVersion,
                    new AddConnectionRights({
                        scopeOfDeliverySize: scopeOfDelivery?.value
                    })
                )
                .pipe(take(1))
                .subscribe({
                    error: (error) => {
                        this.updateConnectionRightsButtonDisabled = false;
                        this.messageServiceHelper.showError({
                            detail: this.widgetTranslations['updateConnectionRightsFailed'],
                            key: this.formDataService.formId!
                        });
                    },
                    complete: () => {
                        this.updateConnectionRightsButtonDisabled = false;
                        this.messageServiceHelper.showSuccess({
                            detail: this.widgetTranslations['updateConnectionRightsSucceded'],
                            key: this.formDataService.formId!
                        });

                        this.refreshRequested.emit();
                    }
                });
        } else {
            this.updateConnectionRightsButtonDisabled = false;
            this.messageServiceHelper.showWarning({
                detail: this.commonTranslations['allRequiredFieldsShouldBeCompleted'],
                key: this.formDataService.formId!
            });
        }
    }

    createTransfer() {
        this.isAddingOpened = true;
    }

    cancelCreateTransfer() {
        this.isAddingOpened = false;
        this.addTransferForm.reset();
    }

    saveTransfer() {
        if (!this.addTransferForm.valid) {
            this.messageServiceHelper.showWarning({
                detail: this.commonTranslations['allRequiredFieldsShouldBeCompleted'],
                key: this.formDataService.formId!
            });
            return;
        }

        let transfers = this.form.get('transferConnectionRights')?.value as TransferConnectionRightFormArrayItem[];

        const newTransfer = this.addTransferForm.value;
        transfers.unshift(newTransfer);
        this.form.patchValue({
            transferConnectionRights: [...transfers],
            hasTransfersChanged: true
        });
        this.addTransferForm.reset();
        this.isAddingOpened = false;
    }

    removeItem(index: number): void {
        let transfers = this.form.get('transferConnectionRights')?.value as TransferConnectionRightFormArrayItem[];
        if (index !== -1) {
            transfers.splice(index, 1);
        }
        this.form.patchValue({
            transferConnectionRights: transfers,
            hasTransfersChanged: true
        });
    }

    updateTotalConnectionRightsTransfer() {
        this.totalConnectionRightsTransfer =
            (this.form.get('transferConnectionRights')?.value as TransferConnectionRightFormArrayItem[]).reduce(
                (sum, transfer) => {
                    return sum + (transfer?.value || 0);
                },
                0
            ) || 0;
    }

    async onMeterFrameClicked(meterFrameId: string, connectionPointId: string) {
        await this.router.navigate([`connection-points/${connectionPointId}/meter-frames/${meterFrameId}`]);
    }

    private recalculateGridItems = () => {
        this.gridItems =
            (this.form.get('transferConnectionRights')?.value as TransferConnectionRightFormArrayItem[])?.map((formItem) => {
                return {
                    meterFrameNumber: formItem.meterFrame?.meterFrameNumber,
                    meterFrameId: formItem.meterFrame?.id,
                    connectionPointId: formItem.meterFrame?.connectionPointId,
                    value: formItem.value
                };
            }) || [];
    };

    setAccordionActiveValues() {
        this.accordionActiveValues =
            this.form.get('transferConnectionRights')?.value.length > 0 ? this.transferConnectionRightsTabName : '';
    }
}
