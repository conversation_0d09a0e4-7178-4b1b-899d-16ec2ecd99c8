import { Component, Input } from '@angular/core';
import { FormGroup } from '@angular/forms';

@Component({
    selector: 'app-ready-for-meter-flag-template',
    templateUrl: './ready-for-meter-flag-template.component.html',
    standalone: false
})
export class ReadyForMeterFlagTemplateComponent {
    @Input()
    flagControl!: FormGroup;

    @Input()
    readyForMeterDate?: Date;

    get visibleReadyForMeterDate(): Date | undefined {
        return this.readyForMeterDate && this.flagControl.value.isSet ? this.readyForMeterDate : undefined;
    }
}
