import { Component } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { Observable } from 'rxjs';
import {
    BranchLineResponsible,
    BranchLineUpdate,
    Connection,
    ConnectionFee,
    ConnectionRightsTransfersUpdate,
    ConnectionRightsUpdate,
    ContactPerson,
    DeliveryInformationUpdate,
    DeliveryOption,
    FormCategory,
    FormsRelationType,
    InstallationFormsApiResponse,
    InstallationFormsClient,
    InstallationInformationUpdate,
    InstructionDataUpdate,
    MainProtection,
    MainProtectionType,
    MeterDeliveryOptionsUpdate,
    MeterResponsible,
    MeterSize,
    MeterTypeUpdate,
    NewInstallation,
    NewInstallationUpdate,
    PayerType,
    PayerUpdate,
    PaymentDetailsUpdate,
    Person,
    PreProtection,
    PreProtectionType,
    RelatedForm,
    RelatedFormUpdate,
    TechnicalInformationUpdate,
    TransferConnectionRightUom,
    VoltageLevelUpdate
} from 'src/app/api/installation-forms-client';
import { ChangesModel } from 'src/app/core/constants/changes-details';
import {
    METER_TRANSFORMER_OTHER_VALUE_ID,
    VOLTAGE_LEVEL_B_LOW_VALUE_ID,
    VOLTAGE_LEVEL_C_VALUE_ID
} from 'src/app/core/constants/constants';
import { RemarkLength } from 'src/app/core/constants/field-lengths';
import { AutomaticFormRefreshService } from 'src/app/core/services/automatic-form-refresh/automatic-form-refresh.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { createMeteringPoint } from 'src/app/core/utils/create-metering-point';
import { getValueOrDefault } from 'src/app/core/utils/get-value-or-default';
import addressLookupValidator from 'src/app/core/utils/validators/address-lookup/address-lookup.validator';
import conditionallyRequiredAddressLookupValidator from 'src/app/core/utils/validators/address-lookup/conditionally-required-address-lookup.validator';
import conditionallyRequiredValidator from 'src/app/core/utils/validators/conditionally-required/conditionally-required.validator';
import dateMustNotBePastValidator from 'src/app/core/utils/validators/date-time/date-must-not-be-past.validator';
import { EmailValidator } from 'src/app/core/utils/validators/email/email.validator';
import { MeterFrameSearchItemModel } from 'src/app/shared/components/meter-frame-search/meter-frame-search-item.model';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';
import { CoreWidgetComponent } from '../../base-components/widgets/core-widget.component';
import { TransferConnectionRightFormArrayItem } from '../../shared-components/connection-rights-section/transfer-connection-right-form-array-item.model';
import { NewInstallationChanges } from './new-installation-changes.model';

@Component({
    selector: 'app-new-installation-widget',
    templateUrl: './new-installation-widget.component.html',
    styleUrls: ['./new-installation-widget.component.scss'],
    standalone: false
})
export class NewInstallationWidgetComponent extends CoreWidgetComponent<NewInstallation, NewInstallationUpdate> {
    public gridAreasOptions: string[] = [];
    override accordionActiveIndexes: number[] = [0, 1, 2, 3, 4];
    override payerPanelIndex: number = 7;

    get meterTypeForm(): FormGroup {
        return this.form.get('technicalInformation.meterType') as FormGroup;
    }

    get connectionForm(): FormGroup {
        return this.form.get('technicalInformation.connection') as FormGroup;
    }

    get branchLineForm(): FormGroup {
        return this.form.get('technicalInformation.branchLine') as FormGroup;
    }

    constructor(
        protected fb: FormBuilder,
        protected readonly messageServiceHelper: MessageServiceHelper,
        protected readonly translateService: TranslateService,
        protected readonly client: InstallationFormsClient,
        protected readonly formDataService: FormDataService,
        protected readonly automaticFormRefreshService: AutomaticFormRefreshService
    ) {
        super(formDataService, automaticFormRefreshService, translateService, messageServiceHelper, client);
        this.initForm();
        this.setChangesModel();
    }

    protected override initForm() {
        this.form = this.fb.group({
            installationInformation: this.fb.group({
                installationAddress: [{ id: '', text: '' }, [addressLookupValidator()]],
                gridAreaId: [
                    '',
                    [
                        (control: AbstractControl) => conditionallyRequiredValidator(control, this.isGridAreaRequired),
                        Validators.maxLength(25)
                    ]
                ],
                remarksToInstallation: ['', [Validators.maxLength(1000)]],
                connectionPoint: [
                    { id: '', connectionPointNumber: '' },
                    [(control: AbstractControl) => conditionallyRequiredValidator(control, this.isConnectionPointRequired)]
                ],
                meterFrame: [{ id: '', meterFrameNumber: '' }],
                consumptionMeteringPoint: [{ meteringPointId: '', meteringPointVersionId: '' }],
                tags: []
            }),
            payer: this.fb.group({
                payerName: ['', [Validators.maxLength(100), Validators.required]],
                payerType: ['', Validators.required],
                payerEmail: [
                    '',
                    [
                        (control: AbstractControl) => conditionallyRequiredValidator(control, this.isPayerTypeNotPublic),
                        EmailValidator()
                    ]
                ],
                payerContactPersonName: ['', [Validators.maxLength(100)]],
                payerContactPersonEmail: ['', [EmailValidator()]],
                payerContactPersonPhoneNumber: ['', [Validators.maxLength(20)]],
                requisition: '',
                cvrOrSeNumber: [
                    '',
                    [
                        (control: AbstractControl) => conditionallyRequiredValidator(control, this.isPayerTypeCompany),
                        Validators.pattern('^[0-9]{8}$')
                    ]
                ],
                eanNumber: [
                    '',
                    [
                        (control: AbstractControl) => conditionallyRequiredValidator(control, this.isPayerTypePublic),
                        Validators.pattern('^[0-9]{13}$')
                    ]
                ],
                payerAddress: [{ id: '', text: '' }, [addressLookupValidator()]]
            }),
            contactPerson: this.fb.group({
                contactPersonCompanyName: ['', [Validators.maxLength(100)]],
                contactPersonName: ['', [Validators.maxLength(100)]],
                contactPersonEmail: ['', [EmailValidator()]],
                contactPersonPhoneNumber: ['', [Validators.maxLength(20)]]
            }),
            voltageLevel: this.fb.group({
                voltageLevelId: [
                    null,
                    [
                        (control: AbstractControl) => conditionallyRequiredValidator(control, this.supportsVoltageLevel),
                        Validators.maxLength(100)
                    ]
                ],
                currentTransformer: [
                    '',
                    [
                        (control: AbstractControl) => conditionallyRequiredValidator(control, this.transformerDataRequired),
                        Validators.maxLength(100)
                    ]
                ],
                voltageTransformer: [
                    '',
                    [
                        (control: AbstractControl) => conditionallyRequiredValidator(control, this.transformerDataRequired),
                        Validators.maxLength(100)
                    ]
                ]
            }),
            technicalInformation: this.fb.group({
                meterType: this.fb.group({
                    meterPlacementId: ['', [Validators.maxLength(100), Validators.required]],
                    installInConstructionPhase: [
                        false,
                        [
                            (control: AbstractControl) =>
                                conditionallyRequiredValidator(control, this.isInstallInConstructionPhaseRequired)
                        ]
                    ],
                    meterSize: [null, [Validators.required]],
                    connectionTypeId: [
                        null,
                        [(control: AbstractControl) => conditionallyRequiredValidator(control, this.isConnectionTypeVisible)]
                    ],
                    meterTransformerId: [
                        null,
                        [(control: AbstractControl) => conditionallyRequiredValidator(control, this.isMeterTransformerVisible)]
                    ],
                    meterTransformerRemark: [
                        null,
                        [
                            (control: AbstractControl) =>
                                conditionallyRequiredValidator(control, this.isMeterTransformerRemarkVisible)
                        ]
                    ]
                }),
                connection: this.fb.group({
                    transformerStationNumber: ['', [Validators.maxLength(100)]],
                    cabinetNumber: ['', [Validators.maxLength(100)]],
                    groundingMethod: [null, [Validators.required]],
                    protectionTypeOfPreProtection: [null, [Validators.required]],
                    fuseSizeOfPreProtection: [
                        null,
                        [
                            (control: AbstractControl) =>
                                conditionallyRequiredValidator(control, this.isPreProtectionFuseSizeRequired),
                            Validators.min(1),
                            Validators.max(999999999)
                        ]
                    ],
                    phaseCount: [
                        null,
                        [
                            (control: AbstractControl) => conditionallyRequiredValidator(control, this.isPhaseCountRequired),
                            Validators.min(1),
                            Validators.max(3)
                        ]
                    ]
                }),
                branchLine: this.fb.group({
                    type: [null, [Validators.required]],
                    branchLineMeterFrame: [null, []],
                    protectionTypeOfMainProtection: [null, [Validators.required]],
                    fuseSizeOfMainProtection: [null, [Validators.required, Validators.min(1), Validators.max(999999999)]],
                    fuseTypeOfMainProtection: [
                        null,
                        [
                            (control: AbstractControl) =>
                                conditionallyRequiredValidator(control, this.isFuseTypeOfMainProtectionRequired)
                        ]
                    ],
                    numberOfPairs: [null, [Validators.min(1), Validators.max(1000)]],
                    protectiveEarthingDimension: [null, []],
                    cableDimensionId: [null, []]
                })
            }),
            instructionData: this.fb.group({
                meterResponsible: '',
                branchLineResponsible: '',
                connectionFeeValidUntilEod: ['', [(control: AbstractControl) => dateMustNotBePastValidator(control)]],
                connectionFeeFee: [null, [Validators.min(-999999999), Validators.max(999999999)]], // Connection fee can be negative, that's not a mistake
                remark: ['', [Validators.maxLength(RemarkLength)]]
            }),
            meterDeliveryOptions: this.fb.group({
                requestedConnectionDateEod: [
                    null,
                    [Validators.required, (control: AbstractControl) => dateMustNotBePastValidator(control)]
                ],
                deliveryOption: [null, [Validators.required]],
                name: [
                    '',
                    [
                        (control: AbstractControl) => conditionallyRequiredValidator(control, this.shouldShowDelivery),
                        Validators.maxLength(100)
                    ]
                ],
                attention: [
                    '',
                    [
                        (control: AbstractControl) => conditionallyRequiredValidator(control, this.shouldShowDelivery),
                        Validators.maxLength(100)
                    ]
                ],
                meterDeliveryAddress: [
                    { id: '', text: '' },
                    [conditionallyRequiredAddressLookupValidator(this.shouldShowDelivery)]
                ]
            }),
            applications: this.fb.group({
                applications: [],
                hasApplicationsChanged: false
            }),
            relatedForms: this.fb.group({
                relatedForms: [],
                hasRelatedFormsChanged: false
            }),
            connectionRights: this.fb.group({
                transferConnectionRights: [],
                hasTransfersChanged: false,
                scopeOfDeliverySize: [null, [Validators.required, Validators.min(1), Validators.max(999999999)]]
            })
        });
    }

    protected override setChangesModel() {
        this.changesModel = new NewInstallationChanges() as unknown as ChangesModel;
    }

    protected override supplyFormData() {
        this.form.setValue({
            installationInformation: {
                installationAddress: {
                    id: this.formDetails.installationAddress?.carId || '',
                    text: this.formDetails.installationAddress?.formattedAddress || ''
                },
                gridAreaId: this.formDetails.gridAreaId,
                remarksToInstallation: this.formDetails.remarksToInstallation || '',
                connectionPoint: {
                    id: this.formDetails.connectionPoint?.id || '',
                    connectionPointNumber: this.formDetails.connectionPoint?.connectionPointNumber || '',
                    electricityConnectionStatusId: this.formDetails.connectionPoint?.electricityConnectionStatusId || ''
                },
                meterFrame: {
                    id: this.formDetails.meterFrame?.id || '',
                    meterFrameNumber: this.formDetails.meterFrame?.meterFrameNumber || ''
                },
                consumptionMeteringPoint: {
                    meteringPointId: this.formDetails.consumptionMeteringPoint?.meteringPointId || '',
                    meteringPointVersionId: this.formDetails.consumptionMeteringPoint?.meteringPointVersionId || ''
                },
                tags: this.formDetails.tags.map((t) => t.id) || []
            },
            contactPerson: {
                contactPersonCompanyName: this.formDetails.contactPerson?.companyName || '',
                contactPersonName: this.formDetails.contactPerson?.name || '',
                contactPersonEmail: this.formDetails.contactPerson?.email || '',
                contactPersonPhoneNumber: this.formDetails.contactPerson?.phoneNumber || ''
            },
            payer: {
                payerName: this.formDetails.payer?.name || '',
                payerEmail: this.formDetails.payer?.email || '',
                payerType: this.formDetails.payer?.type || PayerType.Private,
                payerContactPersonName: this.formDetails.payer?.contactPerson?.name || '',
                payerContactPersonEmail: this.formDetails.payer?.contactPerson?.email || '',
                payerContactPersonPhoneNumber: this.formDetails.payer?.contactPerson?.phoneNumber || '',
                requisition: this.formDetails.payer?.requisition || '',
                cvrOrSeNumber: this.formDetails.payer?.cvrOrSeNumber || '',
                eanNumber: this.formDetails.payer?.eanNumber || '',
                payerAddress: {
                    id: this.formDetails.payer?.address?.carId || '',
                    text: this.formDetails.payer?.address?.formattedAddress || ''
                }
            },
            voltageLevel: {
                voltageLevelId: this.formDetails.voltageLevel?.voltageLevelId || null,
                currentTransformer: this.formDetails.voltageLevel?.currentTransformer || null,
                voltageTransformer: this.formDetails.voltageLevel?.voltageTransformer || null
            },
            technicalInformation: {
                meterType: {
                    meterPlacementId: this.formDetails.technicalInformation?.meterType?.meterPlacementId || null,
                    installInConstructionPhase:
                        this.formDetails.technicalInformation?.meterType?.installInConstructionPhase || false,
                    meterSize: this.formDetails.technicalInformation?.meterType?.meterSize || null,
                    connectionTypeId: this.formDetails.technicalInformation?.meterType?.connectionTypeId || null,
                    meterTransformerId: this.formDetails.technicalInformation?.meterType?.meterTransformerId || null,
                    meterTransformerRemark: this.formDetails.technicalInformation?.meterType?.meterTransformerRemark || null
                },
                connection: {
                    transformerStationNumber: this.formDetails.technicalInformation?.connection?.transformerStationNumber || null,
                    cabinetNumber: this.formDetails.technicalInformation?.connection?.cabinetNumber || null,
                    groundingMethod: this.formDetails.technicalInformation?.connection?.groundingMethod || null,
                    fuseSizeOfPreProtection: this.formDetails.technicalInformation?.connection?.preProtection?.size || null,
                    protectionTypeOfPreProtection:
                        this.formDetails.technicalInformation?.connection?.preProtection?.protectionType || null,
                    phaseCount: this.formDetails.phaseCount || null
                },
                branchLine: {
                    type: this.formDetails.technicalInformation?.branchLine?.type || null,
                    branchLineMeterFrame: this.formDetails.technicalInformation?.branchLine?.meterFrame
                        ? {
                              id: this.formDetails.technicalInformation.branchLine.meterFrame.id,
                              meterFrameNumber: this.formDetails.technicalInformation.branchLine.meterFrame.meterFrameNumber,
                              connectionPointId: this.formDetails.technicalInformation?.branchLine?.connectionPointId || null
                          }
                        : null,
                    fuseTypeOfMainProtection: this.formDetails.technicalInformation?.branchLine?.mainProtection?.fuseType || null,
                    fuseSizeOfMainProtection: this.formDetails.technicalInformation?.branchLine?.mainProtection?.size || null,
                    protectionTypeOfMainProtection:
                        this.formDetails.technicalInformation?.branchLine?.mainProtection?.protectionType || null,
                    numberOfPairs: this.formDetails.technicalInformation?.branchLine?.numberOfPairs || null,
                    protectiveEarthingDimension:
                        this.formDetails.technicalInformation?.branchLine?.protectiveEarthingDimension || null,
                    cableDimensionId: this.formDetails.technicalInformation?.branchLine?.cableDimensionId || null
                }
            },
            instructionData: {
                meterResponsible: this.formDetails.instructionData?.meterResponsible || null,
                branchLineResponsible: this.formDetails.instructionData?.branchLineResponsible || null,
                connectionFeeFee: this.formDetails.instructionData?.connectionFee?.fee || null,
                connectionFeeValidUntilEod: this.formDetails.instructionData?.connectionFee?.validUntilEod || null,
                remark: this.formDetails.instructionData?.remark || null
            },
            meterDeliveryOptions: {
                requestedConnectionDateEod: this.formDetails.meterDeliveryOptions?.requestedConnectionDateEod || '',
                deliveryOption: this.formDetails.meterDeliveryOptions?.deliveryOption || '',
                name: this.formDetails.meterDeliveryOptions?.deliveryInformation?.name || '',
                attention: this.formDetails.meterDeliveryOptions?.deliveryInformation?.attention || '',
                meterDeliveryAddress: {
                    id: this.formDetails.meterDeliveryOptions?.deliveryInformation?.address?.carId || '',
                    text: this.formDetails.meterDeliveryOptions?.deliveryInformation?.address?.formattedAddress || ''
                }
            },
            applications: {
                applications: [...this.formDetails.applications],
                hasApplicationsChanged: false
            },
            relatedForms: {
                relatedForms: [...this.formDetails.relatedForms],
                hasRelatedFormsChanged: false
            },
            connectionRights: {
                transferConnectionRights: this.formDetails?.connectionRights?.connectionRightsTransfers
                    ? [
                          ...this.formDetails.connectionRights.connectionRightsTransfers.map((value) => {
                              const formItemModel: TransferConnectionRightFormArrayItem = {
                                  meterFrame: value.meterFrame
                                      ? {
                                            id: value.meterFrame.id!,
                                            meterFrameNumber: value.meterFrame.meterFrameNumber!,
                                            connectionPointId: value.connectionPointId!
                                        }
                                      : undefined,
                                  value: value.value!
                              };
                              return formItemModel;
                          })
                      ]
                    : [],
                scopeOfDeliverySize: this.formDetails.connectionRights?.scopeOfDeliverySize || 0,
                hasTransfersChanged: false
            }
        });

        if (!this.supportsVoltageLevel()) {
            this.form.get('voltageLevel')?.disable();
        }

        this.initDropDownOptions();
    }

    protected override initDropDownOptions() {
        super.initDropDownOptions();
        this.initBranchLineMeterFrameOptions();
        this.initGridAreasOptions();
    }

    getFormComparisonModel(paymentDetailsOnly: boolean): any {
        return paymentDetailsOnly
            ? {
                  // Contact person
                  contactPersonCompanyName: this.contactPersonForm.get('contactPersonCompanyName')?.value,
                  contactPersonName: this.contactPersonForm.get('contactPersonName')?.value,
                  contactPersonEmail: this.contactPersonForm.get('contactPersonEmail')?.value,
                  contactPersonPhoneNumber: this.contactPersonForm.get('contactPersonPhoneNumber')?.value,

                  //  Payer
                  payerName: this.payerForm.get('payerName')?.value,
                  payerEmail: this.payerForm.get('payerEmail')?.value,
                  payerType: this.payerForm.get('payerType')?.value,
                  payerContactPersonName: this.payerForm.get('payerContactPersonName')?.value,
                  payerContactPersonEmail: this.payerForm.get('payerContactPersonEmail')?.value,
                  payerContactPersonPhoneNumber: this.payerForm.get('payerContactPersonPhoneNumber')?.value,
                  requisition: this.payerForm.get('requisition')?.value,
                  cvrOrSeNumber: this.payerForm.get('cvrOrSeNumber')?.value,
                  eanNumber: this.payerForm.get('eanNumber')?.value,
                  payerAddress: [this.payerForm.get('payerAddress')?.value?.id, this.payerForm.get('payerAddress')?.value?.text]
              }
            : {
                  installationAddress: [
                      this.installationInformationForm.get('installationAddress')?.value?.id,
                      this.installationInformationForm.get('installationAddress')?.value?.text
                  ],
                  gridAreaId: this.installationInformationForm.get('gridAreaId')?.value,
                  connectionPoint: [
                      this.installationInformationForm.get('connectionPoint')?.value?.id || '',
                      this.installationInformationForm.get('connectionPoint')?.value?.connectionPointNumber || '',
                      this.installationInformationForm.get('connectionPoint')?.value?.electricityConnectionStatusId || ''
                  ],
                  meterFrame: [
                      this.installationInformationForm.get('meterFrame')?.value?.id || '',
                      this.installationInformationForm.get('meterFrame')?.value?.meterFrameNumber || ''
                  ],
                  consumptionMeteringPoint: [
                      this.consumptionMeteringPoint?.value?.meteringPointId || '',
                      this.consumptionMeteringPoint?.value?.meteringPointVersionId || ''
                  ],
                  contactPersonCompanyName: this.contactPersonForm.get('contactPersonCompanyName')?.value,
                  contactPersonName: this.contactPersonForm.get('contactPersonName')?.value,
                  contactPersonEmail: this.contactPersonForm.get('contactPersonEmail')?.value,
                  contactPersonPhoneNumber: this.contactPersonForm.get('contactPersonPhoneNumber')?.value,
                  remarksToInstallation: this.installationInformationForm.get('remarksToInstallation')?.value,
                  tags: this.installationInformationForm.get('tags')?.value,

                  payerName: this.payerForm.get('payerName')?.value,
                  payerEmail: this.payerForm.get('payerEmail')?.value,
                  payerType: this.payerForm.get('payerType')?.value,
                  payerContactPersonName: this.payerForm.get('payerContactPersonName')?.value,
                  payerContactPersonEmail: this.payerForm.get('payerContactPersonEmail')?.value,
                  payerContactPersonPhoneNumber: this.payerForm.get('payerContactPersonPhoneNumber')?.value,
                  requisition: this.payerForm.get('requisition')?.value,
                  cvrOrSeNumber: this.payerForm.get('cvrOrSeNumber')?.value,
                  eanNumber: this.payerForm.get('eanNumber')?.value,
                  payerAddress: [this.payerForm.get('payerAddress')?.value?.id, this.payerForm.get('payerAddress')?.value?.text],
                  ...this.meterTypeForm.value,
                  ...this.connectionForm.value,
                  type: this.branchLineForm.value?.type,
                  protectionTypeOfMainProtection: this.branchLineForm.value?.protectionTypeOfMainProtection,
                  fuseSizeOfMainProtection: this.branchLineForm.value?.fuseSizeOfMainProtection,
                  fuseTypeOfMainProtection: this.branchLineForm.value?.fuseTypeOfMainProtection,
                  numberOfPairs: this.branchLineForm.value?.numberOfPairs,
                  protectiveEarthingDimension: this.branchLineForm.value?.protectiveEarthingDimension,
                  cableDimensionId: this.branchLineForm.value?.cableDimensionId,
                  branchLineMeterFrameId: this.branchLineForm.value?.branchLineMeterFrame?.id,

                  //Voltage level
                  voltageLevelId: this.voltageLevelForm.get('voltageLevelId')?.value || null,
                  voltageLevelCurrentTransformer: this.voltageLevelForm.get('currentTransformer')?.value || null,
                  voltageLevelVoltageTransformer: this.voltageLevelForm.get('voltageTransformer')?.value || null,

                  // instructionData
                  instructionDataMeterResponsible: this.instructionDataForm.get('meterResponsible')?.value || null,
                  instructionDataBranchLineResponsible: this.instructionDataForm.get('branchLineResponsible')?.value || null,
                  instructionDataConnectionFeeFee: this.instructionDataForm.get('connectionFeeFee')?.value || null,
                  instructionDataConnectionFeeValidUntilEod:
                      this.instructionDataForm.get('connectionFeeValidUntilEod')?.value || null,
                  instructionDataRemark: this.instructionDataForm.get('remark')?.value || null,

                  // meterDeliveryOptions
                  meterDeliveryOptionsRequestedConnectionDateEod:
                      this.meterDeliveryOptionsForm.get('requestedConnectionDateEod')?.value || null,
                  meterDeliveryOptionsDeliveryOption: this.meterDeliveryOptionsForm.get('deliveryOption')?.value || null,
                  meterDeliveryOptionsName: this.meterDeliveryOptionsForm.get('name')?.value || null,
                  meterDeliveryOptionsAttention: this.meterDeliveryOptionsForm.get('attention')?.value || null,
                  meterDeliveryOptionsMeterDeliveryAddress: [
                      this.meterDeliveryOptionsForm.get('meterDeliveryAddress')?.value?.id || null,
                      this.meterDeliveryOptionsForm.get('meterDeliveryAddress')?.value?.text || null
                  ],

                  //usage
                  hasApplicationsChanged: this.applicationsForm.get('hasApplicationsChanged')?.value || false,

                  //related forms
                  hasRelatedFormsChanged: this.relatedFormsForm.get('hasRelatedFormsChanged')?.value || false,

                  // connection rights
                  hasTransfersChanged: this.connectionRightsForm.get('hasTransfersChanged')?.value || false,
                  scopeOfDeliverySize: this.connectionRightsForm.get('scopeOfDeliverySize')?.value || 0
              };
    }

    convertToComparisonModel(model: NewInstallation, paymentDetailsOnly: boolean): any {
        return paymentDetailsOnly
            ? {
                  // Contact person
                  contactPersonCompanyName: model.contactPerson?.companyName || '',
                  contactPersonName: model.contactPerson?.name || '',
                  contactPersonEmail: model.contactPerson?.email || '',
                  contactPersonPhoneNumber: model.contactPerson?.phoneNumber || '',

                  //  Payer
                  payerName: model.payer?.name || '',
                  payerType: model.payer?.type || '',
                  payerEmail: model.payer?.email || '',
                  payerContactPersonName: model.payer?.contactPerson?.name || '',
                  payerContactPersonEmail: model.payer?.contactPerson?.email || '',
                  payerContactPersonPhoneNumber: model.payer?.contactPerson?.phoneNumber || '',
                  requisition: model.payer?.requisition || '',
                  cvrOrSeNumber: model.payer?.cvrOrSeNumber || '',
                  eanNumber: model.payer?.eanNumber || '',
                  payerAddress: [model.payer?.address?.carId || '', model.payer?.address?.formattedAddress || '']
              }
            : {
                  installationAddress: [
                      model.installationAddress?.carId || '',
                      model.installationAddress?.formattedAddress || ''
                  ],
                  gridAreaId: model.gridAreaId,
                  connectionPoint: [model.connectionPoint?.id || '', model.connectionPoint?.connectionPointNumber || '', model.connectionPoint?.electricityConnectionStatusId || ''],
                  meterFrame: [model.meterFrame?.id || '', model.meterFrame?.meterFrameNumber || ''],
                  consumptionMeteringPoint: [
                      model.consumptionMeteringPoint?.meteringPointId || '',
                      model.consumptionMeteringPoint?.meteringPointVersionId || ''
                  ],
                  contactPersonCompanyName: model.contactPerson?.companyName || '',
                  contactPersonName: model.contactPerson?.name || '',
                  contactPersonEmail: model.contactPerson?.email || '',
                  contactPersonPhoneNumber: model.contactPerson?.phoneNumber || '',
                  remarksToInstallation: model.remarksToInstallation || '',
                  tags: model.tags.map((t) => t.id),

                  //  Payer
                  payerName: model.payer?.name || '',
                  payerType: model.payer?.type || '',
                  payerEmail: model.payer?.email || '',
                  payerContactPersonName: model.payer?.contactPerson?.name || '',
                  payerContactPersonEmail: model.payer?.contactPerson?.email || '',
                  payerContactPersonPhoneNumber: model.payer?.contactPerson?.phoneNumber || '',
                  requisition: model.payer?.requisition || '',
                  cvrOrSeNumber: model.payer?.cvrOrSeNumber || '',
                  eanNumber: model.payer?.eanNumber || '',
                  payerAddress: [model.payer?.address?.carId || '', model.payer?.address?.formattedAddress || ''],

                  //Voltage level
                  voltageLevelId: model.voltageLevel?.voltageLevelId || null,
                  voltageLevelCurrentTransformer: model.voltageLevel?.currentTransformer || null,
                  voltageLevelVoltageTransformer: model.voltageLevel?.voltageTransformer || null,

                  // Technical information
                  meterPlacementId: model.technicalInformation?.meterType?.meterPlacementId || null,
                  installInConstructionPhase: model.technicalInformation?.meterType?.installInConstructionPhase || false,
                  meterSize: model.technicalInformation?.meterType?.meterSize || null,
                  connectionTypeId: model.technicalInformation?.meterType?.connectionTypeId || null,
                  meterTransformerId: model.technicalInformation?.meterType?.meterTransformerId || null,
                  meterTransformerRemark: model.technicalInformation?.meterType?.meterTransformerRemark || null,
                  transformerStationNumber: model.technicalInformation?.connection?.transformerStationNumber || null,
                  cabinetNumber: model.technicalInformation?.connection?.cabinetNumber || null,
                  groundingMethod: model.technicalInformation?.connection?.groundingMethod || null,
                  fuseSizeOfPreProtection: model.technicalInformation?.connection?.preProtection?.size || null,
                  protectionTypeOfPreProtection: model.technicalInformation?.connection?.preProtection?.protectionType || null,
                  phaseCount: this.formDetails?.phaseCount || null,
                  type: model.technicalInformation?.branchLine?.type || null,
                  branchLineMeterFrameId: model.technicalInformation?.branchLine?.meterFrame?.id || null,
                  fuseTypeOfMainProtection: model.technicalInformation?.branchLine?.mainProtection?.fuseType || null,
                  fuseSizeOfMainProtection: model.technicalInformation?.branchLine?.mainProtection?.size || null,
                  protectionTypeOfMainProtection: model.technicalInformation?.branchLine?.mainProtection?.protectionType || null,
                  numberOfPairs: model.technicalInformation?.branchLine?.numberOfPairs || null,
                  protectiveEarthingDimension: model.technicalInformation?.branchLine?.protectiveEarthingDimension || null,
                  cableDimensionId: model.technicalInformation?.branchLine?.cableDimensionId || null,

                  // instructionData
                  instructionDataMeterResponsible: model.instructionData?.meterResponsible || null,
                  instructionDataBranchLineResponsible: model.instructionData?.branchLineResponsible || null,
                  instructionDataConnectionFeeFee: model.instructionData?.connectionFee?.fee || null,
                  instructionDataConnectionFeeValidUntilEod: model.instructionData?.connectionFee?.validUntilEod || null,
                  instructionDataRemark: model.instructionData?.remark || null,

                  // meterDeliveryOptions
                  meterDeliveryOptionsRequestedConnectionDateEod: model.meterDeliveryOptions?.requestedConnectionDateEod || null,
                  meterDeliveryOptionsDeliveryOption: model.meterDeliveryOptions?.deliveryOption || null,
                  meterDeliveryOptionsName: model.meterDeliveryOptions?.deliveryInformation?.name || null,
                  meterDeliveryOptionsAttention: model.meterDeliveryOptions?.deliveryInformation?.attention || null,
                  meterDeliveryOptionsMeterDeliveryAddress: [
                      model.meterDeliveryOptions?.deliveryInformation?.address?.carId || null,
                      model.meterDeliveryOptions?.deliveryInformation?.address?.formattedAddress || null
                  ],

                  //usage
                  hasApplicationsChanged: false,

                  // related forms
                  hasRelatedFormsChanged: false,

                  // connection rights
                  scopeOfDeliverySize: model.connectionRights?.scopeOfDeliverySize || 0,
                  hasTransfersChanged: false
              };
    }

    initBranchLineMeterFrameOptions() {
        this.branchLineMeterFrameOptions =
            this.formDetails.technicalInformation?.branchLine?.meterFrame &&
            this.formDetails.technicalInformation?.branchLine?.connectionPointId
                ? [
                      <MeterFrameSearchItemModel>{
                          id: this.formDetails.technicalInformation.branchLine.meterFrame.id,
                          meterFrameNumber: this.formDetails.technicalInformation.branchLine.meterFrame.meterFrameNumber,
                          connectionPointId: this.formDetails.technicalInformation.branchLine.connectionPointId
                      }
                  ]
                : [];
    }

    initGridAreasOptions() {
        this.gridAreasOptions = this.formDetails.gridAreaId ? [this.formDetails.gridAreaId] : [];
    }

    protected override updateFormData(
        installationFormId: string,
        es_message_id: string,
        row_version: string,
        body: NewInstallationUpdate
    ): Observable<InstallationFormsApiResponse<void>> {
        return this.client.updateNewInstallationFormData(installationFormId, es_message_id, row_version, body);
    }

    protected override createFormDataUpdate(): NewInstallationUpdate {
        const update = new NewInstallationUpdate({
            installationInformationUpdate: this.createInstallationInformationUpdate(),
            payerUpdate: this.createPayerUpdate(),
            technicalInformationUpdate: this.createTechnicalInformationUpdate(),
            instructionDataUpdate: this.createInstructionDataUpdate(),
            meterDeliveryOptionsUpdate: this.createMeterDeliveryOptionsUpdate(),
            phaseCount: this.isPhaseCountRequired() ? this.connectionForm.get('phaseCount')?.value : undefined,
            applicationsUpdate: this.applicationsForm.get('applications')?.value,
            relatedFormsUpdate: this.relatedFormsForm
                .get('relatedForms')
                ?.value.filter((x: RelatedForm) => x.relationType === FormsRelationType.Manual)
                .map((x: RelatedForm) => new RelatedFormUpdate({ formId: x.formId })),
            connectionRightsUpdate: this.createConnectionRightsUpdate(),
            voltageLevelUpdate: this.createVoltageLevelUpdate(),
            gridAreaId: this.installationInformationForm.get('gridAreaId')?.value
        });
        return update;
    }

    protected override createPaymentDetailsUpdate(): PaymentDetailsUpdate {
        const update = new PaymentDetailsUpdate({
            contactPerson: this.createContactPersonUpdate(),
            payer: this.createPayerUpdate()
        });
        return update;
    }

    private createConnectionRightsUpdate(): ConnectionRightsUpdate {
        const connectionRightFormValue = this.connectionRightsForm.value;
        return new ConnectionRightsUpdate({
            connectionRightsTransfersUpdate: this.mapTransferConnectionRights(connectionRightFormValue.transferConnectionRights),
            scopeOfDeliverySizeUpdate: connectionRightFormValue.scopeOfDeliverySize
        });
    }

    private mapTransferConnectionRights(formValues: TransferConnectionRightFormArrayItem[]): ConnectionRightsTransfersUpdate[] {
        return formValues
            ? formValues.map(
                  (fv) =>
                      new ConnectionRightsTransfersUpdate({
                          meterFrameId: fv.meterFrame?.id,
                          value: fv.value,
                          uom: TransferConnectionRightUom.Ampere
                      })
              )
            : [];
    }

    private createVoltageLevelUpdate(): VoltageLevelUpdate | undefined {
        if (!this.supportsVoltageLevel()) {
            return undefined;
        }

        const voltageLevelFormValue = this.voltageLevelForm.value;
        return new VoltageLevelUpdate({
            voltageLevelId: voltageLevelFormValue.voltageLevelId,
            currentTransformer: getValueOrDefault(voltageLevelFormValue.currentTransformer),
            voltageTransformer: getValueOrDefault(voltageLevelFormValue.voltageTransformer)
        });
    }

    private createInstallationInformationUpdate(): InstallationInformationUpdate {
        const installationInformationFormValue = this.installationInformationForm.value;
        return new InstallationInformationUpdate({
            contactPerson: this.createContactPersonUpdate(),
            installationInformationCarId: getValueOrDefault(installationInformationFormValue.installationAddress?.id),
            remarksToInstallation: getValueOrDefault(installationInformationFormValue.remarksToInstallation),
            connectionPointId: getValueOrDefault(installationInformationFormValue.connectionPoint?.id),
            meterFrameId: getValueOrDefault(installationInformationFormValue.meterFrame?.id),
            consumptionMeteringPoint: createMeteringPoint(installationInformationFormValue.consumptionMeteringPoint),
            tags: installationInformationFormValue.tags || []
        });
    }

    private createContactPersonUpdate(): ContactPerson {
        const contactPersonFormValue = this.contactPersonForm.value;
        return new ContactPerson({
            companyName: getValueOrDefault(contactPersonFormValue.contactPersonCompanyName),
            name: getValueOrDefault(contactPersonFormValue.contactPersonName),
            email: getValueOrDefault(contactPersonFormValue.contactPersonEmail),
            phoneNumber: getValueOrDefault(contactPersonFormValue.contactPersonPhoneNumber)
        });
    }

    private createPayerUpdate(): PayerUpdate {
        const payerFormValue = this.payerForm.value;
        return new PayerUpdate({
            contactPerson: new Person({
                email: getValueOrDefault(payerFormValue.payerContactPersonEmail),
                name: getValueOrDefault(payerFormValue.payerContactPersonName),
                phoneNumber: getValueOrDefault(payerFormValue.payerContactPersonPhoneNumber)
            }),
            payerType: payerFormValue.payerType,
            payerCarId: getValueOrDefault(payerFormValue.payerAddress?.id),
            name: getValueOrDefault(payerFormValue.payerName),
            email: getValueOrDefault(payerFormValue.payerEmail),
            requisition: getValueOrDefault(payerFormValue.requisition),
            cvrOrSeNumber: getValueOrDefault(payerFormValue.cvrOrSeNumber),
            eanNumber: getValueOrDefault(payerFormValue.eanNumber)
        });
    }

    private createTechnicalInformationUpdate(): TechnicalInformationUpdate {
        const technicalInformationFormValue = this.technicalInformationForm.value;
        return new TechnicalInformationUpdate({
            meterTypeUpdate: new MeterTypeUpdate({
                meterPlacementId: technicalInformationFormValue.meterType.meterPlacementId,
                installInConstructionPhase: technicalInformationFormValue.meterType.installInConstructionPhase,
                meterSize: technicalInformationFormValue.meterType.meterSize,
                connectionTypeId: technicalInformationFormValue.meterType.connectionTypeId,
                meterTransformerId: technicalInformationFormValue.meterType.meterTransformerId,
                meterTransformerRemark: technicalInformationFormValue.meterType.meterTransformerRemark
            }),
            connectionUpdate: new Connection({
                transformerStationNumber: getValueOrDefault(technicalInformationFormValue.connection.transformerStationNumber),
                cabinetNumber: getValueOrDefault(technicalInformationFormValue.connection.cabinetNumber),
                groundingMethod: technicalInformationFormValue.connection.groundingMethod,
                preProtection: technicalInformationFormValue.connection.protectionTypeOfPreProtection
                    ? new PreProtection({
                          protectionType: technicalInformationFormValue.connection.protectionTypeOfPreProtection,
                          size: technicalInformationFormValue.connection.fuseSizeOfPreProtection
                      })
                    : undefined
            }),
            branchLineUpdate: new BranchLineUpdate({
                type: technicalInformationFormValue.branchLine.type,
                meterFrameId: getValueOrDefault(technicalInformationFormValue.branchLine.branchLineMeterFrame?.id),
                mainProtection: technicalInformationFormValue.branchLine.protectionTypeOfMainProtection
                    ? new MainProtection({
                          protectionType: technicalInformationFormValue.branchLine.protectionTypeOfMainProtection,
                          fuseType: getValueOrDefault(technicalInformationFormValue.branchLine.fuseTypeOfMainProtection),
                          size: technicalInformationFormValue.branchLine.fuseSizeOfMainProtection
                      })
                    : undefined,
                numberOfPairs: technicalInformationFormValue.branchLine.numberOfPairs,
                protectiveEarthingDimension: technicalInformationFormValue.branchLine.protectiveEarthingDimension,
                cableDimensionId: technicalInformationFormValue.branchLine.cableDimensionId
            })
        });
    }

    private createInstructionDataUpdate(): InstructionDataUpdate {
        const instructionDataFormValue = this.instructionDataForm.value;

        return new InstructionDataUpdate({
            meterResponsible: instructionDataFormValue.meterResponsible as MeterResponsible,
            branchLineResponsible: instructionDataFormValue.branchLineResponsible as BranchLineResponsible,
            connectionFee: !!instructionDataFormValue.connectionFeeFee
                ? new ConnectionFee({
                      currency: 'DKK', // So far there's no support for different currencies. Hardcoded DKK
                      fee: instructionDataFormValue.connectionFeeFee,
                      validUntilEod: instructionDataFormValue.connectionFeeValidUntilEod
                  })
                : undefined,
            remark: getValueOrDefault(instructionDataFormValue.remark)
        });
    }

    private createMeterDeliveryOptionsUpdate(): MeterDeliveryOptionsUpdate {
        const meterDeliveryOptionsFormValue = this.meterDeliveryOptionsForm.value;
        return new MeterDeliveryOptionsUpdate({
            requestedConnectionDateEod: meterDeliveryOptionsFormValue.requestedConnectionDateEod,
            deliveryOption: meterDeliveryOptionsFormValue.deliveryOption,
            deliveryInformation: this.shouldShowDelivery()
                ? new DeliveryInformationUpdate({
                      name: getValueOrDefault(meterDeliveryOptionsFormValue.name),
                      attention: getValueOrDefault(meterDeliveryOptionsFormValue.attention),
                      deliveryInformationCarId: getValueOrDefault(meterDeliveryOptionsFormValue.meterDeliveryAddress?.id)
                  })
                : undefined
        });
    }

    isConnectionTypeVisible = () => {
        return this.getMeterSize() === MeterSize.Below63A;
    };

    isGridAreaRequired = () => {
        return !this.form?.get('installationInformation.connectionPoint')?.value?.id;
    };

    isConnectionPointRequired = () => {
        return !this.form?.get('installationInformation.gridAreaId')?.value;
    };

    getMeterSize() {
        return this.form?.get('technicalInformation.meterType.meterSize')?.value;
    }

    isMeterTransformerVisible = () => {
        return this.getMeterSize() === MeterSize.Above63A;
    };

    isMeterTransformerRemarkVisible = () => {
        return this.isMeterTransformerVisible() && this.getMeterTransformer() === METER_TRANSFORMER_OTHER_VALUE_ID;
    };

    getMeterTransformer() {
        return this.form?.get('technicalInformation.meterType.meterTransformerId')?.value;
    }

    isPreProtectionFuseSizeRequired = () => {
        const value = this.form?.get('technicalInformation.connection.protectionTypeOfPreProtection')?.value;
        return value === PreProtectionType.MaxBreaker || value === PreProtectionType.Fuse;
    };

    isPhaseCountRequired = () => {
        return this.formDetails?.category === FormCategory.SmallInstallation;
    };

    isFuseTypeOfMainProtectionRequired = () => {
        const value = this.form?.get('technicalInformation.branchLine.protectionTypeOfMainProtection')?.value;
        return value === MainProtectionType.Fuse;
    };

    shouldShowDelivery = () => {
        return this.form?.get('meterDeliveryOptions.deliveryOption')?.value === DeliveryOption.InstallerWillHandleSendMeter;
    };

    transformerDataRequired = () => {
        const voltageLevelId = this.voltageLevelForm?.get('voltageLevelId')?.value;
        return voltageLevelId && voltageLevelId !== VOLTAGE_LEVEL_B_LOW_VALUE_ID && voltageLevelId !== VOLTAGE_LEVEL_C_VALUE_ID;
    };

    supportsVoltageLevel = () => {
        return this.formDetails?.category === FormCategory.BusinessOrOther;
    };

    isInstallInConstructionPhaseRequired = () => {
        return (
            this.formDetails?.category === FormCategory.ParcelOrCottage ||
            this.formDetails?.category === FormCategory.TerracedHouse ||
            this.formDetails?.category === FormCategory.Allotment ||
            this.formDetails?.category === FormCategory.BusinessOrOther
        );
    };
}
