import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { Subscription } from 'rxjs';
import { MeterReturnOption } from 'src/app/api/installation-forms-client';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import { WidgetWithFormComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget-with-form.component';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';

@Component({
    selector: 'app-meter-return-section',
    templateUrl: './meter-return-section.component.html',
    styleUrls: ['./meter-return-section.component.scss'],
    standalone: false
})
export class MeterReturnSectionComponent extends WidgetWithFormComponent implements OnIni<PERSON>, OnD<PERSON>roy {
    @Input() form!: FormGroup;

    subscription: Subscription = new Subscription();
    returnOptionOptions: SelectItem[] = [];

    constructor(
        private readonly translateService: TranslateService,
        readonly formDataService: FormDataService
    ) {
        super(formDataService);
    }

    ngOnInit(): void {
        this.subscription.add(
            this.form.get('meterReturnReturnOption')?.valueChanges.subscribe((newVal) => {
                setTimeout(() => {
                    ['meterReturnName', 'meterReturnAttention', 'meterReturnAddress'].forEach((x) => {
                        const control = this.form.get(x)!;
                        control.setErrors(null);
                        control.updateValueAndValidity({
                            onlySelf: true,
                            emitEvent: false
                        });
                    });
                });
            })
        );

        this.getEnumTranslations();
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    getMeterReturnOption() {
        return this.form?.get('meterReturnReturnOption')?.value;
    }

    isGridCompanyMeterReturnSectionVisible = () => {
        let returnOption = this.getMeterReturnOption();
        return returnOption !== null && returnOption !== MeterReturnOption.GridCompanyWillHandle;
    };

    getEnumTranslations() {
        this.returnOptionOptions = enumMapper.map(this.translateService.instant('enums.meterReturnOption'), MeterReturnOption);
    }
}
