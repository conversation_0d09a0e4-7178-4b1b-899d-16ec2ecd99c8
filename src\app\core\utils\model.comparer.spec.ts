import { ChangeFieldType, ChangesModel } from 'src/app/core/constants/changes-details';
import { ModelComparer } from './model.comparer';

describe('ModelComparer', () => {
    let comparer: ModelComparer;
    let changesModel: ChangesModel;

    beforeEach(() => {
        // Basic changes model with different field types
        changesModel = {
            stringField: { change: false, type: ChangeFieldType.Default },
            dateField: { change: false, type: ChangeFieldType.Date },
            arrayField: { change: false, type: ChangeFieldType.Array },
            noTypeField: { change: false } // Field with no type specified
        };

        comparer = new ModelComparer(changesModel);
    });

    it('should create an instance', () => {
        expect(comparer).toBeTruthy();
    });

    it('should initialize with the provided changes model', () => {
        expect(comparer['changesModel']).toBe(changesModel);
    });

    it('should not modify the original changes model during comparison', () => {
        const originalModel = { ...changesModel };
        const from = { stringField: 'value', dateField: new Date(), arrayField: ['a', 'b'] };
        const to = { ...from };

        comparer.compareModels(from, to);

        // The original model should remain unchanged
        expect(changesModel).toEqual(originalModel);
    });

    it('should detect no changes when models are identical', () => {
        const from = {
            stringField: 'test',
            dateField: new Date(2023, 0, 15),
            arrayField: ['a', 'b', 'c'],
            noTypeField: 'value'
        };
        const to = { ...from };

        const result = comparer.compareModels(from, to);

        expect(result.changesAmount).toBe(0);
        expect(result.changesModel['stringField'].change).toBe(false);
        expect(result.changesModel['dateField'].change).toBe(false);
        expect(result.changesModel['arrayField'].change).toBe(false);
    });

    it('should detect changes in string fields', () => {
        const from = { stringField: 'original' };
        const to = { stringField: 'changed' };

        const result = comparer.compareModels(from, to);

        expect(result.changesAmount).toBe(1);
        expect(result.changesModel['stringField'].change).toBe(true);
    });

    it('should detect changes in date fields', () => {
        const from = { dateField: new Date(2023, 0, 15) };
        const to = { dateField: new Date(2023, 0, 16) };

        const result = comparer.compareModels(from, to);

        expect(result.changesAmount).toBe(1);
        expect(result.changesModel['dateField'].change).toBe(true);
    });

    it('should ignore time portion when comparing dates', () => {
        const from = { dateField: new Date(2023, 0, 15, 10, 30) }; // 10:30 AM
        const to = { dateField: new Date(2023, 0, 15, 14, 45) }; // 2:45 PM (same day)

        const result = comparer.compareModels(from, to);

        expect(result.changesAmount).toBe(0);
        expect(result.changesModel['dateField'].change).toBe(false);
    });

    it('should detect changes in array fields', () => {
        const from = { arrayField: ['a', 'b', 'c'] };
        const to = { arrayField: ['a', 'b', 'd'] };

        const result = comparer.compareModels(from, to);

        expect(result.changesAmount).toBe(1);
        expect(result.changesModel['arrayField'].change).toBe(true);
    });

    it('should consider array fields equal regardless of order', () => {
        const from = { arrayField: ['a', 'b', 'c'] };
        const to = { arrayField: ['c', 'a', 'b'] };

        const result = comparer.compareModels(from, to);

        expect(result.changesAmount).toBe(0);
        expect(result.changesModel['arrayField'].change).toBe(false);
    });

    it('should detect different array lengths as changes', () => {
        const from = { arrayField: ['a', 'b'] };
        const to = { arrayField: ['a', 'b', 'c'] };

        const result = comparer.compareModels(from, to);

        expect(result.changesAmount).toBe(1);
        expect(result.changesModel['arrayField'].change).toBe(true);
    });

    it('should handle null/undefined values', () => {
        const from = {
            stringField: 'value',
            dateField: new Date(),
            arrayField: ['a']
        };
        const to = {
            stringField: null,
            dateField: undefined,
            arrayField: null
        };

        const result = comparer.compareModels(from, to);

        expect(result.changesAmount).toBe(3);
        expect(result.changesModel['stringField'].change).toBe(true);
        expect(result.changesModel['dateField'].change).toBe(true);
        expect(result.changesModel['arrayField'].change).toBe(true);
    });

    it('should handle both values being null/undefined', () => {
        const from = { stringField: null };
        const to = { stringField: null };

        const result = comparer.compareModels(from, to);

        expect(result.changesAmount).toBe(0);
        expect(result.changesModel['stringField'].change).toBe(false);
    });

    it('should handle one value being null and the other undefined', () => {
        const from = { stringField: null };
        const to = { stringField: undefined };

        const result = comparer.compareModels(from, to);

        expect(result.changesAmount).toBe(0);
        expect(result.changesModel['stringField'].change).toBe(false);
    });

    it('should use string comparison as fallback for default comparison', () => {
        const from = { stringField: 123 };
        const to = { stringField: '123' };

        const result = comparer.compareModels(from, to);

        expect(result.changesAmount).toBe(0);
        expect(result.changesModel['stringField'].change).toBe(false);
    });

    it('should ignore fields with undefined types', () => {
        const from = { noTypeField: 'original' };
        const to = { noTypeField: 'changed' };

        const result = comparer.compareModels(from, to);

        // Since the field has no type, it should be ignored
        expect(result.changesAmount).toBe(0);
    });

    it('should handle missing fields in source objects', () => {
        const from = {}; // No fields
        const to = {
            stringField: 'value',
            dateField: new Date(),
            arrayField: ['a']
        };

        const result = comparer.compareModels(from, to);

        expect(result.changesAmount).toBe(3);
        expect(result.changesModel['stringField'].change).toBe(true);
        expect(result.changesModel['dateField'].change).toBe(true);
        expect(result.changesModel['arrayField'].change).toBe(true);
    });

    it('should compare complex scenarios correctly', () => {
        const from = {
            stringField: 'unchanged',
            dateField: new Date(2023, 0, 15),
            arrayField: ['a', 'b', 'c']
        };

        const to = {
            stringField: 'unchanged', // Same
            dateField: new Date(2023, 0, 16), // Different
            arrayField: ['a', 'c', 'b'] // Same (different order)
        };

        const result = comparer.compareModels(from, to);

        expect(result.changesAmount).toBe(1);
        expect(result.changesModel['stringField'].change).toBe(false);
        expect(result.changesModel['dateField'].change).toBe(true);
        expect(result.changesModel['arrayField'].change).toBe(false);
    });
});
