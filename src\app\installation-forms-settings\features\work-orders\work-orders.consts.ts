export const workOrderTabName: string = 'WorkOrder';
export const workOrderTranslationPath: string = 'formsSettings.workOrders';

export const workOrderMarkdownEnUs: string = `
## Configuration of automatic work orders for form management

**Rules:** Adding a rule below will cause the system to automatically process any form matching the criteria defined in the rule. The system will start the configured work order from the form.

**Trigger:** A work order will be started based on the rule when all the below conditions are met:
- The form matches every enabled criteria in the rule.
- The form allows automatization.
- The form has no problems with the severity type "Error".
- Connection point and primary meter frame has been selected on the form.
- No previous work order with the same type, description and purpose exists on the form.
`;

export const workOrderMarkdownDaDK: string = `
## Konfiguration af automatiske arbejdsordre fra blanketter

**Regler:** Når der tilføjes en regel herunder, vil systemet automatisk begynde at behandle blanketter der opfylder kriterierne defineret i reglen. Der opstartes en arbejdsordre som konfigureret.

**Trigger:** Der startes en automatisk genereret arbejdsordre baseret på reglen, når alle nedenstående betingelser er opfyldt:
- Blanketten opfylder alle opsatte kriterier fra reglen.
- Blanketten tillader automatisering.
- Blanketten har ingen problemer af alvorlighedsgraden "fejl".
- Blanketten har et valgt forbindelsespunkt og en primær målerramme.
- Der findes ikke tidligere arbejdsordre med samme type, beskrivelse og formål på blanketten.
`;
