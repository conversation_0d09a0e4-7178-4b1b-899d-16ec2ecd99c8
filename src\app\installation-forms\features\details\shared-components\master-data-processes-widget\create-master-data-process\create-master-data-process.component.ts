import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { finalize, Subscription, take } from 'rxjs';
import {
    ConnectionPointTemplateDetails,
    CreateMasterDataProcess,
    InstallationFormsClient,
    MasterDataProcessType
} from 'src/app/api/installation-forms-client';
import { WidgetNames } from 'src/app/core/constants/widget-names';
import { FormDataService } from 'src/app/core/services/form-data/form-data.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import { convertSupplyTypeToEnum } from 'src/app/core/utils/mappers/supply-type/supply-type.mapper';
import { getAvailableMasterDataProcessTypes } from 'src/app/core/utils/master-data-processes-per-form-types';
import conditionallyRequiredValidator from 'src/app/core/utils/validators/conditionally-required/conditionally-required.validator';
import { WidgetWithFormComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget-with-form.component';
import { v4 as uuidv4 } from 'uuid';

@Component({
    selector: 'app-create-master-data-process',
    templateUrl: './create-master-data-process.component.html',
    standalone: false
})
export class CreateMasterDataProcessComponent extends WidgetWithFormComponent implements OnDestroy, OnInit {
    private commonTranslations: any;

    form!: FormGroup;

    masterDataProcessTypeOptions: SelectItem[] = [];
    masterDataProcessTemplateOptions: ConnectionPointTemplateDetails[] = [];
    isMasterDataProcessTemplateOptionsLoading: boolean = false;
    isGridAreasOptionsLoading: boolean = false;

    subscription: Subscription = new Subscription();
    @Input() gridAreasOptions!: string[];
    @Output() closeWidget = new EventEmitter();

    constructor(
        private readonly fb: FormBuilder,
        private readonly client: InstallationFormsClient,
        private readonly translateService: TranslateService,
        private readonly messageServiceHelper: MessageServiceHelper,
        protected readonly formDataService: FormDataService
    ) {
        super(formDataService);
        this.form = this.fb.group({
            masterDataProcessType: ['', Validators.required],
            masterDataProcessTemplate: [
                null,
                [(control: AbstractControl) => conditionallyRequiredValidator(control, this.isMasterDataProcessTemplateRequired)]
            ],
            gridAreaId: [
                this.formDataService.gridAreaId!,
                [(control: AbstractControl) => conditionallyRequiredValidator(control, this.isGridAreaIdRequired)]
            ]
        });
    }

    ngOnInit(): void {
        this.commonTranslations = this.translateService.instant('common');
        this.setEnumTranslations();

        this.loadMasterDataProcessTemplateOptions();
        this.loadGridAreaOptions(true);
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    isMasterDataProcessTemplateRequired = () => {
        return (
            this.form?.get('masterDataProcessType')?.value === MasterDataProcessType.CreateConnectionPoint ||
            this.form?.get('masterDataProcessType')?.value === MasterDataProcessType.CloseDownProduction ||
            this.form?.get('masterDataProcessType')?.value === MasterDataProcessType.AddConnectionPointNetSettlementGroup ||
            this.form?.get('masterDataProcessType')?.value === MasterDataProcessType.ModifyConnectionPointNetSettlementGroup
        );
    };

    isGridAreaIdRequired = () => {
        return this.form?.get('masterDataProcessType')?.value === MasterDataProcessType.CreateConnectionPoint;
    };

    isMasterDataProcessTemplateVisible = () => {
        return (
            this.form?.get('masterDataProcessType')?.value === MasterDataProcessType.CreateConnectionPoint ||
            this.form?.get('masterDataProcessType')?.value === MasterDataProcessType.ModifyConnectionPoint ||
            this.form?.get('masterDataProcessType')?.value === MasterDataProcessType.CloseDownProduction ||
            this.form?.get('masterDataProcessType')?.value === MasterDataProcessType.AddConnectionPointNetSettlementGroup ||
            this.form?.get('masterDataProcessType')?.value === MasterDataProcessType.ModifyConnectionPointNetSettlementGroup
        );
    };

    private createMasterDataProcess(): CreateMasterDataProcess {
        const formValue = this.form.value;
        return new CreateMasterDataProcess({
            masterDataProcessType: formValue.masterDataProcessType,
            templateId: formValue.masterDataProcessTemplate,
            gridAreaId: formValue.gridAreaId
        });
    }

    private cleanAndCloseForm() {
        this.form.reset();
        this.closeWidget.emit();
    }

    onCancelClicked() {
        this.cleanAndCloseForm();
    }

    onGridAreasDropdownHide() {
        let gridAreaId = this.formDataService.gridAreaId!;
        if (!gridAreaId) {
            this.form.patchValue({
                gridAreaId: ''
            });
        }
    }

    onCreateClicked() {
        if (this.form.valid) {
            this.processingStarted();
            this.client
                .createMasterDataProcess(this.formDataService.formId!, uuidv4(), this.createMasterDataProcess())
                .pipe(take(1))
                .subscribe({
                    error: (error) => {
                        this.messageServiceHelper.addTranslated(
                            error,
                            this.formDataService.formId!,
                            WidgetNames.masterDataProcessesWidget,
                            {
                                path: 'enums.fieldNames',
                                key: 'FieldName'
                            }
                        );
                        this.processingFinished();
                    },
                    complete: () => {
                        this.processingFinished();
                        this.messageServiceHelper.showSuccess({
                            key: this.formDataService.formId!
                        });

                        this.cleanAndCloseForm();
                    }
                });
        } else {
            this.processingFinished();
            this.messageServiceHelper.showWarning({
                detail: this.commonTranslations['allRequiredFieldsShouldBeCompleted'],
                key: this.formDataService.formId!
            });
        }
    }

    private setEnumTranslations() {
        this.masterDataProcessTypeOptions = enumMapper.map(
            this.translateService.instant('enums.masterDataProcessType'),
            getAvailableMasterDataProcessTypes([this.formDataService.type!], false, this.formDataService.terminationScope)
        );
    }

    onMasterDataProcessTypeChange() {
        this.form.get('masterDataProcessTemplate')?.updateValueAndValidity();
    }

    onGridAreaIdChange() {
        this.form.get('gridAreaId')?.updateValueAndValidity();
    }

    private loadMasterDataProcessTemplateOptions() {
        this.isMasterDataProcessTemplateOptionsLoading = true;
        let supplyType = convertSupplyTypeToEnum(this.formDataService.supplyType!);

        this.client.getConnectionPointTemplatesBySupplyType(uuidv4(), supplyType).subscribe({
            next: (response) => {
                this.masterDataProcessTemplateOptions = response.result.connectionPointTemplatesDetails;
                this.isMasterDataProcessTemplateOptionsLoading = false;
            },
            error: () => {
                this.messageServiceHelper.showError({
                    detail: this.commonTranslations['loadMasterDataProcessTemplatesError'],
                    key: this.formDataService.formId
                });
                this.isMasterDataProcessTemplateOptionsLoading = false;
            }
        });
    }

    private loadGridAreaOptions(selectIfOne: boolean = false) {
        this.isGridAreasOptionsLoading = true;
        this.client
            .getGridAreas(uuidv4(), uuidv4())
            .pipe(
                finalize(() => {
                    this.isGridAreasOptionsLoading = false;
                })
            )
            .subscribe({
                next: (x) => {
                    this.gridAreasOptions =
                        x?.result.gridAreas
                            .filter((gridArea): gridArea is string => gridArea !== undefined)
                            .map((gridArea) => {
                                return gridArea;
                            }) || [];

                    if (this.gridAreasOptions.length === 1 && selectIfOne) {
                        this.form.patchValue({
                            gridAreaId: this.gridAreasOptions[0]
                        });
                    }
                },
                error: (_error) => {
                    this.messageServiceHelper.showError({
                        detail: this.commonTranslations['loadGridAreasError'],
                        key: this.formDataService.formId
                    });
                }
            });
    }
}
