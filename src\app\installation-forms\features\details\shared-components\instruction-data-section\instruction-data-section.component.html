<div #overlayContainer></div>
<div [formGroup]="form">
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="16px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label for="meterResponsible">
                {{ 'instructionDataWidget.meterResponsible' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="meterResponsible"
                [options]="meterResponsibleOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                [showClear]="true"
                formControlName="meterResponsible">
            </p-select>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="16px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label for="branchLineResponsible">
                {{ 'instructionDataWidget.branchLineResponsible' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="branchLineResponsible"
                [options]="branchLineResponsibleOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                [showClear]="true"
                formControlName="branchLineResponsible">
            </p-select>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label for="connectionFeeFee">
                {{ 'instructionDataWidget.connectionFee.fee' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-inputNumber
                id="connectionFeeFee"
                formControlName="connectionFeeFee"
                [showClear]="true"
                [minFractionDigits]="2"
                [maxFractionDigits]="2"></p-inputNumber>
            <small [controlValidationErrors]="form.controls['connectionFeeFee']" class="p-error"></small>
        </div>
    </div>
    <ng-container *ngIf="showConnectionFeeValidUntilEodDatePicker()">
        <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label for="connectionFeeValidUntilEod">{{
                    'instructionDataWidget.connectionFee.validUntilEod' | translate
                }}</label>
            </div>
            <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                <cmbs-calendar
                    inputId="connectionFeeValidUntilEod"
                    formControlName="connectionFeeValidUntilEod"
                    [showClear]="true"
                    [showTime]="false"
                    [appendTo]="overlayContainer">
                </cmbs-calendar>
                <small [controlValidationErrors]="form.controls['connectionFeeValidUntilEod']" class="p-error"></small>
            </div>
        </div>
    </ng-container>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label for="remark">{{ 'instructionDataWidget.remark' | translate }}</label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <div class="relative inline-block w-full">
                <textarea
                    id="remark"
                    pTextarea
                    [maxlength]="RemarkLength"
                    formControlName="remark"
                    [autoResize]="true"></textarea>

                <button
                    *ngIf="form.get('remark')?.enabled && !isInInstructionTextsAddMode"
                    pButton
                    class="p-button-outlined border-none top-right-corner-icon-button cursor-pointer"
                    icon="pi pi-plus"
                    [pTooltip]="'instructionDataWidget.instructionTextsPicker.openPicker' | translate"
                    (click)="turnOnInstructionTextsAddMode()"></button>
                <button
                    *ngIf="form.get('remark')?.enabled && isInInstructionTextsAddMode"
                    pButton
                    class="p-button-outlined border-none top-right-corner-icon-button cursor-pointer"
                    icon="pi pi-minus"
                    [pTooltip]="'instructionDataWidget.instructionTextsPicker.closePicker' | translate"
                    (click)="turnOffInstructionTextsAddMode()"></button>
            </div>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isInInstructionTextsAddMode">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center"></div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <div>
                <div class="flex align-items-center gap-2">
                    <div fxFlex>
                        <p-multiSelect
                            [options]="allInstructionTexts"
                            [loading]="isDuringInstructionTextLoading"
                            [placeholder]="'instructionDataWidget.instructionTextsPicker.selectInstructionTexts' | translate"
                            [appendTo]="overlayContainer"
                            display="chip"
                            optionValue="id"
                            optionLabel="text"
                            class="w-full"
                            #defaultInstructionTextsSelect>
                            <ng-template pTemplate="item" let-option>
                                <div class="flex items-center gap-2" [pTooltip]="option.text">
                                    <div>{{ getSelectableDefaultInstructionTextLabel(option.text) }}</div>
                                </div>
                            </ng-template>
                        </p-multiSelect>
                    </div>
                    <div>
                        <button
                            [disabled]="!defaultInstructionTextsSelect.selectedOptions?.length"
                            pButton
                            class="button-no-wrap p-button-outlined"
                            icon="pi pi-check"
                            (click)="addSelectedInstructionTexts()">
                            {{ 'instructionDataWidget.instructionTextsPicker.apply' | translate }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
