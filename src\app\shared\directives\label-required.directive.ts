import { Directive, OnInit, OnDestroy, Input, HostBinding } from '@angular/core';
import { AbstractControl, Validators } from '@angular/forms';
import { Subject, takeUntil, startWith } from 'rxjs';

@Directive({
    // eslint-disable-next-line @angular-eslint/directive-selector
    selector: '[labelRequired]',
    standalone: false
})
export class LabelRequiredDirective implements OnInit, OnDestroy {
    @Input() public labelRequired!: AbstractControl;
    @HostBinding('class.label-required') elementClass = false;

    private readonly destroy$ = new Subject<void>();

    public ngOnInit(): void {
        if (this.labelRequired === undefined) {
            return;
        }

        this.elementClass = this.isControlRequired(this.labelRequired);

        this.labelRequired.valueChanges.pipe(takeUntil(this.destroy$), startWith(this.labelRequired.value)).subscribe(() => {
            this.elementClass = this.isControlRequired(this.labelRequired);
        });
    }

    private isControlRequired(control: AbstractControl): boolean {
        if (control?.hasValidator(Validators.required)) {
            return true;
        }

        // Check for custom validators
        if (control?.validator) {
            const validatorFn = control.validator({} as AbstractControl);

            // Check if conditionallyRequiredValidator is applied and indicates required
            if (validatorFn && validatorFn['required']) {
                return true;
            }

            // Check if conditionallyRequiredAddressLookupValidator is applied and indicates required
            if (validatorFn && validatorFn['carIdIsEmpty']) {
                return true;
            }
        }

        return false;
    }

    ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }
}
