import { Injectable } from '@angular/core';
import { AutomaticInvoiceRule, ValueRange } from 'src/app/api/installation-forms-client';
import { AutomaticInvoiceRuleListItem } from '../models/automatic-invoice-rule-list-item';

@Injectable({
    providedIn: 'root'
})
export class AutomaticInvoiceMapper {
    mapToAutomaticInvoiceRuleListItem = (rule: AutomaticInvoiceRule): AutomaticInvoiceRuleListItem => ({
        id: rule.id,
        order: rule.order,
        displayName: rule.displayName,
        voltageLevels: rule.voltageLevels,
        formTypes: rule.formTypes,
        formCategories: rule.formCategories,
        scopeOfDelivery: this.mapScopeOfDelivery(rule.scopeOfDeliveryMin, rule.scopeOfDeliveryMax),
        canTransferInvoiceAutomatically: rule.canTransferInvoiceAutomatically,
        productionCapacityRange: this.mapValueRange(rule.productionCapacityRange),
        hadExistingProduction: rule.hadExistingProduction,
        invoiceType: rule.invoiceType,
        tariff: rule.tariff
    });

    private mapScopeOfDelivery(min?: number, max?: number): string | undefined {
        if (min === undefined && max === undefined) {
            return undefined;
        }
        const minValue = min ?? 0;
        const maxValue = max ?? '∞';
        return `${minValue} - ${maxValue}`;
    }

    mapValueRange = (valueRange?: ValueRange) => {
        if (!valueRange) {
            return undefined;
        }
        return `${valueRange.min ?? '0'} - ${valueRange.max ?? '∞'} kW`;
    };
}
