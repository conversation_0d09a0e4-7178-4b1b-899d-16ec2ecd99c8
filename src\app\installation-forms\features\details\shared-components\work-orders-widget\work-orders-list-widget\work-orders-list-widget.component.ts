import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription, retry, take } from 'rxjs';
import {
    InstallationFormsClient,
    PendingUpdateAreaType,
    WorkOrder,
    WorkOrderEntryState,
    WorkOrderType
} from 'src/app/api/installation-forms-client';
import { SMALL_TABLE_ROWS_PER_PAGE } from 'src/app/core/constants/constants';
import { AutomaticFormRefreshService } from 'src/app/core/services/automatic-form-refresh/automatic-form-refresh.service';
import { FormDataService } from 'src/app/core/services/form-data/form-data.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { v4 as uuidv4 } from 'uuid';

@Component({
    selector: 'app-work-orders-list-widget',
    templateUrl: './work-orders-list-widget.component.html',
    styleUrls: ['./work-orders-list-widget.component.scss'],
    standalone: false
})
export class WorkOrdersListWidgetComponent implements OnDestroy, OnInit {
    loading = true;
    columns = ['workOrderType', 'workOrderPurpose', 'status', 'workOrderStarted'];
    rowsPerPage = SMALL_TABLE_ROWS_PER_PAGE;
    workOrderRemoveMeter = WorkOrderType.RemoveMeter;

    workOrders: WorkOrder[] = [];

    subscription: Subscription = new Subscription();

    constructor(
        private readonly client: InstallationFormsClient,
        private readonly messageServiceHelper: MessageServiceHelper,
        private readonly router: Router,
        private readonly formDataService: FormDataService,
        private readonly automaticFormRefreshService: AutomaticFormRefreshService
    ) {}

    isRowClickable = (row: WorkOrder) => {
        return row.state !== WorkOrderEntryState.ValidationFailed;
    };

    async handleRowClick(row: WorkOrder) {
        const selection = window.getSelection();
        if (selection && selection.toString().length > 0) {
            return;
        }
        if (!this.isRowClickable(row)) {
            return;
        }

        const type = row.workOrderType;

        if (row.processId) {
            await this.router.navigate([`process-center/work-orders/details/WorkOrders${type}/${row.processId}`]);
        }
    }

    ngOnInit(): void {
        this.automaticFormRefreshService.subscribeForRefresh(PendingUpdateAreaType.WorkOrders, this.getWorkOrders);
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
        this.automaticFormRefreshService.unsubscribeForRefresh(PendingUpdateAreaType.WorkOrders);
    }

    getWorkOrders = () => {
        this.subscription.add(
            this.client
                .getWorkOrders(this.formDataService.formId!, uuidv4())
                .pipe(retry({ count: 3, delay: 250 }), take(1))
                .subscribe({
                    next: (response) => {
                        this.workOrders = response.result;
                        this.loading = false;
                    },
                    error: (_) => {
                        this.messageServiceHelper.showError({
                            key: this.formDataService.formId!
                        });
                    }
                })
        );
    };
}
