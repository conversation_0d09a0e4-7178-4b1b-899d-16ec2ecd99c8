<p-tabs value="0">
    <p-tablist>
        <p-tab value="0">
            <div class="flex align-items-center gap-2">
                <i class="fa-regular fa-comments"></i>
                <span>{{ 'chatAndNotesWidget.chat.title' | translate }}</span>
                <i *ngIf="hasUnreadMessages()" class="pi pi-circle-fill color-warning"></i>
            </div>
        </p-tab>
        <p-tab value="1">
            <div class="flex align-items-center gap-2">
                <i class="fa-regular fa-note"></i>
                <span>{{ 'chatAndNotesWidget.notes.title' | translate }} ({{ notesCount }})</span>
            </div>
        </p-tab>
        <p-tab value="2">
            <div class="flex align-items-center gap-2">
                <i class="fa-solid fa-chevron-up"></i>
            </div>
        </p-tab>
    </p-tablist>
    <p-tabpanels>
        <p-tabpanel value="0">
            <app-chat></app-chat>
        </p-tabpanel>
        <p-tabpanel value="1">
            <app-notes (notesCountEmitter)="setNotesCount($event)"></app-notes>
        </p-tabpanel>
        <p-tabpanel value="2">
            <div></div>
        </p-tabpanel>
    </p-tabpanels>
</p-tabs>
