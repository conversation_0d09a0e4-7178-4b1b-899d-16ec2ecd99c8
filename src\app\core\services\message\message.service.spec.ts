import { TestBed } from '@angular/core/testing';
import { TranslateService } from '@ngx-translate/core';
import { MockService } from 'ng-mocks';
import { MessageService } from 'primeng/api';
import { TranslateServiceHelper } from '../translate/translate.service';
import { MessageServiceHelper, MessageServiceType } from './message.service';

describe('MessageServiceHelper', () => {
    let service: MessageServiceHelper;
    let messageService: MessageService;
    let translateService: TranslateService;
    let translateServiceHelper: TranslateServiceHelper;

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                MessageServiceHelper,
                { provide: MessageService, useValue: MockService(MessageService) },
                { provide: TranslateService, useValue: MockService(TranslateService) },
                { provide: TranslateServiceHelper, useValue: MockService(TranslateServiceHelper) }
            ]
        });

        service = TestBed.inject(MessageServiceHelper);
        messageService = TestBed.inject(MessageService);
        translateService = TestBed.inject(TranslateService);
        translateServiceHelper = TestBed.inject(TranslateServiceHelper);
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    it('should handle error with invalid JSON', () => {
        const translatedTitle = 'title';
        const error = { response: 'Invalid JSON' };
        const formId = 'form1';
        const widgetTranslationKey = 'widgetKey';
        jest.spyOn(translateService, 'instant').mockImplementation((key) =>
            key === 'common.errorTitle' ? translatedTitle : undefined
        );
        const spy = jest.spyOn(messageService, 'add');

        service.addTranslated(error, formId, widgetTranslationKey);

        expect(spy).toHaveBeenCalledWith({
            summary: translatedTitle,
            severity: MessageServiceType.ERROR,
            detail: expect.stringContaining('Error occurred while trying to display a message:')
        });
    });

    it('should show success message with translated title', () => {
        const translatedTitle = 'Success Title';
        const message = { detail: 'Success message' };
        jest.spyOn(translateService, 'instant').mockReturnValue(translatedTitle);
        const spy = jest.spyOn(messageService, 'add');

        service.showSuccess(message);

        expect(spy).toHaveBeenCalledWith({
            ...message,
            severity: MessageServiceType.SUCCESS,
            summary: translatedTitle
        });
    });

    it('should show warning message with translated title', () => {
        const translatedTitle = 'Warning Title';
        const message = { detail: 'Warning message' };
        jest.spyOn(translateService, 'instant').mockReturnValue(translatedTitle);
        const spy = jest.spyOn(messageService, 'add');

        service.showWarning(message);

        expect(spy).toHaveBeenCalledWith({
            ...message,
            severity: MessageServiceType.WARNING,
            summary: translatedTitle
        });
    });

    it('should show error message with translated title', () => {
        const translatedTitle = 'Error Title';
        const message = { detail: 'Error message' };
        jest.spyOn(translateService, 'instant').mockReturnValue(translatedTitle);
        const spy = jest.spyOn(messageService, 'add');

        service.showError(message);

        expect(spy).toHaveBeenCalledWith({
            ...message,
            severity: MessageServiceType.ERROR,
            summary: translatedTitle
        });
    });

    it('should handle valid problem details with translations when widget translation exists', () => {
        const errorTitle = 'Error Title';
        const translatedDetail = 'Translated Error Detail';
        const formId = 'form1';
        const resultCode = 'someErrorCode';
        const widgetTranslationKey = 'widgetKey';
        const problemDetails = {
            severity: 'Error',
            resultCode: resultCode,
            detail: 'Original detail',
            title: 'Original title'
        };

        jest.spyOn(translateService, 'instant').mockReturnValue(errorTitle);
        jest.spyOn(translateServiceHelper, 'getTranslationOrNull').mockImplementation((key) =>
            key === `${widgetTranslationKey}.${resultCode}` ? translatedDetail : null
        );

        const spy = jest.spyOn(messageService, 'add');

        service.addTranslated({ response: JSON.stringify(problemDetails) }, formId, widgetTranslationKey);

        expect(spy).toHaveBeenCalledWith({
            severity: MessageServiceType.ERROR,
            summary: errorTitle,
            detail: translatedDetail,
            key: formId
        });
    });

    it('should fall back to common translation when widget translation is missing', () => {
        const errorTitle = 'Error Title';
        const commonTranslation = 'Common Error Translation';
        const formId = 'form1';
        const resultCode = 'someErrorCode';
        const widgetTranslationKey = 'widgetKey';
        const problemDetails = {
            severity: 'Error',
            resultCode: resultCode,
            detail: 'Original detail',
            title: 'Original title'
        };

        jest.spyOn(translateService, 'instant').mockReturnValue(errorTitle);
        jest.spyOn(translateServiceHelper, 'getTranslationOrNull').mockImplementation((key) => {
            if (key === `${widgetTranslationKey}.${resultCode}`) return null;
            if (key === `common.${resultCode}`) return commonTranslation;
            return null;
        });

        const spy = jest.spyOn(messageService, 'add');

        service.addTranslated({ response: JSON.stringify(problemDetails) }, formId, widgetTranslationKey);

        expect(spy).toHaveBeenCalledWith({
            severity: MessageServiceType.ERROR,
            summary: errorTitle,
            detail: commonTranslation,
            key: formId
        });
    });

    it('should fall back to problem details when no translations exist', () => {
        const errorTitle = 'Error Title';
        const formId = 'form1';
        const resultCode = 'someErrorCode';
        const widgetTranslationKey = 'widgetKey';
        const originalDetail = 'Original detail';
        const problemDetails = {
            severity: 'Error',
            resultCode: resultCode,
            detail: originalDetail,
            title: 'Original title'
        };

        jest.spyOn(translateService, 'instant').mockReturnValue(errorTitle);
        jest.spyOn(translateServiceHelper, 'getTranslationOrNull').mockReturnValue(null);

        const spy = jest.spyOn(messageService, 'add');

        service.addTranslated({ response: JSON.stringify(problemDetails) }, formId, widgetTranslationKey);

        expect(spy).toHaveBeenCalledWith({
            severity: MessageServiceType.ERROR,
            summary: errorTitle,
            detail: originalDetail,
            key: formId
        });
    });

    it('should translate substitutions correctly', () => {
        const errorTitle = 'Error Title';
        const translatedDetail = 'Error with {field}';
        const formId = 'form1';
        const resultCode = 'validationError';
        const widgetTranslationKey = 'widgetKey';
        const problemDetails = {
            severity: 'Error',
            resultCode: resultCode,
            substitutions: { field: 'EMAIL' }
        };

        const translatedField = 'Email Address';
        const substitutionsPath = [{ key: 'field', path: 'fields' }];

        jest.spyOn(translateService, 'instant').mockImplementation((key) =>
            key === 'common.errorTitle' ? errorTitle : key === 'fields.EMAIL' ? translatedField : key
        );

        jest.spyOn(translateServiceHelper, 'hasTranslation').mockReturnValue(true);
        jest.spyOn(translateServiceHelper, 'getTranslationOrNull').mockReturnValue(translatedDetail);

        const spy = jest.spyOn(messageService, 'add');

        service.addTranslated({ response: JSON.stringify(problemDetails) }, formId, widgetTranslationKey, ...substitutionsPath);

        expect(spy).toHaveBeenCalledWith(
            expect.objectContaining({
                severity: MessageServiceType.ERROR,
                summary: errorTitle,
                detail: translatedDetail,
                key: formId
            })
        );
    });

    describe('MessageServiceType', () => {
        it('should parse Info type correctly', () => {
            expect(MessageServiceType.parseType('Info')).toBe(MessageServiceType.INFO);
        });

        it('should parse Warning type correctly', () => {
            expect(MessageServiceType.parseType('Warning')).toBe(MessageServiceType.WARNING);
        });

        it('should default to ERROR for undefined type', () => {
            expect(MessageServiceType.parseType(undefined)).toBe(MessageServiceType.ERROR);
        });

        it('should default to ERROR for unrecognized type', () => {
            expect(MessageServiceType.parseType('Unknown')).toBe(MessageServiceType.ERROR);
        });
    });
});
