import { ChangeDetector<PERSON>ef, Component, EventEmitter, Input, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup } from '@angular/forms';
import { AuthorizationService } from '@kmd-elements-ui/authorization';
import { TranslateService } from '@ngx-translate/core';
import * as deepequal from 'fast-deep-equal';
import { MenuItem } from 'primeng/api';
import { AutoCompleteSelectEvent } from 'primeng/autocomplete';
import { Subject, finalize, map, of, switchMap, take, takeUntil } from 'rxjs';
import {
    ApplicationUserAutocomplete,
    CaseWorker,
    CaseWorkerUpdate,
    InstallationFormsClient
} from 'src/app/api/installation-forms-client';
import { ChangesModel } from 'src/app/core/constants/changes-details';
import { WidgetNames } from 'src/app/core/constants/widget-names';
import { AutomaticFormRefreshService } from 'src/app/core/services/automatic-form-refresh/automatic-form-refresh.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import conditionallyRequiredValidator from 'src/app/core/utils/validators/conditionally-required/conditionally-required.validator';
import { EmailValidator } from 'src/app/core/utils/validators/email/email.validator';
import { WidgetWithChangesModelComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget-with-changes-model.component';
import { v4 as uuidv4 } from 'uuid';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';
import { CaseWorkerChanges } from './case-worker-changes.model';

@Component({
    selector: 'app-case-worker-widget',
    templateUrl: './case-worker-widget.component.html',
    styleUrls: ['./case-worker-widget.component.scss'],
    standalone: false
})
export class CaseWorkerWidgetComponent extends WidgetWithChangesModelComponent implements OnInit, OnDestroy {
    public form!: FormGroup;

    private _caseWorker!: CaseWorker;

    private widgetTranslations: any;
    private commonTranslations: any;
    public menuItems: MenuItem[] = [];

    private destroy$ = new Subject<void>();
    private search$ = new Subject<string>();
    private clear$ = new Subject<void>();

    filteredUsers: ApplicationUserAutocomplete[] = [];

    duringActionConfirmation: boolean = false;
    attemptedAction: (() => void) | null = null;

    @Input()
    set caseWorker(value: CaseWorker | null | undefined) {
        if (
            this._caseWorker &&
            !this.isDuringForceRefresh &&
            this.hasConflictingChanges(this._caseWorker, this.getFormValueAsCaseWorker(), value)
        ) {
            this.automaticFormRefreshService.notifyConflictingChangeOccurred();
            return;
        }
        this.dataLoaded();
        this._caseWorker = value || new CaseWorker();
        if (this.canSupplyFormData) {
            this.supplyFormData();
        }
    }

    get isReadOnly() {
        return !this.hasUserWritePermission || this.isProcessing;
    }

    get caseWorker(): CaseWorker | null | undefined {
        return this._caseWorker;
    }

    @Output() changesMade = new EventEmitter<{ key: string; hasChanges: boolean }>();

    constructor(
        private fb: FormBuilder,
        private readonly cd: ChangeDetectorRef,
        private readonly client: InstallationFormsClient,
        private readonly translateService: TranslateService,
        private readonly messageServiceHelper: MessageServiceHelper,
        private readonly authService: AuthorizationService,
        readonly formDataService: FormDataService,
        readonly automaticFormRefreshService: AutomaticFormRefreshService
    ) {
        super(formDataService, automaticFormRefreshService);
        this.form = this.fb.group({
            id: [null],
            name: [
                { text: '' },
                [
                    (control: AbstractControl) =>
                        conditionallyRequiredValidator(control, this.notAllFieldsAreEmpty, (control) => control.value?.text)
                ]
            ],
            email: ['', [EmailValidator()]]
        });

        this.changesModel = new CaseWorkerChanges() as unknown as ChangesModel;
    }

    override ngOnInit(): void {
        super.ngOnInit();
        this.commonTranslations = this.translateService.instant('common');
        this.widgetTranslations = this.translateService.instant('caseWorkerWidget');

        this.registerActions();

        this.setFormEditability(this.hasUserWritePermission);
        this.setMenuItems();
        this.formDataService.editabilityChange$.subscribe(() => {
            this.setFormEditability(this.hasUserWritePermission);
            this.setMenuItems();
        });
    }

    override ngOnDestroy(): void {
        super.ngOnDestroy();
        this.destroy$.next();
        this.destroy$.complete();
        this.destroy$.unsubscribe();
    }

    notAllFieldsAreEmpty = () => {
        return (
            this.form?.controls &&
            Object.keys(this.form.controls).some((fieldName) => {
                const fieldValue = this.form.get(fieldName)?.value;
                return (fieldName !== 'name' && fieldValue) || (fieldName === 'name' && fieldValue?.text);
            })
        );
    };

    onChange(attemptedValue: CaseWorker) {
        if (!deepequal.default(attemptedValue, this._caseWorker)) {
            const fieldsToBeReset = Object.values(this.form.controls);
            fieldsToBeReset.forEach((field: AbstractControl) => {
                field.setErrors(null);
                field.updateValueAndValidity({
                    onlySelf: true,
                    emitEvent: false
                });
            });
            this.form.updateValueAndValidity({
                onlySelf: true,
                emitEvent: false
            });

            if (!this.hasUserWritePermission) {
                return;
            }

            this.emitDirty(this.recalculateChanges(this._caseWorker, this.getFormValueAsCaseWorker()));
        }
    }

    private allFieldsAreEmpty(): boolean {
        return !this.notAllFieldsAreEmpty();
    }

    private supplyFormData() {
        this.form.setValue({
            id: this._caseWorker?.id || null,
            name: { text: this._caseWorker?.name || '' },
            email: this._caseWorker?.email || ''
        });
    }

    private reinitAfterSave() {
        const formValue = this.form.value;
        this._caseWorker = new CaseWorker({
            id: formValue.id,
            name: formValue.name?.text || '',
            phoneNumber: formValue.phoneNumber || '',
            email: formValue.email || ''
        });
        this.setMenuItems();
        this.changesModel = new CaseWorkerChanges() as unknown as ChangesModel;
        this.changesAmount = 0;
        this.cd.detectChanges();
        this.emitDirty(false);
        this.form.updateValueAndValidity();
    }

    protected confirmationAssignToMe() {
        if (!!this.caseWorker?.email) {
            this.attemptedAction = () => this.assignToMe();
        } else {
            this.assignToMe();
        }
    }

    protected confirmationUnassignMe() {
        if (this.formDataService.isCurrentUserAssignedAsCaseWorker) {
            this.unAssign();
        } else {
            this.attemptedAction = () => this.unAssign();
        }
    }

    protected assignToMe() {
        this.subscription.add(
            this.authService
                .getUser()
                .pipe(take(1))
                .subscribe({
                    next: (user) => {
                        this.saveCaseWorker(
                            new CaseWorker({
                                id: user?.id,
                                name: `${user?.firstName} ${user?.lastName}`,
                                email: user?.email,
                                phoneNumber: ''
                            })
                        );
                    }
                })
        );
    }

    protected unAssign() {
        this.processingStarted();
        if (this._caseWorker?.name) {
            this.subscription.add(
                this.client
                    .deleteInstallationFormCaseWorkerAssignment(
                        this.formDataService.formId!,
                        uuidv4(),
                        this.formDataService.rowVersion
                    )
                    .pipe(
                        take(1),
                        finalize(() => this.processingFinished())
                    )
                    .subscribe({
                        error: (_) => {
                            this.messageServiceHelper.showError({
                                key: this.formDataService.formId!
                            });
                        },
                        complete: () => {
                            this.caseWorker = new CaseWorker({
                                id: undefined,
                                name: '',
                                email: '',
                                phoneNumber: ''
                            });
                            this.messageServiceHelper.showSuccess({
                                detail: this.widgetTranslations['unassignSuccessDetails']
                            });
                            this.reinitAfterSave();
                            this.automaticFormRefreshService.forceRefresh();
                        }
                    })
            );
        }
    }

    getFormValueAsCaseWorker(): CaseWorker {
        return new CaseWorker({
            ...this.form.value,
            name: this.form.value.name?.text
        });
    }

    caseWorkerToFormValue(caseWorker: CaseWorker) {
        return {
            id: caseWorker.id,
            name: { text: caseWorker.name },
            email: caseWorker.email
        };
    }

    onSaveClicked() {
        const newEmailValue = this.form.get('email')?.value;
        if (this._caseWorker.email && newEmailValue && this._caseWorker.email !== newEmailValue) {
            this.attemptedAction = () => this.saveCaseWorker();
        } else {
            this.saveCaseWorker();
        }
    }

    saveCaseWorker(overridenValue: CaseWorker | null = null) {
        this.processingStarted();
        if (!overridenValue && this.allFieldsAreEmpty()) {
            this.unAssign();
            return;
        }

        if (overridenValue || this.form.valid) {
            const updateValue: CaseWorkerUpdate = new CaseWorkerUpdate(overridenValue || this.getFormValueAsCaseWorker());

            this.subscription.add(
                this.client
                    .updateInstallationFormCaseWorkerAssignment(
                        this.formDataService.formId!,
                        uuidv4(),
                        this.formDataService.rowVersion,
                        updateValue
                    )
                    .pipe(
                        take(1),
                        finalize(() => this.processingFinished())
                    )
                    .subscribe({
                        error: (error) => {
                            if (error.status === 409) {
                                this.messageServiceHelper.showError({
                                    detail: this.commonTranslations['concurrentModification'],
                                    key: this.formDataService.formId!
                                });
                                return;
                            }

                            this.messageServiceHelper.showError({
                                key: this.formDataService.formId!
                            });
                        },
                        complete: () => {
                            this.messageServiceHelper.showSuccess({
                                detail: this.widgetTranslations['assignSuccessDetails'],
                                key: this.formDataService.formId!
                            });

                            if (overridenValue) {
                                this.form.setValue(this.caseWorkerToFormValue(overridenValue));
                            }

                            this.reinitAfterSave();
                            this.automaticFormRefreshService.forceRefresh();
                        }
                    })
            );
        } else {
            this.processingFinished();
            this.messageServiceHelper.showWarning({
                detail: this.commonTranslations['allRequiredFieldsShouldBeCompleted'],
                key: this.formDataService.formId!
            });
        }
    }

    onCancelChangesClicked() {
        this.supplyFormData();
        this.onChange(this.getFormValueAsCaseWorker());
    }

    search(event: any) {
        this.search$.next(event.query);
    }

    onSelectUser(event: AutoCompleteSelectEvent) {
        this.form.patchValue({
            id: event.value.applicationUser?.id,
            email: event.value.applicationUser?.email
        });
        this.onChange(this.getFormValueAsCaseWorker());
    }

    onClear() {
        this.clear$.next();
    }

    onConfirmationDialogYesButtonClick() {
        this.attemptedAction!();
        this.attemptedAction = null;
    }

    onConfirmationDialogNoButtonClick() {
        this.attemptedAction = null;
    }

    private emitDirty(hasChanges: boolean) {
        this.changesMade.emit({
            key: WidgetNames.caseWorkerWidget,
            hasChanges: hasChanges
        });
    }

    private registerActions() {
        this.search$
            .pipe(
                takeUntil(this.destroy$),
                switchMap((query) => {
                    query = query.trim();
                    if (!query || query.length === 0) {
                        return of([]);
                    }
                    return this.client
                        .getApplicationUsersByName(uuidv4(), undefined, query)
                        .pipe(map((response) => response.result.users));
                })
            )
            .subscribe((x) => {
                this.filteredUsers = x;
            });

        this.clear$.subscribe(() => {
            this.form.get('name')?.patchValue('');
        });

        this.subscription.add(this.search$);
        this.subscription.add(this.clear$);
    }

    private setMenuItems() {
        if (!this.widgetTranslations) {
            return;
        }
        this.menuItems = [
            {
                label: this.widgetTranslations['unassign'],
                icon: 'fa-solid fa-trash-can',
                visible: this.hasUserWritePermission && !!this.caseWorker?.name,
                id: 'unassignButton',
                command: (_event) => {
                    this.confirmationUnassignMe();
                }
            }
        ];
    }

    public hasVisibleMenuItems = () => this.menuItems.some((x) => x.visible);
}
