
$local_branch = git rev-parse --abbrev-ref HEAD
$valid_branch_regex = "^((feature|bug|incident)\/[0-9]+-[A-Za-z0-9._-]+|(release|poc)\/[A-Za-z0-9._-]+)$"

$message = "Your branch name is incorrect. Branch names in this project must adhere to this contract: $valid_branch_regex. Your commit will be rejected. You should rename your branch to a valid name and try again. See: https://dev.azure.com/kmddk/COMBAS/_wiki/wikis/COMBAS.wiki/2464/Branching-Strategy?anchor=naming-patterns"

if (!($local_branch -match $valid_branch_regex)) {
    Write-Host $message
    exit 1
}

exit 0