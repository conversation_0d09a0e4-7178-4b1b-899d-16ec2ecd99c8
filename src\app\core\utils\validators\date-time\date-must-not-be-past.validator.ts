import { AbstractControl, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';

export default function dateMustNotBePastValidator(
    control: AbstractControl,
    includeTime: boolean = false
): ValidationErrors | null {
    const controlValue: Date = control.value;
    if (!controlValue?.getTime) {
        return null;
    }

    const currentDate = new Date();
    const thresholdDate = includeTime
        ? currentDate
        : new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate());
    const isError = controlValue < thresholdDate;
    if (isError) {
        return { dateMustNotBePast: true };
    }
    return null;
}
