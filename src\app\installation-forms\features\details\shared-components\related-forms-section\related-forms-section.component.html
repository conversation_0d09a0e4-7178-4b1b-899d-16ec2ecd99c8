<div #overlayContainer></div>
<p-accordion [(value)]="accordionActiveValues">
    <p-accordion-panel [value]="relatedFormsTabName">
        <p-accordion-header>{{ 'relatedFormsSection.title' | translate }}</p-accordion-header>
        <p-accordion-content>
            <ng-container [formGroup]="addForm" *ngIf="!isReadOnly">
                <div class="zebra-item mb-20" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
                    <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                        <label for="formNumber">
                            {{ 'relatedFormsSection.formNumber' | translate }}
                        </label>
                    </div>
                    <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                        <div class="flex flex-row align-items-center">
                            <p-autoComplete
                                formControlName="newRelatedForm"
                                [showEmptyMessage]="true"
                                [suggestions]="filtered"
                                class="w-full"
                                field="formNumber"
                                [appendTo]="overlayContainer"
                                [minLength]="1"
                                [forceSelection]="true"
                                [showClear]="true"
                                (completeMethod)="search($event)"
                                (onClear)="onClear()"
                                (onSelect)="onSelect()">
                            </p-autoComplete>
                            <div>
                                <button
                                    class="button-no-wrap p-button-outlined ml-5"
                                    id="updateconnectionRightsButton"
                                    [disabled]="!hasNewRelatedFormPicked"
                                    type="button"
                                    pButton
                                    pRipple
                                    (click)="addNewRelatedForm()">
                                    {{ 'relatedFormsSection.addRelatedForm' | translate }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </ng-container>

            <div [formGroup]="form" *ngIf="form.get('relatedForms')?.value.length; else emptyMessage">
                <p-table
                    [value]="form.get('relatedForms')?.value"
                    [paginator]="form.get('relatedForms')?.value?.length > rowsPerPage"
                    [rows]="rowsPerPage"
                    [rowHover]="true">
                    <ng-template pTemplate="header">
                        <tr>
                            <th id="related-forms-formNumber">{{ 'relatedFormsSection.formNumber' | translate }}</th>
                            <th id="related-forms-formType">{{ 'relatedFormsSection.formType' | translate }}</th>
                            <th id="related-forms-state">{{ 'relatedFormsSection.state' | translate }}</th>
                            <th id="related-forms-origin">{{ 'relatedFormsSection.origin' | translate }}</th>
                            <th id="related-forms-createdDate">{{ 'relatedFormsSection.createdDate' | translate }}</th>
                            <th id="related-forms-actions"></th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-item>
                        <tr class="zebra-item table-row" (click)="handleRowClick(item)">
                            <td>{{ item.formNumber }}</td>
                            <td>{{ 'enums.type.' + item.type | translate }}</td>
                            <td>{{ 'enums.state.' + item.state | translate }}</td>
                            <td>
                                <div [ngSwitch]="item.direction">
                                    <span *ngSwitchCase="'NoDirection'">{{
                                        'enums.formsRelationOrigin.' + item.origin | translate
                                    }}</span>
                                    <span *ngSwitchDefault
                                        >{{ 'enums.formsRelationOrigin.' + item.origin | translate }} ({{
                                            'enums.formsRelationDirection.' + item.direction | translate
                                        }})</span
                                    >
                                </div>
                            </td>
                            <td>{{ item.createdDate | formatDate }}</td>
                            <td>
                                <button
                                    *ngIf="item.relationType === 'Manual'"
                                    [disabled]="isReadOnly"
                                    pButton
                                    class="p-button-outlined border-none"
                                    icon="fa-solid fa-trash-can"
                                    (click)="removeItem(item, $event)"></button>
                            </td>
                        </tr>
                    </ng-template>
                </p-table>
            </div>

            <ng-template #emptyMessage>
                <p class="pl-3">{{ 'relatedFormsSection.noRelatedFormsFound' | translate }}</p>
            </ng-template>
        </p-accordion-content>
    </p-accordion-panel>
</p-accordion>
