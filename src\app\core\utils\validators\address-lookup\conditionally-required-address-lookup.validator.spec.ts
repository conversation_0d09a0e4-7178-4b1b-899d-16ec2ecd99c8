import { FormControl } from '@angular/forms';
import conditionallyRequiredAddressLookupValidator from './conditionally-required-address-lookup.validator';

describe('conditionallyRequiredAddressLookupValidator', () => {
    it('should validate when condition is true', () => {
        const shouldBeRequired = () => true;
        const validator = conditionallyRequiredAddressLookupValidator(shouldBeRequired);

        const validControl = new FormControl({ text: 'Valid address' });
        expect(validator(validControl)).toBeNull();

        const invalidControl = new FormControl({ text: '' });
        expect(validator(invalidControl)).toEqual({ carIdIsEmpty: true });

        const nullControl = new FormControl(null);
        expect(validator(nullControl)).toEqual({ carIdIsEmpty: true });
    });

    it('should not validate when condition is false', () => {
        const shouldBeRequired = () => false;
        const validator = conditionallyRequiredAddressLookupValidator(shouldBeRequired);

        const validControl = new FormControl({ text: 'Valid address' });
        expect(validator(validControl)).toBeNull();

        const invalidControl = new FormControl({ text: '' });
        expect(validator(invalidControl)).toBeNull();

        const nullControl = new FormControl(null);
        expect(validator(nullControl)).toBeNull();
    });

    it('should handle null/undefined values correctly when condition is true', () => {
        const shouldBeRequired = () => true;
        const validator = conditionallyRequiredAddressLookupValidator(shouldBeRequired);

        const nullControl = new FormControl(null);
        expect(() => validator(nullControl)).not.toThrow();
    });
});
