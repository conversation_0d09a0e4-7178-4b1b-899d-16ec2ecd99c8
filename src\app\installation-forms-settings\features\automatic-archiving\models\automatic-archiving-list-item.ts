import {
    FormState,
    FormType,
    MasterDataProcessType,
    ResponsibleForMeter,
    WorkOrderType
} from 'src/app/api/installation-forms-client';

export interface AutomaticArchivingListItem {
    id: string;
    order?: number;
    displayName: string;
    formTypes?: FormType[];
    formStates?: FormState[];
    responsibleForMeter?: (ResponsibleForMeter | undefined)[];
    meterInstalled?: boolean;
    startsAsConstruction?: boolean;
    verifiedWorkOrderType?: WorkOrderType;
    verifiedWorkOrderDescription?: string;
    masterDataProcessTypes?: MasterDataProcessType[];
}
