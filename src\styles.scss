$color-error: #d32f2f;
$color-warning: #ffb300;
$color-info: #04827b;
$color-link: #0ed274;

:host {
    ::ng-deep {
        @mixin color-error {
            color: $color-error;
        }

        .color-success,
        .fa-chevron-circle-right {
            color: $color-link;
        }

        .color-error {
            @include color-error;
        }

        .color-warning {
            color: $color-warning;
        }

        .color-info {
            color: $color-info;
        }

        a:hover {
            cursor: grab;
        }

        .show-more {
            margin-top: 10px;
            color: #000000;
            text-align: center;

            &:hover {
                color: #0ed274;
            }

            fa-icon {
                vertical-align: middle;

                &.green {
                    color: #0ed274;
                }

                &.black {
                    color: #000000;
                }

                &.gray {
                    color: #c1c1c1;
                }
            }
        }

        p-inputnumber,
        .p-inputnumber {
            width: 100%;
        }

        .p-select {
            width: 100%;
        }

        .p-multiselect {
            width: 100%;
        }

        .p-multiselect-panel {
            max-width: 60vw;
        }

        .zebra-item .zebra-caption {
            word-break: break-word;
        }

        .add-point {
            cursor: pointer !important;
        }

        .no-pointer {
            cursor: default !important;
        }

        .p-autocomplete {
            width: 100%;
        }

        .p-toggleswitch.p-toggleswitch-checked:not(:disabled) .p-toggleswitch-slider:hover {
            background: #222e2c;
        }

        .p-autocomplete-input {
            min-width: 180px;
        }

        .panel-without-header {
            .p-panel-content {
                border-top-right-radius: 0.5rem;
                border-top-left-radius: 0.5rem;
            }

            .p-panel-header {
                display: none;
            }
        }

        .p-card-content {
            min-height: 1.25rem;
        }

        .button-no-wrap {
            white-space: nowrap;
        }

        .p-panel .p-panel-header .p-panel-header-icon {
            margin-left: 10px;
        }

        .table-row {
            display: table-row;
        }

        .chat-message {
            max-width: 70%;

            .p-card-body {
                background: #e9f0ee;
            }
        }

        .note {
            max-width: 100%;

            .p-card-body {
                background: #fffdab;
            }
        }

        .add-chat-or-note-textarea {
            min-height: fit-content;
        }

        .chat-scroll-panel {
            width: 100%;
            height: 300px;
        }

        .break-word {
            word-wrap: break-word;
        }

        .table-header {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
        }

        .line {
            flex-grow: 1;
            margin: 1rem;
            border-bottom: 1px solid #e7ebef;
        }

        .p-tabs {
            margin-top: -0.7rem;
            .p-tabpanels {
                padding: 0.5rem;
                border-bottom-left-radius: 0.5rem;
                border-bottom-right-radius: 0.5rem;
            }

            .p-tablist {
                background: transparent;
                .p-tablist-tab-list {
                    border-top-left-radius: 0.5rem;
                    border-top-right-radius: 0.5rem;
                    .p-tab {
                        border: none;
                        background: none;
                        font-weight: 600;
                        &:nth-last-of-type(1) {
                            margin-left: auto;
                            padding-left: 1.5rem;
                            padding-right: 1.5rem;
                            margin-right: 0px;
                        }
                    }
                }
            }
        }

        .p-dialog .p-dialog-content:last-of-type {
            border-top-right-radius: 0;
            border-top-left-radius: 0;
        }

        .actions-column {
            width: 50px;
        }

        // to match the height of the input fields
        .p-tag {
            min-height: 1.75rem;
            background: $color-warning;
            font-size: 1rem;
            font-weight: 500;
            color: #1B6163;
        }
    }
}

