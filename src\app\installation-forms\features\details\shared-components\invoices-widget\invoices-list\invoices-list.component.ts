import { ChangeDetectorRef, Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CheckboxChangeEvent } from 'primeng/checkbox';
import { finalize, Subscription, switchMap } from 'rxjs';
import {
    InstallationFormsClient,
    Invoice,
    InvoiceIsRequiredFlagUpdate,
    InvoiceState,
    PendingUpdateAreaType,
    TransferToErp
} from 'src/app/api/installation-forms-client';
import { SMALL_TABLE_ROWS_PER_PAGE } from 'src/app/core/constants/constants';
import { WidgetNames } from 'src/app/core/constants/widget-names';
import { AutomaticFormRefreshService } from 'src/app/core/services/automatic-form-refresh/automatic-form-refresh.service';
import { FormDataService } from 'src/app/core/services/form-data/form-data.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { v4 as uuidv4 } from 'uuid';

@Component({
    selector: 'app-invoices-list',
    templateUrl: './invoices-list.component.html',
    styleUrl: './invoices-list.component.scss',
    standalone: false
})
export class InvoicesListComponent implements OnInit, OnDestroy {
    widgetName: string = WidgetNames.invoicesWidget;
    @Input() canEditInvoices!: boolean;

    private widgetTranslations: any;
    private subscription: Subscription = new Subscription();

    invoices: Invoice[] = [];

    columns = ['createdDate', 'createdInErpDate', 'status', 'number', 'totalSum', 'isRequired'];
    rowsPerPage = SMALL_TABLE_ROWS_PER_PAGE;

    constructor(
        readonly formDataService: FormDataService,
        private readonly client: InstallationFormsClient,
        private readonly messageServiceHelper: MessageServiceHelper,
        private readonly router: Router,
        private readonly automaticFormRefreshService: AutomaticFormRefreshService,
        private cdr: ChangeDetectorRef
    ) {}

    ngOnInit() {
        this.automaticFormRefreshService.subscribeForRefresh(PendingUpdateAreaType.Invoices, this.getInvoices);
    }

    ngOnDestroy(): void {
        this.automaticFormRefreshService.unsubscribeForRefresh(PendingUpdateAreaType.Invoices);
    }

    getInvoices = () => {
        this.subscription.add(
            this.client.getInvoices(this.formDataService.formId!, uuidv4()).subscribe({
                next: (response) => {
                    this.invoices = response.result;
                },
                error: (_error) => {
                    this.messageServiceHelper.showError({
                        detail: this.widgetTranslations['getInvoicesError'],
                        key: this.formDataService.formId!
                    });
                }
            })
        );
    };

    async goToInvoice(invoiceId?: string) {
        if (!invoiceId) {
            return;
        }

        const selection = window.getSelection();
        if (selection && selection.toString().length > 0) {
            return;
        }

        await this.router.navigate([`single-invoicing/${invoiceId}`]);
    }

    formatTotalSum(invoice: Invoice) {
        if (!invoice.totalSum) {
            return '';
        }

        if (!invoice.currency) {
            return invoice.totalSum.toString();
        }

        return `${invoice.totalSum} ${invoice.currency}`;
    }

    onIsRequiredClick(event: Event) {
        //this is a special case where checkbox change event is propagated after click event, so without this
        //table row click event is fired first and we cannot stop it from executing it's logic - we'd be redirected to invoice preview
        event.stopPropagation();
    }

    onRequiredChange(event: CheckboxChangeEvent, invoice: Invoice) {
        event.originalEvent?.stopPropagation();
        this.formDataService.processingStarted();
        this.subscription.add(
            this.client
                .updateInvoiceIsRequiredFlag(
                    invoice.invoiceEntryId,
                    uuidv4(),
                    new InvoiceIsRequiredFlagUpdate({
                        formId: this.formDataService.formId!,
                        isRequired: event.checked
                    })
                )
                .pipe(
                    switchMap(() => this.client.getInvoices(this.formDataService.formId!, uuidv4())),
                    finalize(() => this.formDataService.processingFinished())
                )
                .subscribe({
                    error: (_) => {
                        this.messageServiceHelper.showError({
                            key: this.formDataService.formId!
                        });
                    },
                    complete: () => {
                        this.messageServiceHelper.showSuccess({
                            key: this.formDataService.formId!
                        });
                    }
                })
        );
        this.cdr.detectChanges();
    }

    canTransferInvoice(invoice: Invoice): boolean {
        return (
            this.canEditInvoices &&
            (invoice.status === InvoiceState.Calculated || invoice.status === InvoiceState.TransferStarted)
        );
    }

    transferInvoice(event: MouseEvent, invoice: Invoice): void {
        event?.stopPropagation();
        this.formDataService.processingStarted();

        this.subscription.add(
            this.client
                .transferInvoiceToErp(
                    invoice.invoiceEntryId,
                    uuidv4(),
                    new TransferToErp({
                        formId: this.formDataService.formId!
                    }),
                    undefined
                )
                .pipe(finalize(() => this.formDataService.processingFinished()))
                .subscribe({
                    error: (_) => {
                        this.messageServiceHelper.showError({
                            key: this.formDataService.formId!
                        });
                    },
                    complete: () => {
                        this.messageServiceHelper.showSuccess({
                            key: this.formDataService.formId!
                        });
                        this.getInvoices();
                    }
                })
        );
    }
}
