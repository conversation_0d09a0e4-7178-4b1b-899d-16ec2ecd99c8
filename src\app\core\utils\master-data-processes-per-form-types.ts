import { FormType, MasterDataProcessType, TerminationScope } from 'src/app/api/installation-forms-client';

const FORM_TYPES_PER_MASTER_DATA_PROCESS_TYPE: Record<MasterDataProcessType, FormType[]> = {
    [MasterDataProcessType.ModifyConnectionPoint]: [...Object.values(FormType)],
    [MasterDataProcessType.CreateConnectionPoint]: [FormType.NewInstallation],
    [MasterDataProcessType.CloseDownSupplyType]: [FormType.NewInstallation, FormType.Termination],
    [MasterDataProcessType.CloseDownProduction]: [FormType.Termination],
    [MasterDataProcessType.AddConnectionPointNetSettlementGroup]: [FormType.EnergyProduction],
    [MasterDataProcessType.ModifyConnectionPointNetSettlementGroup]: [FormType.EnergyProduction]
};

const FORM_TYPES_PER_MASTER_DATA_PROCESS_TYPE_FOR_AUTOMATION: Record<MasterDataProcessType, FormType[]> = {
    [MasterDataProcessType.ModifyConnectionPoint]: [...Object.values(FormType)],
    [MasterDataProcessType.CreateConnectionPoint]: [FormType.NewInstallation],
    [MasterDataProcessType.CloseDownSupplyType]: [FormType.Termination],
    [MasterDataProcessType.CloseDownProduction]: [FormType.Termination],
    [MasterDataProcessType.AddConnectionPointNetSettlementGroup]: [FormType.EnergyProduction],
    [MasterDataProcessType.ModifyConnectionPointNetSettlementGroup]: [FormType.EnergyProduction]
};

const TERMINATION_SCOPE_INCLUDE_MAP: Record<TerminationScope, MasterDataProcessType[]> = {
    [TerminationScope.EntireInstallation]: [MasterDataProcessType.CloseDownSupplyType],
    [TerminationScope.EnergyProduction]: [MasterDataProcessType.CloseDownProduction]
};

/**
 * Gets available master data process types based on selected form types and termination scope.
 *
 * @param selectedFormTypes - Array of form types to check. Each form type must be supported by the process type.
 * @param terminationScope - Optional termination scope that adds additional allowed process types when Termination form is selected.
 *
 * @returns Array of available master data process types that support all selected form types.
 * If Termination form is selected and termination scope is provided, includes additional process types based on the termination scope.
 * Returns empty array if no form types are selected.
 *
 * @example
 * // Get process types for new installation, from automation perspective
 * getAvailableMasterDataProcessTypes([FormType.NewInstallation], true)
 *
 *  * @example
 * // Get process types for new installation, from manual creation perspective
 * getAvailableMasterDataProcessTypes([FormType.NewInstallation], false)

 * @example
 * // Get process types for termination with scope, from automation perspective
 * getAvailableMasterDataProcessTypes([FormType.Termination], true, TerminationScope.EntireInstallation)
 */

export function getAvailableMasterDataProcessTypes(
    selectedFormTypes: FormType[],
    isAutomation: boolean,
    terminationScope?: TerminationScope
): MasterDataProcessType[] {
    if (!selectedFormTypes?.length) {
        return [];
    }

    // Get base available process types
    const formTypesPerProcessType = isAutomation
        ? FORM_TYPES_PER_MASTER_DATA_PROCESS_TYPE_FOR_AUTOMATION
        : FORM_TYPES_PER_MASTER_DATA_PROCESS_TYPE;

    const baseProcessTypes = Object.entries(formTypesPerProcessType)
        .filter(([_, formTypes]) => selectedFormTypes.every((type) => formTypes.includes(type)))
        .map(([processType]) => processType as MasterDataProcessType);

    // If Termination is selected and we have a termination scope, include additional process types
    if (selectedFormTypes.includes(FormType.Termination) && terminationScope) {
        const additionalProcessTypes = TERMINATION_SCOPE_INCLUDE_MAP[terminationScope] ?? [];

        const otherFormTypes = selectedFormTypes.filter((type) => type !== FormType.Termination);

        const compatibleAdditionalProcessTypes = additionalProcessTypes.filter((processType) =>
            otherFormTypes.every((formType) => formTypesPerProcessType[processType].includes(formType))
        );

        return [...baseProcessTypes, ...compatibleAdditionalProcessTypes];
    }

    return baseProcessTypes;
}
