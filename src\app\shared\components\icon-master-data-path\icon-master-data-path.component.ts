import { Component, Input } from '@angular/core';
import { FormDataService } from 'src/app/core/services/form-data/form-data.service';

@Component({
    selector: 'app-icon-master-data-path',
    templateUrl: './icon-master-data-path.component.html',
    styleUrl: './icon-master-data-path.component.scss',
    standalone: false,
    host: { hostID: crypto.randomUUID().toString() }
})
export class IconMasterDataPathComponent {
    @Input() path?: string;
    constructor(protected formDataService: FormDataService) {}
}
