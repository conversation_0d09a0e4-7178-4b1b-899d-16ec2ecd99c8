<p-panel class="panel-without-header">
    <app-master-data-compare-toggle></app-master-data-compare-toggle>
    <div fxLayout="column" class="zebra-container" fxLayoutGap="0.5rem">
        <p-accordion [multiple]="true" [(value)]="accordionActiveIndexes">
            <p-accordion-panel [value]="0">
                <p-accordion-header>{{ 'installationWidget.title' | translate }}</p-accordion-header>
                <p-accordion-content>
                    <app-installation-section
                        [form]="installationInformationForm"
                        [meterFramesOptions]="meterFramesOptions"
                        [consumptionMeteringPointType]="formDetails.consumptionMeteringPointType">
                    </app-installation-section>
                    <app-voltage-section [form]="voltageLevelForm" nextZebraContainer></app-voltage-section>
                    <app-related-forms-section [form]="relatedFormsForm"></app-related-forms-section>
                </p-accordion-content>
            </p-accordion-panel>

            <p-accordion-panel [value]="1">
                <p-accordion-header>{{ 'extensionWidget.title' | translate }}</p-accordion-header>
                <p-accordion-content>
                    <app-extension-section [form]="extensionForm"></app-extension-section>
                </p-accordion-content>
            </p-accordion-panel>

            <p-accordion-panel [value]="2">
                <p-accordion-header>{{ 'connectionRights.title' | translate }}</p-accordion-header>
                <p-accordion-content>
                    <app-connection-rights-section
                        [form]="connectionRightsForm"
                        [formHasChanges]="changesAmount > 0"
                        (refreshRequested)="this.refreshRequested.emit()">
                    </app-connection-rights-section>
                </p-accordion-content>
            </p-accordion-panel>

            <p-accordion-panel [value]="3">
                <p-accordion-header>{{ 'technicalInformationWidget.title' | translate }}</p-accordion-header>
                <p-accordion-content>
                    <app-technical-information-section
                        [form]="technicalInformationForm"
                        [branchLineMeterFrameOptions]="branchLineMeterFrameOptions"
                        (refreshRequested)="this.refreshRequested.emit()">
                    </app-technical-information-section>
                </p-accordion-content>
            </p-accordion-panel>

            <p-accordion-panel [value]="4">
                <p-accordion-header>{{ 'meterDeliveryWidget.title' | translate }}</p-accordion-header>
                <p-accordion-content>
                    <app-meter-delivery-section [form]="meterDeliveryOptionsForm"> </app-meter-delivery-section>
                </p-accordion-content>
            </p-accordion-panel>

            <p-accordion-panel [value]="5">
                <p-accordion-header>{{ 'instructionDataWidget.title' | translate }}</p-accordion-header>
                <p-accordion-content>
                    <app-instruction-data-section [form]="instructionDataForm"></app-instruction-data-section>
                </p-accordion-content>
            </p-accordion-panel>

            <p-accordion-panel [value]="6">
                <p-accordion-header>{{ 'usageSection.title' | translate }}</p-accordion-header>
                <p-accordion-content>
                    <app-usage-section [form]="applicationsForm"> </app-usage-section>
                </p-accordion-content>
            </p-accordion-panel>

            <p-accordion-panel [value]="7">
                <p-accordion-header>{{ 'installerWidget.title' | translate }}</p-accordion-header>
                <p-accordion-content>
                    <app-installer-widget [installer]="formDetails.installer"></app-installer-widget>
                </p-accordion-content>
            </p-accordion-panel>

            <p-accordion-panel [value]="8">
                <p-accordion-header>{{ 'payerWidget.title' | translate }}</p-accordion-header>
                <p-accordion-content>
                    <app-payer-section [form]="payerForm" [isPayerRequired]="true"> </app-payer-section>
                </p-accordion-content>
            </p-accordion-panel>

            <p-accordion-panel [value]="9">
                <p-accordion-header
                    >{{ 'installationWidget.contactPerson.title' | translate }} ({{
                        'installationWidget.title' | translate
                    }})</p-accordion-header
                >
                <p-accordion-content>
                    <app-installation-contact-person-section [form]="contactPersonForm" [isContactPersonRequired]="true">
                    </app-installation-contact-person-section>
                </p-accordion-content>
            </p-accordion-panel>
        </p-accordion>
    </div>

    <ng-template [ngIf]="shouldShowChangesPanel() && changesAmount > 0">
        <div class="mt-10" fxLayout="row" fxLayoutAlign="space-between center">
            <div class="app-updated-text">
                <ng-container *ngIf="changesAmount === 1; else elseTemplate">
                    {{ 'common.singleChangedFieldInformation' | translate: { amount: changesAmount } }}
                </ng-container>
                <ng-template #elseTemplate>
                    {{ 'common.pluralChangedFieldsInformation' | translate: { amount: changesAmount } }}
                </ng-template>
            </div>
            <div>
                <button
                    id="cancelButton"
                    [disabled]="isProcessing"
                    type="button"
                    pButton
                    pRipple
                    class="mr-2 p-button-secondary"
                    (click)="onCancelChangesClicked()">
                    {{ 'common.cancelChanges' | translate | titlecase }}
                </button>
                <button id="saveButton" type="button" pButton pRipple (click)="onSaveClicked()">
                    <span *ngIf="isProcessing" class="fa fa-spin fa-spinner"></span>
                    {{ 'common.saveChanges' | translate | titlecase }}
                </button>
            </div>
        </div>
    </ng-template>
</p-panel>
