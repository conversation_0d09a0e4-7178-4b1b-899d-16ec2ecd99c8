import { Component } from '@angular/core';
import { AbstractControl, FormBuilder, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { finalize } from 'rxjs';
import {
    AutomaticArchivingRule,
    AutomaticArchivingRuleCreateOrUpdate,
    FormState,
    FormType,
    InstallationFormsClient,
    MasterDataProcessType,
    ResponsibleForMeter,
    ValueListType,
    WorkOrderType
} from 'src/app/api/installation-forms-client';
import { SETTINGS_DISPLAY_NAME_MAX_LENGTH } from 'src/app/core/constants/constants';
import { BooleanOptionsService } from 'src/app/core/services/boolean-options/boolean-options.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { ValueListsService } from 'src/app/core/services/value-lists/value-lists.service';
import { isAnyOfRequiredFormTypesSelected } from 'src/app/core/utils/form-type-checkers';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import { mapWorkOrderTypeToWorkDescriptionValueList } from 'src/app/core/utils/mappers/work-orders/work-order-type-to-value-list.mapper';
import conditionallyRequiredValidator from 'src/app/core/utils/validators/conditionally-required/conditionally-required.validator';
import { v4 as uuidv4 } from 'uuid';
import { BaseRuleEditComponent } from '../../base-components/base-rule-edit.component';
import {
    automaticArchivingRelevantStates,
    automaticArchivingTabName,
    automaticArchivingTranslationPath
} from '../constants/automatic-archiving.consts';
import {
    METER_INSTALLED_FORM_TYPES,
    METER_RESPONSIBLE_FORM_TYPES,
    STARTS_AS_CONSTRUCTION_FORM_TYPES
} from '../constants/properties-per-form-type';

@Component({
    selector: 'app-automatic-archiving-edit',
    templateUrl: './automatic-archiving-edit.component.html',
    standalone: false
})
export class AutomaticArchivingEditComponent extends BaseRuleEditComponent<AutomaticArchivingRule> {
    formTypeOptions: SelectItem[] = [];
    formStateOptions: SelectItem[] = [];
    responsibleForMeterOptions: SelectItem[] = [];
    verifiedWorkOrderTypeOptions: SelectItem[] = [];
    masterDataProcessTypesOptions: SelectItem[] = [];
    relevantStates: FormState[] = automaticArchivingRelevantStates;

    constructor(
        private readonly client: InstallationFormsClient,
        private readonly fb: FormBuilder,
        protected readonly translateService: TranslateService,
        protected readonly messageServiceHelper: MessageServiceHelper,
        protected readonly valueListsService: ValueListsService,
        protected readonly booleanOptionsService: BooleanOptionsService
    ) {
        super(translateService, messageServiceHelper, automaticArchivingTabName, automaticArchivingTranslationPath);
        this.initForm();
        this.updateFormStatesOptions();
        this.addFormControlsValueChanges();
    }

    override initForm(): void {
        this.form = this.fb.group({
            id: [null, Validators.required],
            displayName: [null, [Validators.required, Validators.maxLength(SETTINGS_DISPLAY_NAME_MAX_LENGTH)]],
            formTypes: [null],
            formStates: [null],
            responsibleForMeter: [null],
            meterInstalled: [null],
            startsAsConstruction: [null],
            verifiedWorkOrderType: [null],
            verifiedWorkOrderDescription: [
                null,
                [(control: AbstractControl) => conditionallyRequiredValidator(control, this.isVerifiedWorkOrderTypeSelected)]
            ],
            masterDataProcessTypes: [null]
        });
    }

    override getEnumTranslations(): void {
        this.formTypeOptions = enumMapper.map(this.translateService.instant('enums.type'), FormType);
        this.responsibleForMeterOptions = enumMapper.map(
            this.translateService.instant('enums.responsibleForMeter'),
            ResponsibleForMeter
        );
        this.verifiedWorkOrderTypeOptions = enumMapper.map(this.translateService.instant('enums.workOrderType'), WorkOrderType);
        this.masterDataProcessTypesOptions = enumMapper.map(
            this.translateService.instant('enums.masterDataProcessType'),
            MasterDataProcessType
        );
    }

    override setFormValue(rule: AutomaticArchivingRule) {
        this.form.patchValue({
            id: rule.id,
            displayName: rule.displayName,
            formTypes: rule.formTypes,
            formStates: rule.formStates,
            responsibleForMeter: rule.responsibleForMeter,
            meterInstalled: rule.meterInstalled,
            startsAsConstruction: rule.startsAsConstruction,
            verifiedWorkOrderType: rule.verifiedWorkOrderType,
            verifiedWorkOrderDescription: rule.verifiedWorkOrderDescription,
            masterDataProcessTypes: rule.masterDataProcessTypes
        });
    }

    override saveEditedClick() {
        if (this.form.invalid) {
            return;
        }

        const formValue = this.form.value;

        const rule: AutomaticArchivingRuleCreateOrUpdate = new AutomaticArchivingRuleCreateOrUpdate({
            displayName: formValue.displayName,
            formTypes: formValue.formTypes ?? [],
            formStates: formValue.formStates?.filter((state: FormState) => this.relevantStates.includes(state)) ?? [],
            responsibleForMeter: formValue.responsibleForMeter ?? [],
            meterInstalled: formValue.meterInstalled ?? null,
            startsAsConstruction: formValue.startsAsConstruction ?? null,
            verifiedWorkOrderType: formValue.verifiedWorkOrderType ?? null,
            verifiedWorkOrderDescription: formValue.verifiedWorkOrderDescription ?? null,
            masterDataProcessTypes: formValue.masterDataProcessTypes ?? []
        });

        this.isProcessing = true;
        this.subscription.add(
            this.client
                .updateAutomaticArchivingRuleById(formValue.id, uuidv4(), rule)
                .pipe(finalize(() => (this.isProcessing = false)))
                .subscribe({
                    next: (response) => {
                        this.ruleEdited.emit(response.result);
                        this.showSuccessMessage();
                    },
                    error: (_) => {
                        this.showErrorMessage(this.widgetTranslations['automaticArchivingRuleEditError']);
                    }
                })
        );
    }

    addFormControlsValueChanges() {
        this.subscription.add(
            this.form.get('verifiedWorkOrderType')?.valueChanges.subscribe((_) => {
                const verifiedWorkOrderDescriptionControl = this.form.get('verifiedWorkOrderDescription');
                if (verifiedWorkOrderDescriptionControl) {
                    verifiedWorkOrderDescriptionControl.updateValueAndValidity();
                }
            })
        );

        this.subscription.add(
            this.form.get('verifiedWorkOrderType')?.valueChanges.subscribe((_) => {
                this.form.get('verifiedWorkOrderDescription')?.setValue(null);
            })
        );

        this.subscription.add(
            this.form.get('formTypes')?.valueChanges.subscribe(() => {
                this.updateFormStatesOptions();
                const selectedFormStates = this.form.get('formStates')?.value;
                const filteredFormStates = selectedFormStates.filter((state: FormState) => this.relevantStates.includes(state));
                this.form.get('formStates')?.setValue(filteredFormStates);
            })
        );
    }

    updateFormStatesOptions() {
        const formTypes = this.form?.get('formTypes')?.value || [];
        this.relevantStates = automaticArchivingRelevantStates;

        if (formTypes.length !== 0 && !formTypes.includes(FormType.SealBreach) && !formTypes.includes(FormType.Termination)) {
            this.relevantStates = this.relevantStates.filter((state) => state !== FormState.Registered);
        }
        if (
            formTypes.length !== 0 &&
            !formTypes.includes(FormType.EnergyProduction) &&
            !formTypes.includes(FormType.ChangeBranchLine) &&
            !formTypes.includes(FormType.MoveMeter)
        ) {
            this.relevantStates = this.relevantStates.filter((state) => state !== FormState.Instructed);
        }

        this.formStateOptions = enumMapper.map(this.translateService.instant('enums.state'), this.relevantStates);
    }

    isVerifiedWorkOrderTypeSelected = () => {
        return this.form?.get('verifiedWorkOrderType')?.value != null;
    };

    isMeterResponsibleRequired = (): boolean => {
        const formTypes = this.form?.get('formTypes')?.value || [];
        return isAnyOfRequiredFormTypesSelected(formTypes, METER_RESPONSIBLE_FORM_TYPES);
    };

    isMeterInstalledRequired = (): boolean => {
        const formTypes = this.form?.get('formTypes')?.value || [];
        return isAnyOfRequiredFormTypesSelected(formTypes, METER_INSTALLED_FORM_TYPES);
    };

    isStartsAsConstructionRequired = (): boolean => {
        const formTypes = this.form?.get('formTypes')?.value || [];
        return isAnyOfRequiredFormTypesSelected(formTypes, STARTS_AS_CONSTRUCTION_FORM_TYPES);
    };

    getWorkOrderDescriptionValueListType = (): ValueListType => {
        return mapWorkOrderTypeToWorkDescriptionValueList(
            this.form?.get('verifiedWorkOrderType')?.value ?? WorkOrderType.General
        );
    };
}
