import { Component, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AuthorizationService } from '@kmd-elements-ui/authorization';
import {
    KeyActions,
    KeyboardShortcuts,
    KeyboardShortcutsService,
    KeyboardShortcutsSubscriptionManager
} from '@kmd-elements-ui/shortcuts';
import { TabsService } from '@kmd-elements-ui/tabs';
import { TranslateService } from '@ngx-translate/core';
import { TableRowReorderEvent } from 'primeng/table';
import { BehaviorSubject, Subject, Subscription, filter, of, switchMap, tap } from 'rxjs';
import { AutomaticInvoiceRule, InstallationFormsClient, ReorderModel } from 'src/app/api/installation-forms-client';
import { Permissions } from 'src/app/core/constants/permissions';
import { InvoicingPricesService } from 'src/app/core/services/invoicing-prices/invoicing-prices.service';
import { v4 as uuidv4 } from 'uuid';
import { MessageServiceHelper } from '../../../core/services/message/message.service';
import { ValueListsService } from '../../../core/services/value-lists/value-lists.service';
import { RulesListColumn } from '../models/rules-list-column';
import { AutomaticInvoiceCreateComponent } from './automatic-invoice-create/automatic-invoice-create.component';
import { AutomaticInvoiceEditComponent } from './automatic-invoice-edit/automatic-invoice-edit.component';
import {
    automaticInvoiceMarkdownDaDK,
    automaticInvoiceMarkdownEnUs,
    automaticInvoiceTabName,
    automaticInvoiceTranslationPath
} from './automatic-invoice.consts';
import { AUTOMATIC_INVOICE_COLUMNS } from './constants/columns';
import { AutomaticInvoiceRuleListItem } from './models/automatic-invoice-rule-list-item';
import { AutomaticInvoiceMapper } from './services/automatic-invoice.mapper';

@Component({
    selector: 'app-automatic-invoicing',
    templateUrl: './automatic-invoice.component.html',
    styleUrls: ['./automatic-invoice.component.scss'],
    standalone: false
})
export class AutomaticInvoiceComponent implements KeyboardShortcutsSubscriptionManager, OnInit, OnDestroy {
    private widgetTranslations: any;

    private _isCreating: boolean = false;
    private _ruleInEdit?: AutomaticInvoiceRule;
    private _isReordered: boolean = false;
    tabRefreshed$: Subject<void> = new Subject<void>();

    get isCreating(): boolean {
        return this._isCreating;
    }

    set isCreating(value: boolean) {
        this._isCreating = value;
        this.recalculateDirty();
    }

    get ruleInEdit(): AutomaticInvoiceRule | undefined {
        return this._ruleInEdit;
    }

    set ruleInEdit(value: AutomaticInvoiceRule | undefined) {
        this._ruleInEdit = value;
        this.recalculateDirty();
    }

    get isReordered(): boolean {
        return this._isReordered;
    }

    set isReordered(value: boolean) {
        this._isReordered = value;
        this.recalculateDirty();
    }

    automaticInvoicingRules: AutomaticInvoiceRule[] = [];
    automaticInvoicingRuleListItems: AutomaticInvoiceRuleListItem[] = [];
    originalRuleOrder: string[] = [];
    subscription: Subscription = new Subscription();
    alreadyUsedDisplayNames: string[] = [];

    automaticInvoiceMarkdownEnUs = automaticInvoiceMarkdownEnUs;
    automaticInvoiceMarkdownDaDK = automaticInvoiceMarkdownDaDK;

    isProcessing: boolean = false;

    dirty$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

    automaticInvoiceTabName = automaticInvoiceTabName;

    hasUserFormConfigurationWritePermission: boolean = false;

    private _tabId?: string;
    columns: RulesListColumn[] = AUTOMATIC_INVOICE_COLUMNS;

    private keyEventsSubscription: Subscription = new Subscription();

    @ViewChild('automaticInvoiceCreate') automaticInvoiceCreate?: AutomaticInvoiceCreateComponent;
    @ViewChild('automaticInvoiceEdit') automaticInvoiceEdit?: AutomaticInvoiceEditComponent;

    keyActions: KeyActions = {
        [KeyboardShortcuts.ShiftPlusN]: () => {
            this.startCreating();
        },
        [KeyboardShortcuts.Escape]: () => {
            this.tabsService.close();
        },
        [KeyboardShortcuts.Enter]: () => {
            if (this.ruleInEdit) {
                this.automaticInvoiceEdit?.saveEditedClick();
                return;
            }

            if (this.isCreating) {
                this.automaticInvoiceCreate?.createClick();
                return;
            }

            if (this.isReordered) {
                this.saveReorder();
            }
        }
    };

    attemptedAction: (() => void) | null = null;
    private ruleToDelete?: AutomaticInvoiceRule;

    constructor(
        private readonly client: InstallationFormsClient,
        private readonly invoicingPricesService: InvoicingPricesService,
        private readonly translateService: TranslateService,
        private readonly messageServiceHelper: MessageServiceHelper,
        private readonly tabsService: TabsService,
        private readonly activatedRoute: ActivatedRoute,
        private readonly authService: AuthorizationService,
        private readonly keyboardShortcutsService: KeyboardShortcutsService,
        private readonly valueListsService: ValueListsService,
        private readonly automaticInvoiceMapper: AutomaticInvoiceMapper
    ) {
        this.addTab();
    }

    ngOnInit(): void {
        this.widgetTranslations = this.translateService.instant(automaticInvoiceTranslationPath);

        this.authService
            .hasPermissions([Permissions.formConfiguration.write])
            .subscribe((x) => (this.hasUserFormConfigurationWritePermission = x));

        this.registerRefreshButtonListener();
        this.getInvoiceRules();
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    subscribeKeyShortcuts(): void {
        this.keyEventsSubscription = this.keyboardShortcutsService.registerKeyboardShortcuts(this.keyActions).subscribe();
    }

    unsubscribeKeyShortcuts(): void {
        this.keyEventsSubscription.unsubscribe();
    }

    getInvoiceRules() {
        this.isProcessing = true;
        this.subscription.add(
            this.client.getAutomaticInvoicesRules(uuidv4()).subscribe({
                next: (response) => {
                    const sortedRules = [...response.result];
                    sortedRules.sort((a, b) => a.order - b.order);
                    this.automaticInvoicingRules = sortedRules;
                    this.automaticInvoicingRuleListItems = sortedRules.map((rule) =>
                        this.automaticInvoiceMapper.mapToAutomaticInvoiceRuleListItem(rule)
                    );
                    this.originalRuleOrder = this.automaticInvoicingRuleListItems.map((x) => x.id);
                    this.isProcessing = false;
                    this.alreadyUsedDisplayNames = this.automaticInvoicingRuleListItems.map((x) => x.displayName);
                },
                error: (_error) => {
                    this.messageServiceHelper.showError({
                        detail: this.widgetTranslations['getInvoiceRulesError'],
                        key: automaticInvoiceTabName
                    });
                    this.isProcessing = false;
                }
            })
        );
    }

    protected deleteRule(event: MouseEvent, rule: AutomaticInvoiceRule) {
        event.stopPropagation();
        this.ruleToDelete = rule;
        this.attemptedAction = () => this.performDelete();
    }

    private performDelete() {
        if (!this.ruleToDelete) {
            return;
        }

        this.isProcessing = true;
        this.subscription.add(
            this.client.deleteAutomaticInvoicesRuleById(this.ruleToDelete.id, uuidv4()).subscribe({
                complete: () => {
                    this.messageServiceHelper.showSuccess({
                        key: automaticInvoiceTabName
                    });
                    this.automaticInvoicingRules = this.automaticInvoicingRules.filter((r) => r.id !== this.ruleToDelete?.id);
                    this.automaticInvoicingRuleListItems = this.automaticInvoicingRuleListItems.filter(
                        (r) => r.id !== this.ruleToDelete?.id
                    );
                    this.originalRuleOrder = this.automaticInvoicingRuleListItems.map((x) => x.id);
                    if (this.ruleInEdit?.id === this.ruleToDelete?.id) {
                        this.ruleInEdit = undefined;
                    }

                    this.alreadyUsedDisplayNames = this.alreadyUsedDisplayNames.filter(
                        (name) => name !== this.ruleToDelete?.displayName
                    );

                    this.isProcessing = false;
                    this.ruleToDelete = undefined;
                    this.invoicingPricesService.invalidatePricesCache();
                },
                error: (_error) => {
                    this.messageServiceHelper.showError({
                        detail: this.widgetTranslations['invoiceRuleDeleteError'],
                        key: automaticInvoiceTabName
                    });
                    this.isProcessing = false;
                    this.ruleToDelete = undefined;
                }
            })
        );
    }

    onConfirmationDialogYesButtonClick() {
        if (this.attemptedAction) {
            this.attemptedAction();
        }
        this.attemptedAction = null;
    }

    onConfirmationDialogNoButtonClick() {
        this.attemptedAction = null;
        this.ruleToDelete = undefined;
    }

    protected editRule(event: MouseEvent, rule: AutomaticInvoiceRule) {
        event.stopPropagation();
        this.ruleInEdit = this.automaticInvoicingRules.find((r) => r.id === rule.id);
        this.isCreating = false;
    }

    protected startCreating() {
        this.isCreating = true;
        this.ruleInEdit = undefined;
    }

    protected cancelCreating() {
        this.isCreating = false;
    }

    protected cancelEditing() {
        this.ruleInEdit = undefined;
    }

    protected onRuleCreated(rule: AutomaticInvoiceRule) {
        this.isCreating = false;
        this.automaticInvoicingRules.push(rule);
        this.automaticInvoicingRuleListItems.push(this.automaticInvoiceMapper.mapToAutomaticInvoiceRuleListItem(rule));
        this.originalRuleOrder.push(rule.id);
        this.alreadyUsedDisplayNames.push(rule.displayName);
        this.invoicingPricesService.invalidatePricesCache();
    }

    protected onRuleEdited(updatedRule: AutomaticInvoiceRule) {
        let editedIndex = this.automaticInvoicingRules.findIndex((r) => r.id === updatedRule.id);
        if (editedIndex === -1) {
            return;
        }

        this.alreadyUsedDisplayNames.splice(
            this.alreadyUsedDisplayNames.indexOf(this.ruleInEdit!.displayName),
            1,
            updatedRule.displayName
        );
        this.automaticInvoicingRules[editedIndex] = updatedRule;
        this.automaticInvoicingRuleListItems[editedIndex] =
            this.automaticInvoiceMapper.mapToAutomaticInvoiceRuleListItem(updatedRule);
        this.ruleInEdit = undefined;
        this.invoicingPricesService.invalidatePricesCache();
    }

    private addTab() {
        this._tabId = this.tabsService.addOrUpdateTab(this.activatedRoute, {
            icon$: of('fas fa-th-list'),
            areaLabel$: of(this.translateService.instant(`${automaticInvoiceTranslationPath}.tabAreaLabel`)),
            label$: of(this.translateService.instant(`${automaticInvoiceTranslationPath}.tabLabel`)),
            tooltip$: of(this.translateService.instant(`${automaticInvoiceTranslationPath}.tabLabel`)),
            dirty$: this.dirty$,
            canReload$: of(true)
        });
    }

    registerRefreshButtonListener(): void {
        this.subscription.add(
            this.tabsService.reloadButtonClicked$
                .pipe(
                    filter((tabId) => tabId === this._tabId),
                    switchMap((tabId) => this.tabsService.reload(tabId)),
                    filter((canBeReloaded) => canBeReloaded),
                    tap(() => {
                        this.getInvoiceRules();
                        this.valueListsService.fetchValueLists();
                        this.tabRefreshed$.next();
                    })
                )
                .subscribe(() => {
                    this.tabsService.tabReloaded(this._tabId as string);
                    this.undoChanges();
                })
        );
    }

    undoChanges() {
        this.cancelCreating();
        this.cancelEditing();
        this.cancelReorder();
    }

    onRowReorder(_: TableRowReorderEvent) {
        this.setIsReordered();
    }

    setIsReordered() {
        this.isReordered = !this.automaticInvoicingRuleListItems.every(
            (rule, index) => rule.id === this.originalRuleOrder[index]
        );
    }

    cancelReorder() {
        this.automaticInvoicingRules = this.originalRuleOrder.map(
            (id) => this.automaticInvoicingRules.find((rule) => rule.id === id)!
        );
        this.automaticInvoicingRuleListItems = this.originalRuleOrder.map(
            (id) => this.automaticInvoicingRuleListItems.find((rule) => rule.id === id)!
        );
        this.setIsReordered();
    }

    saveReorder() {
        this.subscription.add(
            this.client
                .reorderAutomaticInvoicesRules(
                    uuidv4(),
                    new ReorderModel({
                        reorderedIds: this.automaticInvoicingRuleListItems.map((x) => x.id)
                    })
                )
                .subscribe({
                    complete: () => {
                        this.messageServiceHelper.showSuccess({
                            key: automaticInvoiceTabName
                        });
                        this.originalRuleOrder = this.automaticInvoicingRuleListItems.map((x) => x.id);
                        this.isProcessing = false;
                        this.isReordered = false;
                    },
                    error: (_error) => {
                        this.messageServiceHelper.showError({
                            detail: this.widgetTranslations['invoiceRuleReorderError'],
                            key: automaticInvoiceTabName
                        });
                        this.isProcessing = false;
                    }
                })
        );
    }

    recalculateDirty() {
        this.dirty$.next(this.isCreating || this.isReordered || !!this.ruleInEdit);
    }
}
