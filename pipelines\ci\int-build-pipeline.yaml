name: $(date:yyyyMMdd)$(rev:.r)_$(SourceBranchName)

trigger:
  batch: true
  branches:
    include:
      - "master"
    exclude:
      - "temp-release/*"
      - "release/*"

pool:
  vmImage: "ubuntu-22.04"

variables:
  - name: registryName
    value: "kmdelementsinteuwacr001"
  - template: ci-variables-template.yaml
  - template: ../global-variables-template.yaml

resources:
  repositories:
    - repository: commonAks
      type: git
      name: COMBAS/KMD.Elements.Pipelines

extends:
  template: ci/templates/build-microfrontend-image-template.yaml@commonAks
  parameters:
    registryName: ${{ variables.registryName }}
    minifiedDist: false
    testCommand: $(testCommand)
    testResultPath: $(testResultPath)
    coverageResultPath: $(coverageResultPath)
