<div #overlayContainer></div>
<ng-container [formGroup]="form">
    <div formArrayName="plants">
        <div *ngFor="let item of getPlants().controls; let i = index" [formGroupName]="i">
            <h2 class="mb-10 mt-10">{{ 'energyProductionSection.plants.plant' | translate }} {{ i + 1 }}:</h2>

            <button
                *ngIf="!form.disabled && allowMultiplePlants && i !== 0"
                pButton
                icon="fa-solid fa-trash-can"
                (click)="removePlant(i)"
                class="p-button-outlined mb-10 border-none"></button>

            <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
                <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                    <label [labelRequired]="item.get('phaseCount')!" for="phaseCount">
                        {{ 'energyProductionSection.plants.phaseCount' | translate }}
                    </label>
                </div>
                <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                    <p-select
                        id="phaseCount"
                        [options]="phaseCountOptions"
                        optionValue="value"
                        optionLabel="label"
                        [appendTo]="overlayContainer"
                        [placeholder]="'common.selectValue' | translate"
                        formControlName="phaseCount"
                        (ngModelChange)="onValueChange()">
                    </p-select>
                    <small [controlValidationErrors]="item.get('phaseCount')!" class="p-error"></small>
                </div>
            </div>

            <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
                <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                    <label [labelRequired]="item.get('totalPowerInKw')!" for="totalPowerInKw">
                        {{ 'energyProductionSection.plants.totalPowerInKw' | translate }}
                    </label>
                </div>
                <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                    <p-inputNumber
                        id="totalPowerInKw"
                        formControlName="totalPowerInKw"
                        [min]="1"
                        [max]="999999999"
                        [useGrouping]="false"
                        [minFractionDigits]="0"
                        [maxFractionDigits]="1"
                        (ngModelChange)="onTotalPowerChange()"></p-inputNumber>
                    <small [controlValidationErrors]="item.get('totalPowerInKw')!" class="p-error"></small>
                </div>
            </div>

            <div
                class="zebra-item"
                fxLayout="row"
                fxLayout.lt-sm="column"
                fxLayoutGap="15px"
                *ngIf="commissioningDateSodVisible()">
                <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                    <label [labelRequired]="item.get('commissioningDateSod')!" for="commissioningDateSod">
                        {{ 'energyProductionSection.plants.commissioningDateSod' | translate }}
                    </label>
                </div>
                <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                    <cmbs-calendar
                        inputId="commissioningDateSod"
                        formControlName="commissioningDateSod"
                        [showClear]="true"
                        [showTime]="false"
                        [appendTo]="overlayContainer"
                        (onSelect)="onValueChange()">
                    </cmbs-calendar>
                    <small [controlValidationErrors]="item.get('commissioningDateSod')!" class="p-error"></small>
                </div>
            </div>

            <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="brandNameVisible()">
                <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                    <label [labelRequired]="item.get('brandName')!" for="brandName">
                        {{ 'energyProductionSection.plants.brandName' | translate }}
                    </label>
                </div>
                <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                    <input
                        id="brandName"
                        type="text"
                        pInputText
                        maxlength="100"
                        formControlName="brandName"
                        (ngModelChange)="onValueChange()" />
                    <small [controlValidationErrors]="item.get('brandName')!" class="p-error"></small>
                </div>
            </div>

            <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="modelNameVisible()">
                <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                    <label [labelRequired]="item.get('modelName')!" for="modelName">
                        {{ 'energyProductionSection.plants.modelName' | translate }}
                    </label>
                </div>
                <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                    <input
                        id="modelName"
                        type="text"
                        pInputText
                        maxlength="100"
                        formControlName="modelName"
                        (ngModelChange)="onValueChange()" />
                    <small [controlValidationErrors]="item.get('modelName')!" class="p-error"></small>
                </div>
            </div>

            <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="typeNameVisible()">
                <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                    <label [labelRequired]="item.get('typeName')!" for="typeName">
                        {{ 'energyProductionSection.plants.typeName' | translate }}
                    </label>
                </div>
                <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                    <input
                        id="typeName"
                        type="text"
                        pInputText
                        maxlength="100"
                        formControlName="typeName"
                        (ngModelChange)="onValueChange()" />
                    <small [controlValidationErrors]="item.get('typeName')!" class="p-error"></small>
                </div>
            </div>

            <div
                class="zebra-item"
                fxLayout="row"
                fxLayout.lt-sm="column"
                fxLayoutGap="15px"
                *ngIf="solarSystemAreaInM2Visible()">
                <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                    <label [labelRequired]="item.get('solarSystemAreaInM2')!" for="solarSystemAreaInM2">
                        {{ 'energyProductionSection.plants.solarSystemAreaInM2' | translate }}
                    </label>
                </div>
                <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                    <p-inputNumber
                        id="solarSystemAreaInM2"
                        formControlName="solarSystemAreaInM2"
                        [min]="1"
                        [max]="999999999"
                        [useGrouping]="false"
                        [minFractionDigits]="0"
                        [maxFractionDigits]="2"
                        (ngModelChange)="onValueChange()"></p-inputNumber>
                    <small [controlValidationErrors]="item.get('solarSystemAreaInM2')!" class="p-error"></small>
                </div>
            </div>
        </div>
    </div>
</ng-container>
<ng-container [ngSwitch]="type">
    <app-wind-plant-section *ngSwitchCase="'Wind'" [form]="windPlantForm" (changesMade)="onValueChange()" nextZebraContainer />
    <app-power-plant-section
        *ngSwitchCase="'PowerPlant'"
        [form]="powerPlantForm"
        (changesMade)="onValueChange()"
        nextZebraContainer />
</ng-container>
<button
    pButton
    label="{{ 'energyProductionSection.plants.addPlant' | translate }}"
    (click)="createPlant()"
    class="p-button-outlined mt-20 mb-20"
    *ngIf="!form.disabled && allowMultiplePlants"></button>
<div>
    <h2 class="mb-10 mt-10">{{ 'energyProductionSection.plants.totalCount' | translate }}</h2>
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label for="calculatedTotalPowerInKw"
                >{{ 'energyProductionSection.plants.calculatedTotalPowerInKw' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input id="calculatedTotalPowerInKw" type="text" pInputText [disabled]="true" [value]="calculatedTotalPowerSum" />
        </div>
    </div>
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label for="calculatedTotalProductionCapacity"
                >{{ 'energyProductionSection.calculatedTotalProductionCapacity' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input
                id="currentCalculatedTotalProductionCapacity"
                type="text"
                pInputText
                [disabled]="true"
                [value]="currentCalculatedTotalProductionCapacity" />
        </div>
    </div>
</div>
