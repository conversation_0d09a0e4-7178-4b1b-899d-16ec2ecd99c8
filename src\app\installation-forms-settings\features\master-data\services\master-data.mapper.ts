import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import {
    AutomaticMasterDataProcessRule,
    ConnectionPointTemplateDetails,
    ScopeOfDeliveryRange,
    TotalCapacityRange,
    TransferConnectionRightUom
} from 'src/app/api/installation-forms-client';
import { MasterDataListItem } from '../models/master-data-list-item';

@Injectable({
    providedIn: 'root'
})
export class MasterDataMapper {
    constructor(private translateService: TranslateService) {}

    mapToMasterDataListItem = (
        rule: AutomaticMasterDataProcessRule,
        masterDataProcessTemplateOptions: ConnectionPointTemplateDetails[]
    ): MasterDataListItem => ({
        id: rule.id,
        order: rule.order,
        displayName: rule.displayName,
        formTypes: rule.formTypes,
        formStates: rule.formStates,
        voltageLevels: rule.voltageLevels,
        totalCapacity: this.mapTotalCapacity(rule.totalCapacityRange),
        scopeOfDelivery: this.mapScopeOfDelivery(rule.scopeOfDeliveryRange),
        startsAsConstruction: rule.startsAsConstruction,
        terminationScope: rule.terminationScope,
        masterDataProcessType: rule.masterDataProcessType,
        masterDataProcessTemplate:
            masterDataProcessTemplateOptions.find((template) => template.id === rule.masterDataProcessTemplateId)?.name ??
            rule.masterDataProcessTemplateId,
        masterDataProcessAutomationLevel: rule.masterDataProcessAutomationLevel,
        masterDataProcessWorkOrderAutomationLevel: rule.masterDataProcessWorkOrderAutomationLevel,
        workOrderType: rule.workOrderType,
        workOrderDescriptionId: rule.workOrderDescriptionId,
        workOrderPurposeId: rule.workOrderPurposeId
    });

    mapTotalCapacity = (totalCapacityRange?: TotalCapacityRange) => {
        if (!totalCapacityRange) {
            return undefined;
        }
        return `${totalCapacityRange.min ?? '0'} - ${totalCapacityRange.max ?? '∞'} kW`;
    };

    mapScopeOfDelivery = (scopeOfDeliveryRange?: ScopeOfDeliveryRange) => {
        if (!scopeOfDeliveryRange) {
            return undefined;
        }
        return `${scopeOfDeliveryRange.min ?? '0'} - ${scopeOfDeliveryRange.max ?? '∞'} ${this.mapScopeOfDeliveryUom(scopeOfDeliveryRange.uom)}`;
    };

    mapScopeOfDeliveryUom = (uom: TransferConnectionRightUom) => {
        return this.translateService.instant(`enums.transferConnectionRightUom.${uom}`);
    };
}
