import { OnDestroy, OnInit, Pipe, PipeTransform } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';

@Pipe({
    name: 'translateEnumArray',
    standalone: false
})
export class TranslateEnumArrayPipe implements PipeTransform, OnDestroy {
    subscription: Subscription = new Subscription();
    enumsTranslations: any;

    constructor(private readonly translateService: TranslateService) {
        this.enumsTranslations = this.translateService.instant('enums');
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    transform(enumArray: any[], translationPath: string): string {
        if (!enumArray || !translationPath) {
            return '';
        }

        let translationPathParts: string[] = translationPath.split('.');
        let enumName = translationPathParts[1];
        return enumArray.map((item) => this.enumsTranslations[enumName][item]).join(', ');
    }
}
