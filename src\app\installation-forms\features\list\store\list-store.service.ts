import { Injectable } from '@angular/core';
import { ComponentStore } from '@ngrx/component-store';
import { tapResponse } from '@ngrx/operators';
import { TableLazyLoadEvent } from 'primeng/table';
import { Observable } from 'rxjs';
import { InstallationFormsSearchResult } from '../models/search-result';

export interface InstallationFormsSearchState {
    searchResult: InstallationFormsSearchResult;
    loading: boolean;
    lazyLoadEvent: TableLazyLoadEvent;
}

const defaultState: InstallationFormsSearchState = {
    searchResult: {
        items: [],
        totalRecords: 0
    } as InstallationFormsSearchResult,
    loading: false,
    lazyLoadEvent: {}
};

@Injectable()
export class InstallationFormsSearchStoreService extends ComponentStore<InstallationFormsSearchState> {
    constructor() {
        super(defaultState);
    }

    readonly searchResult$ = this.select(({ searchResult }) => searchResult);
    readonly loading$ = this.select(({ loading }) => loading);
    readonly lazyLoadEvent$ = this.select(({ lazyLoadEvent }) => lazyLoadEvent);

    onSearchResultChange = this.effect((searchResult$: Observable<InstallationFormsSearchResult>) =>
        searchResult$.pipe(
            tapResponse(
                (result) => {
                    this.patchState({ searchResult: result });
                },
                (_error) => {
                    this.patchState({ searchResult: defaultState.searchResult });
                }
            )
        )
    );

    setLoading = this.updater((state, loading: boolean) => ({
        ...state,
        loading: loading
    }));

    setLazyLoadEvent = this.updater((state, lazyLoadEvent: TableLazyLoadEvent) => ({
        ...state,
        lazyLoadEvent: lazyLoadEvent
    }));
}
