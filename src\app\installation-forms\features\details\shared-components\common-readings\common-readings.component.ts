import { Component, Input } from '@angular/core';
import { CommonReading } from 'src/app/api/installation-forms-client';
import { SMALL_TABLE_ROWS_PER_PAGE } from 'src/app/core/constants/constants';

@Component({
    selector: 'app-common-readings',
    templateUrl: './common-readings.component.html',
    styleUrls: ['./common-readings.component.scss'],
    standalone: false
})
export class CommonReadingsComponent {
    static _columns: { field: string; header: string; valueTranslationKeyPath?: string }[] = [
        {
            field: 'type',
            header: 'commonReadings.columnHeaders.type',
            valueTranslationKeyPath: 'enums.commonReadingType'
        },
        {
            field: 'number',
            header: 'commonReadings.columnHeaders.number'
        }
    ];

    private _items!: CommonReading[];

    get items() {
        return this._items;
    }

    @Input()
    set items(value: CommonReading[]) {
        this._items = value;
    }

    first = 0;
    maxRowsPerPage = SMALL_TABLE_ROWS_PER_PAGE;

    get columns() {
        return CommonReadingsComponent._columns;
    }
}
