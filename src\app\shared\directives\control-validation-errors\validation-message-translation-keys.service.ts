import { Injectable } from '@angular/core';

@Injectable()
export class ValidationMessageTranslationKeysService {
    private _keys = {
        required: 'common.fieldIsRequired',
        min: 'common.fieldIsOutsideCorrectMinRange',
        max: 'common.fieldIsOutsideCorrectMaxRange',
        emailFormatError: 'common.fieldIsInWrongFormat',
        carIdIsEmpty: 'common.fieldIsRequired',
        pattern: 'common.fieldIsInWrongFormat',
        dateMustNotBePast: 'common.dateMustNotBePast',
        displayNameAlreadyUsed: 'common.displayNameAlreadyUsed',
        multipleWhitespaces: 'common.multipleWhitespaces'
    };

    public get keys() {
        return this._keys;
    }
}
