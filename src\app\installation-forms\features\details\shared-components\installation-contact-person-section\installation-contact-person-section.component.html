<div>
    <p-toggleButton
        id="showContactPersonDataToggleButton"
        [onLabel]="'installationWidget.contactPerson.addContactPersonData' | translate"
        [offLabel]="'installationWidget.contactPerson.deleteContactPersonData' | translate"
        onIcon="fa-solid fa-plus"
        [ngModel]="!isContactPersonVisible"
        offIcon="fa-regular fa-trash-can"
        iconPos="left"
        class="mb-20 p-button-outlined"
        (onChange)="onShowContactPersonDataToggleChange($event)"
        *ngIf="!isContactPersonRequired && (!isReadOnly || canUpdatePaymentDetails)"></p-toggleButton>
    <div [formGroup]="form" *ngIf="isContactPersonVisible">
        <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label for="contactPersonCompanyName">{{ 'installationWidget.contactPerson.companyName' | translate }}</label>
            </div>
            <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                <input
                    id="contactPersonCompanyName"
                    type="text"
                    pInputText
                    maxlength="100"
                    formControlName="contactPersonCompanyName" />
            </div>
        </div>
        <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label for="contactPersonName">{{ 'installationWidget.contactPerson.name' | translate }}</label>
            </div>
            <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                <input id="contactPersonName" type="text" pInputText maxlength="100" formControlName="contactPersonName" />
            </div>
        </div>
        <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label for="contactPersonPhoneNumber">{{ 'installationWidget.contactPerson.phoneNumber' | translate }}</label>
            </div>
            <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                <input
                    id="contactPersonPhoneNumber"
                    type="text"
                    pInputText
                    maxlength="100"
                    formControlName="contactPersonPhoneNumber" />
            </div>
        </div>
        <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label for="contactPersonEmail">{{ 'installationWidget.contactPerson.email' | translate }}</label>
            </div>
            <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                <input id="contactPersonEmail" type="text" pInputText maxlength="100" formControlName="contactPersonEmail" />
                <small [controlValidationErrors]="form.controls['contactPersonEmail']" class="p-error"></small>
            </div>
        </div>
    </div>
</div>
