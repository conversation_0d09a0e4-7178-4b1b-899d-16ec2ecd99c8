import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { finalize, Subscription } from 'rxjs';
import {
    InstallationFormsClient,
    MarkFormTaskAsCompleted,
    PendingUpdateAreaType,
    ReExecuteFormTask,
    TaskListEntry,
    TaskState,
    TaskType
} from 'src/app/api/installation-forms-client';
import { LARGE_TABLE_ROWS_PER_PAGE } from 'src/app/core/constants/constants';
import { WidgetNames } from 'src/app/core/constants/widget-names';
import { AutomaticFormRefreshService } from 'src/app/core/services/automatic-form-refresh/automatic-form-refresh.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { WidgetComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget.component';
import { v4 as uuidv4 } from 'uuid';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';
import TaskGroupListItem from './task-group-list-item.model';

@Component({
    selector: 'app-tasks-widget',
    templateUrl: './tasks-widget.component.html',
    styleUrl: './tasks-widget.component.scss',
    standalone: false
})
export class TasksWidgetComponent extends WidgetComponent implements OnInit, OnDestroy {
    widgetName: string = WidgetNames.tasksWidget;
    private widgetTranslations: any;

    items: TaskGroupListItem[] = [];

    columns = ['name', 'state', 'isManual', 'startedDate', 'completionDate'];
    loading = true;
    rowsPerPage = LARGE_TABLE_ROWS_PER_PAGE;

    subscription: Subscription = new Subscription();

    constructor(
        readonly formDataService: FormDataService,
        private readonly client: InstallationFormsClient,
        private readonly translateService: TranslateService,
        private readonly messageServiceHelper: MessageServiceHelper,
        private readonly automaticFormRefreshService: AutomaticFormRefreshService
    ) {
        super(formDataService);
    }

    ngOnInit() {
        this.widgetTranslations = this.translateService.instant('tasksWidget');

        this.automaticFormRefreshService.subscribeForRefresh(PendingUpdateAreaType.Tasks, this.getTasks);
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
        this.automaticFormRefreshService.unsubscribeForRefresh(PendingUpdateAreaType.Tasks);
    }

    mergeTaskStateIntoGroup(task: TaskListEntry, currentGroupState: TaskState): TaskState {
        switch (task.state) {
            case TaskState.NotStarted:
                return currentGroupState;
            case TaskState.InProgress:
                return TaskState.InProgress;
            case TaskState.Failed:
                return TaskState.Failed;
            case TaskState.Completed:
                return task.isLastTaskInGroup ? TaskState.Completed : TaskState.InProgress;
        }
    }

    groupTasks(tasks: TaskListEntry[]) {
        const groups: TaskGroupListItem[] = [];
        for (let task of tasks) {
            const existingGroupMatchResult = groups.filter((t) => t.groupIdentifier === task.taskGroupIdentifier);
            let group: TaskGroupListItem;
            if (existingGroupMatchResult.length > 0) {
                group = existingGroupMatchResult[0];
                if (task.isManual) {
                    group.name = task.name;
                    group.isManual = true;
                }
                group.state = this.mergeTaskStateIntoGroup(task, group.state);
                group.currentTask =
                    task.state === TaskState.InProgress || task.state === TaskState.Failed ? task : group.currentTask;
                group.completionDate = task.isLastTaskInGroup && task.state === TaskState.Completed ? task.completionDate! : null;
            } else {
                group = {
                    name: task.name,
                    type: task.taskGroup,
                    state: this.mergeTaskStateIntoGroup(task, TaskState.NotStarted),
                    currentTask: task.state === TaskState.InProgress || task.state === TaskState.Failed ? task : null,
                    startedDate: task.startedDate || null,
                    completionDate: task.isLastTaskInGroup && task.state === TaskState.Completed ? task.completionDate! : null,
                    groupIdentifier: task.taskGroupIdentifier,
                    isManual: task.isManual
                };
                groups.push(group);
            }
        }

        return groups;
    }

    getTasks = () => {
        this.subscription.add(
            this.client.getTasks(this.formDataService.formId!, uuidv4()).subscribe({
                next: (response) => {
                    this.items = this.groupTasks(response.result);
                    this.loading = false;
                },
                error: (_error) => {
                    this.messageServiceHelper.showError({
                        detail: this.widgetTranslations['getTasksError'],
                        key: this.formDataService.formId!
                    });
                }
            })
        );
    };

    canReExecuteTask(task: TaskListEntry | null): boolean {
        return this.canEditTasks() && !!task && !task.isManual && task.state === TaskState.Failed;
    }

    reExecuteTask(event: MouseEvent, task: TaskListEntry): void {
        event?.stopPropagation();
        this.loading = true;
        this.processingStarted();

        this.subscription.add(
            this.client
                .reExecuteTask(
                    task.id,
                    uuidv4(),
                    this.formDataService.rowVersion,
                    new ReExecuteFormTask({ formId: this.formDataService.formId! }),
                    undefined
                )
                .subscribe({
                    error: (error) => {
                        this.showReExecuteTaskErrorMessage(error, task);
                        this.processingFinished();
                        this.loading = false;
                    },
                    complete: () => {
                        this.processingFinished();
                        this.messageServiceHelper.showSuccess({ key: this.formDataService.formId! });
                        this.getTasks();
                    }
                })
        );
    }

    showReExecuteTaskErrorMessage(error: any, task: TaskListEntry) {
        switch (task.type) {
            case TaskType.AutomaticEmails:
                this.messageServiceHelper.addTranslated(error, this.formDataService.formId!, WidgetNames.emailsWidget, {
                    path: 'enums.fieldNames',
                    key: 'FieldName'
                });
                break;
            case TaskType.CreateInvoice:
                this.messageServiceHelper.addTranslated(error, this.formDataService.formId!, WidgetNames.invoicesWidget, {
                    path: 'enums.connectionRightsCategories',
                    key: 'Category'
                });
                break;
            case TaskType.CreateConnectionRights:
            default:
                this.messageServiceHelper.showError({ key: this.formDataService.formId! });
                break;
        }
    }

    canMarkAsCompleted(task: TaskListEntry | null): boolean {
        return (
            this.canEditTasks() &&
            !!task &&
            task.isManual &&
            (task.state === TaskState.InProgress || task.state === TaskState.NotStarted)
        );
    }

    markAsCompleted(event: MouseEvent, task: TaskListEntry): void {
        event?.stopPropagation();
        this.loading = true;
        this.processingStarted();

        this.subscription.add(
            this.client
                .markTaskAsCompleted(
                    task.id,
                    uuidv4(),
                    new MarkFormTaskAsCompleted({ formId: this.formDataService.formId! }),
                    undefined
                )
                .pipe(finalize(() => this.processingFinished()))
                .subscribe({
                    error: (_) => {
                        this.messageServiceHelper.showError({ key: this.formDataService.formId! });

                        this.loading = false;
                    },
                    complete: () => {
                        this.messageServiceHelper.showSuccess({ key: this.formDataService.formId! });
                        this.getTasks();
                    }
                })
        );
    }

    shouldShowTaskStatusIcon(taskGroup: TaskGroupListItem) {
        return taskGroup.state !== TaskState.NotStarted;
    }

    protected canEditTasks = () => this.formDataService.canCurrentUserExecuteActions;
}
