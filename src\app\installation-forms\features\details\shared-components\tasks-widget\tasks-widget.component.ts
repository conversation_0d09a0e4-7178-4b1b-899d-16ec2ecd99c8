import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { finalize, Subscription } from 'rxjs';
import {
    InstallationFormsClient,
    MarkFormTaskAsCompleted,
    PendingUpdateAreaType,
    ReExecuteFormTask,
    TaskGroupEntry,
    TaskState,
    TaskType
} from 'src/app/api/installation-forms-client';
import { LARGE_TABLE_ROWS_PER_PAGE } from 'src/app/core/constants/constants';
import { WidgetNames } from 'src/app/core/constants/widget-names';
import { AutomaticFormRefreshService } from 'src/app/core/services/automatic-form-refresh/automatic-form-refresh.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { WidgetComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget.component';
import { v4 as uuidv4 } from 'uuid';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';

@Component({
    selector: 'app-tasks-widget',
    templateUrl: './tasks-widget.component.html',
    standalone: false
})
export class TasksWidgetComponent extends WidgetComponent implements OnInit, OnDestroy {
    widgetName: string = WidgetNames.tasksWidget;
    private widgetTranslations: any;

    items: TaskGroupEntry[] = [];

    columns = ['name', 'state', 'isManual', 'startedDate', 'completionDate'];
    loading = true;
    rowsPerPage = LARGE_TABLE_ROWS_PER_PAGE;

    subscription: Subscription = new Subscription();

    constructor(
        protected readonly formDataService: FormDataService,
        private readonly client: InstallationFormsClient,
        private readonly translateService: TranslateService,
        private readonly messageServiceHelper: MessageServiceHelper,
        private readonly automaticFormRefreshService: AutomaticFormRefreshService
    ) {
        super(formDataService);
    }

    ngOnInit() {
        this.widgetTranslations = this.translateService.instant('tasksWidget');

        this.automaticFormRefreshService.subscribeForRefresh(PendingUpdateAreaType.Tasks, this.getTasks);
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
        this.automaticFormRefreshService.unsubscribeForRefresh(PendingUpdateAreaType.Tasks);
    }

    getTasks = () => {
        this.loading = true;
        this.subscription.add(
            this.client
                .getTasks(this.formDataService.formId!, uuidv4())
                .pipe(finalize(() => (this.loading = false)))
                .subscribe({
                    next: (response) => {
                        this.items = response.result;
                    },
                    error: (_error) => {
                        this.messageServiceHelper.showError({
                            detail: this.widgetTranslations['getTasksError'],
                            key: this.formDataService.formId!
                        });
                    }
                })
        );
    };

    canReExecuteTask(taskGroup: TaskGroupEntry | null): boolean {
        return this.canEditTasks() && !!taskGroup && !taskGroup.isLatestStartedTaskManual && taskGroup.state === TaskState.Failed;
    }

    reExecuteTask(event: MouseEvent, taskGroup: TaskGroupEntry): void {
        event?.stopPropagation();
        this.loading = true;
        this.processingStarted();

        this.subscription.add(
            this.client
                .reExecuteTask(
                    taskGroup.currentTaskInProgressId!,
                    uuidv4(),
                    this.formDataService.rowVersion,
                    new ReExecuteFormTask({ formId: this.formDataService.formId! })
                )
                .pipe(
                    finalize(() => {
                        this.processingFinished();
                        this.loading = false;
                    })
                )
                .subscribe({
                    error: (error) => {
                        this.showReExecuteTaskErrorMessage(error, taskGroup);
                    },
                    complete: () => {
                        this.messageServiceHelper.showSuccess({ key: this.formDataService.formId! });
                        this.getTasks();
                    }
                })
        );
    }

    showReExecuteTaskErrorMessage(error: any, taskGroup: TaskGroupEntry) {
        const currentTask = taskGroup.tasks.find((x) => x.id === taskGroup.currentTaskInProgressId)!;
        switch (currentTask.type) {
            case TaskType.AutomaticEmails:
                this.messageServiceHelper.addTranslated(error, this.formDataService.formId!, WidgetNames.emailsWidget, {
                    path: 'enums.fieldNames',
                    key: 'FieldName'
                });
                break;
            case TaskType.CreateInvoice:
                this.messageServiceHelper.addTranslated(error, this.formDataService.formId!, WidgetNames.invoicesWidget, {
                    path: 'enums.connectionRightsCategories',
                    key: 'Category'
                });
                break;
            case TaskType.CreateConnectionRights:
            default:
                this.messageServiceHelper.showError({ key: this.formDataService.formId! });
                break;
        }
    }

    canMarkAsCompleted(taskGroup: TaskGroupEntry | null): boolean {
        return (
            this.canEditTasks() &&
            !!taskGroup?.isLatestStartedTaskManual &&
            (taskGroup.state === TaskState.InProgress || taskGroup.state === TaskState.NotStarted)
        );
    }

    markAsCompleted(event: MouseEvent, taskGroup: TaskGroupEntry): void {
        event?.stopPropagation();
        this.loading = true;
        this.processingStarted();

        this.subscription.add(
            this.client
                .markTaskAsCompleted(
                    taskGroup.currentTaskInProgressId!,
                    uuidv4(),
                    new MarkFormTaskAsCompleted({ formId: this.formDataService.formId! })
                )
                .pipe(finalize(() => this.processingFinished()))
                .subscribe({
                    error: (_) => {
                        this.messageServiceHelper.showError({ key: this.formDataService.formId! });

                        this.loading = false;
                    },
                    complete: () => {
                        this.messageServiceHelper.showSuccess({ key: this.formDataService.formId! });
                        this.getTasks();
                    }
                })
        );
    }

    shouldShowTaskStatusIcon(taskGroup: TaskGroupEntry) {
        return taskGroup.state !== TaskState.NotStarted;
    }

    protected canEditTasks = () => this.formDataService.canCurrentUserExecuteActions;
}
