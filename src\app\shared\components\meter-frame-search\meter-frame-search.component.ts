import { Component, EventEmitter, forwardRef, Input, OnDestroy, OnInit, Optional, Output } from '@angular/core';
import { <PERSON><PERSON><PERSON>r, ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { of, Subject, Subscription } from 'rxjs';
import { catchError, map, switchMap, takeUntil } from 'rxjs/operators';
import {
    InstallationFormsClient,
    MeterFramesSearchFilterModel,
    MeterFramesSearchQueryModel
} from 'src/app/api/installation-forms-client';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { convertSupplyTypeToEnum } from 'src/app/core/utils/mappers/supply-type/supply-type.mapper';
import { v4 as uuidv4 } from 'uuid';
import { FormDataService } from '../../../core/services/form-data/form-data.service';
import { MeterFrameSearchItemModel } from './meter-frame-search-item.model';

@Component({
    selector: 'app-meter-frame-search',
    templateUrl: './meter-frame-search.component.html',
    styleUrls: ['./meter-frame-search.component.scss'],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => MeterFrameSearchComponent),
            multi: true
        }
    ],
    standalone: false,
    host: { hostID: crypto.randomUUID().toString() }
})
export class MeterFrameSearchComponent implements ControlValueAccessor, OnInit, OnDestroy {
    static readonly empty = { id: '', meterFrameNumber: '', connectionPointId: '' } as MeterFrameSearchItemModel;
    @Input() options: MeterFrameSearchItemModel[] = [];
    @Input() showValidForSharedBranchLineOnly: boolean = false;
    @Input() showWithValidElectricityPurposeOnly: boolean = false;

    private destroy$ = new Subject<void>();

    private searchSubject$ = new Subject<string>();
    private clearSubject$ = new Subject<void>();

    // Functions to propagate changes to the form control
    private onChange: (value: MeterFrameSearchItemModel) => void = () => {};
    private onTouched: () => void = () => {};

    subscription: Subscription = new Subscription();

    value: MeterFrameSearchItemModel = MeterFrameSearchComponent.empty;
    isDisabled: boolean = false;

    constructor(
        @Optional() public readonly controlContainer: ControlContainer,
        private readonly client: InstallationFormsClient,
        private readonly router: Router,
        private readonly messageServiceHelper: MessageServiceHelper,
        private readonly formDataService: FormDataService,
        private readonly translateService: TranslateService
    ) {}

    ngOnInit(): void {
        this.registerActions();
    }

    ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }

    private registerActions() {
        this.searchSubject$
            .pipe(
                takeUntil(this.destroy$),
                switchMap((query) => {
                    query = query.trim();
                    if (!query || query.length === 0) {
                        return of([]);
                    }
                    return this.client
                        .searchMeterFrames(
                            uuidv4(),
                            new MeterFramesSearchQueryModel({
                                filter: new MeterFramesSearchFilterModel({
                                    supplyType: convertSupplyTypeToEnum(this.formDataService.supplyType!),
                                    meterFrameNumber: query,
                                    validForSharedBranchLineOnly: this.showValidForSharedBranchLineOnly,
                                    withValidElectricityPurposeOnly: this.showWithValidElectricityPurposeOnly
                                })
                            })
                        )
                        .pipe(
                            catchError((_error) => {
                                this.messageServiceHelper.showError({
                                    detail: this.translateService.instant('common.meterFramesSearchError'),
                                    key: this.formDataService.formId
                                });
                                return of(undefined);
                            }),
                            map((response) => {
                                if (!response) {
                                    return [];
                                }
                                return response.result.results.map(
                                    (searchResultItem) =>
                                        <MeterFrameSearchItemModel>{
                                            id: searchResultItem.id,
                                            meterFrameNumber: searchResultItem.meterFrameNumber,
                                            connectionPointId: searchResultItem.connectionPointId
                                        }
                                );
                            })
                        );
                })
            )
            .subscribe((x) => {
                this.options = x;
            });

        this.clearSubject$.subscribe(() => {
            this.value = MeterFrameSearchComponent.empty;
            this.onSelect(MeterFrameSearchComponent.empty);
        });
    }

    search(event: any) {
        this.searchSubject$.next(event.query);
    }

    onClear() {
        this.clearSubject$.next();
    }

    onSelect(value: MeterFrameSearchItemModel) {
        this.value = value;
        this.onChange(this.value);
        this.onTouched();
    }

    writeValue(value: MeterFrameSearchItemModel): void {
        this.value = value;
    }

    registerOnChange(fn: any): void {
        this.onChange = fn;
    }

    registerOnTouched(fn: any): void {
        this.onTouched = fn;
    }

    setDisabledState(isDisabled: boolean): void {
        this.isDisabled = isDisabled;
    }

    async onDetailsClick() {
        if (this.value.id) {
            const meterFrame = this.value as MeterFrameSearchItemModel;
            await this.router.navigate([`connection-points/${meterFrame.connectionPointId}/meter-frames/${meterFrame.id}`]);
        }
    }
}
