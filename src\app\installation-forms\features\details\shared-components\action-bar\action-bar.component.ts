import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { FormScreeningStatus, FormState } from 'src/app/api/installation-forms-client';
import { WidgetNames } from 'src/app/core/constants/widget-names';
import { WidgetComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget.component';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';

@Component({
    selector: 'app-action-bar',
    templateUrl: './action-bar.component.html',
    styleUrls: ['./action-bar.component.scss'],
    standalone: false
})
export class ActionBarComponent extends WidgetComponent {
    widgetName: string = WidgetNames.actionBarWidget;
    @Input() possibleStateChanges!: FormState[];
    @Input() instructChangeComment?: string;

    public stateChangeDialogVisible = false;
    public stateChangeDialogState?: string;
    public screeningStatusRevertToHandleManuallyDialogVisible = false;

    @Output() stateChangeEvent: EventEmitter<{ state: string; comment?: string }> = new EventEmitter();
    @Output() screeningStatusChangeEvent: EventEmitter<{ state: string }> = new EventEmitter();

    public form!: FormGroup;

    constructor(
        private fb: FormBuilder,
        readonly formDataService: FormDataService
    ) {
        super(formDataService);
        this.form = this.fb.group({
            stateChangeComment: ['', [Validators.required]]
        });
    }

    get canChangeState() {
        return this.hasUserWritePermission && this.isCurrentUserAssignedAsCaseWorker && !this.isProcessing;
    }

    get canChangeScreeningStatus() {
        return (
            this.hasUserWritePermission &&
            this.isCurrentUserAssignedAsCaseWorker &&
            !this.isProcessing &&
            this.formDataService.allowedScreeningStatusTransition
        );
    }

    onStateChangeButtonClick(state: string) {
        // TODO: Consider extending PossibleStateChanges enum with a flag indicating whether or not the comment is accepted and required
        const statesThatRequireStateChangeComment = [
            FormState.Cancelled,
            FormState.Rejected,
            FormState.Returned,
            FormState.Instructed
        ];

        if (statesThatRequireStateChangeComment.includes(state as FormState)) {
            this.showStateChangeDialog(state);
            this.setChangeCommentForInstruction(state);
        } else {
            this.changeState(state);
        }
    }

    changeState(state: string) {
        this.stateChangeEvent.emit({ state, comment: undefined });
    }

    onStateChangeDialogButtonClick() {
        const state = this.stateChangeDialogState as FormState;
        const comment = this.form.get('stateChangeComment')!.value;
        this.stateChangeEvent.emit({ state, comment });
        this.stateChangeDialogVisible = false;
    }

    showStateChangeDialog(state: string) {
        this.stateChangeDialogVisible = true;
        this.stateChangeDialogState = state;

        // The dialog may have been previously shown and touched.
        // Make sure there's no data in there
        if (state != FormState.Instructed) {
            this.form.reset();
        }
    }

    onScreeningStatusChangeButtonClick() {
        const status = this.formDataService.allowedScreeningStatusTransition!;
        if (status === FormScreeningStatus.HandleManually) {
            this.showScreeningStatusRevertDialog();
        } else {
            this.changeScreeningStatus();
        }
    }

    showScreeningStatusRevertDialog() {
        this.screeningStatusRevertToHandleManuallyDialogVisible = true;
        // The dialog may have been previously shown and touched.
        // Make sure there's no data in there
        this.form.reset();
    }

    onScreeningStatusRevertConfirmationDialogYesButtonClick() {
        this.changeScreeningStatus();
        this.screeningStatusRevertToHandleManuallyDialogVisible = false;
    }

    onScreeningStatusRevertConfirmationDialogNoButtonClick() {
        this.screeningStatusRevertToHandleManuallyDialogVisible = false;
    }

    changeScreeningStatus() {
        const status = this.formDataService.allowedScreeningStatusTransition!;
        this.screeningStatusChangeEvent.emit({ state: status });
    }

    shouldShowErrors(fieldName: string): boolean {
        const field = this.form.get(fieldName);
        return (field && field.invalid && (field.dirty || field.touched)) || false;
    }

    hasError(fieldName: string, errorType: string): boolean {
        const field = this.form.get(fieldName);
        return field && field.errors && field.errors[errorType];
    }

    setChangeCommentForInstruction(state: string) {
        if (state == FormState.Instructed) {
            this.form.setValue({ stateChangeComment: this.instructChangeComment || '' });
        }
    }
}
