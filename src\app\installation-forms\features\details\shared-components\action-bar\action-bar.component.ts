import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { FormScreeningStatus, FormState, GisStatus } from 'src/app/api/installation-forms-client';
import { WidgetNames } from 'src/app/core/constants/widget-names';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';
import { WidgetWithFormComponent } from '../../base-components/widgets/widget-with-form.component';

@Component({
    selector: 'app-action-bar',
    templateUrl: './action-bar.component.html',
    styleUrls: ['./action-bar.component.scss'],
    standalone: false
})
export class ActionBarComponent extends WidgetWithFormComponent {
    widgetName: string = WidgetNames.actionBarWidget;
    @Input() possibleStateChanges!: FormState[];
    @Input() instructChangeComment?: string;

    stateChangeDialogVisible = false;
    stateChangeDialogState?: FormState;
    screeningStatusRevertToHandleManuallyDialogVisible = false;

    @Output() stateChangeEvent: EventEmitter<{ state: string; comment?: string }> = new EventEmitter();
    @Output() screeningStatusChangeEvent: EventEmitter<{ state: string }> = new EventEmitter();

    form!: FormGroup;

    constructor(
        private readonly fb: FormBuilder,
        protected readonly formDataService: FormDataService,
        private readonly translateService: TranslateService
    ) {
        super(formDataService);
        this.form = this.fb.group({
            stateChangeWarning: [null],
            stateChangeComment: ['', [Validators.required]]
        });
    }

    get canChangeState() {
        return this.hasUserWritePermission && this.isCurrentUserAssignedAsCaseWorker && !this.isProcessing;
    }

    get canChangeScreeningStatus() {
        return (
            this.hasUserWritePermission &&
            this.isCurrentUserAssignedAsCaseWorker &&
            !this.isProcessing &&
            this.formDataService.allowedScreeningStatusTransition
        );
    }

    onStateChangeButtonClick(state: FormState) {
        const statesThatRequireStateChangeComment = [
            FormState.Cancelled,
            FormState.Rejected,
            FormState.Returned,
            FormState.Instructed
        ];

        if (statesThatRequireStateChangeComment.includes(state)) {
            this.showStateChangeDialog(state);
            this.setCommentAndWarningForInstruction(state);
        } else {
            this.changeState(state);
        }
    }

    changeState(state: string) {
        this.stateChangeEvent.emit({ state, comment: undefined });
    }

    onStateChangeDialogButtonClick() {
        const state = this.stateChangeDialogState as FormState;
        const comment = this.form.get('stateChangeComment')!.value;
        this.stateChangeEvent.emit({ state, comment });
        this.stateChangeDialogVisible = false;
    }

    showStateChangeDialog(state: FormState) {
        this.stateChangeDialogVisible = true;
        this.stateChangeDialogState = state;

        // The dialog may have been previously shown and touched.
        // Make sure there's no data in there
        if (state != FormState.Instructed) {
            this.form.reset();
        }
    }

    onScreeningStatusChangeButtonClick() {
        const status = this.formDataService.allowedScreeningStatusTransition!;
        if (status === FormScreeningStatus.HandleManually) {
            this.showScreeningStatusRevertDialog();
        } else {
            this.changeScreeningStatus();
        }
    }

    showScreeningStatusRevertDialog() {
        this.screeningStatusRevertToHandleManuallyDialogVisible = true;
        // The dialog may have been previously shown and touched.
        // Make sure there's no data in there
        this.form.reset();
    }

    onScreeningStatusRevertConfirmationDialogYesButtonClick() {
        this.changeScreeningStatus();
        this.screeningStatusRevertToHandleManuallyDialogVisible = false;
    }

    onScreeningStatusRevertConfirmationDialogNoButtonClick() {
        this.screeningStatusRevertToHandleManuallyDialogVisible = false;
    }

    changeScreeningStatus() {
        const status = this.formDataService.allowedScreeningStatusTransition!;
        this.screeningStatusChangeEvent.emit({ state: status });
    }

    setCommentAndWarningForInstruction(state: string) {
        if (state == FormState.Instructed) {
            const isGisStatusOk = this.formDataService.gisStatus && this.formDataService.gisStatus === GisStatus.Ok;
            this.form.setValue({
                stateChangeComment: this.instructChangeComment || '',
                stateChangeWarning: isGisStatusOk
                    ? null
                    : this.translateService.instant('actionBar.stateHandling.gisStatusNotOkWarning')
            });
        }
    }
}
