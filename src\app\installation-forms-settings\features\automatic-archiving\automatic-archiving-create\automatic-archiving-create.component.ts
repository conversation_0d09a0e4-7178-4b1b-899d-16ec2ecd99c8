import { Component, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { finalize } from 'rxjs';
import {
    AutomaticArchivingRule,
    AutomaticArchivingRuleCreateOrUpdate,
    FormState,
    FormType,
    InstallationFormsClient,
    MasterDataProcessType,
    ResponsibleForMeter,
    ValueListType,
    WorkOrderType
} from 'src/app/api/installation-forms-client';
import { SETTINGS_DISPLAY_NAME_MAX_LENGTH } from 'src/app/core/constants/constants';
import { BooleanOptionsService } from 'src/app/core/services/boolean-options/boolean-options.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { ValueListsService } from 'src/app/core/services/value-lists/value-lists.service';
import { isAnyOfRequiredFormTypesSelected } from 'src/app/core/utils/form-type-checkers';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import conditionallyRequiredValidator from 'src/app/core/utils/validators/conditionally-required/conditionally-required.validator';
import { multipleWhitespaceValidator } from 'src/app/core/utils/validators/multiple-whitespace-validator/multiple-whitespace-validator';
import { v4 as uuidv4 } from 'uuid';
import { alreadyUsedDisplayNameValidator } from '../../../../core/utils/validators/already-used-display-name-validator/already-used-display-name-validator';
import { BaseRuleCreateComponent } from '../../base-components/base-rule-create.components';
import {
    automaticArchivingRelevantStates,
    automaticArchivingTabName,
    automaticArchivingTranslationPath
} from '../constants/automatic-archiving.consts';
import {
    METER_INSTALLED_FORM_TYPES,
    METER_RESPONSIBLE_FORM_TYPES,
    STARTS_AS_CONSTRUCTION_FORM_TYPES
} from '../constants/properties-per-form-type';

@Component({
    selector: 'app-automatic-archiving-create',
    templateUrl: './automatic-archiving-create.component.html',
    styleUrl: './automatic-archiving-create.component.scss',
    standalone: false
})
export class AutomaticArchivingCreateComponent extends BaseRuleCreateComponent<AutomaticArchivingRule> implements OnInit {
    formTypeOptions: SelectItem[] = [];
    formStateOptions: SelectItem[] = [];
    responsibleForMeterOptions: SelectItem[] = [];
    verifiedWorkOrderTypeOptions: SelectItem[] = [];
    masterDataProcessTypesOptions: SelectItem[] = [];
    relevantStates: FormState[] = automaticArchivingRelevantStates;

    constructor(
        private readonly client: InstallationFormsClient,
        protected readonly fb: FormBuilder,
        protected readonly translateService: TranslateService,
        protected readonly messageServiceHelper: MessageServiceHelper,
        protected readonly valueListsService: ValueListsService,
        protected readonly booleanOptionsService: BooleanOptionsService
    ) {
        super(fb, translateService, messageServiceHelper, automaticArchivingTranslationPath, automaticArchivingTabName);
    }

    ngOnInit(): void {
        super.ngOnInit();
        this.updateFormStatesOptions();
        this.addFormControlsValueChanges();
    }

    override initForm(): void {
        this.form = this.fb.group({
            displayName: [
                null,
                [
                    Validators.required,
                    Validators.maxLength(SETTINGS_DISPLAY_NAME_MAX_LENGTH),
                    alreadyUsedDisplayNameValidator(this.alreadyUsedDisplayNames),
                    multipleWhitespaceValidator()
                ]
            ],
            formTypes: [null],
            formStates: [null],
            responsibleForMeter: [null],
            meterInstalled: [null],
            startsAsConstruction: [null],
            verifiedWorkOrderType: [null],
            verifiedWorkOrderDescription: [
                null,
                [(control: AbstractControl) => conditionallyRequiredValidator(control, this.isVerifiedWorkOrderTypeSelected)]
            ],
            masterDataProcessTypes: [null]
        });
    }

    override getEnumTranslations(): void {
        this.formTypeOptions = enumMapper.map(this.translateService.instant('enums.type'), FormType);
        this.responsibleForMeterOptions = enumMapper.map(
            this.translateService.instant('enums.responsibleForMeter'),
            ResponsibleForMeter
        );
        this.verifiedWorkOrderTypeOptions = enumMapper.map(this.translateService.instant('enums.workOrderType'), WorkOrderType);
        this.masterDataProcessTypesOptions = enumMapper.map(
            this.translateService.instant('enums.masterDataProcessType'),
            MasterDataProcessType
        );
    }

    override createClick() {
        if (this.form.invalid) {
            return;
        }

        const formValue = this.form.value;

        const rule: AutomaticArchivingRuleCreateOrUpdate = new AutomaticArchivingRuleCreateOrUpdate({
            displayName: formValue.displayName,
            formTypes: formValue.formTypes ?? [],
            formStates: formValue.formStates?.filter((state: FormState) => this.relevantStates.includes(state)) ?? [],
            meterInstalled: formValue.meterInstalled ?? null,
            responsibleForMeter: formValue.responsibleForMeter ?? [],
            startsAsConstruction: formValue.startsAsConstruction ?? null,
            verifiedWorkOrderType: formValue.verifiedWorkOrderType ?? null,
            verifiedWorkOrderDescription: formValue.verifiedWorkOrderDescription ?? null,
            masterDataProcessTypes: formValue.masterDataProcessTypes ?? []
        });

        this.isProcessing = true;
        this.subscription.add(
            this.client
                .createAutomaticArchivingRule(uuidv4(), rule)
                .pipe(finalize(() => (this.isProcessing = false)))
                .subscribe({
                    next: (response) => {
                        this.ruleCreated.emit(response.result);
                        this.showSuccessMessage();
                    },
                    error: (_) => {
                        this.showErrorMessage(this.widgetTranslations['automaticArchivingRuleCreationError']);
                    }
                })
        );
    }

    addFormControlsValueChanges() {
        this.subscription.add(
            this.form.get('verifiedWorkOrderType')?.valueChanges.subscribe((_) => {
                const verifiedWorkOrderDescriptionControl = this.form.get('verifiedWorkOrderDescription');
                if (verifiedWorkOrderDescriptionControl) {
                    verifiedWorkOrderDescriptionControl.updateValueAndValidity();
                }
            })
        );

        this.subscription.add(
            this.form.get('verifiedWorkOrderType')?.valueChanges.subscribe((_) => {
                this.form.get('verifiedWorkOrderDescription')?.setValue(null);
            })
        );
        this.subscription.add(
            this.form.get('formTypes')?.valueChanges.subscribe(() => {
                this.updateFormStatesOptions();
                const selectedFormStates = this.form.get('formStates')?.value;
                const filteredFormStates = selectedFormStates.filter((state: FormState) => this.relevantStates.includes(state));
                this.form.get('formStates')?.setValue(filteredFormStates);

                if (!this.isMeterResponsibleRequired()) {
                    this.form.get('responsibleForMeter')?.setValue([]);
                }

                if (!this.isMeterInstalledRequired()) {
                    this.form.get('meterInstalled')?.setValue([]);
                }

                if (!this.isStartsAsConstructionRequired()) {
                    this.form.get('startsAsConstruction')?.setValue([]);
                }
            })
        );
    }

    updateFormStatesOptions() {
        const formTypes = this.form?.get('formTypes')?.value || [];
        this.relevantStates = automaticArchivingRelevantStates;

        if (formTypes.length !== 0 && !formTypes.includes(FormType.SealBreach) && !formTypes.includes(FormType.Termination)) {
            this.relevantStates = this.relevantStates.filter((state) => state !== FormState.Registered);
        }
        if (
            formTypes.length !== 0 &&
            !formTypes.includes(FormType.EnergyProduction) &&
            !formTypes.includes(FormType.ChangeBranchLine) &&
            !formTypes.includes(FormType.MoveMeter)
        ) {
            this.relevantStates = this.relevantStates.filter((state) => state !== FormState.Instructed);
        }

        this.formStateOptions = enumMapper.map(this.translateService.instant('enums.state'), this.relevantStates);
    }

    isVerifiedWorkOrderTypeSelected = () => {
        return this.form?.get('verifiedWorkOrderType')?.value != null;
    };

    isMeterResponsibleRequired = (): boolean => {
        const formTypes = this.form?.get('formTypes')?.value || [];
        return isAnyOfRequiredFormTypesSelected(formTypes, METER_RESPONSIBLE_FORM_TYPES);
    };

    isMeterInstalledRequired = (): boolean => {
        const formTypes = this.form?.get('formTypes')?.value || [];
        return isAnyOfRequiredFormTypesSelected(formTypes, METER_INSTALLED_FORM_TYPES);
    };

    isStartsAsConstructionRequired = (): boolean => {
        const formTypes = this.form?.get('formTypes')?.value || [];
        return isAnyOfRequiredFormTypesSelected(formTypes, STARTS_AS_CONSTRUCTION_FORM_TYPES);
    };

    getWorkOrderDescriptionValueListType = (): ValueListType => {
        switch (this.form?.get('verifiedWorkOrderType')?.value) {
            case WorkOrderType.InstallMeter:
                return ValueListType.WorkOrderInstallMeterElectricityWorkDescriptionList;
            case WorkOrderType.ReplaceMeter:
                return ValueListType.WorkOrderReplaceMeterElectricityWorkDescriptionList;
            case WorkOrderType.RemoveMeter:
                return ValueListType.WorkOrderRemoveMeterElectricityWorkDescriptionList;
            case WorkOrderType.General:
                return ValueListType.WorkOrderGeneralElectricityWorkDescriptionList;
            default:
                return ValueListType.WorkOrderGeneralElectricityWorkDescriptionList;
        }
    };
}
