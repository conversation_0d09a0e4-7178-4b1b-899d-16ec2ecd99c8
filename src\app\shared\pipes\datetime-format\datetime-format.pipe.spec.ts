import { TestBed } from '@angular/core/testing';
import { DateTimeService } from 'src/app/core/services/date-time/date-time.service';
import { FormatDateTimePipe } from './datetime-format.pipe';

describe('FormatDateTimePipe', () => {
    let pipe: FormatDateTimePipe;
    let dateTimeService: DateTimeService;
    let transformDateTimeSpy: jest.SpyInstance;

    beforeEach(() => {
        const dateTimeServiceMock = {
            transformDateTime: jest.fn()
        };

        TestBed.configureTestingModule({
            providers: [FormatDateTimePipe, { provide: DateTimeService, useValue: dateTimeServiceMock }]
        });

        pipe = TestBed.inject(FormatDateTimePipe);
        dateTimeService = TestBed.inject(DateTimeService);
        transformDateTimeSpy = jest.spyOn(dateTimeService, 'transformDateTime');
    });

    it('should create an instance', () => {
        expect(pipe).toBeTruthy();
    });

    it('should delegate to the DateTimeService transformDateTime method', () => {
        const testDate = new Date(2023, 0, 15, 14, 30); // January 15, 2023, 14:30
        transformDateTimeSpy.mockReturnValue('mocked-datetime-string');

        const result = pipe.transform(testDate);

        expect(dateTimeService.transformDateTime).toHaveBeenCalledWith(testDate);
        expect(result).toBe('mocked-datetime-string');
    });

    it('should pass undefined to the service when input is undefined', () => {
        transformDateTimeSpy.mockReturnValue('');

        pipe.transform(undefined);

        expect(dateTimeService.transformDateTime).toHaveBeenCalledWith(undefined);
    });
});
