import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map, shareReplay } from 'rxjs/operators';
import { InstallationFormsClient, InstructionText } from 'src/app/api/installation-forms-client';
import { v4 as uuidv4 } from 'uuid';

@Injectable({
    providedIn: 'root'
})
export class DefaultInstructionTextService {
    private instructionTexts: Observable<InstructionText[]> | null = null;

    constructor(private client: InstallationFormsClient) {}

    getInstructionTexts(): Observable<InstructionText[] | undefined> {
        if (!this.instructionTexts) {
            this.instructionTexts = this.client.getInstructionTexts(uuidv4()).pipe(
                map((response) => response.result),
                shareReplay(1)
            );
        }
        return this.instructionTexts;
    }

    invalidateInstructionTextsCache(): void {
        this.instructionTexts = null;
    }
}
