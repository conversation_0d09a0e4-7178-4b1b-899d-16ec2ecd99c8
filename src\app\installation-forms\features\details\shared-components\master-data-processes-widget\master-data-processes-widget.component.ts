import { Component, OnDestroy, OnInit } from '@angular/core';
import { AuthorizationService } from '@kmd-elements-ui/authorization';
import { Subscription } from 'rxjs';
import { Permissions } from 'src/app/core/constants/permissions';
import { AutomaticFormRefreshService } from 'src/app/core/services/automatic-form-refresh/automatic-form-refresh.service';
import { WidgetComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget.component';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';

@Component({
    selector: 'app-master-data-processes-widget',
    templateUrl: './master-data-processes-widget.component.html',
    standalone: false
})
export class MasterDataProcessesWidgetComponent extends WidgetComponent implements OnDestroy, OnInit {
    showCreateMasterDataProcess: boolean = false;
    subscription: Subscription = new Subscription();
    hasMasterDataReadPermission = false;
    hasMasterDataWritePermission = false;

    constructor(
        private readonly authService: AuthorizationService,
        protected readonly formDataService: FormDataService,
        private readonly automaticFormRefreshService: AutomaticFormRefreshService
    ) {
        super(formDataService);
    }

    ngOnInit(): void {
        this.authService.hasPermissions([Permissions.masterData.read]).subscribe((x) => (this.hasMasterDataReadPermission = x));
        this.authService.hasPermissions([Permissions.masterData.write]).subscribe((x) => (this.hasMasterDataWritePermission = x));
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    closeCreateMasterDataProcess() {
        this.showCreateMasterDataProcess = false;
        this.automaticFormRefreshService.restartPolling();
    }

    protected createMasterDataProcessButtonClick() {
        this.showCreateMasterDataProcess = true;
    }

    protected showCreateMasterDataProcessButton = () =>
        this.formDataService.canCurrentUserExecuteActions && this.hasMasterDataWritePermission;
}
