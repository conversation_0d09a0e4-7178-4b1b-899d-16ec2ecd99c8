import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { AuthorizationService } from '@kmd-elements-ui/authorization';
import { Subscription } from 'rxjs';
import { Permissions } from 'src/app/core/constants/permissions';
import { AutomaticFormRefreshService } from 'src/app/core/services/automatic-form-refresh/automatic-form-refresh.service';
import { WidgetComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget.component';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';

@Component({
    selector: 'app-master-data-processes-widget',
    templateUrl: './master-data-processes-widget.component.html',
    styleUrl: './master-data-processes-widget.component.scss',
    standalone: false
})
export class MasterDataProcessesWidgetComponent extends WidgetComponent implements OnD<PERSON>roy, OnInit {
    public showCreateMasterDataProcess: boolean = false;
    public subscription: Subscription = new Subscription();
    public hasMasterDataReadPermission = false;
    public hasMasterDataWritePermission = false;

    constructor(
        private readonly authService: AuthorizationService,
        readonly formDataService: FormDataService,
        private readonly automaticFormRefreshService: AutomaticFormRefreshService
    ) {
        super(formDataService);
    }

    ngOnInit(): void {
        this.authService.hasPermissions([Permissions.masterData.read]).subscribe((x) => (this.hasMasterDataReadPermission = x));
        this.authService.hasPermissions([Permissions.masterData.write]).subscribe((x) => (this.hasMasterDataWritePermission = x));
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    closeCreateMasterDataProcess() {
        this.showCreateMasterDataProcess = false;
        this.automaticFormRefreshService.restartPolling();
    }

    protected createMasterDataProcessButtonClick() {
        this.showCreateMasterDataProcess = true;
    }

    protected showCreateMasterDataProcessButton = () =>
        this.formDataService.canCurrentUserExecuteActions && this.hasMasterDataWritePermission;
}
