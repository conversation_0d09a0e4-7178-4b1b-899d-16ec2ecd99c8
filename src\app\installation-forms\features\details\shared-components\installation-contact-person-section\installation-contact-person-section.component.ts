import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { WidgetWithFormComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget-with-form.component';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';

@Component({
    selector: 'app-installation-contact-person-section',
    templateUrl: './installation-contact-person-section.component.html',
    standalone: false
})
export class InstallationContactPersonSectionComponent extends WidgetWithFormComponent {
    @Input() form!: FormGroup;

    private _isContactPersonVisible: boolean = false;

    @Input()
    set isContactPersonVisible(value: boolean) {
        if (this._isContactPersonVisible !== value) {
            this._isContactPersonVisible = value;
        }
    }

    get isContactPersonVisible() {
        return this._isContactPersonVisible;
    }

    private _isContactPersonRequired: boolean = false;

    @Input()
    set isContactPersonRequired(value: boolean) {
        this._isContactPersonRequired = value;
        if (this._isContactPersonRequired) {
            this.isContactPersonVisible = true;
            this.contactPersonDataVisibilityChanged.emit(true);
        }
    }

    get isContactPersonRequired() {
        return this._isContactPersonRequired;
    }

    @Output()
    contactPersonDataVisibilityChanged: EventEmitter<boolean> = new EventEmitter();

    constructor(protected readonly formDataService: FormDataService) {
        super(formDataService);
    }

    onShowContactPersonDataToggleChange(event: any) {
        const newVal = !event.checked;
        this.contactPersonDataVisibilityChanged.emit(newVal);
    }
}
