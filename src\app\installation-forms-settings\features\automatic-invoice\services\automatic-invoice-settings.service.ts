import { Injectable } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { FormCategory, FormType, InvoiceType } from 'src/app/api/installation-forms-client';
import {
    energyProductionFormCategories,
    newInstallationFormCategories
} from 'src/app/core/constants/form-categories-per-form-type';
import { isAllOfRequiredFormTypesSelected, isAnyOfRequiredFormTypesSelected } from 'src/app/core/utils/form-type-checkers';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import { FORM_CATEGORY_FORM_TYPES, INVOICE_TYPE_FORM_TYPES } from '../constants/properties-per-form-types';

@Injectable({
    providedIn: 'root'
})
export class AutomaticInvoiceSettingsService {
    constructor(private readonly translateService: TranslateService) {}

    private filterRelevantFormCategories(form: FormGroup, relevantFormCategories: FormCategory[]) {
        const selectedFormCategories = form.get('formCategories')?.value || [];
        const filteredCategories = selectedFormCategories.filter((category: FormCategory) =>
            relevantFormCategories.includes(category)
        );
        form.get('formCategories')?.setValue(filteredCategories);
    }

    private filterRelevantFormTypes(form: FormGroup, relevantFormTypes: FormType[]) {
        const selectedFormTypes = form.get('formTypes')?.value || [];
        const filteredFormTypes = selectedFormTypes.filter((formType: FormType) => relevantFormTypes.includes(formType));
        form.get('formTypes')?.setValue(filteredFormTypes);
    }

    // Form type validation methods
    isFormCategoryRelevant = (form: FormGroup): boolean => {
        const formTypes = form.get('formTypes')?.value || [];
        return isAnyOfRequiredFormTypesSelected(formTypes, FORM_CATEGORY_FORM_TYPES);
    };

    isEnergyProductionSelected(form: FormGroup): boolean {
        const selectedTypes = form?.get('formTypes')?.value || [];
        return selectedTypes.includes(FormType.EnergyProduction);
    }

    showTariff(form?: FormGroup): boolean {
        return form?.get('invoiceType')?.value == InvoiceType.StandardFee;
    }

    updateHadExistingProductionValidation(form: FormGroup) {
        const hadExistingProduction = form.get('hadExistingProduction');
        if (!this.isEnergyProductionSelected(form)) {
            hadExistingProduction?.setValue(null);
        }
    }

    updateProductionCapacityValidation(form: FormGroup) {
        const productionCapacityMin = form.get('productionCapacityMin');
        const productionCapacityMax = form.get('productionCapacityMax');
        if (!this.isEnergyProductionSelected(form)) {
            productionCapacityMin?.setValue(null);
            productionCapacityMax?.setValue(null);
        }
    }

    updateFormCategories(form: FormGroup) {
        const formTypes = form?.get('formTypes')?.value || [];
        let relevantCategories: FormCategory[] = [];

        if (isAllOfRequiredFormTypesSelected(formTypes, [FormType.NewInstallation, FormType.EnergyProduction])) {
            relevantCategories = Object.values(FormCategory);
        } else if (isAllOfRequiredFormTypesSelected(formTypes, [FormType.NewInstallation])) {
            relevantCategories = newInstallationFormCategories;
        } else if (isAllOfRequiredFormTypesSelected(formTypes, [FormType.EnergyProduction])) {
            relevantCategories = energyProductionFormCategories;
        } else {
            relevantCategories = [];
        }

        const formCategoriesOptions = enumMapper.map(this.translateService.instant('enums.category'), relevantCategories);

        this.filterRelevantFormCategories(form, relevantCategories);
        return formCategoriesOptions;
    }

    updateInvoiceTypes(form: FormGroup) {
        const formTypes = form?.get('formTypes')?.value || [];
        const relevantInvoiceTypes: InvoiceType[] = [];
        INVOICE_TYPE_FORM_TYPES.forEach((value, key) => {
            if (isAllOfRequiredFormTypesSelected(value, formTypes)) {
                relevantInvoiceTypes.push(key);
            }
        });

        const invoiceTypeOptions = enumMapper.map(this.translateService.instant('enums.invoiceType'), relevantInvoiceTypes);
        return invoiceTypeOptions;
    }

    updateShowTariffValidation(form: FormGroup) {
        if (!this.showTariff(form)) {
            form.get('tariff')?.setValue(null);
        }
    }
}
