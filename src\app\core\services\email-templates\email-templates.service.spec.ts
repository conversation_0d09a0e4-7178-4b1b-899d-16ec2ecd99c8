import { TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { InstallationFormsClient, SupplyType } from 'src/app/api/installation-forms-client';
import { EmailTemplatesService } from './email-templates.service';

// Mock UUID to make testing deterministic
jest.mock('uuid', () => ({
    v4: () => 'test-uuid'
}));

describe('EmailTemplatesService', () => {
    let service: EmailTemplatesService;
    let mockClient: InstallationFormsClient;

    // Simplified mock response
    const mockResponse = {
        result: { templates: [{ id: 1, name: 'Template 1' }] }
    };

    beforeEach(() => {
        // Create mock for the client
        mockClient = {
            getEmailTemplatesList: jest.fn()
        } as Partial<InstallationFormsClient> as InstallationFormsClient;

        TestBed.configureTestingModule({
            providers: [EmailTemplatesService, { provide: InstallationFormsClient, useValue: mockClient }]
        });

        service = TestBed.inject(EmailTemplatesService);
        mockClient = TestBed.inject(InstallationFormsClient);
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    it('should call client with correct parameters', () => {
        (mockClient.getEmailTemplatesList as jest.Mock).mockReturnValue(of(mockResponse));

        service.getEmailTemplates(SupplyType.Electricity);

        expect(mockClient.getEmailTemplatesList).toHaveBeenCalledWith('test-uuid', SupplyType.Electricity);
    });

    it('should cache responses per supply type', () => {
        (mockClient.getEmailTemplatesList as jest.Mock).mockReturnValue(of(mockResponse));

        // First call for ELECTRICITY
        service.getEmailTemplates(SupplyType.Electricity).subscribe();
        expect(mockClient.getEmailTemplatesList).toHaveBeenCalledTimes(1);

        // Second call for same supply type should use cache
        service.getEmailTemplates(SupplyType.Electricity).subscribe();
        expect(mockClient.getEmailTemplatesList).toHaveBeenCalledTimes(1);

        // Call for different supply type should make a new request
        service.getEmailTemplates(SupplyType.Water).subscribe();
        expect(mockClient.getEmailTemplatesList).toHaveBeenCalledTimes(2);
    });

    it('should return the result property from the response', (done) => {
        (mockClient.getEmailTemplatesList as jest.Mock).mockReturnValue(of(mockResponse));

        service.getEmailTemplates(SupplyType.Electricity).subscribe((result) => {
            expect(result).toBe(mockResponse.result);
            done();
        });
    });
});
