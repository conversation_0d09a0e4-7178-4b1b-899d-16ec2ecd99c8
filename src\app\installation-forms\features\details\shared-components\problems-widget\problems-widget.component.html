<p-panel header="{{ 'problemsWidget.title' | translate }}" [toggleable]="true">
    <p-accordion [multiple]="true" [value]="[0, 1, 2, 3]">
        <p-accordion-panel *ngIf="errors.length > 0" [value]="0">
            <p-accordion-header>{{ 'problemsWidget.errors' | translate }} ({{ errorsCount }})</p-accordion-header>
            <p-accordion-content>
                <ng-container *ngFor="let group of errors; let last = last">
                    <app-problems-list
                        [problems]="group.problems"
                        type="error"
                        [isProcessing]="isProcessing"
                        (markAsAcknowledged)="markProblemAsAcknowledgedConfirmation($event)">
                    </app-problems-list>
                    <p-divider class="pb-2" *ngIf="!last" type="solid"></p-divider>
                </ng-container>
            </p-accordion-content>
        </p-accordion-panel>

        <p-accordion-panel *ngIf="warnings.length > 0" [value]="1">
            <p-accordion-header>{{ 'problemsWidget.warnings' | translate }} ({{ warningsCount }})</p-accordion-header>
            <p-accordion-content>
                <ng-container *ngFor="let group of warnings; let last = last">
                    <app-problems-list
                        [problems]="group.problems"
                        type="warning"
                        [isProcessing]="isProcessing"
                        (markAsAcknowledged)="markProblemAsAcknowledgedConfirmation($event)">
                    </app-problems-list>
                    <p-divider class="pb-2" *ngIf="!last" type="solid"></p-divider>
                </ng-container>
            </p-accordion-content>
        </p-accordion-panel>

        <p-accordion-panel *ngIf="infos.length > 0" [value]="2">
            <p-accordion-header>{{ 'problemsWidget.info' | translate }} ({{ infosCount }})</p-accordion-header>
            <p-accordion-content>
                <ng-container *ngFor="let group of infos; let last = last">
                    <app-problems-list
                        [problems]="group.problems"
                        type="info"
                        [isProcessing]="isProcessing"
                        (markAsAcknowledged)="markProblemAsAcknowledgedConfirmation($event)">
                    </app-problems-list>
                    <p-divider class="pb-2" *ngIf="!last" type="solid"></p-divider>
                </ng-container>
            </p-accordion-content>
        </p-accordion-panel>

        <p-accordion-panel *ngIf="acknowledged.length > 0" [value]="3">
            <p-accordion-header>{{ 'problemsWidget.acknowledged' | translate }} ({{ acknowledged.length }})</p-accordion-header>
            <p-accordion-content>
                <app-problems-list [problems]="acknowledged" type="acknowledged" [isProcessing]="isProcessing">
                </app-problems-list>
            </p-accordion-content>
        </p-accordion-panel>
    </p-accordion>
</p-panel>

<app-confirmation-dialog
    [headerKey]="'problemsWidget.markAsAcknowledgedConfirmationTitle'"
    [messageKey]="'problemsWidget.markAsAcknowledgedConfirmationMessage'"
    [visible]="!!attemptedAction"
    (confirmEvent)="onConfirmationDialogYesButtonClick()"
    (cancelEvent)="onConfirmationDialogNoButtonClick()">
</app-confirmation-dialog>
