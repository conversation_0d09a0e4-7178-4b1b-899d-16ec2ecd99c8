﻿import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { isAuthenticatedAndAuthorized } from '@kmd-elements-ui/authorization';
import { Permissions } from '../core/constants/permissions';
import { ChangeBranchLineDetailsComponent } from './features/details/change-branch-line/change-branch-line-details.component';
import { ChangeOfMeterDetailsComponent } from './features/details/change-of-meter/change-of-meter-details.component';
import { EnergyProductionDetailsComponent } from './features/details/energy-production/energy-production-details.component';
import { ExtensionDetailsComponent } from './features/details/extension/extension-details.component';
import { MoveMeterDetailsComponent } from './features/details/move-meter/move-meter-details.component';
import { NewInstallationDetailsComponent } from './features/details/new-installation/new-installation-details.component';
import { SealBreachDetailsComponent } from './features/details/seal-breach/seal-breach-details.component';
import { TerminationDetailsComponent } from './features/details/termination/termination-details.component';
import { InstallationFormsListComponent } from './features/list/list.component';

const routes: Routes = [
    {
        path: '',
        component: InstallationFormsListComponent,
        canActivate: [isAuthenticatedAndAuthorized([Permissions.installationForms.read])],
        data: {
            reuse: true
        }
    },
    {
        path: 'details/NewInstallation/:formId',
        component: NewInstallationDetailsComponent,
        canActivate: [isAuthenticatedAndAuthorized([Permissions.installationForms.read])],
        data: {
            reuse: true
        }
    },
    {
        path: 'details/SealBreach/:formId',
        component: SealBreachDetailsComponent,
        canActivate: [isAuthenticatedAndAuthorized([Permissions.installationForms.read])],
        data: {
            reuse: true
        }
    },
    {
        path: 'details/Termination/:formId',
        component: TerminationDetailsComponent,
        canActivate: [isAuthenticatedAndAuthorized([Permissions.installationForms.read])],
        data: {
            reuse: true
        }
    },
    {
        path: 'details/ChangeMeter/:formId',
        component: ChangeOfMeterDetailsComponent,
        canActivate: [isAuthenticatedAndAuthorized([Permissions.installationForms.read])],
        data: {
            reuse: true
        }
    },
    {
        path: 'details/MoveMeter/:formId',
        component: MoveMeterDetailsComponent,
        canActivate: [isAuthenticatedAndAuthorized([Permissions.installationForms.read])],
        data: {
            reuse: true
        }
    },
    {
        path: 'details/ChangeBranchLine/:formId',
        component: ChangeBranchLineDetailsComponent,
        canActivate: [isAuthenticatedAndAuthorized([Permissions.installationForms.read])],
        data: {
            reuse: true
        }
    },
    {
        path: 'details/Extension/:formId',
        component: ExtensionDetailsComponent,
        canActivate: [isAuthenticatedAndAuthorized([Permissions.installationForms.read])],
        data: {
            reuse: true
        }
    },
    {
        path: 'details/EnergyProduction/:formId',
        component: EnergyProductionDetailsComponent,
        canActivate: [isAuthenticatedAndAuthorized([Permissions.installationForms.read])],
        data: {
            reuse: true
        }
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class InstallationFormsRoutingModule {}
