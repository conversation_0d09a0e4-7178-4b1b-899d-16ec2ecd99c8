<ng-container *ngIf="invoices && invoices.length; else emptyMessage">
    <p-table
        [columns]="columns"
        [value]="invoices"
        [rowHover]="true"
        responsiveLayout="scroll"
        [paginator]="invoices.length > rowsPerPage"
        [rows]="rowsPerPage"
        [customSort]="false"
        [sortField]="'createdDate'"
        [sortOrder]="-1">
        <ng-template pTemplate="header" let-columns>
            <tr>
                <th *ngFor="let col of columns" [pSortableColumn]="col" [id]="col">
                    <div fxLayout="row">
                        {{ 'invoicesWidget.columnNames.' + col | translate }}
                    </div>
                </th>
                <th id="invoices-actions"></th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-invoice>
            <tr
                class="zebra-item"
                [ngClass]="invoice.invoiceId ? 'add-point' : 'no-pointer'"
                (click)="goToInvoice(invoice.invoiceId)">
                <td>{{ invoice.createdDate | formatDateTime }}</td>
                <td>{{ invoice.createdInErpDate | formatDateTime }}</td>
                <td>{{ 'invoicesWidget.invoiceStatuses.' + invoice.status | translate }}</td>
                <td>{{ invoice.number }}</td>
                <td>{{ formatTotalSum(invoice) }}</td>
                <td>
                    <p-checkbox
                        [(ngModel)]="invoice.isRequired"
                        [binary]="true"
                        [disabled]="!canEditInvoices"
                        (click)="onIsRequiredClick($event)"
                        (onChange)="onRequiredChange($event, invoice)">
                    </p-checkbox>
                </td>
                <td>
                    <button
                        type="button"
                        *ngIf="canTransferInvoice(invoice)"
                        pRipple
                        pButton
                        (click)="transferInvoice($event, invoice)">
                        {{ 'invoicesWidget.transferButton' | translate }}
                    </button>
                </td>
            </tr>
        </ng-template>
        <ng-template pTemplate="paginatorright"> </ng-template>
    </p-table>
</ng-container>

<ng-template #emptyMessage>
    <p>{{ 'invoicesWidget.noInvoicesFound' | translate }}</p>
</ng-template>
