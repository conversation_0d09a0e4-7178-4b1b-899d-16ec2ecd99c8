# repo root dir
Set-Location $PSScriptRoot/../../

Write-Host "Pre-commit prettier linting started"

# Prevent lint when merge with conflicts
# https://stackoverflow.com/a/27800562
if (git rev-parse -q --verify MERGE_HEAD -ne $null) {
    Write-Host "Merging in progress, skiping linting, exiting"
    exit 0;
}

if (git rev-parse -q --verify REBASE_HEAD -ne $null) {
    Write-Host "Rebasing in progress, skiping linting, exiting"
    exit 0;
}

$prettierVersion = npx prettier --version
if ($null -eq $prettierVersion) {
    Write-Host "Formatting not possible - prettier not found."
}
$stagedFiles = git diff --cached --name-only --diff-filter=ACM "*.ts" "*.html"
$unstagedFiles = git diff --name-only --diff-filter=ACM "*.ts" "*.html"

$stagedList = ($stagedFiles ?? '').Split() | where { $_ }
$unstagedList = ($unstagedFiles ?? '').Split() | where { $_ }

$toLint = $stagedList | Where-Object { $unstagedList -notcontains $_ }

if ($toLint.Count -eq 0) {
    Write-Host "Nothing to lint, aborting"
    exit 0
}

npx prettier --write $toLint

$toLint | ForEach-Object { git add $_ }

Write-Host "Formatting finished"

exit 0;

