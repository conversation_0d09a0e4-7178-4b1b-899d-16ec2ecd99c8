import { Component, Input, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { MultiSelect } from 'primeng/multiselect';
import { Subscription, finalize, take } from 'rxjs';
import {
    BranchLineResponsible,
    InstallationFormsClient,
    InstructionText,
    MeterResponsible
} from 'src/app/api/installation-forms-client';
import { RemarkLength } from 'src/app/core/constants/field-lengths';
import { AutomaticFormRefreshService } from 'src/app/core/services/automatic-form-refresh/automatic-form-refresh.service';
import { FormDataService } from 'src/app/core/services/form-data/form-data.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import { WidgetWithFormComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget-with-form.component';
import { v4 as uuidv4 } from 'uuid';
import { DefaultInstructionTextsListItem } from '../../../../../installation-forms-settings/features/default-instruction-texts/models/default-instruction-texts-list-item';

@Component({
    selector: 'app-instruction-data-section',
    templateUrl: './instruction-data-section.component.html',
    styleUrls: ['./instruction-data-section.component.scss'],
    standalone: false
})
export class InstructionDataSectionComponent extends WidgetWithFormComponent implements OnInit, OnDestroy {
    static readonly MAX_VISIBLE_INSTRUCTION_TEXT_CHARACTERS = 50;
    @Input() form!: FormGroup;

    private widgetTranslations: any;
    public RemarkLength = RemarkLength;
    subscription: Subscription = new Subscription();

    meterResponsibleOptions: SelectItem[] = [];
    branchLineResponsibleOptions: SelectItem[] = [];
    allInstructionTexts: DefaultInstructionTextsListItem[] = [];
    isDuringInstructionTextLoading = false;
    isInInstructionTextsAddMode = false;

    @ViewChild('defaultInstructionTextsSelect') defaultInstructionTextsSelect!: MultiSelect;

    constructor(
        private readonly translateService: TranslateService,
        readonly formDataService: FormDataService,
        private readonly client: InstallationFormsClient,
        private readonly messageServiceHelper: MessageServiceHelper,
        private readonly automaticFormRefreshService: AutomaticFormRefreshService
    ) {
        super(formDataService);
    }

    ngOnInit(): void {
        this.getEnumTranslations();
        this.widgetTranslations = this.translateService.instant('instructionDataWidget');
        this.automaticFormRefreshService.forceRefreshRequested$.subscribe(() => this.loadDefaultInstructionTexts());
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    public showConnectionFeeValidUntilEodDatePicker() {
        return !!this.form?.get('connectionFeeFee')?.value;
    }

    getEnumTranslations() {
        this.branchLineResponsibleOptions = enumMapper.map(
            this.translateService.instant('enums.branchLineResponsible'),
            BranchLineResponsible
        );

        this.meterResponsibleOptions = enumMapper.map(this.translateService.instant('enums.meterResponsible'), MeterResponsible);
    }

    getSelectableDefaultInstructionTextLabel(instructionTextValue: string) {
        return instructionTextValue.length > InstructionDataSectionComponent.MAX_VISIBLE_INSTRUCTION_TEXT_CHARACTERS
            ? `${instructionTextValue.substring(0, 50)}...`
            : instructionTextValue;
    }

    loadDefaultInstructionTexts() {
        if (!this.isInInstructionTextsAddMode) {
            return;
        }

        this.isDuringInstructionTextLoading = true;
        this.subscription.add(
            this.client
                .getInstructionTexts(uuidv4())
                .pipe(
                    take(1),
                    finalize(() => (this.isDuringInstructionTextLoading = false))
                )
                .subscribe({
                    next: (response) => {
                        this.allInstructionTexts = response.result;
                    },
                    error: (_error) => {
                        this.messageServiceHelper.showError({
                            detail: this.widgetTranslations['getInstructionTextsError'],
                            key: this.formDataService.formId!
                        });
                    }
                })
        );
    }

    turnOffInstructionTextsAddMode() {
        this.isInInstructionTextsAddMode = false;
    }

    turnOnInstructionTextsAddMode() {
        this.isInInstructionTextsAddMode = true;
        this.loadDefaultInstructionTexts();
    }

    addSelectedInstructionTexts() {
        let selectedTexts = this.defaultInstructionTextsSelect.selectedOptions.map((so: InstructionText) => so.text);
        if (selectedTexts.length == 0) {
            return;
        }
        let remarkValue = (<string>this.form.get('remark')?.value || '').trimEnd();
        if (remarkValue.length > 0) {
            remarkValue += '\n\n';
        }
        remarkValue += selectedTexts.join('\n\n');
        this.form.patchValue({ remark: remarkValue });
        this.defaultInstructionTextsSelect.selectedOptions = [];
        this.isInInstructionTextsAddMode = false;
    }
}
