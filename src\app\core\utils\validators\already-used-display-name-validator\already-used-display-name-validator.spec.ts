import { FormControl } from '@angular/forms';
import { alreadyUsedDisplayNameValidator } from './already-used-display-name-validator';

describe('alreadyUsedDisplayNameValidator', () => {
    it('should return null when display name is not in the list of already used names', () => {
        const alreadyUsedDisplayNames = ['Name1', 'Name2', 'Name3'];
        const validator = alreadyUsedDisplayNameValidator(alreadyUsedDisplayNames);

        const control = new FormControl('NewName');
        const result = validator(control);

        expect(result).toBeNull();
    });

    it('should return \'true\' when display name is in the list of already used names', () => {
        const alreadyUsedDisplayNames = ['Name1', 'Name2', 'Name3'];
        const validator = alreadyUsedDisplayNameValidator(alreadyUsedDisplayNames);

        const control = new FormControl('Name2');
        const result = validator(control);

        expect(result).toEqual({ displayNameAlreadyUsed: true });
    });

    it('should return null when control value is null or empty', () => {
        const alreadyUsedDisplayNames = ['Name1', 'Name2', 'Name3'];
        const validator = alreadyUsedDisplayNameValidator(alreadyUsedDisplayNames);

        const nullControl = new FormControl(null);
        const emptyControl = new FormControl('');

        expect(validator(nullControl)).toBeNull();
        expect(validator(emptyControl)).toBeNull();
    });

    it('should handle case sensitivity correctly', () => {
        const alreadyUsedDisplayNames = ['Name1', 'Name2', 'Name3'];
        const validator = alreadyUsedDisplayNameValidator(alreadyUsedDisplayNames);

        const control = new FormControl('name2'); // lowercase version
        const result = validator(control);

        // Should not match because the validator is case-sensitive
        expect(result).toBeNull();
    });
});
