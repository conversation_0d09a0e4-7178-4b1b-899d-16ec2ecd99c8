import { ColumnTransformationType } from 'src/app/core/enums/column-transformation-type';
import { RulesListColumn } from '../../models/rules-list-column';
import { automaticInvoiceTranslationPath } from '../automatic-invoice.consts';

export const AUTOMATIC_INVOICE_COLUMNS: RulesListColumn[] = [
    {
        field: 'displayName',
        header: `${automaticInvoiceTranslationPath}.displayName`,
        columnTransformationType: ColumnTransformationType.None,
        isDefault: true
    },
    {
        field: 'formTypes',
        header: `${automaticInvoiceTranslationPath}.formTypes`,
        translationPath: 'enums.type.',
        columnTransformationType: ColumnTransformationType.TranslateEnumArray,
        isDefault: true
    },
    {
        field: 'formCategories',
        header: `${automaticInvoiceTranslationPath}.formCategories`,
        translationPath: 'enums.category.',
        columnTransformationType: ColumnTransformationType.TranslateEnumArray,
        isDefault: true
    },
    {
        field: 'voltageLevels',
        header: `${automaticInvoiceTranslationPath}.voltageLevels`,
        translationPath: 'enums.automationVoltageLevel.',
        columnTransformationType: ColumnTransformationType.TranslateEnumArray,
        isDefault: true
    },
    {
        field: 'scopeOfDelivery',
        header: `${automaticInvoiceTranslationPath}.scopeOfDelivery`,
        columnTransformationType: ColumnTransformationType.None,
        isDefault: true
    },
    {
        field: 'canTransferInvoiceAutomatically',
        header: `${automaticInvoiceTranslationPath}.canTransferInvoiceAutomatically`,
        columnTransformationType: ColumnTransformationType.Boolean,
        isDefault: true
    },
    {
        field: 'productionCapacityRange',
        header: `${automaticInvoiceTranslationPath}.productionCapacityRange`,
        columnTransformationType: ColumnTransformationType.None,
        isDefault: true
    },
    {
        field: 'hadExistingProduction',
        header: `${automaticInvoiceTranslationPath}.hadExistingProduction`,
        columnTransformationType: ColumnTransformationType.Boolean,
        isDefault: true
    },
    {
        field: 'invoiceType',
        header: `${automaticInvoiceTranslationPath}.invoiceType`,
        translationPath: 'enums.invoiceType.',
        columnTransformationType: ColumnTransformationType.Translate,
        isDefault: true
    },
    {
        field: 'tariff',
        header: `${automaticInvoiceTranslationPath}.tariff`,
        columnTransformationType: ColumnTransformationType.InvoicingPrice,
        isDefault: true
    }
];
