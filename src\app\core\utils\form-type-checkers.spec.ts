import { FormType } from 'src/app/api/installation-forms-client';
import { isAllOfRequiredFormTypesSelected, isAnyOfRequiredFormTypesSelected } from './form-type-checkers';

describe('Form Type Checkers', () => {
    describe('isAnyOfRequiredFormTypesSelected', () => {
        it('should return true when at least one required type is present', () => {
            const formTypes = [FormType.NewInstallation, FormType.EnergyProduction, FormType.Extension];
            const requiredTypes = [FormType.NewInstallation, FormType.ChangeBranchLine];

            const result = isAnyOfRequiredFormTypesSelected(formTypes, requiredTypes);

            expect(result).toBe(true);
        });

        it('should return false when none of the required types are present', () => {
            const formTypes = [FormType.EnergyProduction, FormType.Extension];
            const requiredTypes = [FormType.NewInstallation, FormType.ChangeBranchLine];

            const result = isAnyOfRequiredFormTypesSelected(formTypes, requiredTypes);

            expect(result).toBe(false);
        });

        it('should handle empty arrays correctly', () => {
            expect(isAnyOfRequiredFormTypesSelected([], [FormType.EnergyProduction])).toBe(false);
            expect(isAnyOfRequiredFormTypesSelected([FormType.EnergyProduction], [])).toBe(false);
            expect(isAnyOfRequiredFormTypesSelected([], [])).toBe(false);
        });

        it('should work with readonly array of required types', () => {
            const formTypes = [FormType.NewInstallation, FormType.EnergyProduction];
            const requiredTypes = [FormType.NewInstallation, FormType.ChangeBranchLine] as const;

            const result = isAnyOfRequiredFormTypesSelected(formTypes, requiredTypes);

            expect(result).toBe(true);
        });
    });

    describe('isAllOfRequiredFormTypesSelected', () => {
        it('should return true when all required types are present', () => {
            const formTypes = [FormType.NewInstallation, FormType.EnergyProduction, FormType.Extension];
            const requiredTypes = [FormType.NewInstallation, FormType.EnergyProduction, FormType.Extension];

            const result = isAllOfRequiredFormTypesSelected(formTypes, requiredTypes);

            expect(result).toBe(true);
        });

        it('should return false when some required types are missing', () => {
            const formTypes = [FormType.NewInstallation, FormType.EnergyProduction];
            const requiredTypes = [FormType.NewInstallation, FormType.ChangeBranchLine];

            const result = isAllOfRequiredFormTypesSelected(formTypes, requiredTypes);

            expect(result).toBe(false);
        });

        it('should return true when required types list is empty', () => {
            const formTypes = [FormType.NewInstallation, FormType.EnergyProduction];

            const result = isAllOfRequiredFormTypesSelected(formTypes, []);

            expect(result).toBe(true);
        });

        it('should handle empty form types array correctly', () => {
            expect(isAllOfRequiredFormTypesSelected([], [FormType.NewInstallation])).toBe(false);
        });

        it('should work with readonly array of required types', () => {
            const formTypes = [FormType.NewInstallation, FormType.EnergyProduction, FormType.Extension];
            const requiredTypes = [FormType.NewInstallation, FormType.EnergyProduction, FormType.Extension] as const;

            const result = isAllOfRequiredFormTypesSelected(formTypes, requiredTypes);

            expect(result).toBe(true);
        });
    });
});
