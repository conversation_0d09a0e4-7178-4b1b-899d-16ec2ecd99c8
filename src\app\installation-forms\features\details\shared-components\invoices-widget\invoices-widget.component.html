<p-panel [header]="'invoicesWidget.title' | translate" [toggleable]="true">
    <ng-template pTemplate="icons">
        <button
            type="button"
            pButton
            pRipple
            class="p-button-outlined"
            [disabled]="isProcessing"
            *ngIf="canCreateInvoices()"
            (click)="createNewInvoiceButtonClick()"
            size="small">
            {{ 'invoicesWidget.createInvoice' | translate }}
        </button>
    </ng-template>

    <app-create-invoice *ngIf="showCreateInvoice" (closeWidget)="closeCreateInvoice()"> </app-create-invoice>

    <div *ngIf="!hasInvoiceReadPermission" fxFlex="nogrow" fxLayoutAlign="start center">
        <p-tag severity="warn" value="{{ 'common.lackOfPermissions' | translate }}"> </p-tag>
    </div>

    <app-invoices-list *ngIf="hasInvoiceReadPermission" [canEditInvoices]="canEditInvoices()"> </app-invoices-list>
</p-panel>
