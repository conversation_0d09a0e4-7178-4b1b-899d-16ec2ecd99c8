name: $(date:yyyyMMdd)$(rev:.r)_$(SourceBranchName)

trigger:
  batch: true
  branches:
    include:
      - master
  paths:
    exclude:
      - pipelines

pool:
  vmImage: "ubuntu-22.04"

variables:
  - template: ci-variables-template.yaml

resources:
  repositories:
    - repository: commonPipelines
      type: git
      name: COMBAS/KMD.Elements.Pipelines

extends:
  template: ci/templates/sonar-front-job-template.yaml@commonPipelines
  parameters:
    sonarExclusions: "**/app/api/*.ts,**/assets/css/*.css"
    sonarCoverageExclusions: "**/app/api/*.ts,**/app/shared/components/**/*,**/app/installation-forms/features/details/**/*,**/app/installation-forms-settings/features/**/*"
    sonarCpdExclusions: "**/*.html"
    sonarLcovReportPath: "**/coverage/lcov.info"
    testCommand: $(testCommand)
    testResultPath: $(testResultPath)
    coverageResultPath: $(coverageResultPath)
