import { ValueListType, WorkOrderType } from 'src/app/api/installation-forms-client';

const workOrderTypeToValueList: Record<WorkOrderType, ValueListType> = {
    [WorkOrderType.General]: ValueListType.WorkOrderGeneralElectricityWorkDescriptionList,
    [WorkOrderType.InstallMeter]: ValueListType.WorkOrderInstallMeterElectricityWorkDescriptionList,
    [WorkOrderType.ReplaceMeter]: ValueListType.WorkOrderReplaceMeterElectricityWorkDescriptionList,
    [WorkOrderType.RemoveMeter]: ValueListType.WorkOrderRemoveMeterElectricityWorkDescriptionList
};

export const mapWorkOrderTypeToWorkDescriptionValueList = (workOrderType: WorkOrderType): ValueListType => {
    return workOrderTypeToValueList[workOrderType];
};
