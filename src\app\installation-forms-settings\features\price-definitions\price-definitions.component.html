<p-card>
    <app-markdown-viewer [markdownEnUs]="priceDefinitionsMarkdownEnUs" [markdownDaDK]="priceDefinitionsMarkdownDaDK">
    </app-markdown-viewer>
    <button
        pButton
        label="{{ 'formsSettings.priceDefinitions.addPriceDefinition' | translate }}"
        (click)="addEmptyPriceDefinitionClick()"
        class="p-button-outlined mt-20 mb-20"
        [disabled]="isProcessing || isAnyBeingEdited"
        *ngIf="hasUserFormConfigurationWritePermission"></button>
    <p-table [value]="internalPriceDefinitions" [loading]="isProcessing" [totalRecords]="internalPriceDefinitions.length">
        <ng-template pTemplate="header">
            <tr>
                <th id="displayName" style="width: 30%">
                    {{ 'formsSettings.priceDefinitions.displayName' | translate }}
                </th>
                <th id="externalPriceName" style="width: 30%">
                    {{ 'formsSettings.priceDefinitions.externalPriceName' | translate }}
                </th>
                <th id="supplyType" style="width: 30%">{{ 'formsSettings.priceDefinitions.supplyType' | translate }}</th>
                <th id="actions" style="width: 10%"></th>
            </tr>
        </ng-template>

        <ng-template pTemplate="body" let-priceDefinition>
            <tr class="zebra-item table-row">
                <td>
                    <ng-container *ngIf="!priceDefinition.isEdit">
                        {{ priceDefinition.displayName }}
                    </ng-container>
                    <ng-container *ngIf="priceDefinition.isEdit">
                        <input pInputText type="text" [(ngModel)]="priceDefinition.displayName" />
                        <small class="color-error" *ngIf="getDisplayNameError(priceDefinition)">
                            {{ getDisplayNameError(priceDefinition) }}
                        </small>
                    </ng-container>
                </td>
                <td>
                    <ng-container *ngIf="!priceDefinition.isEdit">
                        {{ priceDefinition.externalPriceName }}

                        <span class="color-error mt-2" *ngIf="!priceDefinition.externalPriceId">
                            <i class="fa-solid fa-circle-exclamation color-error"></i>
                            {{ 'formsSettings.priceDefinitions.externalPriceMissing' | translate }}
                        </span>
                    </ng-container>
                    <ng-container *ngIf="priceDefinition.isEdit">
                        <p-select
                            [appendTo]="overlayContainer"
                            [options]="externalPriceOptions"
                            [(ngModel)]="priceDefinition.externalPriceId"
                            (onChange)="onExternalPriceSelected($event.value, priceDefinition)">
                        </p-select>
                    </ng-container>
                </td>

                <td>
                    {{ priceDefinition.supplyType ?? 'enums.supplyType.' + priceDefinition.supplyType | translate: '' }}
                </td>

                <td *ngIf="hasUserFormConfigurationWritePermission">
                    <ng-container *ngIf="!priceDefinition.isEdit" class="flex gap-2">
                        <button
                            [disabled]="isProcessing || isAnyBeingEdited"
                            pButton
                            icon="fa-solid fa-pen"
                            class="p-button-outlined border-none"
                            (click)="editPriceDefinitionClick($event, priceDefinition)"></button>
                        <button
                            *ngIf="!priceDefinition.isEdit"
                            [disabled]="isProcessing || isAnyBeingEdited"
                            pButton
                            icon="fa-solid fa-trash-can"
                            class="p-button-outlined border-none"
                            (click)="deletePriceDefinitionClick($event, priceDefinition)"></button>
                    </ng-container>
                    <ng-container *ngIf="priceDefinition.isEdit" class="flex gap-2">
                        <button
                            [disabled]="!isValidPriceDefinition(priceDefinition)"
                            pButton
                            icon="fa-solid fa-check"
                            class="p-button-outlined border-none"
                            (click)="savePriceDefinition(priceDefinition)"></button>
                        <button
                            pButton
                            icon="fa-solid fa-xmark"
                            class="p-button-outlined border-none"
                            (click)="cancelEdit(priceDefinition)"></button>
                    </ng-container>
                </td>
            </tr>
        </ng-template>
    </p-table>
</p-card>

<app-confirmation-dialog
    [headerKey]="'formsSettings.priceDefinitions.deleteConfirmationTitle'"
    [messageKey]="'formsSettings.priceDefinitions.deleteConfirmationMessage'"
    [visible]="!!attemptedAction"
    (confirmEvent)="onConfirmationDialogYesButtonClick()"
    (cancelEvent)="onConfirmationDialogNoButtonClick()">
</app-confirmation-dialog>

<p-toast [key]="priceDefinitionsTabName"></p-toast>

<p-toast #genericMessagesToast></p-toast>

<div #overlayContainer></div>
