import { Component, Input, ViewEncapsulation } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Component({
    selector: 'app-markdown-viewer',
    templateUrl: './markdown-viewer.component.html',
    styleUrl: './markdown-viewer.component.scss',
    encapsulation: ViewEncapsulation.ShadowDom, // We want to keep styles provided by markdown and not rewrited by webpack or global styles
    standalone: false
})
export class MarkdownViewerComponent {
    @Input() markdownEnUs!: string;
    @Input() markdownDaDK!: string;

    currentLang: string;

    constructor(private readonly translateService: TranslateService) {
        this.currentLang = this.translateService.currentLang;
    }
}
