import { TestBed, discardPeriodicTasks, fakeAsync, tick } from '@angular/core/testing';
import { TabsService } from '@kmd-elements-ui/tabs';
import { TranslateService } from '@ngx-translate/core';
import { Subject, of, throwError } from 'rxjs';
import { InstallationFormsClient, PendingUpdateAreaType } from 'src/app/api/installation-forms-client';
import { environment } from 'src/environments/environment';
import { FormDataService } from '../form-data/form-data.service';
import { MessageServiceHelper } from '../message/message.service';
import { AutomaticFormRefreshService } from './automatic-form-refresh.service';

// Mock UUID to make testing deterministic
jest.mock('uuid', () => ({
    v4: () => 'test-uuid'
}));

describe('AutomaticFormRefreshService', () => {
    let service: AutomaticFormRefreshService;
    let mockClient: InstallationFormsClient;
    let mockMessageService: MessageServiceHelper;
    let mockTranslateService: TranslateService;
    let mockTabsService: TabsService;
    let mockFormDataService: FormDataService;
    let tabActiveSubject: Subject<boolean>;

    const testFormId = 'test-form-id';
    const testTabId = 'test-tab-id';

    const mockCommonTranslations = {
        errorRefreshingDetail: 'Error refreshing detail',
        staleDataDetail: 'Stale data detail'
    };

    const mockPendingUpdatesResponse = {
        result: {
            pendingUpdateAreas: [PendingUpdateAreaType.Notes],
            latestChangeTimestamp: new Date()
        }
    };

    // Save original environment settings
    let originalRefreshSettings: any;

    beforeEach(() => {
        // Store the original environment settings
        originalRefreshSettings = { ...environment.automaticRefresh };

        // Override environment settings for testing
        environment.automaticRefresh = {
            initialRefreshIntervalInSeconds: 1,
            intervalExtensions: [
                { afterSeconds: 10, newIntervalInSeconds: 2 },
                { afterSeconds: 30, newIntervalInSeconds: 5 }
            ]
        };

        tabActiveSubject = new Subject<boolean>();

        // Create mocks
        mockClient = {
            getPendingUpdates: jest.fn().mockReturnValue(of(mockPendingUpdatesResponse))
        } as Partial<InstallationFormsClient> as InstallationFormsClient;

        mockMessageService = {
            showError: jest.fn(),
            showWarning: jest.fn()
        } as Partial<MessageServiceHelper> as MessageServiceHelper;

        mockTranslateService = {
            instant: jest.fn().mockReturnValue(mockCommonTranslations)
        } as Partial<TranslateService> as TranslateService;

        mockTabsService = {
            isTabActiveListener: jest.fn().mockReturnValue(tabActiveSubject)
        } as Partial<TabsService> as TabsService;

        mockFormDataService = {
            isProcessing: false
        } as Partial<FormDataService> as FormDataService;

        TestBed.configureTestingModule({
            providers: [
                AutomaticFormRefreshService,
                { provide: InstallationFormsClient, useValue: mockClient },
                { provide: MessageServiceHelper, useValue: mockMessageService },
                { provide: TranslateService, useValue: mockTranslateService },
                { provide: TabsService, useValue: mockTabsService },
                { provide: FormDataService, useValue: mockFormDataService }
            ]
        });

        service = TestBed.inject(AutomaticFormRefreshService);
    });

    // Properly placed afterEach hook at the top level
    afterEach(() => {
        environment.automaticRefresh = originalRefreshSettings;
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
        expect(mockTranslateService.instant).toHaveBeenCalledWith('common');
    });

    describe('startRefreshingForm', () => {
        it('should set up form refreshing', () => {
            const stopSpy = jest.spyOn(service, 'stopRefreshingForm');

            service.startRefreshingForm(testFormId, testTabId);

            expect(stopSpy).toHaveBeenCalled();
            expect(mockTabsService.isTabActiveListener).toHaveBeenCalledWith(testTabId);
        });

        it('should start polling and fetch data', fakeAsync(() => {
            const fetchSpy = jest.spyOn(service, 'fetchData');

            service.startRefreshingForm(testFormId, testTabId);
            tick(1000); // Initial poll happens after 1 second

            expect(fetchSpy).toHaveBeenCalled();

            // Clean up all periodic timers at the end of the test
            discardPeriodicTasks();
        }));

        it('should not fetch data when tab is inactive', fakeAsync(() => {
            const fetchSpy = jest.spyOn(service, 'fetchData');

            service.startRefreshingForm(testFormId, testTabId);
            tabActiveSubject.next(false); // Set tab to inactive
            tick(1000);

            expect(fetchSpy).not.toHaveBeenCalled();

            // Clean up all periodic timers at the end of the test
            discardPeriodicTasks();
        }));

        it('should not fetch data when form is processing', fakeAsync(() => {
            const fetchSpy = jest.spyOn(service, 'fetchData');
            (mockFormDataService as any).isProcessing = true;

            service.startRefreshingForm(testFormId, testTabId);
            tick(1000);

            expect(fetchSpy).not.toHaveBeenCalled();

            // Clean up all periodic timers at the end of the test
            discardPeriodicTasks();
        }));
    });

    describe('stopRefreshingForm', () => {
        it('should unsubscribe from all subscriptions', () => {
            const unsubscribeSpy = jest.spyOn(service.subscription, 'unsubscribe');

            service.stopRefreshingForm();

            expect(unsubscribeSpy).toHaveBeenCalled();
        });
    });

    describe('subscribeForRefresh and unsubscribeForRefresh', () => {
        it('should register and unregister refresh callbacks', () => {
            const callback = jest.fn();
            const area = PendingUpdateAreaType.Notes;

            service.subscribeForRefresh(area, callback);
            service.areasChanged([area]);

            expect(callback).toHaveBeenCalled();

            // Reset and test unsubscribe
            callback.mockReset();
            service.unsubscribeForRefresh(area);
            service.areasChanged([area]);

            expect(callback).not.toHaveBeenCalled();
        });
    });

    describe('restartPolling', () => {
        it('should reset counters and enable polling', () => {
            service.restartPolling();

            // We can't directly test private properties, but we can test the behavior
            expect(service['pollingEnabled']).toBe(true);

            // Test private properties using any type assertion
            expect((service as any).secondsCounter).toBe(-1);
            expect((service as any).failuresCounter).toBe(0);
        });
    });

    describe('forceRefresh', () => {
        it('should emit forceRefreshRequested and restart polling', () => {
            const forceRefreshSpy = jest.spyOn(service.forceRefreshRequested$, 'next');
            const restartPollingSpy = jest.spyOn(service, 'restartPolling');

            service.forceRefresh();

            expect(forceRefreshSpy).toHaveBeenCalled();
            expect(restartPollingSpy).toHaveBeenCalled();
            expect(service['latestChangeTimestamp']).toBeUndefined();
        });
    });

    describe('areasChanged', () => {
        it('should call registered callbacks for changed areas', () => {
            const callback1 = jest.fn();
            const callback2 = jest.fn();

            service.subscribeForRefresh(PendingUpdateAreaType.Notes, callback1);
            service.subscribeForRefresh(PendingUpdateAreaType.MasterDataProcesses, callback2);

            service.areasChanged([PendingUpdateAreaType.Notes]);

            expect(callback1).toHaveBeenCalled();
            expect(callback2).not.toHaveBeenCalled();
        });
    });

    describe('fetchData', () => {
        it('should call client with correct parameters', () => {
            service['formId'] = testFormId;
            service.fetchData();

            expect(mockClient.getPendingUpdates).toHaveBeenCalledWith(testFormId, 'test-uuid', undefined, undefined);
        });

        it('should update latestChangeTimestamp on success', () => {
            service['formId'] = testFormId;
            service.fetchData();

            expect(service['latestChangeTimestamp']).toBe(mockPendingUpdatesResponse.result.latestChangeTimestamp);
        });

        it('should call areasChanged when there are pending updates', () => {
            const areasChangedSpy = jest.spyOn(service, 'areasChanged');
            service['formId'] = testFormId;

            service.fetchData();

            expect(areasChangedSpy).toHaveBeenCalledWith(mockPendingUpdatesResponse.result.pendingUpdateAreas);
        });

        it('should increment failures counter on error', () => {
            (mockClient.getPendingUpdates as jest.Mock).mockReturnValueOnce(throwError(() => new Error('Test error')));
            service['formId'] = testFormId;

            service.fetchData();

            expect((service as any).failuresCounter).toBe(1);
        });

        it('should show error message after max failures', () => {
            (mockClient.getPendingUpdates as jest.Mock).mockReturnValue(throwError(() => new Error('Test error')));
            service['formId'] = testFormId;

            // Simulate max failures
            (service as any).failuresCounter = AutomaticFormRefreshService.MAX_FAILURES_UNTIL_REFRESH_STOPS - 1;
            service.fetchData();

            expect(mockMessageService.showError).toHaveBeenCalledWith({
                detail: mockCommonTranslations.errorRefreshingDetail,
                key: testFormId
            });
        });
    });

    describe('notifyConflictingChangeOccurred', () => {
        it('should show warning and disable polling', () => {
            service['formId'] = testFormId;
            service.notifyConflictingChangeOccurred();

            expect(mockMessageService.showWarning).toHaveBeenCalledWith({
                detail: mockCommonTranslations.staleDataDetail,
                key: testFormId,
                life: AutomaticFormRefreshService.CONFLICTING_CHANGE_MESSAGE_LIFE_IN_SECONDS * 1000
            });

            expect(service['pollingEnabled']).toBe(false);
        });
    });

    describe('tabActiveStateChanged', () => {
        it('should restart polling when tab becomes active', () => {
            const restartPollingSpy = jest.spyOn(service, 'restartPolling');

            service.tabActiveStateChanged(true);

            expect(restartPollingSpy).toHaveBeenCalled();
            expect(service['pollingEnabled']).toBe(true);
        });

        it('should disable polling when tab becomes inactive', () => {
            service.tabActiveStateChanged(false);

            expect(service['pollingEnabled']).toBe(false);
        });
    });
});
