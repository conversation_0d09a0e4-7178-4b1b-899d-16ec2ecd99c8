import { MeteringPoint } from 'src/app/api/installation-forms-client';
import { createMeteringPoint } from './create-metering-point';

describe('createMeteringPoint', () => {
    it('should create a MeteringPoint when meteringPointId is provided', () => {
        const input = { meteringPointId: 'MP123' };
        const result = createMeteringPoint(input);

        expect(result).toBeInstanceOf(MeteringPoint);
        expect(result?.meteringPointId).toBe('MP123');
        expect(result?.meteringPointVersionId).toBe('');
    });

    it('should create a MeteringPoint when meteringPointVersionId is provided', () => {
        const input = { meteringPointVersionId: 'MPV456' };
        const result = createMeteringPoint(input);

        expect(result).toBeInstanceOf(MeteringPoint);
        expect(result?.meteringPointId).toBe('');
        expect(result?.meteringPointVersionId).toBe('MPV456');
    });

    it('should create a MeteringPoint when both IDs are provided', () => {
        const input = { meteringPointId: 'MP123', meteringPointVersionId: 'MPV456' };
        const result = createMeteringPoint(input);

        expect(result).toBeInstanceOf(MeteringPoint);
        expect(result?.meteringPointId).toBe('MP123');
        expect(result?.meteringPointVersionId).toBe('MPV456');
    });

    it('should return undefined when neither ID is provided', () => {
        const input = { someOtherProp: 'value' };
        const result = createMeteringPoint(input);

        expect(result).toBeUndefined();
    });

    it('should handle null or undefined input', () => {
        expect(createMeteringPoint(null)).toBeUndefined();
        expect(createMeteringPoint(undefined)).toBeUndefined();
    });

    it('should handle empty object input', () => {
        expect(createMeteringPoint({})).toBeUndefined();
    });

    it('should handle input with empty string IDs', () => {
        const input = { meteringPointId: '', meteringPointVersionId: '' };
        const result = createMeteringPoint(input);

        expect(result).toBeUndefined();
    });
});
