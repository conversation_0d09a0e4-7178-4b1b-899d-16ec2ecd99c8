import { ValueListType } from 'src/app/api/installation-forms-client';
import { ColumnTransformationType } from 'src/app/core/enums/column-transformation-type';
import { SearchOperator } from 'src/app/core/enums/search-operator';
import { ColumnFilterType } from '../../../../core/enums/column-filter-type';

export interface InstallationFormsListColumn {
    field: string;
    header: string;
    translationPath?: string;
    columnFilterType: ColumnFilterType;
    columnTransformationType: ColumnTransformationType;
    enumReference?: any;
    valueListType?: ValueListType;
    isVisible: boolean;
    operators?: SearchOperator[];
}
