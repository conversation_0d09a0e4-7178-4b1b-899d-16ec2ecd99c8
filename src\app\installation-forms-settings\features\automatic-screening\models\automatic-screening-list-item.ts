import {
    AutomationVoltageLevel,
    ChangeMeterChangeReason,
    FormCategory,
    FormState,
    FormType,
    GroundingMethod,
    MeterReturnOption,
    MeterSize,
    ScopeOfDeliveryRange,
    TerminationScope
} from 'src/app/api/installation-forms-client';

export interface AutomaticScreeningListItem {
    id: string;
    order: number;
    displayName: string;
    formTypes?: FormType[];
    formStates?: FormState[];
    formCategories?: FormCategory[];
    voltageLevels?: AutomationVoltageLevel[];
    scopeOfDelivery?: string;
    terminationScope?: TerminationScope;
    meterReturnOptionTypes?: MeterReturnOption[];
    meterPlacementCodes?: string[];
    meterSize?: MeterSize;
    groundingMethods?: GroundingMethod[];
    reasonsForChange?: ChangeMeterChangeReason[];
}
