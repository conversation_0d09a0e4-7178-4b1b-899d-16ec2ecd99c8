import { TestBed } from '@angular/core/testing';
import { TranslateService } from '@ngx-translate/core';
import { TaskGroupPathTooltipPipe } from './task-group-path-tooltip.pipe';

describe('TaskGroupPathTooltipPipe', () => {
    let pipe: TaskGroupPathTooltipPipe;
    let translateService: TranslateService;
    let translateInstantSpy: jest.SpyInstance;

    beforeEach(() => {
        const translateServiceMock = {
            instant: jest.fn((key: string) => {
                return 'Translated ' + key;
            })
        };

        TestBed.configureTestingModule({
            providers: [TaskGroupPathTooltipPipe, { provide: TranslateService, useValue: translateServiceMock }]
        });

        translateService = TestBed.inject(TranslateService);
        translateInstantSpy = jest.spyOn(translateService, 'instant');
        pipe = TestBed.inject(TaskGroupPathTooltipPipe);
    });

    it('should create an instance', () => {
        expect(pipe).toBeTruthy();
    });

    it('should return empty string for undefined path', () => {
        const result = pipe.transform(undefined);
        expect(result).toBe('');
    });

    it('should return empty string for null path', () => {
        const result = pipe.transform(null as any);
        expect(result).toBe('');
    });

    it('should return empty string for empty path', () => {
        const result = pipe.transform('');
        expect(result).toBe('');
    });

    it('should translate path using the correct key format', () => {
        const path = 'somePath';
        const result = pipe.transform(path);

        expect(translateService.instant).toHaveBeenCalledWith('tasksWidget.taskGroups.' + path);
        expect(result).toBe('Translated tasksWidget.taskGroups.somePath');
    });

    it('should handle different paths correctly', () => {
        const paths = ['path1', 'path2', 'path3'];

        paths.forEach((path) => {
            translateInstantSpy.mockImplementation((key: string) => {
                if (key === 'tasksWidget.taskGroups.' + path) {
                    return `Translation for ${path}`;
                }
                return 'Translated ' + key;
            });

            const result = pipe.transform(path);
            expect(result).toBe(`Translation for ${path}`);
            expect(translateService.instant).toHaveBeenCalledWith('tasksWidget.taskGroups.' + path);
        });
    });
});
