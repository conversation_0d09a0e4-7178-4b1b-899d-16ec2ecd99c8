import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AuthorizationService } from '@kmd-elements-ui/authorization';
import {
    KeyActions,
    KeyboardShortcuts,
    KeyboardShortcutsService,
    KeyboardShortcutsSubscriptionManager
} from '@kmd-elements-ui/shortcuts';
import { TabsService } from '@kmd-elements-ui/tabs';
import { TranslateService } from '@ngx-translate/core';
import { TableRowReorderEvent } from 'primeng/table';
import { BehaviorSubject, catchError, EMPTY, filter, finalize, of, Subscription, switchMap, tap } from 'rxjs';
import {
    AutomaticScreeningRule,
    InstallationFormsClient,
    ReorderModel,
    ValueListType
} from 'src/app/api/installation-forms-client';
import { Permissions } from 'src/app/core/constants/permissions';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { ValueListsService } from 'src/app/core/services/value-lists/value-lists.service';
import { v4 as uuidv4 } from 'uuid';
import { RulesListColumn } from '../models/rules-list-column';
import { AutomaticScreeningCreateComponent } from './automatic-screening-create/automatic-screening-create.component';
import { AutomaticScreeningEditComponent } from './automatic-screening-edit/automatic-screening-edit.component';
import {
    automaticScreeningMarkdownDaDK,
    automaticScreeningMarkdownEnUs,
    automaticScreeningTabName,
    automaticScreeningTranslationPath
} from './constants/automatic-screening.consts';
import { AUTOMATIC_SCREENING_COLUMNS } from './constants/columns';
import { AutomaticScreeningListItem } from './models/automatic-screening-list-item';
import { AutomaticScreeningMapper } from './services/automatic-screening-mapper';

@Component({
    selector: 'app-automatic-screening',
    templateUrl: './automatic-screening.component.html',
    styleUrl: './automatic-screening.component.scss',
    standalone: false
})
export class AutomaticScreeningComponent implements KeyboardShortcutsSubscriptionManager, OnInit, OnDestroy {
    private widgetTranslations: any;

    private _isCreating: boolean = false;
    private _ruleInEdit?: AutomaticScreeningRule;
    private _isReordered: boolean = false;

    get isCreating(): boolean {
        return this._isCreating;
    }

    set isCreating(value: boolean) {
        this._isCreating = value;
        this.recalculateDirty();
    }

    get ruleInEdit(): AutomaticScreeningRule | undefined {
        return this._ruleInEdit;
    }

    set ruleInEdit(value: AutomaticScreeningRule | undefined) {
        this._ruleInEdit = value;
        this.recalculateDirty();
    }

    get isReordered(): boolean {
        return this._isReordered;
    }

    set isReordered(value: boolean) {
        this._isReordered = value;
        this.recalculateDirty();
    }

    automaticScreeningRules: AutomaticScreeningListItem[] = [];
    rawAutomaticScreeningRules: AutomaticScreeningRule[] = [];

    subscription: Subscription = new Subscription();

    automaticScreeningMarkdownEnUs = automaticScreeningMarkdownEnUs;
    automaticScreeningMarkdownDaDK = automaticScreeningMarkdownDaDK;

    isProcessing: boolean = false;

    dirty$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

    automaticScreeningTabName = automaticScreeningTabName;

    hasUserFormConfigurationWritePermission: boolean = false;

    private _tabId?: string;

    columns: RulesListColumn[] = AUTOMATIC_SCREENING_COLUMNS;

    private keyEventsSubscription: Subscription = new Subscription();

    @ViewChild('automaticScreeningCreate') automaticScreeningCreate?: AutomaticScreeningCreateComponent;
    @ViewChild('automaticScreeningEdit') automaticScreeningEdit?: AutomaticScreeningEditComponent;

    keyActions: KeyActions = {
        [KeyboardShortcuts.ShiftPlusN]: () => {
            this.startCreating();
        },
        [KeyboardShortcuts.Escape]: () => {
            this.tabsService.close();
        },
        [KeyboardShortcuts.Enter]: () => {
            if (this.ruleInEdit) {
                this.automaticScreeningEdit?.saveEditedClick();
                return;
            }

            if (this.isCreating) {
                this.automaticScreeningCreate?.createClick();
                return;
            }

            if (this.isReordered) {
                this.saveReorder();
            }
        }
    };
    attemptedAction: (() => void) | null = null;
    private ruleToDelete?: AutomaticScreeningListItem;

    private originalRuleOrder: string[] = [];

    alreadyUsedDisplayNames: string[] = [];

    constructor(
        private readonly client: InstallationFormsClient,
        private readonly translateService: TranslateService,
        private readonly messageServiceHelper: MessageServiceHelper,
        private readonly tabsService: TabsService,
        private readonly activatedRoute: ActivatedRoute,
        private readonly authService: AuthorizationService,
        private readonly keyboardShortcutsService: KeyboardShortcutsService,
        private readonly automaticScreeningMapper: AutomaticScreeningMapper,
        private readonly valueListService: ValueListsService
    ) {
        this.addTab();
    }

    ngOnInit(): void {
        this.widgetTranslations = this.translateService.instant(automaticScreeningTranslationPath);

        this.authService
            .hasPermissions([Permissions.formConfiguration.write])
            .subscribe((x) => (this.hasUserFormConfigurationWritePermission = x));

        this.registerRefreshButtonListener();
        this.loadScreeningWithTemplates();
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    subscribeKeyShortcuts(): void {
        this.keyEventsSubscription = this.keyboardShortcutsService.registerKeyboardShortcuts(this.keyActions).subscribe();
    }

    unsubscribeKeyShortcuts(): void {
        this.keyEventsSubscription.unsubscribe();
    }

    private loadScreeningWithTemplates() {
        this.isProcessing = true;
        this.subscription.add(
            this.client
                .getAutomaticScreeningRules(uuidv4())
                .pipe(
                    catchError((_) => {
                        this.messageServiceHelper.showError({
                            key: automaticScreeningTabName,
                            detail: this.widgetTranslations['getScreeningRulesError']
                        });
                        return EMPTY;
                    })
                )
                .pipe(finalize(() => (this.isProcessing = false)))
                .subscribe({
                    next: (response) => {
                        const sortedRules = [...response.result];
                        sortedRules.sort((a, b) => a.order - b.order);
                        this.rawAutomaticScreeningRules = sortedRules;
                        this.automaticScreeningRules = sortedRules.map((rule) =>
                            this.automaticScreeningMapper.mapToAutomaticScreeningListItem(rule)
                        );
                        this.originalRuleOrder = this.automaticScreeningRules.map((x) => x.id);
                        this.alreadyUsedDisplayNames = this.automaticScreeningRules.map((x) => x.displayName);
                    }
                })
        );
    }

    protected editRule(event: MouseEvent, rule: AutomaticScreeningListItem) {
        event.stopPropagation();
        this.ruleInEdit = this.rawAutomaticScreeningRules.find((r) => r.id === rule.id);
        this.isCreating = false;
    }

    protected startCreating() {
        this.isCreating = true;
        this.ruleInEdit = undefined;
    }

    protected cancelCreating() {
        this.isCreating = false;
    }

    protected cancelEditing() {
        this.ruleInEdit = undefined;
    }

    protected onRuleCreated(rule: AutomaticScreeningRule) {
        this.isCreating = false;
        this.rawAutomaticScreeningRules.push(rule);
        this.automaticScreeningRules.push(this.automaticScreeningMapper.mapToAutomaticScreeningListItem(rule));
        this.originalRuleOrder.push(rule.id);
        this.alreadyUsedDisplayNames.push(rule.displayName);
    }

    protected onRuleEdited(updatedRule: AutomaticScreeningRule) {
        const editedIndex = this.automaticScreeningRules.findIndex((r) => r.id === updatedRule.id);
        if (editedIndex === -1) {
            return;
        }

        this.alreadyUsedDisplayNames.splice(
            this.alreadyUsedDisplayNames.indexOf(this.ruleInEdit!.displayName),
            1,
            updatedRule.displayName
        );
        this.rawAutomaticScreeningRules[editedIndex] = updatedRule;
        this.automaticScreeningRules[editedIndex] = this.automaticScreeningMapper.mapToAutomaticScreeningListItem(updatedRule);
        this.ruleInEdit = undefined;
    }

    private addTab() {
        this._tabId = this.tabsService.addOrUpdateTab(this.activatedRoute, {
            icon$: of('fas fa-th-list'),
            areaLabel$: of(this.translateService.instant(`${automaticScreeningTranslationPath}.tabAreaLabel`)),
            label$: of(this.translateService.instant(`${automaticScreeningTranslationPath}.tabLabel`)),
            tooltip$: of(this.translateService.instant(`${automaticScreeningTranslationPath}.tabLabel`)),
            dirty$: this.dirty$,
            canReload$: of(true)
        });
    }

    registerRefreshButtonListener(): void {
        this.subscription.add(
            this.tabsService.reloadButtonClicked$
                .pipe(
                    filter((tabId) => tabId === this._tabId),
                    switchMap((tabId) => this.tabsService.reload(tabId)),
                    filter((canBeReloaded) => canBeReloaded),
                    tap(() => {
                        this.loadScreeningWithTemplates();
                        this.valueListService.fetchValueLists();
                    })
                )
                .subscribe(() => {
                    this.tabsService.tabReloaded(this._tabId as string);
                    this.undoChanges();
                })
        );
    }

    undoChanges() {
        this.cancelCreating();
        this.cancelEditing();
        this.cancelReorder();
    }

    onRowReorder(_: TableRowReorderEvent) {
        this.setIsReordered();
    }

    setIsReordered() {
        this.isReordered = !this.automaticScreeningRules.every((rule, index) => rule.id === this.originalRuleOrder[index]);
    }

    cancelReorder() {
        this.automaticScreeningRules = this.originalRuleOrder.map(
            (id) => this.automaticScreeningRules.find((rule) => rule.id === id)!
        );
        this.setIsReordered();
    }

    protected deleteRule(event: MouseEvent, rule: AutomaticScreeningListItem) {
        event.stopPropagation();
        this.ruleToDelete = rule;
        this.attemptedAction = () => this.performDelete();
    }

    private performDelete() {
        if (!this.ruleToDelete) {
            return;
        }

        this.isProcessing = true;
        this.subscription.add(
            this.client
                .deleteAutomaticScreeningRuleById(this.ruleToDelete.id, uuidv4())
                .pipe(
                    finalize(() => {
                        this.isProcessing = false;
                        this.ruleToDelete = undefined;
                    })
                )
                .subscribe({
                    complete: () => {
                        this.messageServiceHelper.showSuccess({
                            key: automaticScreeningTabName
                        });
                        this.automaticScreeningRules = this.automaticScreeningRules.filter((r) => r.id !== this.ruleToDelete?.id);
                        this.originalRuleOrder = this.originalRuleOrder.filter((id) => id !== this.ruleToDelete?.id);
                        this.rawAutomaticScreeningRules = this.rawAutomaticScreeningRules.filter(
                            (r) => r.id !== this.ruleToDelete?.id
                        );
                        if (this.ruleInEdit?.id === this.ruleToDelete?.id) {
                            this.ruleInEdit = undefined;
                        }

                        this.alreadyUsedDisplayNames = this.alreadyUsedDisplayNames.filter(
                            (name) => name !== this.ruleToDelete?.displayName
                        );
                    },
                    error: (_error) => {
                        this.messageServiceHelper.showError({
                            key: automaticScreeningTabName,
                            detail: this.widgetTranslations['automaticScreeningRuleDeleteError']
                        });
                    }
                })
        );
    }

    onConfirmationDialogYesButtonClick() {
        if (this.attemptedAction) {
            this.attemptedAction();
        }
        this.attemptedAction = null;
    }

    onConfirmationDialogNoButtonClick() {
        this.attemptedAction = null;
        this.ruleToDelete = undefined;
    }

    saveReorder() {
        this.isProcessing = true;
        this.subscription.add(
            this.client
                .reorderAutomaticScreeningRules(
                    uuidv4(),
                    new ReorderModel({
                        reorderedIds: this.automaticScreeningRules.map((x) => x.id)
                    })
                )
                .pipe(finalize(() => (this.isProcessing = false)))
                .subscribe({
                    complete: () => {
                        this.messageServiceHelper.showSuccess({
                            key: automaticScreeningTabName
                        });
                        this.originalRuleOrder = this.automaticScreeningRules.map((x) => x.id);
                        this.isReordered = false;
                    },
                    error: (_error) => {
                        this.messageServiceHelper.showError({
                            key: automaticScreeningTabName,
                            detail: this.widgetTranslations['automaticScreeningRuleReorderError']
                        });
                    }
                })
        );
    }

    recalculateDirty() {
        this.dirty$.next(this.isCreating || this.isReordered || !!this.ruleInEdit);
    }

    getValueListType(colName: string): ValueListType | undefined {
        return colName === 'meterPlacementCodes' ? ValueListType.MeterFramePlacementCode : undefined;
    }
}
