<div #overlayContainer></div>
<ng-container [formGroup]="connectionForm">
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="showPhaseCount">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="connectionForm.controls['phaseCount']" for="phaseCount">
                {{ 'technicalInformationWidget.connection.phaseCount' | translate }}
            </label>
        </div>
        <div class="zebra-edit" [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70" fxFlex.lt-md="50">
            <p-select
                id="phaseCount"
                [options]="phaseCountOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="phaseCount">
            </p-select>
            <small [controlValidationErrors]="connectionForm.controls['phaseCount']" class="p-error"></small>
        </div>
        <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields"></div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="connectionForm.controls['cabinetNumber']" for="cabinetNumber">
                {{ 'technicalInformationWidget.connection.cabinetNumber' | translate }}
            </label>
            <app-icon-master-data-path [path]="formDataService.masterDataToCompareResult?.cabinetNumber?.path">
            </app-icon-master-data-path>
        </div>

        <div class="zebra-edit" [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70" fxFlex.lt-md="50">
            <input id="cabinetNumber" type="text" pInputText maxlength="100" formControlName="cabinetNumber" />
            <small [controlValidationErrors]="connectionForm.controls['cabinetNumber']" class="p-error"></small>
        </div>
        <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields">
            <input
                id="cabinetNumberMD"
                type="text"
                pInputText
                [value]="formDataService.masterDataToCompareResult?.cabinetNumber?.value ?? ''"
                [disabled]="true" />
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="connectionForm.controls['transformerStationNumber']" for="transformerStationNumber">
                {{ 'technicalInformationWidget.connection.transformerStationNumber' | translate }}
            </label>
            <app-icon-master-data-path [path]="formDataService.masterDataToCompareResult?.transformerStationNumber?.path">
            </app-icon-master-data-path>
        </div>
        <div class="zebra-edit" [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70" fxFlex.lt-md="50">
            <input
                id="transformerStationNumber"
                type="text"
                pInputText
                maxlength="100"
                formControlName="transformerStationNumber" />
            <small [controlValidationErrors]="connectionForm.controls['transformerStationNumber']" class="p-error"></small>
        </div>
        <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields">
            <input
                id="transformerStationNumberMD"
                type="text"
                pInputText
                [value]="formDataService.masterDataToCompareResult?.transformerStationNumber?.value ?? ''"
                [disabled]="true" />
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="connectionForm.controls['groundingMethod']" for="groundingMethod">
                {{ 'technicalInformationWidget.connection.groundingMethod' | translate }}
            </label>
            <app-icon-master-data-path [path]="formDataService.masterDataToCompareResult?.groundingMethod?.path">
            </app-icon-master-data-path>
        </div>
        <div class="zebra-edit" [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70" fxFlex.lt-md="50">
            <p-select
                id="groundingMethod"
                [options]="groundingMethodOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="groundingMethod">
            </p-select>
            <small [controlValidationErrors]="connectionForm.controls['groundingMethod']" class="p-error"></small>
        </div>
        <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields">
            <input
                id="groundingMethodMD"
                type="text"
                pInputText
                [value]="formDataService.masterDataToCompareResult?.groundingMethod?.value ?? ''"
                [disabled]="true" />
        </div>
    </div>

    <div class="zebra-item align-items-center" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="connectionForm.controls['protectionTypeOfPreProtection']" for="protectionTypeOfPreProtection">
                {{ 'technicalInformationWidget.connection.protectionTypeOfPreProtection' | translate }}
            </label>
            <app-icon-master-data-path [path]="formDataService.masterDataToCompareResult?.preProtectionType?.path">
            </app-icon-master-data-path>
        </div>
        <div class="zebra-edit" [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70" fxFlex.lt-md="50">
            <p-select
                id="protectionTypeOfPreProtection"
                [options]="protectionTypeOfPreProtectionOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="protectionTypeOfPreProtection">
            </p-select>
            <small [controlValidationErrors]="connectionForm.controls['protectionTypeOfPreProtection']" class="p-error"></small>
        </div>
        <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields">
            <input
                id="protectionTypeOfPreProtectionMD"
                type="text"
                pInputText
                [value]="formDataService.masterDataToCompareResult?.preProtectionType?.value ?? ''"
                [disabled]="true" />
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="connectionForm.controls['fuseSizeOfPreProtection']" for="fuseSizeOfPreProtection">
                {{ 'technicalInformationWidget.connection.fuseSizeOfPreProtection' | translate }}
            </label>
            <app-icon-master-data-path [path]="formDataService.masterDataToCompareResult?.preProtectionSize?.path">
            </app-icon-master-data-path>
        </div>
        <div class="zebra-edit" [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70" fxFlex.lt-md="50">
            <p-inputNumber id="fuseSizeOfPreProtection" formControlName="fuseSizeOfPreProtection"></p-inputNumber>
            <small [controlValidationErrors]="connectionForm.controls['fuseSizeOfPreProtection']" class="p-error"></small>
        </div>
        <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields">
            <p-inputNumber
                id="fuseSizeOfPreProtectionMD"
                [ngModel]="formDataService.masterDataToCompareResult?.preProtectionSize?.value"
                [ngModelOptions]="{ standalone: true }"
                [disabled]="true">
            </p-inputNumber>
        </div>
    </div>
</ng-container>

<ng-container [formGroup]="branchLineForm">
    <div class="zebra-item align-items-center" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="branchLineForm.controls['type']" for="type">
                {{ 'technicalInformationWidget.branchLine.type' | translate }}
            </label>
            <app-icon-master-data-path [path]="formDataService.masterDataToCompareResult?.branchLineTypeShared?.path">
            </app-icon-master-data-path>
        </div>
        <div class="zebra-edit" [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70" fxFlex.lt-md="50">
            <p-select
                id="type"
                [options]="branchLineTypeOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="type">
            </p-select>
            <small [controlValidationErrors]="branchLineForm.controls['type']" class="p-error"></small>
        </div>
        <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields">
            <p-selectButton
                [options]="yesNoOptions"
                id="branchLineTypeSharedMD"
                optionLabel="label"
                optionValue="value"
                [ngModel]="formDataService.masterDataToCompareResult?.branchLineTypeShared?.value"
                [ngModelOptions]="{ standalone: true }"
                [disabled]="true">
            </p-selectButton>
        </div>
    </div>

    <div
        class="zebra-item"
        fxLayout="row"
        fxLayout.lt-sm="column"
        fxLayoutGap="15px"
        *ngIf="areBranchLineTypeIdentifiersVisible()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="branchLineForm.controls['branchLineMeterFrame']" for="branchLineMeterFrame">
                {{ 'technicalInformationWidget.branchLine.meterFrame' | translate }}
            </label>
        </div>
        <div class="zebra-edit" [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70" fxFlex.lt-md="50">
            <app-meter-frame-search
                id="branchLineMeterFrame"
                formControlName="branchLineMeterFrame"
                [options]="branchLineMeterFrameOptions"
                [showValidForSharedBranchLineOnly]="true"
                [showWithValidElectricityPurposeOnly]="true" />
            <small [controlValidationErrors]="branchLineForm.controls['branchLineMeterFrame']" class="p-error"></small>
        </div>
        <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields"></div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label
                [labelRequired]="branchLineForm.controls['protectionTypeOfMainProtection']"
                for="protectionTypeOfMainProtection">
                {{ 'technicalInformationWidget.branchLine.protectionTypeOfMainProtection' | translate }}
            </label>
            <app-icon-master-data-path [path]="formDataService.masterDataToCompareResult?.mainProtectionType?.path">
            </app-icon-master-data-path>
        </div>
        <div class="zebra-edit" [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70" fxFlex.lt-md="50">
            <p-select
                id="protectionTypeOfMainProtection"
                [options]="protectionTypeOfMainProtectionOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="protectionTypeOfMainProtection">
            </p-select>
            <small [controlValidationErrors]="branchLineForm.controls['protectionTypeOfMainProtection']" class="p-error"></small>
        </div>
        <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields">
            <input
                id="protectionTypeOfMainProtectionMD"
                type="text"
                pInputText
                [value]="formDataService.masterDataToCompareResult?.mainProtectionType?.value ?? ''"
                [disabled]="true" />
        </div>
    </div>

    <div
        class="zebra-item"
        fxLayout="row"
        fxLayout.lt-sm="column"
        fxLayoutGap="15px"
        *ngIf="isFuseTypeOfMainProtectionRequired()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="branchLineForm.controls['fuseTypeOfMainProtection']" for="fuseTypeOfMainProtection">
                {{ 'technicalInformationWidget.branchLine.fuseTypeOfMainProtection' | translate }}
            </label>
            <app-icon-master-data-path [path]="formDataService.masterDataToCompareResult?.mainProtectionFuseType?.path">
            </app-icon-master-data-path>
        </div>
        <div class="zebra-edit" [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70" fxFlex.lt-md="50">
            <input
                id="fuseTypeOfMainProtection"
                type="text"
                pInputText
                maxlength="100"
                formControlName="fuseTypeOfMainProtection" />
            <small [controlValidationErrors]="branchLineForm.controls['fuseTypeOfMainProtection']" class="p-error"></small>
        </div>
        <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields">
            <input
                id="fuseTypeOfMainProtectionMD"
                type="text"
                pInputText
                [value]="formDataService.masterDataToCompareResult?.mainProtectionFuseType?.value ?? ''"
                [disabled]="true" />
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="branchLineForm.controls['fuseSizeOfMainProtection']" for="fuseSizeOfMainProtection">
                {{ 'technicalInformationWidget.branchLine.fuseSizeOfMainProtection' | translate }}
            </label>
            <app-icon-master-data-path [path]="formDataService.masterDataToCompareResult?.mainProtectionSize?.path">
            </app-icon-master-data-path>
        </div>
        <div class="zebra-edit" [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70" fxFlex.lt-md="50">
            <p-inputNumber id="fuseSizeOfMainProtection" formControlName="fuseSizeOfMainProtection"></p-inputNumber>
            <small [controlValidationErrors]="branchLineForm.controls['fuseSizeOfMainProtection']" class="p-error"></small>
        </div>
        <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields">
            <p-inputNumber
                id="fuseSizeOfMainProtectionMD"
                [ngModel]="formDataService.masterDataToCompareResult?.mainProtectionSize?.value"
                [ngModelOptions]="{ standalone: true }"
                [disabled]="true">
            </p-inputNumber>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="branchLineForm.controls['numberOfPairs']" for="numberOfPairs">
                {{ 'technicalInformationWidget.branchLine.numberOfPairs' | translate }}
            </label>
            <app-icon-master-data-path [path]="formDataService.masterDataToCompareResult?.numberOfPairs?.path">
            </app-icon-master-data-path>
        </div>
        <div class="zebra-edit" [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70" fxFlex.lt-md="50">
            <p-inputNumber id="numberOfPairs" formControlName="numberOfPairs"></p-inputNumber>
            <small [controlValidationErrors]="branchLineForm.controls['numberOfPairs']" class="p-error"></small>
        </div>
        <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields">
            <p-inputNumber
                id="numberOfPairsMD"
                [ngModel]="formDataService.masterDataToCompareResult?.numberOfPairs?.value"
                [ngModelOptions]="{ standalone: true }"
                [disabled]="true">
            </p-inputNumber>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="branchLineForm.controls['cableDimensionId']" for="cableDimensionId">
                {{ 'technicalInformationWidget.branchLine.cableDimensionId' | translate }}
            </label>
            <app-icon-master-data-path [path]="formDataService.masterDataToCompareResult?.cableDimensionId?.path">
            </app-icon-master-data-path>
        </div>
        <div class="zebra-edit" [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70" fxFlex.lt-md="50">
            <cmbs-value-list-dropdown
                id="cableDimensionId"
                formControlName="cableDimensionId"
                [valueListType]="branchLineCableDimensionValueListType"
                [valueList]="valueListsService.getValueList(branchLineCableDimensionValueListType) | async">
            </cmbs-value-list-dropdown>
            <small [controlValidationErrors]="branchLineForm.controls['cableDimensionId']" class="p-error"></small>
        </div>
        <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields">
            <input
                id="cableDimensionIdMD"
                type="text"
                pInputText
                [value]="
                    formDataService.masterDataToCompareResult?.cableDimensionId?.value ?? ''
                        | valueListSingleItem: branchLineCableDimensionValueListType
                        | async
                "
                [disabled]="true" />
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="branchLineForm.controls['protectiveEarthingDimensionId']" for="protectiveEarthingDimensionId">
                {{ 'technicalInformationWidget.branchLine.protectiveEarthingDimensionId' | translate }}
            </label>
            <app-icon-master-data-path [path]="formDataService.masterDataToCompareResult?.protectiveEarthingDimensionId?.path">
            </app-icon-master-data-path>
        </div>

        <div class="zebra-edit" [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70" fxFlex.lt-md="50">
            <cmbs-value-list-dropdown
                id="protectiveEarthingDimensionId"
                formControlName="protectiveEarthingDimensionId"
                [valueListType]="branchLineElectricityPEmm2ValueListType"
                [valueList]="valueListsService.getValueList(branchLineElectricityPEmm2ValueListType) | async">
            </cmbs-value-list-dropdown>
            <small [controlValidationErrors]="branchLineForm.controls['protectiveEarthingDimensionId']" class="p-error"></small>
        </div>
        <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields">
            <input
                id="protectiveEarthingDimensionIdMD"
                type="text"
                pInputText
                [value]="
                    formDataService.masterDataToCompareResult?.protectiveEarthingDimensionId?.value ?? ''
                        | valueListSingleItem: branchLineElectricityPEmm2ValueListType
                        | async
                "
                [disabled]="true" />
        </div>
    </div>
</ng-container>

<ng-container [formGroup]="meterTypeForm">
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="showMeterPlacement">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="meterTypeForm.controls['meterPlacementId']" for="meterPlacementId">
                {{ 'technicalInformationWidget.meterType.meterPlacementId' | translate }}
            </label>
            <app-icon-master-data-path [path]="formDataService.masterDataToCompareResult?.meterPlacementId?.path">
            </app-icon-master-data-path>
        </div>
        <div class="zebra-edit" [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70" fxFlex.lt-md="50">
            <cmbs-value-list-dropdown
                id="meterPlacementId"
                formControlName="meterPlacementId"
                [valueListType]="meterFramePlacementValueListType"
                [messageKey]="this.formDataService.formId"
                [valueList]="valueListsService.getValueList(meterFramePlacementValueListType) | async">
            </cmbs-value-list-dropdown>
            <small [controlValidationErrors]="meterTypeForm.controls['meterPlacementId']" class="p-error"></small>
        </div>
        <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields">
            <input
                id="meterPlacementIdMD"
                type="text"
                pInputText
                [value]="
                    formDataService.masterDataToCompareResult?.meterPlacementId?.value ?? ''
                        | valueListSingleItem: meterFramePlacementValueListType
                        | async
                "
                [disabled]="true" />
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="showInstallInConstructionPhase">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="meterTypeForm.controls['installInConstructionPhase']" for="installInConstructionPhase">
                {{ 'technicalInformationWidget.meterType.installInConstructionPhase' | translate }}
            </label>
        </div>
        <div class="zebra-edit" [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70" fxFlex.lt-md="50">
            <p-checkbox formControlName="installInConstructionPhase" [binary]="true"></p-checkbox>
            <small [controlValidationErrors]="meterTypeForm.controls['installInConstructionPhase']" class="p-error"></small>
        </div>
        <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields"></div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="meterTypeForm.controls['meterSize']" for="meterSize">
                {{ 'technicalInformationWidget.meterType.meterSize' | translate }}
            </label>
        </div>
        <div class="zebra-edit" [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70" fxFlex.lt-md="50">
            <p-select
                id="meterSize"
                [options]="meterSizeOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="meterSize">
            </p-select>
            <small [controlValidationErrors]="meterTypeForm.controls['meterSize']" class="p-error"></small>
        </div>
        <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields"></div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isConnectionTypeVisible()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="meterTypeForm.controls['connectionTypeId']" for="connectionTypeId">
                {{ 'technicalInformationWidget.meterType.connectionTypeId' | translate }}
            </label>
            <app-icon-master-data-path [path]="formDataService.masterDataToCompareResult?.connectionTypeId?.path">
            </app-icon-master-data-path>
        </div>
        <div class="zebra-edit" [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70" fxFlex.lt-md="50">
            <cmbs-value-list-dropdown
                id="connectionTypeId"
                formControlName="connectionTypeId"
                [showClear]="false"
                [valueListType]="meterFrameConnectionTypeValueListType"
                [messageKey]="this.formDataService.formId"
                [valueList]="valueListsService.getValueList(meterFrameConnectionTypeValueListType) | async">
            </cmbs-value-list-dropdown>
            <small [controlValidationErrors]="meterTypeForm.controls['connectionTypeId']" class="p-error"></small>
        </div>
        <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields">
            <input
                id="connectionTypeIdMD"
                type="text"
                pInputText
                [value]="
                    formDataService.masterDataToCompareResult?.connectionTypeId?.value ?? ''
                        | valueListSingleItem: meterFrameConnectionTypeValueListType
                        | async
                "
                [disabled]="true" />
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isConnectionTypeChangeVisible()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="meterTypeForm.controls['connectionTypeChange']" for="connectionTypeChange">
                {{ 'technicalInformationWidget.meterType.connectionTypeChange' | translate }}
            </label>
        </div>
        <div class="zebra-edit" [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70" fxFlex.lt-md="50">
            <p-select
                id="connectionTypeChange"
                [options]="connectionTypeChangeOptions"
                [showClear]="true"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="connectionTypeChange">
            </p-select>
            <small [controlValidationErrors]="meterTypeForm.controls['connectionTypeChange']" class="p-error"></small>
        </div>
        <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields"></div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isMeterTransformerVisible()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="meterTypeForm.controls['meterTransformerId']" for="meterTransformerId">
                {{ 'technicalInformationWidget.meterType.meterTransformerId' | translate }}
            </label>
            <app-icon-master-data-path [path]="formDataService.masterDataToCompareResult?.meterTransformerId?.path">
            </app-icon-master-data-path>
        </div>
        <div class="zebra-edit" [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70" fxFlex.lt-md="50">
            <cmbs-value-list-dropdown
                id="meterTransformerId"
                formControlName="meterTransformerId"
                [valueListType]="meterFrameMeterTransformerCodeValueListType"
                [messageKey]="this.formDataService.formId"
                [valueList]="valueListsService.getValueList(meterFrameMeterTransformerCodeValueListType) | async">
            </cmbs-value-list-dropdown>
            <small [controlValidationErrors]="meterTypeForm.controls['meterTransformerId']" class="p-error"></small>
        </div>
        <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields">
            <input
                id="meterTransformerIdMD"
                type="text"
                pInputText
                [value]="
                    formDataService.masterDataToCompareResult?.meterTransformerId?.value ?? ''
                        | valueListSingleItem: meterFrameMeterTransformerCodeValueListType
                        | async
                "
                [disabled]="true" />
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isMeterTransformerRemarkVisible()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="meterTypeForm.controls['meterTransformerRemark']" for="meterTransformerRemark">
                {{ 'technicalInformationWidget.meterType.meterTransformerRemark' | translate }}
            </label>
        </div>
        <div class="zebra-edit" [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70" fxFlex.lt-md="50">
            <textarea
                id="meterTransformerRemark"
                pTextarea
                maxlength="2000"
                [autoResize]="true"
                formControlName="meterTransformerRemark"></textarea>
            <small [controlValidationErrors]="meterTypeForm.controls['meterTransformerRemark']" class="p-error"></small>
        </div>
        <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields"></div>
    </div>
</ng-container>
