<ng-container [formGroup]="flagControl">
    <div class="zebra-caption" fxFlex="30" fxLayoutAlign="start center">
        <label for="defaultFlag">{{ 'flagsWidget.types.' + flagControl.value.type!.toString() | translate }} </label>
    </div>
    <div class="zebra-edit align-content-center" fxFlex="20">
        <div class="flex gap-2 align-items-center">
            <p-checkbox id="defaultFlag" formControlName="isSet" [binary]="true"></p-checkbox>
        </div>
    </div>
    <div class="zebra-caption" fxFlex="50" fxFlex.lt-md="50" fxLayoutAlign="start center">
        <button
            id="cancelButton"
            type="button"
            pButton
            pRipple
            [size]="'small'"
            class="p-button-secondary"
            (click)="onSendToInternalResourceClicked()"
            [disabled]="isSending"
            *ngIf="canBeSendToInternalResource">
            {{ 'flagsWidget.sendToInternalResource' | translate | titlecase }}
        </button>
    </div>
    <div class="zebra-edit" fxFlex="0" fxFlex.lt-md="50"></div>
</ng-container>
