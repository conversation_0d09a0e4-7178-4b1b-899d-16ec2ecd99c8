<div fxLayout="column" class="mt-10 zebra-container">
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label for="companyName">{{ 'installerWidget.companyName' | translate }}</label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input id="companyName" type="text" pInputText [disabled]="true" value="{{ installer?.companyName }}" />
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label for="cvr">{{ 'installerWidget.cvr' | translate }}</label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input id="cvr" type="text" pInputText [disabled]="true" value="{{ installer?.cvr }}" />
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label for="companyAuthorizationNumber">{{ 'installerWidget.companyAuthorizationNumber' | translate }}</label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input
                id="companyAuthorizationNumber"
                type="text"
                pInputText
                [disabled]="true"
                value="{{ installer?.companyAuthorizationNumber }}" />
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label for="companyPhoneNumber">{{ 'installerWidget.companyPhoneNumber' | translate }}</label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input id="companyPhoneNumber" type="text" pInputText [disabled]="true" value="{{ installer?.companyPhoneNumber }}" />
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label for="companyEmail">{{ 'installerWidget.companyEmail' | translate }}</label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input id="companyEmail" type="text" pInputText [disabled]="true" value="{{ installer?.companyEmail }}" />
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label for="name">{{ 'installerWidget.name' | translate }}</label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input id="name" type="text" pInputText [disabled]="true" value="{{ installer?.name }}" />
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label for="phoneNumber">{{ 'installerWidget.phoneNumber' | translate }}</label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input id="phoneNumber" type="text" pInputText [disabled]="true" value="{{ installer?.phoneNumber }}" />
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label for="email">{{ 'installerWidget.email' | translate }}</label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input id="email" type="text" pInputText [disabled]="true" value="{{ installer?.email }}" />
        </div>
    </div>

    <p-accordion
        [value]="0"
        *ngIf="
            installer?.secondaryContact?.name || installer?.secondaryContact?.phoneNumber || installer?.secondaryContact?.email
        ">
        <p-accordion-panel [value]="0">
            <p-accordion-header>{{ 'installerWidget.secondaryContact.title' | translate }}</p-accordion-header>
            <p-accordion-content>
                <div
                    *ngIf="installer?.secondaryContact?.name"
                    class="zebra-item"
                    fxLayout="row"
                    fxLayout.lt-sm="column"
                    fxLayoutGap="15px">
                    <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                        <label for="name">{{ 'installerWidget.secondaryContact.name' | translate }}</label>
                    </div>
                    <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                        <input
                            id="name"
                            type="text"
                            pInputText
                            [disabled]="true"
                            value="{{ installer?.secondaryContact?.name }}" />
                    </div>
                </div>

                <div
                    *ngIf="installer?.secondaryContact?.phoneNumber"
                    class="zebra-item"
                    fxLayout="row"
                    fxLayout.lt-sm="column"
                    fxLayoutGap="15px">
                    <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                        <label for="phoneNumber">{{ 'installerWidget.secondaryContact.phoneNumber' | translate }}</label>
                    </div>
                    <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                        <input
                            id="phoneNumber"
                            type="text"
                            pInputText
                            [disabled]="true"
                            value="{{ installer?.secondaryContact?.phoneNumber }}" />
                    </div>
                </div>

                <div
                    *ngIf="installer?.secondaryContact?.email"
                    class="zebra-item"
                    fxLayout="row"
                    fxLayout.lt-sm="column"
                    fxLayoutGap="15px">
                    <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                        <label for="email">{{ 'installerWidget.secondaryContact.email' | translate }}</label>
                    </div>
                    <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                        <input
                            id="email"
                            type="text"
                            pInputText
                            [disabled]="true"
                            value="{{ installer?.secondaryContact?.email }}" />
                    </div>
                </div>
            </p-accordion-content>
        </p-accordion-panel>
    </p-accordion>
</div>
