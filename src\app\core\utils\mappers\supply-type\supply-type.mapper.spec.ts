import { SupplyType, SupplyTypes } from 'src/app/api/installation-forms-client';
import { convertSupplyTypeToEnum } from './supply-type.mapper';

describe('SupplyTypeMapper', () => {
    describe('convertSupplyTypeToEnum', () => {
        it('should convert SupplyType.Electricity to SupplyTypes.Electricity', () => {
            const result = convertSupplyTypeToEnum(SupplyType.Electricity);
            expect(result).toBe(SupplyTypes.Electricity);
        });

        it('should convert SupplyType.Heating to SupplyTypes.Heating', () => {
            const result = convertSupplyTypeToEnum(SupplyType.Heating);
            expect(result).toBe(SupplyTypes.Heating);
        });

        it('should convert SupplyType.Water to SupplyTypes.Water', () => {
            const result = convertSupplyTypeToEnum(SupplyType.Water);
            expect(result).toBe(SupplyTypes.Water);
        });

        it('should return SupplyTypes.Electricity for any unhandled value', () => {
            // Use a non-existent value for testing (type assertion to bypass TypeScript)
            const result = convertSupplyTypeToEnum('OTHER_VALUE' as unknown as string);
            expect(result).toBe(SupplyTypes.Electricity);
        });
    });
});
