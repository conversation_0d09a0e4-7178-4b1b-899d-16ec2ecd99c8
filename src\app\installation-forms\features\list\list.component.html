<ng-container cmbsPersistableSettingsRoot>
    <p-table
        #installationFormsTable
        [cmbsUsePersistableSettings]="true"
        [defaultSort]="defaultSort"
        [defaultPageSize]="defaultPage.pageSize"
        [columns]="selectedColumns"
        responsiveLayout="scroll"
        [value]="(searchResult$ | async)?.items ?? []"
        [(first)]="first"
        [(rows)]="maxRowsPerPage"
        [totalRecords]="(searchResult$ | async)?.totalRecords ?? 0"
        [rowsPerPageOptions]="[50, 100, 200]"
        [lazy]="true"
        (onLazyLoad)="loadItemsLazy($event)"
        [lazyLoadOnInit]="true"
        [loading]="(isLoading$ | async) ?? false"
        [showLoader]="false"
        [paginator]="true"
        currentPageReportTemplate="{first} - {last} {{ 'common.of' | translate }} {totalRecords}"
        [showCurrentPageReport]="true"
        paginatorDropdownScrollHeight="210px"
        dataKey="formId"
        [autoLayout]="true"
        showGridlines
        selectionMode="single"
        [(selection)]="selectedRow"
        (onRowSelect)="onRowSelect($event)"
        [reorderableColumns]="true"
        (onColReorder)="handleOnColumnReorder($event)">
        <ng-template pTemplate="caption">
            <div class="table-header" *ngIf="searchResult$ | async as searchResult">
                <div *ngIf="searchResult">{{ 'common.searchResults' | translate }} ({{ searchResult.totalRecords }})</div>
                <div class="line"></div>
                <div class="table-caption flex gap-1">
                    <cmbs-display-columns-selector
                        cmbsUsePersistableSettings
                        [appendTo]="overlayContainer"
                        [currentVisibleColumns]="selectedColumns"
                        [defaultDisplayColumns]="allColumns"
                        [isVisibleProperty]="'isVisible'"
                        [propertyNameProperty]="'field'"
                        [propertyNameTranslationProperty]="'header'"
                        (visibilityChange)="selectedColumnsChange($event)">
                    </cmbs-display-columns-selector>

                    <cmbs-persistable-search-buttons
                        [tableId]="tableId"
                        [tieredMenuAnchor]="overlayContainer"
                        (settingsRevertedOrReset)="settingsRevertedOrReset($event)">
                    </cmbs-persistable-search-buttons>
                </div>
            </div>
        </ng-template>

        <ng-template pTemplate="header" let-columns>
            <tr>
                <ng-container *ngFor="let col of columns">
                    <th
                        id="columnHeader"
                        class="max-w-full white-space-nowrap"
                        [pSortableColumn]="col.field"
                        [hidden]="!col.isVisible">
                        {{ col.header }}
                        <p-sortIcon *ngIf="isSortableColumn(col.field)" [field]="col.field"></p-sortIcon>
                    </th>
                </ng-container>
            </tr>
            <tr *ngIf="((isLoading$ | async) ?? false) === false">
                <th
                    *ngFor="let col of columns"
                    [id]="col.field"
                    class="max-w-full white-space-nowrap"
                    [hidden]="!col.isVisible"
                    pReorderableColumn>
                    <ng-container [ngSwitch]="col.columnFilterType">
                        <p-columnFilter
                            *ngSwitchCase="'String'"
                            type="text"
                            [field]="col.field"
                            [id]="col.field + 'Filter'"
                            class="ml-auto"
                            [matchModeOptions]="getAllowedOperators(col.operators)"
                            placeholder="{{ col.header }} filter" />
                        <p-columnFilter
                            *ngSwitchCase="'Address'"
                            type="text"
                            [field]="col.field"
                            [id]="col.field + 'Filter'"
                            class="ml-auto"
                            [matchModeOptions]="getAllowedOperators(col.operators)"
                            placeholder="{{ 'address.pattern' | translate }}" />
                        <p-columnFilter
                            *ngSwitchCase="'StringWithNoValueFilter'"
                            type="text"
                            [field]="col.field"
                            [id]="col.field + 'Filter'"
                            class="ml-auto"
                            (onHide)="onMatchModeChange()"
                            [matchModeOptions]="getAllowedOperators(col.operators)"
                            placeholder="{{ col.header }} filter">
                        </p-columnFilter>
                        <p-columnFilter
                            *ngSwitchCase="'Boolean'"
                            type="boolean"
                            matchMode="equals"
                            [field]="col.field"
                            [id]="col.field + 'Filter'"
                            class="ml-auto"
                            placeholder="{{ col.header }} filter">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-select
                                    [options]="(booleanOptionsService.getBooleanOptions() | async) ?? []"
                                    [ngModel]="value"
                                    (onChange)="filter($event.value)"
                                    [appendTo]="installationFormsTable"
                                    [disabled]="(isLoading$ | async) ?? false"
                                    [placeholder]="'common.yesNoPlaceholder' | translate">
                                </p-select>
                            </ng-template>
                        </p-columnFilter>
                        <p-columnFilter
                            *ngSwitchCase="'Number'"
                            type="numeric"
                            [field]="col.field"
                            [id]="col.field + 'Filter'"
                            class="ml-auto"
                            [matchModeOptions]="getAllowedOperators(col.operators)"
                            placeholder="{{ col.header }} filter" />
                        <p-columnFilter
                            *ngSwitchCase="'Multiselect'"
                            [field]="col.field"
                            [id]="col.field + 'Filter'"
                            matchMode="in"
                            [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-multiSelect
                                    styleClass="filterMultiSelect"
                                    [ngModel]="value"
                                    placeholder="{{ col.header }} filter"
                                    [options]="getTranslationsForEnum(col)"
                                    (onChange)="filter($event.value)"
                                    optionValue="value"
                                    optionLabel="label"
                                    display="chip"
                                    [appendTo]="installationFormsTable"
                                    [disabled]="(isLoading$ | async) ?? false">
                                </p-multiSelect>
                            </ng-template>
                        </p-columnFilter>
                        <p-columnFilter
                            *ngSwitchCase="'ValueList'"
                            [field]="col.field"
                            [id]="col.field + 'Filter'"
                            matchMode="in"
                            [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <app-value-list-multiselect
                                    class="filterMultiSelect"
                                    [ngModel]="value"
                                    [valueListType]="col.valueListType"
                                    placeholder="{{ col.header }} filter"
                                    (ngModelChange)="filter($event)"
                                    [disabled]="(isLoading$ | async) ?? false"
                                    [appendTo]="installationFormsTable"
                                    [display]="'chip'">
                                </app-value-list-multiselect>
                            </ng-template>
                        </p-columnFilter>
                        <p-columnFilter *ngSwitchCase="'Date'" [field]="col.field" [id]="col.field + 'Filter'" type="date">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <cmbs-calendar
                                    [ngModel]="value"
                                    placeholder="{{ col.header }} filter"
                                    (onSelect)="filter($event)"
                                    [showTime]="false"
                                    [showClear]="false"
                                    [appendTo]="installationFormsTable"
                                    [disabled]="(isLoading$ | async) ?? false">
                                </cmbs-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </ng-container>
                </th>
            </tr>
        </ng-template>

        <ng-template pTemplate="body" let-rowData let-columns="columns">
            <ng-container *ngIf="(isLoading$ | async) ?? false">
                <tr class="row">
                    <td *ngFor="let col of columns">
                        <p-skeleton class="w-full"></p-skeleton>
                    </td>
                </tr>
            </ng-container>

            <ng-container *ngIf="((isLoading$ | async) ?? false) === false" class="zebra-container">
                <tr class="row zebra-item" [pSelectableRow]="rowData">
                    <td *ngFor="let col of columns" [hidden]="!col.isVisible">
                        <ng-container [ngSwitch]="col.columnTransformationType">
                            <div *ngSwitchCase="'None'">
                                {{ rowData[col.field] }}
                            </div>
                            <div *ngSwitchCase="'Translate'">
                                {{ col.translationPath + rowData[col.field] | translate }}
                            </div>
                            <div *ngSwitchCase="'TranslateEnumArray'">
                                {{ rowData[col.field] | translateEnumArray: col.translationPath }}
                            </div>
                            <div *ngSwitchCase="'NoValueEnumTranslate'">
                                {{ rowData[col.field] | noValueEnumTranslate: col.translationPath }}
                            </div>
                            <div *ngSwitchCase="'Date'">
                                {{ rowData[col.field] | formatDate }}
                            </div>
                            <div *ngSwitchCase="'DateTime'">
                                {{ rowData[col.field] | formatDateTime }}
                            </div>
                            <div *ngSwitchCase="'Array'">
                                {{ rowData[col.field].join(', ') }}
                            </div>
                            <div *ngSwitchCase="'Boolean'">
                                {{ rowData[col.field] | booleanToYesNo }}
                            </div>
                        </ng-container>
                    </td>
                </tr>
            </ng-container>
        </ng-template>

        <ng-template pTemplate="emptymessage" let-columns>
            <tr>
                <td [attr.colspan]="columns.length">
                    <div class="p-text-truncate">{{ 'common.emptyList' | translate }}</div>
                </td>
            </tr>
        </ng-template>
    </p-table>
</ng-container>

<p-toast #genericMessagesToast></p-toast>
<div #overlayContainer></div>
