import { TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { InstructionText } from 'src/app/api/installation-forms-client';
import { DefaultInstructionTextService } from 'src/app/core/services/default-instruction-text/default-instruction-text.service';
import { InstructionTextsPipe } from './instruction-text.pipe';

describe('InstructionTextsPipe', () => {
    let pipe: InstructionTextsPipe;
    let defaultInstructionTextService: DefaultInstructionTextService;
    let getInstructionTextsSpy: jest.SpyInstance;

    // Using type assertion to simplify our mock
    const mockInstructionTexts: InstructionText[] = [
        { id: 'item1', text: 'Text One', order: 1 } as unknown as InstructionText,
        { id: 'item2', text: 'Text Two', order: 2 } as unknown as InstructionText,
        { id: 'item3', text: 'Text Three', order: 3 } as unknown as InstructionText
    ];

    beforeEach(() => {
        const instructionTextsServiceMock = {
            getInstructionTexts: jest.fn().mockReturnValue(of(mockInstructionTexts))
        };

        TestBed.configureTestingModule({
            providers: [InstructionTextsPipe, { provide: DefaultInstructionTextService, useValue: instructionTextsServiceMock }]
        });

        pipe = TestBed.inject(InstructionTextsPipe);
        defaultInstructionTextService = TestBed.inject(DefaultInstructionTextService);
        getInstructionTextsSpy = jest.spyOn(defaultInstructionTextService, 'getInstructionTexts');
    });

    it('should create an instance', () => {
        expect(pipe).toBeTruthy();
    });

    it('should return empty string when ids array is undefined', (done) => {
        pipe.transform(undefined as any).subscribe((result) => {
            expect(result).toBe('');
            expect(defaultInstructionTextService.getInstructionTexts).not.toHaveBeenCalled();
            done();
        });
    });

    it('should return empty string when ids array is empty', (done) => {
        pipe.transform([]).subscribe((result) => {
            expect(result).toBe('');
            expect(defaultInstructionTextService.getInstructionTexts).not.toHaveBeenCalled();
            done();
        });
    });

    it('should return empty string when instructionText is undefined', (done) => {
        getInstructionTextsSpy.mockReturnValueOnce(of(undefined));

        pipe.transform(['item1']).subscribe((result) => {
            expect(result).toBe('');
            expect(defaultInstructionTextService.getInstructionTexts).toHaveBeenCalledWith();
            done();
        });
    });

    it('should map ids to display values and join them with commas', (done) => {
        pipe.transform(['item1', 'item3']).subscribe((result) => {
            expect(result).toBe('Text One, Text Three');
            expect(defaultInstructionTextService.getInstructionTexts).toHaveBeenCalledWith();
            done();
        });
    });

    it('should handle ids that do not exist in the instruction texts list', (done) => {
        pipe.transform(['item1', 'nonExistent', 'item3']).subscribe((result) => {
            expect(result).toBe('Text One, Text Three');
            expect(defaultInstructionTextService.getInstructionTexts).toHaveBeenCalledWith();
            done();
        });
    });

    it('should filter out empty values', (done) => {
        const listWithEmptyValue = [
            { id: 'item1', text: 'Text One', order: 1 } as unknown as InstructionText,
            { id: 'item2', text: '', order: 2 } as unknown as InstructionText,
            { id: 'item3', text: 'Text Three', order: 3 } as unknown as InstructionText
        ];
        getInstructionTextsSpy.mockReturnValueOnce(of(listWithEmptyValue));

        pipe.transform(['item1', 'item2', 'item3']).subscribe((result) => {
            expect(result).toBe('Text One, Text Three');
            done();
        });
    });

    it('should return single value without comma when only one valid id is found', (done) => {
        pipe.transform(['item2']).subscribe((result) => {
            expect(result).toBe('Text Two');
            done();
        });
    });
});
