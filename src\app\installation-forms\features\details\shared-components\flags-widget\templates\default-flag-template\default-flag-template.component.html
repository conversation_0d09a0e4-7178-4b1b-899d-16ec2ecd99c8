<ng-container [formGroup]="flagControl">
    <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
        <label for="defaultFlag">{{ 'flagsWidget.types.' + flagControl.value.type!.toString() | translate }} </label>
    </div>
    <div class="zebra-edit" fxFlex="20" fxFlex.lt-md="50">
        <p-checkbox id="defaultFlag" formControlName="isSet" [binary]="true"></p-checkbox>
    </div>
    <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center" *ngIf="shouldShowManuallySetDate()">
        <div>{{ 'flagsWidget.manuallySetDate' | translate }}</div>
    </div>
    <div class="zebra-edit" fxFlex="20" fxFlex.lt-md="50" *ngIf="shouldShowManuallySetDate()">
        {{ flagControl.value.manuallySetDate | formatDateTime }}
    </div>
</ng-container>
