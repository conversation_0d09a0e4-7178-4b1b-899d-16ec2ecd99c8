import { Component, Input, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { finalize } from 'rxjs';
import {
    AutomaticEmailsRule,
    AutomaticEmailsRuleCreateOrUpdate,
    CommunicationTemplateModel,
    FormCategory,
    FormState,
    FormType,
    InstallationFormsClient
} from 'src/app/api/installation-forms-client';
import { SETTINGS_DISPLAY_NAME_MAX_LENGTH } from 'src/app/core/constants/constants';
import { newInstallationFormCategories } from 'src/app/core/constants/form-categories-per-form-type';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import { multipleWhitespaceValidator } from 'src/app/core/utils/validators/multiple-whitespace-validator/multiple-whitespace-validator';
import { v4 as uuidv4 } from 'uuid';
import { isAllOfRequiredFormTypesSelected, isAnyOfRequiredFormTypesSelected } from '../../../../core/utils/form-type-checkers';
import { alreadyUsedDisplayNameValidator } from '../../../../core/utils/validators/already-used-display-name-validator/already-used-display-name-validator';
import { BaseRuleCreateComponent } from '../../base-components/base-rule-create.components';
import { automaticEmailsTabName, automaticEmailsTranslationPath } from '../constants/automatic-emails.consts';
import { FORM_CATEGORY_FORM_TYPES } from '../constants/properties-per-form-types';

@Component({
    selector: 'app-automatic-emails-create',
    templateUrl: './automatic-emails-create.component.html',
    styleUrl: './automatic-emails-create.component.scss',
    standalone: false
})
export class AutomaticEmailsCreateComponent extends BaseRuleCreateComponent<AutomaticEmailsRule> implements OnInit {
    @Input() emailTemplateOptions: CommunicationTemplateModel[] = [];

    formTypeOptions: SelectItem[] = [];
    formStateOptions: SelectItem[] = [];
    formCategoriesOptions: SelectItem[] = [];

    constructor(
        private readonly client: InstallationFormsClient,
        protected readonly fb: FormBuilder,
        protected readonly translateService: TranslateService,
        protected readonly messageServiceHelper: MessageServiceHelper
    ) {
        super(fb, translateService, messageServiceHelper, automaticEmailsTranslationPath, automaticEmailsTabName);
    }

    ngOnInit(): void {
        super.ngOnInit();
        this.addFormControlsValueChanges();
    }

    override initForm(): void {
        this.form = this.fb.group({
            displayName: [
                null,
                [
                    Validators.required,
                    Validators.maxLength(SETTINGS_DISPLAY_NAME_MAX_LENGTH),
                    alreadyUsedDisplayNameValidator(this.alreadyUsedDisplayNames),
                    multipleWhitespaceValidator()
                ]
            ],
            formTypes: [null, [Validators.required]],
            formStates: [null],
            formCategories: [null],
            emailTemplateId: [null, [Validators.required]]
        });
    }

    override getEnumTranslations(): void {
        this.formTypeOptions = enumMapper.map(this.translateService.instant('enums.type'), FormType);
        this.formStateOptions = enumMapper.map(this.translateService.instant('enums.state'), [
            FormState.Registered,
            FormState.Instructed,
            FormState.Completed
        ]);
    }

    override createClick() {
        if (this.form.invalid) {
            return;
        }

        const formValue = this.form.value;

        const rule: AutomaticEmailsRuleCreateOrUpdate = new AutomaticEmailsRuleCreateOrUpdate({
            displayName: formValue.displayName,
            formTypes: formValue.formTypes ?? [],
            formStates: formValue.formStates ?? [],
            formCategories: formValue.formCategories ?? [],
            templateId: formValue.emailTemplateId
        });

        this.isProcessing = true;
        this.subscription.add(
            this.client
                .createAutomaticEmailsRule(uuidv4(), rule)
                .pipe(finalize(() => (this.isProcessing = false)))
                .subscribe({
                    next: (response) => {
                        this.ruleCreated.emit(response.result);
                        this.showSuccessMessage();
                    },
                    error: (_) => {
                        this.showErrorMessage(this.widgetTranslations['automaticEmailsRuleCreationError']);
                    }
                })
        );
    }

    addFormControlsValueChanges() {
        this.subscription.add(
            this.form.get('formTypes')?.valueChanges.subscribe(() => {
                this.updateFormCategories();
            })
        );
    }

    updateFormCategories() {
        const formTypes = this.form?.get('formTypes')?.value || [];
        let relevantCategories: FormCategory[] = [];

        if (isAllOfRequiredFormTypesSelected(formTypes, [FormType.NewInstallation])) {
            relevantCategories = newInstallationFormCategories;
        } else {
            relevantCategories = [];
        }

        this.formCategoriesOptions = enumMapper.map(this.translateService.instant('enums.category'), relevantCategories);

        this.filterRelevantFormCategories(relevantCategories);
    }

    filterRelevantFormCategories(relevantFormCategories: FormCategory[]) {
        const selectedFormCategories = this.form.get('formCategories')?.value || [];
        const filteredCategories = selectedFormCategories.filter((category: FormCategory) =>
            relevantFormCategories.includes(category)
        );
        this.form.get('formCategories')?.setValue(filteredCategories);
    }

    isFormCategoryRelevant = (): boolean => {
        const formTypes = this.form?.get('formTypes')?.value || [];
        return isAnyOfRequiredFormTypesSelected(formTypes, FORM_CATEGORY_FORM_TYPES);
    };
}
