import { FormControl } from '@angular/forms';
import addressLookupValidator from './address-lookup.validator';

describe('addressLookupValidator', () => {
    const validator = addressLookupValidator();

    it('should return null for valid address with text', () => {
        const control = new FormControl({ text: 'Some address' });
        const result = validator(control);
        expect(result).toBeNull();
    });

    it('should return error when control value is null or undefined', () => {
        const control1 = new FormControl(null);
        const control2 = new FormControl(undefined);

        expect(validator(control1)).toEqual({ carIdIsEmpty: true });
        expect(validator(control2)).toEqual({ carIdIsEmpty: true });
    });

    it('should return error when text is empty', () => {
        const control = new FormControl({ text: '' });
        const result = validator(control);
        expect(result).toEqual({ carIdIsEmpty: true });
    });

    it('should return error when text contains only whitespace', () => {
        const control = new FormControl({ text: '   ' });
        const result = validator(control);
        expect(result).toEqual({ carIdIsEmpty: true });
    });

    it('should return error when text property is missing', () => {
        const control = new FormControl({ otherProp: 'value' });
        const result = validator(control);
        expect(result).toEqual({ carIdIsEmpty: true });
    });
});
