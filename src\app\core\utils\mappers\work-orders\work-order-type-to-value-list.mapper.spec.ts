import { ValueListType, WorkOrderType } from 'src/app/api/installation-forms-client';
import { mapWorkOrderTypeToWorkDescriptionValueList } from './work-order-type-to-value-list.mapper';

describe('WorkOrderTypeToValueList Mapper', () => {
    it('should map General work order type to correct value list', () => {
        expect(mapWorkOrderTypeToWorkDescriptionValueList(WorkOrderType.General)).toBe(
            ValueListType.WorkOrderGeneralElectricityWorkDescriptionList
        );
    });

    it('should map InstallMeter work order type to correct value list', () => {
        expect(mapWorkOrderTypeToWorkDescriptionValueList(WorkOrderType.InstallMeter)).toBe(
            ValueListType.WorkOrderInstallMeterElectricityWorkDescriptionList
        );
    });

    it('should map ReplaceMeter work order type to correct value list', () => {
        expect(mapWorkOrderTypeToWorkDescriptionValueList(WorkOrderType.ReplaceMeter)).toBe(
            ValueListType.WorkOrderReplaceMeterElectricityWorkDescriptionList
        );
    });

    it('should map RemoveMeter work order type to correct value list', () => {
        expect(mapWorkOrderTypeToWorkDescriptionValueList(WorkOrderType.RemoveMeter)).toBe(
            ValueListType.WorkOrderRemoveMeterElectricityWorkDescriptionList
        );
    });

    it('should handle undefined work order type', () => {
        expect(mapWorkOrderTypeToWorkDescriptionValueList(undefined as unknown as WorkOrderType)).toBeUndefined();
    });

    it('should test all work order types are mapped', () => {
        // This test ensures we haven't missed any WorkOrderType in our mapping
        const allWorkOrderTypes = Object.values(WorkOrderType);
        allWorkOrderTypes.forEach((workOrderType) => {
            expect(mapWorkOrderTypeToWorkDescriptionValueList(workOrderType)).toBeDefined();
        });
    });
});
