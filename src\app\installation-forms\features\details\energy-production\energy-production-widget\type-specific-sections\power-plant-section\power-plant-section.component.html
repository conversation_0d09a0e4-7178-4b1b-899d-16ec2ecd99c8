<ng-container [formGroup]="form">
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['primaryEnergySource']" for="primaryEnergySource">
                {{ 'energyProductionSection.powerPlant.primaryEnergySource' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input
                id="primaryEnergySource"
                type="text"
                pInputText
                maxlength="100"
                formControlName="primaryEnergySource"
                (ngModelChange)="onValueChange()" />
            <small [controlValidationErrors]="form.controls['primaryEnergySource']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['primaryEnergyShareInPercent']" for="primaryEnergyShareInPercent">
                {{ 'energyProductionSection.powerPlant.primaryEnergyShareInPercent' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input
                id="primaryEnergyShareInPercent"
                type="text"
                pInputText
                maxlength="100"
                formControlName="primaryEnergyShareInPercent"
                (ngModelChange)="onValueChange()" />
            <small [controlValidationErrors]="form.controls['primaryEnergyShareInPercent']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['secondaryEnergySource']" for="secondaryEnergySource">
                {{ 'energyProductionSection.powerPlant.secondaryEnergySource' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input
                id="secondaryEnergySource"
                type="text"
                pInputText
                maxlength="100"
                formControlName="secondaryEnergySource"
                (ngModelChange)="onValueChange()" />
            <small [controlValidationErrors]="form.controls['secondaryEnergySource']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['secondaryEnergyShareInPercent']" for="secondaryEnergyShareInPercent">
                {{ 'energyProductionSection.powerPlant.secondaryEnergyShareInPercent' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input
                id="secondaryEnergyShareInPercent"
                type="text"
                pInputText
                maxlength="100"
                formControlName="secondaryEnergyShareInPercent"
                (ngModelChange)="onValueChange()" />
            <small [controlValidationErrors]="form.controls['secondaryEnergyShareInPercent']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['plantTypeName']" for="plantTypeName">
                {{ 'energyProductionSection.powerPlant.plantTypeName' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input
                id="plantTypeName"
                type="text"
                pInputText
                maxlength="100"
                formControlName="plantTypeName"
                (ngModelChange)="onValueChange()" />
            <small [controlValidationErrors]="form.controls['plantTypeName']" class="p-error"></small>
        </div>
    </div>
</ng-container>
