import { Directive, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';

@Directive()
export abstract class BaseRuleCreateComponent<T> implements OnInit, OnDestroy {
    @Input() isProcessing!: boolean;
    @Input() alreadyUsedDisplayNames: string[] = [];
    @Output() cancelCreating = new EventEmitter<void>();
    @Output() ruleCreated = new EventEmitter<T>();

    protected form!: FormGroup;
    protected subscription = new Subscription();
    protected widgetTranslations: any;

    constructor(
        protected readonly fb: FormBuilder,
        protected readonly translateService: TranslateService,
        protected readonly messageServiceHelper: MessageServiceHelper,
        protected readonly translationKey: string,
        protected readonly tabName: string
    ) {}

    ngOnInit(): void {
        this.initForm();
        this.widgetTranslations = this.translateService.instant(this.translationKey);
        this.getEnumTranslations();
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    abstract getEnumTranslations(): void;
    abstract initForm(): void;
    abstract createClick(): void;

    protected showSuccessMessage(): void {
        this.messageServiceHelper.showSuccess({
            key: this.tabName
        });
    }

    protected showErrorMessage(detail: string): void {
        this.messageServiceHelper.showError({
            key: this.tabName,
            detail: detail
        });
    }

    protected showWarningMessage(detail: string): void {
        this.messageServiceHelper.showWarning({
            key: this.tabName,
            detail: detail
        });
    }

    protected cancelCreateClick(): void {
        this.form.reset();
        this.cancelCreating.emit();
    }
}
