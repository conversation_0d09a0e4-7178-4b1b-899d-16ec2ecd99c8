<p-panel [header]="'tasksWidget.title' | translate" [toggleable]="true">
    <ng-container *ngIf="items && items.length; else emptyMessage">
        <p-table
            [columns]="columns"
            [value]="items"
            [rowHover]="true"
            responsiveLayout="scroll"
            [paginator]="items.length > rowsPerPage"
            [rows]="rowsPerPage"
            [loading]="loading"
            [customSort]="false"
            [sortField]="'createdDate'"
            [sortOrder]="-1">
            <ng-template pTemplate="header" let-columns>
                <tr>
                    <th id="icon-task-group"></th>
                    <th *ngFor="let col of columns" [pSortableColumn]="col" [id]="col">
                        <div fxLayout="row">
                            {{ 'tasksWidget.columnNames.' + col | translate }}
                        </div>
                    </th>
                    <th id="tasks-actions"></th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-taskGroup>
                <tr class="zebra-item">
                    <td><app-icon-task-group [taskGroup]="taskGroup.type"></app-icon-task-group></td>
                    <td>{{ taskGroup.name }}</td>
                    <td>
                        <span>{{ 'tasksWidget.taskStatuses.' + taskGroup.state | translate }}</span>
                    </td>
                    <td class="w-2rem pl-0">
                        <app-icon-task-is-manual
                            class="mr-2"
                            [isManual]="taskGroup.isLatestStartedTaskManual"
                            *ngIf="shouldShowTaskStatusIcon(taskGroup)"></app-icon-task-is-manual>
                    </td>
                    <td>{{ taskGroup.startedDate | formatDateTime }}</td>
                    <td>{{ taskGroup.completionDate | formatDateTime }}</td>
                    <td>
                        <button
                            type="button"
                            *ngIf="canReExecuteTask(taskGroup)"
                            pRipple
                            pButton
                            (click)="reExecuteTask($event, taskGroup)">
                            {{ 'tasksWidget.reExecute' | translate }}
                        </button>

                        <button
                            type="button"
                            *ngIf="canMarkAsCompleted(taskGroup)"
                            pRipple
                            pButton
                            (click)="markAsCompleted($event, taskGroup)">
                            {{ 'tasksWidget.markAsCompleted' | translate }}
                        </button>
                    </td>
                </tr>
            </ng-template>
            <ng-template pTemplate="paginatorright"> </ng-template>
        </p-table>
    </ng-container>
</p-panel>

<ng-template #emptyMessage>
    <p>{{ 'tasksWidget.noTasksFound' | translate }}</p>
</ng-template>
