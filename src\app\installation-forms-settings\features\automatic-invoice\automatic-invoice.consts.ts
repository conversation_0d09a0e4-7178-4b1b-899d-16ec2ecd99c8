export const automaticInvoiceTabName: string = 'AutomaticInvoicing';
export const automaticInvoiceTranslationPath: string = 'formsSettings.invoice';

export const automaticInvoiceMarkdownEnUs: string = `
## Configuration of automatic invoicing for form management

**Rules:** Adding a rule below will cause the system to automatically process any form matching the criteria defined in the rule. The system will update connection rights from the form and generate an invoice based on this update.

**Trigger:** An automatic invoice will be generated based on the rule when all the below conditions are met:
- The form matches every enabled criteria in the rule.
- The form allows automatization.
- The form has no problems with the severity type "Error".
- Connection point and primary meter frame has been selected on the form.
- There is no previous invoice on the form.
`;

export const automaticInvoiceMarkdownDaDK: string = `
## Konfiguration af automatisk fakturering fra blanketter

**Regler:** Når der tilføjes en regel herunder, vil systemet automatisk begynde at behandle blanketter der opfylder kriterierne defineret i reglen. Der opdateres automatisk tillslutningsrettigheder fra blanketten og efterfølgende dannes faktureringsgrundlag på baggrund heraf.

**Trigger:** Der dannes et automatisk genereret faktureringsgrundlag baseret på reglen, når alle nedenstående betingelser er opfyldt:
- Blanketten opfylder alle opsatte kriterier fra reglen.
- Blanketten tillader automatisering.
- Blanketten har ingen problemer af alvorlighedsgraden "fejl".
- Blanketten har et valgt forbindelsespunkt og en primær målerramme.
- Der er ikke tidligere lavet en faktura på blanketten.
`;
