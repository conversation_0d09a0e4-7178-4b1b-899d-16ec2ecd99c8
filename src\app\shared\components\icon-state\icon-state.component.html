<ng-container [ngSwitch]="iconState">
    <i class="fa-solid fa-circle-check color-success" *ngSwitchCase="'Completed'"></i>
    <i class="fa-regular fa-circle-check color-success" *ngSwitchCase="'InProgress'"></i>
    <i class="fa-solid fa-circle-exclamation color-warning" *ngSwitchCase="'RequiresManualHandling'"></i>
    <i class="fa-solid fa-circle-xmark color-error" *ngSwitchCase="'Failed'"></i>
</ng-container>
