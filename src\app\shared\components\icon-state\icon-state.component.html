<ng-container [ngSwitch]="iconState">
    <i class="fa-solid fa-circle-check color-success" *ngSwitchCase="IconState.Completed"></i>
    <i class="fa-solid fa-circle-check color-success" *ngSwitchCase="IconState.Sent"></i>
    <i class="fa-solid fa-circle-check color-success" *ngSwitchCase="IconState.ResponseReceived"></i>
    <i class="fa-regular fa-circle-check color-success" *ngSwitchCase="IconState.InProgress"></i>
    <i class="fa-solid fa-circle-xmark color-error" *ngSwitchCase="IconState.Failed"></i>
</ng-container>
