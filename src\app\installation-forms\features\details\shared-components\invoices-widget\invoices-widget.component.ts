import { ChangeDetectorRef, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthorizationService } from '@kmd-elements-ui/authorization';
import { TranslateService } from '@ngx-translate/core';
import { CheckboxChangeEvent } from 'primeng/checkbox';
import { finalize, Subscription, switchMap } from 'rxjs';
import {
    FormType,
    InstallationFormsClient,
    Invoice,
    InvoiceIsRequiredFlagUpdate,
    InvoiceState,
    PendingUpdateAreaType,
    TransferToErp
} from 'src/app/api/installation-forms-client';
import { SMALL_TABLE_ROWS_PER_PAGE } from 'src/app/core/constants/constants';
import { Permissions } from 'src/app/core/constants/permissions';
import { WidgetNames } from 'src/app/core/constants/widget-names';
import { AutomaticFormRefreshService } from 'src/app/core/services/automatic-form-refresh/automatic-form-refresh.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { WidgetComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget.component';
import { v4 as uuidv4 } from 'uuid';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';

@Component({
    selector: 'app-invoices-widget',
    templateUrl: './invoices-widget.component.html',
    styleUrl: './invoices-widget.component.scss',
    standalone: false
})
export class InvoicesWidgetComponent extends WidgetComponent implements OnInit, OnDestroy {
    widgetName: string = WidgetNames.invoicesWidget;

    private widgetTranslations: any;

    invoices: Invoice[] = [];

    columns = ['createdDate', 'createdInErpDate', 'status', 'number', 'totalSum', 'isRequired'];
    rowsPerPage = SMALL_TABLE_ROWS_PER_PAGE;

    subscription: Subscription = new Subscription();

    createNewInvoiceButtonDisabled: boolean = false;

    hasInvoicingWritePermission = false;

    constructor(
        readonly formDataService: FormDataService,
        private readonly client: InstallationFormsClient,
        private readonly messageServiceHelper: MessageServiceHelper,
        private readonly translateService: TranslateService,
        private readonly authService: AuthorizationService,
        private readonly router: Router,
        private cdr: ChangeDetectorRef,
        private readonly automaticFormRefreshService: AutomaticFormRefreshService
    ) {
        super(formDataService);
    }

    ngOnInit() {
        this.widgetTranslations = this.translateService.instant(WidgetNames.invoicesWidget);

        this.authService
            .hasPermissions([Permissions.singleInvoicing.write])
            .subscribe((x) => (this.hasInvoicingWritePermission = x));

        this.automaticFormRefreshService.subscribeForRefresh(PendingUpdateAreaType.Invoices, this.getInvoices);
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
        this.automaticFormRefreshService.unsubscribeForRefresh(PendingUpdateAreaType.Invoices);
    }

    getInvoices = () => {
        this.subscription.add(
            this.client.getInvoices(this.formDataService.formId!, uuidv4()).subscribe({
                next: (response) => {
                    this.invoices = response.result;
                },
                error: (_error) => {
                    this.messageServiceHelper.showError({
                        detail: this.widgetTranslations['getInvoicesError'],
                        key: this.formDataService.formId!
                    });
                }
            })
        );
    };

    createNewInvoice() {
        this.processingStarted();

        this.subscription.add(
            this.client.createInvoice(this.formDataService.formId!, uuidv4()).subscribe({
                next: (_response) => {
                    this.processingFinished();
                    this.messageServiceHelper.showSuccess({
                        detail: this.widgetTranslations['postInvoicesSuccess'],
                        key: this.formDataService.formId!
                    });
                    this.getInvoices();
                },
                error: (error) => {
                    this.messageServiceHelper.addTranslated(error, this.formDataService.formId!, WidgetNames.invoicesWidget, {
                        path: 'enums.connectionRightsCategories',
                        key: 'Category'
                    });

                    this.processingFinished();
                }
            })
        );
    }

    async goToInvoice(invoiceId?: string) {
        if (!invoiceId) {
            return;
        }

        const selection = window.getSelection();
        if (selection && selection.toString().length > 0) {
            return;
        }

        await this.router.navigate([`single-invoicing/${invoiceId}`]);
    }

    formatTotalSum(invoice: Invoice) {
        if (!invoice.totalSum) {
            return '';
        }

        if (!invoice.currency) {
            return invoice.totalSum.toString();
        }

        return `${invoice.totalSum} ${invoice.currency}`;
    }

    protected showCreateNewInvoiceButton(): boolean {
        return (
            (this.formDataService.type === FormType.NewInstallation || this.formDataService.type === FormType.Extension) &&
            this.formDataService.canCurrentUserExecuteActions &&
            this.hasInvoicingWritePermission
        );
    }

    onRequiredChange(event: CheckboxChangeEvent, invoice: Invoice) {
        event.originalEvent?.stopPropagation();
        this.processingStarted();
        this.subscription.add(
            this.client
                .updateInvoiceIsRequiredFlag(
                    invoice.invoiceEntryId,
                    uuidv4(),
                    new InvoiceIsRequiredFlagUpdate({
                        formId: this.formDataService.formId!,
                        isRequired: event.checked
                    })
                )
                .pipe(
                    switchMap(() => this.client.getInvoices(this.formDataService.formId!, uuidv4())),
                    finalize(() => this.processingFinished())
                )
                .subscribe({
                    error: (_) => {
                        this.messageServiceHelper.showError({
                            key: this.formDataService.formId!
                        });
                    },
                    complete: () => {
                        this.messageServiceHelper.showSuccess({
                            key: this.formDataService.formId!
                        });
                    }
                })
        );
        this.cdr.detectChanges();
    }

    canTransferInvoice(invoice: Invoice): boolean {
        return (
            this.canEditInvoices &&
            (invoice.status === InvoiceState.Calculated || invoice.status === InvoiceState.TransferStarted)
        );
    }

    transferInvoice(event: MouseEvent, invoice: Invoice): void {
        event?.stopPropagation();
        this.processingStarted();

        this.subscription.add(
            this.client
                .transferInvoiceToErp(
                    invoice.invoiceEntryId,
                    uuidv4(),
                    new TransferToErp({
                        formId: this.formDataService.formId!
                    }),
                    undefined
                )
                .pipe(finalize(() => this.processingFinished()))
                .subscribe({
                    error: (_) => {
                        this.messageServiceHelper.showError({
                            key: this.formDataService.formId!
                        });
                    },
                    complete: () => {
                        this.messageServiceHelper.showSuccess({
                            key: this.formDataService.formId!
                        });
                        this.getInvoices();
                    }
                })
        );
    }

    get canEditInvoices() {
        return !this.isReadOnly && this.hasInvoicingWritePermission;
    }
}
