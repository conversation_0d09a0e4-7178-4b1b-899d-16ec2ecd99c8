import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { AuthorizationService } from '@kmd-elements-ui/authorization';
import { Subscription } from 'rxjs';
import { FormState, FormType, PendingUpdateAreaType } from 'src/app/api/installation-forms-client';
import { Permissions } from 'src/app/core/constants/permissions';
import { WidgetNames } from 'src/app/core/constants/widget-names';
import { AutomaticFormRefreshService } from 'src/app/core/services/automatic-form-refresh/automatic-form-refresh.service';
import { WidgetComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget.component';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';

@Component({
    selector: 'app-invoices-widget',
    templateUrl: './invoices-widget.component.html',
    styleUrl: './invoices-widget.component.scss',
    standalone: false
})
export class InvoicesWidgetComponent extends WidgetComponent implements OnInit, OnDestroy {
    widgetName: string = WidgetNames.invoicesWidget;
    private subscription: Subscription = new Subscription();

    createNewInvoiceButtonDisabled: boolean = false;

    public showCreateInvoice: boolean = false;
    public hasInvoiceReadPermission = false;
    public hasInvoiceWritePermission = false;

    constructor(
        readonly formDataService: FormDataService,
        private readonly authService: AuthorizationService,
        private readonly automaticFormRefreshService: AutomaticFormRefreshService
    ) {
        super(formDataService);
    }

    ngOnInit() {
        this.authService.hasPermissions([Permissions.singleInvoicing.read]).subscribe((x) => (this.hasInvoiceReadPermission = x));
        this.authService
            .hasPermissions([Permissions.singleInvoicing.write])
            .subscribe((x) => (this.hasInvoiceWritePermission = x));
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
        this.automaticFormRefreshService.unsubscribeForRefresh(PendingUpdateAreaType.Invoices);
    }

    closeCreateInvoice() {
        this.showCreateInvoice = false;
    }

    protected canCreateInvoices(): boolean {
        return this.canEditInvoices() && this.formDataService.hasValidPayerAddress;
    }

    protected canEditInvoices(): boolean {
        return (
            (this.formDataService.state === FormState.Registered ||
                this.formDataService.state === FormState.Instructed ||
                this.formDataService.state === FormState.Completed) &&
            this.formDataService.canCurrentUserExecuteActions &&
            this.hasInvoiceWritePermission &&
            !this.isReadOnly
        );
    }

    protected createNewInvoiceButtonClick() {
        this.showCreateInvoice = true;
    }
}
