import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { AuthorizationService } from '@kmd-elements-ui/authorization';
import { Subscription } from 'rxjs';
import { FormState, PendingUpdateAreaType } from 'src/app/api/installation-forms-client';
import { Permissions } from 'src/app/core/constants/permissions';
import { WidgetNames } from 'src/app/core/constants/widget-names';
import { AutomaticFormRefreshService } from 'src/app/core/services/automatic-form-refresh/automatic-form-refresh.service';
import { WidgetComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget.component';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';

@Component({
    selector: 'app-invoices-widget',
    templateUrl: './invoices-widget.component.html',
    standalone: false
})
export class InvoicesWidgetComponent extends WidgetComponent implements OnInit, OnDestroy {
    widgetName: string = WidgetNames.invoicesWidget;
    subscription: Subscription = new Subscription();

    createNewInvoiceButtonDisabled: boolean = false;

    showCreateInvoice: boolean = false;
    hasInvoiceReadPermission = false;
    hasInvoiceWritePermission = false;

    constructor(
        protected readonly formDataService: FormDataService,
        private readonly authService: AuthorizationService,
        private readonly automaticFormRefreshService: AutomaticFormRefreshService
    ) {
        super(formDataService);
    }

    ngOnInit() {
        this.authService.hasPermissions([Permissions.singleInvoicing.read]).subscribe((x) => (this.hasInvoiceReadPermission = x));
        this.authService
            .hasPermissions([Permissions.singleInvoicing.write])
            .subscribe((x) => (this.hasInvoiceWritePermission = x));
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
        this.automaticFormRefreshService.unsubscribeForRefresh(PendingUpdateAreaType.Invoices);
    }

    closeCreateInvoice() {
        this.showCreateInvoice = false;
        this.automaticFormRefreshService.restartPolling();
    }

    protected canCreateInvoices(): boolean {
        return this.canEditInvoices();
    }

    protected canEditInvoices(): boolean {
        return (
            (this.formDataService.state === FormState.Registered ||
                this.formDataService.state === FormState.Instructed ||
                this.formDataService.state === FormState.Completed) &&
            this.formDataService.canCurrentUserExecuteActions &&
            this.hasInvoiceWritePermission &&
            !this.isReadOnly
        );
    }

    protected createNewInvoiceButtonClick() {
        this.showCreateInvoice = true;
    }
}
