import { Directive, ElementRef, Input, OnDestroy, OnInit } from '@angular/core';
import { AbstractControl } from '@angular/forms';
import { combineLatest, Observable, Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, startWith, switchMap, takeUntil } from 'rxjs/operators';
import { ValidationMessageProviderService } from './validation-message-provider.service';

/**
 * Extract arguments of function
 */
export type ArgumentsType<F> = F extends (...args: infer A) => any ? A : never;

/**
 * Creates an object like O. Optionally provide minimum set of properties P which the objects must share to conform
 */
type ObjectLike<O extends object, P extends keyof O = keyof O> = Pick<O, P>;

/**
 * Extract a touched changed observable from an abstract control
 * @param control AbstractControl like object with markAsTouched method
 */
export const extractTouchedChanges = (
    control: ObjectLike<AbstractControl, 'markAsTouched' | 'markAsUntouched'>
): Observable<boolean> => {
    const prevMarkAsTouched = control.markAsTouched.bind(control);
    const prevMarkAsUntouched = control.markAsUntouched.bind(control);

    const touchedChanges$ = new Subject<boolean>();

    function nextMarkAsTouched(...args: ArgumentsType<AbstractControl['markAsTouched']>) {
        prevMarkAsTouched(...args);
        touchedChanges$.next(true);
    }

    function nextMarkAsUntouched(...args: ArgumentsType<AbstractControl['markAsUntouched']>) {
        prevMarkAsUntouched(...args);
        touchedChanges$.next(false);
    }

    control.markAsTouched = nextMarkAsTouched;
    control.markAsUntouched = nextMarkAsUntouched;

    return touchedChanges$;
};

@Directive({
    // eslint-disable-next-line @angular-eslint/directive-selector
    selector: '[controlValidationErrors]',
    standalone: false
})
export class ControlValidationErrorsDirective implements OnInit, OnDestroy {
    @Input() controlValidationErrors!: AbstractControl;
    private readonly delay: number = 100;
    private readonly destroy$ = new Subject<void>();

    constructor(
        private readonly element: ElementRef,
        private readonly translationService: ValidationMessageProviderService
    ) {}

    ngOnInit(): void {
        if (!this.controlValidationErrors?.enabled) {
            return;
        }

        combineLatest([
            extractTouchedChanges(this.controlValidationErrors).pipe(distinctUntilChanged()),
            this.controlValidationErrors.statusChanges.pipe(startWith(this.controlValidationErrors.status))
        ])
            .pipe(
                takeUntil(this.destroy$),
                debounceTime(this.delay),
                switchMap(() => {
                    return this.translationService.getMessages(this.controlValidationErrors.errors);
                })
            )
            .subscribe((messages) => {
                this.showValidationMessages(messages);
            });
    }

    private showValidationMessages(messages: string[]) {
        if (Array.isArray(messages) && messages?.length > 0 && this.controlValidationErrors.enabled) {
            this.showMessage(messages[0]);
        } else {
            this.showMessage('');
        }
    }

    private showMessage(message: string) {
        this.element.nativeElement.innerText = message;
    }

    ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }
}
