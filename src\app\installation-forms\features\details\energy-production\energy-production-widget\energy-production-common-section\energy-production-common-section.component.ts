import { Component, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { Subscription, pairwise, startWith } from 'rxjs';
import { EnergyProductionConnectionType, FormCategory, OwnerIdentificationType } from 'src/app/api/installation-forms-client';
import { FormDataService } from 'src/app/core/services/form-data/form-data.service';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import { WidgetWithFormComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget-with-form.component';

@Component({
    selector: 'app-energy-production-common-section',
    templateUrl: './energy-production-common-section.component.html',
    styleUrls: ['./energy-production-common-section.component.scss'],
    standalone: false
})
export class EnergyProductionCommonSectionComponent extends WidgetWithFormComponent implements OnInit, OnDestroy {
    @Input() form!: FormGroup;
    @Input() type!: FormCategory;

    subscription: Subscription = new Subscription();
    energyProductionConnectionTypeOptions: SelectItem[] = [];
    ownerIdentificationTypeOptions: SelectItem[] = [];

    constructor(
        private readonly translateService: TranslateService,
        readonly formDataService: FormDataService
    ) {
        super(formDataService);
    }

    ngOnInit(): void {
        this.getEnumTranslations();
        this.form
            .get('ownerIdentificationType')
            ?.valueChanges.pipe(startWith(null), pairwise())
            .subscribe(([prev, next]) => {
                if (prev && prev !== next) {
                    this.form.get('ownerIdentifier')?.setValue('');
                    this.form.get('ownerIdentifierBirthDate')?.setValue('');
                }
            });
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    getEnumTranslations() {
        this.energyProductionConnectionTypeOptions = enumMapper.map(
            this.translateService.instant('enums.energyProductionConnectionType'),
            EnergyProductionConnectionType
        );

        this.ownerIdentificationTypeOptions = enumMapper.map(
            this.translateService.instant('enums.ownerIdentificationType'),
            OwnerIdentificationType
        );
    }
}
