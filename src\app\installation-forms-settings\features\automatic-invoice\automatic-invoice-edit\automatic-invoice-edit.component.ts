import { Component } from '@angular/core';
import { AbstractControl, FormBuilder, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import {
    AutomaticInvoiceRule,
    AutomaticInvoiceRuleCreateOrUpdate,
    AutomationVoltageLevel,
    FormType,
    InstallationFormsClient,
    Price,
    SupplyType,
    ValueRange
} from 'src/app/api/installation-forms-client';
import { SETTINGS_DISPLAY_NAME_MAX_LENGTH } from 'src/app/core/constants/constants';
import { WidgetNames } from 'src/app/core/constants/widget-names';
import { BooleanOptionsService } from 'src/app/core/services/boolean-options/boolean-options.service';
import { InvoicingPricesService } from 'src/app/core/services/invoicing-prices/invoicing-prices.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import conditionallyRequiredValidator from 'src/app/core/utils/validators/conditionally-required/conditionally-required.validator';
import { v4 as uuidv4 } from 'uuid';
import { BaseRuleEditComponent } from '../../base-components/base-rule-edit.component';
import { automaticInvoiceTabName, automaticInvoiceTranslationPath } from '../automatic-invoice.consts';
import { AutomaticInvoiceSettingsService } from '../services/automatic-invoice-settings.service';

@Component({
    selector: 'app-automatic-invoice-edit',
    templateUrl: './automatic-invoice-edit.component.html',
    standalone: false
})
export class AutomaticInvoiceEditComponent extends BaseRuleEditComponent<AutomaticInvoiceRule> {
    formTypeOptions: SelectItem[] = [];
    voltageLevelOptions: SelectItem[] = [];
    formCategoriesOptions: SelectItem[] = [];
    invoiceTypeOptions: SelectItem[] = [];
    tariffOptions: SelectItem[] = [];

    constructor(
        private readonly client: InstallationFormsClient,
        private readonly invoicingPricesService: InvoicingPricesService,
        private readonly fb: FormBuilder,
        protected readonly translateService: TranslateService,
        protected readonly messageServiceHelper: MessageServiceHelper,
        protected readonly booleanOptionsService: BooleanOptionsService,
        protected readonly settingsService: AutomaticInvoiceSettingsService
    ) {
        super(translateService, messageServiceHelper, automaticInvoiceTabName, automaticInvoiceTranslationPath);
        this.initForm();
        this.addFormControlsValueChanges();
        this.loadExistingTariffs();
    }

    override initForm(): void {
        this.form = this.fb.group({
            id: [null, Validators.required],
            displayName: [null, [Validators.required, Validators.maxLength(SETTINGS_DISPLAY_NAME_MAX_LENGTH)]],
            voltageLevels: [null, Validators.required],
            formTypes: [null, Validators.required],
            formCategories: [null],
            scopeOfDeliveryMin: [null, [Validators.min(0), Validators.max(2147483647)]],
            scopeOfDeliveryMax: [null, [Validators.min(0), Validators.max(2147483647)]],
            canTransferInvoiceAutomatically: [false, Validators.required],
            productionCapacityMin: [null, [Validators.min(0), Validators.max(2147483647)]],
            productionCapacityMax: [null, [Validators.min(0), Validators.max(2147483647)]],
            hadExistingProduction: [null],
            invoiceType: [null, Validators.required],
            tariff: [
                null,
                (control: AbstractControl) =>
                    conditionallyRequiredValidator(control, () => this.settingsService.showTariff(this.form))
            ]
        });
    }

    override getEnumTranslations() {
        this.formTypeOptions = enumMapper.map(this.translateService.instant('enums.type'), FormType);
        this.voltageLevelOptions = enumMapper.map(
            this.translateService.instant('enums.automationVoltageLevel'),
            AutomationVoltageLevel
        );
        this.invoiceTypeOptions = this.settingsService.updateInvoiceTypes(this.form);
    }

    loadExistingTariffs() {
        this.subscription.add(
            this.invoicingPricesService.getPricesBySupplyType(SupplyType.Electricity).subscribe({
                next: (response) => {
                    this.tariffOptions =
                        response?.map((price: Price) => {
                            return {
                                label: price.displayName,
                                value: price.id
                            };
                        }) || [];
                },
                error: (_) => {
                    this.messageServiceHelper.showError({
                        detail: this.widgetTranslations['loadPricesError'],
                        key: WidgetNames.invoicesWidget
                    });
                }
            })
        );
    }

    override setFormValue(rule: AutomaticInvoiceRule) {
        this.form.patchValue({
            id: rule.id,
            displayName: rule.displayName,
            voltageLevels: rule.voltageLevels,
            formTypes: rule.formTypes,
            formCategories: rule.formCategories,
            scopeOfDeliveryMin: rule.scopeOfDeliveryMin,
            scopeOfDeliveryMax: rule.scopeOfDeliveryMax,
            canTransferInvoiceAutomatically: rule.canTransferInvoiceAutomatically,
            productionCapacityMin: rule.productionCapacityRange?.min ?? null,
            productionCapacityMax: rule.productionCapacityRange?.max ?? null,
            hadExistingProduction: rule.hadExistingProduction,
            invoiceType: rule.invoiceType,
            tariff: rule.tariff
        });
    }

    override saveEditedClick() {
        if (this.form.invalid) {
            return;
        }
        const id = this.form.controls['id'].value;
        const rule: AutomaticInvoiceRuleCreateOrUpdate = new AutomaticInvoiceRuleCreateOrUpdate({
            displayName: this.form.controls['displayName'].value,
            voltageLevels: this.form.controls['voltageLevels'].value,
            formCategories: this.form.controls['formCategories'].value,
            formTypes: this.form.controls['formTypes'].value,
            scopeOfDeliveryMin: this.form.controls['scopeOfDeliveryMin'].value,
            scopeOfDeliveryMax: this.form.controls['scopeOfDeliveryMax'].value,
            canTransferInvoiceAutomatically: this.form.controls['canTransferInvoiceAutomatically'].value,
            productionCapacityRange:
                this.form.controls['productionCapacityMin'].value || this.form.controls['productionCapacityMax'].value
                    ? new ValueRange({
                          min: this.form.controls['productionCapacityMin'].value,
                          max: this.form.controls['productionCapacityMax'].value
                      })
                    : undefined,
            hadExistingProduction: this.form.controls['hadExistingProduction'].value,
            invoiceType: this.form.controls['invoiceType'].value,
            tariff: this.form.controls['tariff'].value
        });
        if (
            rule.scopeOfDeliveryMin !== undefined &&
            rule.scopeOfDeliveryMax !== undefined &&
            rule.scopeOfDeliveryMin > rule.scopeOfDeliveryMax
        ) {
            this.showWarningMessage(this.widgetTranslations['invoiceRuleWarningMinGreaterThanMax']);
            return;
        }
        this.isProcessing = true;
        this.subscription.add(
            this.client.updateAutomaticInvoicesRuleById(id, uuidv4(), rule).subscribe({
                next: (response) => {
                    this.ruleEdited.emit(response.result);
                    this.showSuccessMessage();
                    this.isProcessing = false;
                },
                error: (_) => {
                    this.showErrorMessage(this.widgetTranslations['invoiceRuleEditError']);
                    this.isProcessing = false;
                }
            })
        );
    }

    addFormControlsValueChanges() {
        this.subscription.add(
            this.form.get('formTypes')?.valueChanges.subscribe(() => {
                this.settingsService.updateHadExistingProductionValidation(this.form);
                this.settingsService.updateProductionCapacityValidation(this.form);
                this.formCategoriesOptions = this.settingsService.updateFormCategories(this.form);
                this.settingsService.updateShowTariffValidation(this.form);
                this.invoiceTypeOptions = this.settingsService.updateInvoiceTypes(this.form);
            })
        );
    }
}
