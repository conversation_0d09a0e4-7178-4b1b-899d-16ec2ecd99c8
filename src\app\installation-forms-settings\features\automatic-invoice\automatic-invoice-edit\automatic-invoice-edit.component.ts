import { Component } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import {
    AutomaticInvoiceRule,
    AutomaticInvoiceRuleCreateOrUpdate,
    AutomationVoltageLevel,
    FormType,
    InstallationFormsClient
} from 'src/app/api/installation-forms-client';
import { SETTINGS_DISPLAY_NAME_MAX_LENGTH } from 'src/app/core/constants/constants';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import { v4 as uuidv4 } from 'uuid';
import { BaseRuleEditComponent } from '../../base-components/base-rule-edit.component';
import { automaticInvoiceTabName, automaticInvoiceTranslationPath } from '../automatic-invoice.consts';

@Component({
    selector: 'app-automatic-invoice-edit',
    templateUrl: './automatic-invoice-edit.component.html',
    styleUrl: './automatic-invoice-edit.component.scss',
    standalone: false
})
export class AutomaticInvoiceEditComponent extends BaseRuleEditComponent<AutomaticInvoiceRule> {
    formTypeOptions: SelectItem[] = [];
    voltageLevelOptions: SelectItem[] = [];

    constructor(
        protected readonly fb: FormBuilder,
        protected readonly translateService: TranslateService,
        protected readonly client: InstallationFormsClient,
        protected readonly messageServiceHelper: MessageServiceHelper
    ) {
        super(fb, translateService, messageServiceHelper, automaticInvoiceTabName, automaticInvoiceTranslationPath);
        this.initForm();
    }

    override initForm(): void {
        this.form = this.fb.group({
            id: [null, Validators.required],
            displayName: [null, [Validators.required, Validators.maxLength(SETTINGS_DISPLAY_NAME_MAX_LENGTH)]],
            voltageLevels: [null, Validators.required],
            formTypes: [null, Validators.required],
            scopeOfDeliveryMin: [null, [Validators.min(0), Validators.max(2147483647)]],
            scopeOfDeliveryMax: [null, [Validators.min(0), Validators.max(2147483647)]],
            canTransferInvoiceAutomatically: [false, Validators.required]
        });
    }

    override getEnumTranslations() {
        this.formTypeOptions = enumMapper.map(this.translateService.instant('enums.type'), [
            FormType.NewInstallation,
            FormType.Extension,
            FormType.Termination
        ]);
        this.voltageLevelOptions = enumMapper.map(
            this.translateService.instant('enums.automationVoltageLevel'),
            AutomationVoltageLevel
        );
    }

    override setFormValue(rule: AutomaticInvoiceRule) {
        this.form.patchValue({
            id: rule.id,
            displayName: rule.displayName,
            voltageLevels: rule.voltageLevels,
            formTypes: rule.formTypes,
            scopeOfDeliveryMin: rule.scopeOfDeliveryMin,
            scopeOfDeliveryMax: rule.scopeOfDeliveryMax,
            canTransferInvoiceAutomatically: rule.canTransferInvoiceAutomatically
        });
    }

    override saveEditedClick() {
        if (this.form.invalid) {
            return;
        }
        const id = this.form.controls['id'].value;
        const rule: AutomaticInvoiceRuleCreateOrUpdate = new AutomaticInvoiceRuleCreateOrUpdate({
            displayName: this.form.controls['displayName'].value,
            voltageLevels: this.form.controls['voltageLevels'].value,
            formTypes: this.form.controls['formTypes'].value,
            scopeOfDeliveryMin: this.form.controls['scopeOfDeliveryMin'].value,
            scopeOfDeliveryMax: this.form.controls['scopeOfDeliveryMax'].value,
            canTransferInvoiceAutomatically: this.form.controls['canTransferInvoiceAutomatically'].value
        });
        if (
            rule.scopeOfDeliveryMin !== undefined &&
            rule.scopeOfDeliveryMax !== undefined &&
            rule.scopeOfDeliveryMin > rule.scopeOfDeliveryMax
        ) {
            this.showWarningMessage(this.widgetTranslations['invoiceRuleWarningMinGreaterThanMax']);
            return;
        }
        this.isProcessing = true;
        this.subscription.add(
            this.client.updateAutomaticInvoicesRuleById(id, uuidv4(), rule).subscribe({
                next: (response) => {
                    this.ruleEdited.emit(response.result);
                    this.showSuccessMessage();
                    this.isProcessing = false;
                },
                error: (_) => {
                    this.showErrorMessage(this.widgetTranslations['invoiceRuleEditError']);
                    this.isProcessing = false;
                }
            })
        );
    }
}
