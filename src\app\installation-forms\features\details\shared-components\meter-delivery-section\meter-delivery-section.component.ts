import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { Subscription } from 'rxjs';
import { DeliveryOption } from 'src/app/api/installation-forms-client';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import { WidgetWithFormComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget-with-form.component';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';

@Component({
    selector: 'app-meter-delivery-section',
    templateUrl: './meter-delivery-section.component.html',
    standalone: false
})
export class MeterDeliverySectionComponent extends WidgetWithFormComponent implements OnInit, OnD<PERSON>roy {
    @Input() form!: FormGroup;
    @Input() showDeliveryOption: boolean = true;

    deliveryOptions: SelectItem[] = [];

    @Output() changesMade = new EventEmitter<{ key: string; hasChanges: boolean }>();

    subscription: Subscription = new Subscription();

    constructor(
        private readonly translateService: TranslateService,
        protected readonly formDataService: FormDataService
    ) {
        super(formDataService);
    }

    ngOnInit(): void {
        this.subscription.add(
            this.form.get('deliveryOption')?.valueChanges.subscribe((_) => {
                setTimeout(() => {
                    ['name', 'meterDeliveryAddress', 'attention'].forEach((x) => {
                        const control = this.form.get(x)!;
                        control.setErrors(null);
                        control.updateValueAndValidity({
                            onlySelf: true,
                            emitEvent: false
                        });
                    });
                });
            })
        );

        this.getEnumTranslations();
    }

    shouldShowDelivery = () => {
        return this.form?.get('deliveryOption')?.value === DeliveryOption.InstallerWillHandleSendMeter;
    };

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    private getEnumTranslations() {
        this.deliveryOptions = enumMapper.map(this.translateService.instant('enums.meterDeliveryOption'), DeliveryOption);
    }
}
