<div #overlayContainer></div>
<p-card>
    <app-markdown-viewer [markdownEnUs]="internalResourceMarkdownEnUs" [markdownDaDK]="internalResourceMarkdownDaDK">
    </app-markdown-viewer>
    <div fxLayout="column" class="mt-10 zebra-container" [formGroup]="form">
        <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label [labelRequired]="form.controls['defaultSenderAddress']" for="defaultSenderAddress">
                    {{ internalResourceTranslationPath + '.defaultSenderAddress' | translate }}
                </label>
            </div>
            <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                <input
                    id="defaultSenderAddress"
                    type="text"
                    pInputText
                    maxlength="320"
                    formControlName="defaultSenderAddress"
                    autoTrimWhitespaces />
                <small [controlValidationErrors]="form.controls['defaultSenderAddress']" class="p-error"></small>
            </div>
        </div>

        <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label [labelRequired]="form.controls['defaultReceiverAddress']" for="defaultReceiverAddress">
                    {{ internalResourceTranslationPath + '.defaultReceiverAddress' | translate }}
                </label>
            </div>
            <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                <input
                    id="defaultReceiverAddress"
                    type="text"
                    pInputText
                    maxlength="320"
                    formControlName="defaultReceiverAddress"
                    autoTrimWhitespaces />
                <small [controlValidationErrors]="form.controls['defaultReceiverAddress']" class="p-error"></small>
            </div>
        </div>

        <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label [labelRequired]="form.controls['defaultSubject']" for="defaultSubject">
                    {{ internalResourceTranslationPath + '.defaultSubject' | translate }}
                </label>
            </div>
            <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                <p-inputgroup>
                    <input
                        id="defaultSubject"
                        type="text"
                        pInputText
                        maxlength="200"
                        formControlName="defaultSubject"
                        autoTrimWhitespaces />
                    <p-inputgroup-addon [pTooltip]="tooltipContent" tooltipPosition="left">
                        <i class="fa-solid fa-info"></i>
                    </p-inputgroup-addon>
                </p-inputgroup>
                <small [controlValidationErrors]="form.controls['defaultSubject']" class="p-error"></small>
            </div>
        </div>

        <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label [labelRequired]="form.controls['templateId']" for="templateId">
                    {{ internalResourceTranslationPath + '.template' | translate }}
                </label>
            </div>
            <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                <p-select
                    id="emailTemplateOptions"
                    [options]="templateOptions"
                    optionValue="value"
                    optionLabel="label"
                    [appendTo]="overlayContainer"
                    [placeholder]="'common.selectValue' | translate"
                    formControlName="templateId">
                </p-select>
                <small [controlValidationErrors]="form.controls['templateId']" class="p-error"></small>
            </div>
        </div>

        <div class="flex justify-content-end mt-10">
            <button
                id="cancelButton"
                [disabled]="isProcessing || (this.dirty$ | async) === false"
                type="button"
                pButton
                pRipple
                class="mr-2 p-button-secondary"
                (click)="onCancelChangesClicked()">
                {{ 'common.cancelChanges' | translate | titlecase }}
            </button>
            <button
                id="saveButton"
                type="button"
                pButton
                pRipple
                (click)="updateInternalResource()"
                [disabled]="isProcessing || (this.dirty$ | async) === false || this.form.invalid">
                <span *ngIf="isProcessing" class="fa fa-spin fa-spinner"></span>
                {{ 'common.saveChanges' | translate | titlecase }}
            </button>
        </div>
    </div>
</p-card>

<p-toast [key]="internalResourceTabName"></p-toast>

<p-toast #genericMessagesToast></p-toast>

<ng-template #tooltipContent>
    <div class="flex items-center w-30rem">
        <app-markdown-viewer
            [markdownEnUs]="internalResourceSubjectTooltipEnUs"
            [markdownDaDK]="internalResourceSubjectTooltipDaDK">
        </app-markdown-viewer>
    </div>
</ng-template>
