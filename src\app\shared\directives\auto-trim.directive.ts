import { Directive, HostListener, Self } from '@angular/core';
import { NgControl } from '@angular/forms';

@Directive({
    // eslint-disable-next-line @angular-eslint/directive-selector
    selector: '[autoTrimWhitespaces]',
    standalone: false
})
export class AutoTrimDirective {
    constructor(@Self() private readonly ngControl: NgControl) {}

    @HostListener('blur')
    onBlur() {
        if (this.ngControl.control?.value) {
            const value = this.ngControl.control.value;
            if (typeof value === 'string') {
                const trimmedValue = value.trim();
                if (trimmedValue !== value) {
                    this.ngControl.control.setValue(trimmedValue);
                }
            }
        }
    }

    @HostListener('input', ['$event.target.value'])
    onInput(value: string) {
        // Optional: Prevent typing spaces at the beginning
        if (value?.startsWith(' ')) {
            this.ngControl.control?.setValue(value.trimStart());
        }
    }
}
