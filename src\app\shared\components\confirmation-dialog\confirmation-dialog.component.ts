import { Component, EventEmitter, Input, Output } from '@angular/core';

@Component({
    selector: 'app-confirmation-dialog',
    templateUrl: './confirmation-dialog.component.html',
    styleUrl: './confirmation-dialog.component.scss',
    standalone: false,
    host: { hostID: crypto.randomUUID().toString() }
})
export class ConfirmationDialogComponent {
    @Input() headerKey: string = '';
    @Input() messageKey: string = '';
    @Input() visible: boolean = false;

    @Output() confirmEvent = new EventEmitter<void>();
    @Output() cancelEvent = new EventEmitter<void>();

    onYesClick(): void {
        this.confirmEvent.emit();
    }

    onNoClick(): void {
        this.cancelEvent.emit();
    }
}
