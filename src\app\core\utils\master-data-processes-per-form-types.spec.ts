import { FormType, MasterDataProcessType, TerminationScope } from 'src/app/api/installation-forms-client';
import { getAvailableMasterDataProcessTypes } from './master-data-processes-per-form-types';

describe('getAvailableMasterDataProcessTypes', () => {
    describe('invalid inputs', () => {
        it('should return empty array for no form types', () => {
            expect(getAvailableMasterDataProcessTypes([])).toEqual([]);
        });

        it('should return empty array for undefined form types', () => {
            expect(getAvailableMasterDataProcessTypes(undefined as unknown as FormType[])).toEqual([]);
        });
    });

    describe('single form type scenarios', () => {
        it('should return correct process types for NewInstallation', () => {
            const result = getAvailableMasterDataProcessTypes([FormType.NewInstallation]);
            expect(result).toEqual([
                MasterDataProcessType.ModifyConnectionPoint,
                MasterDataProcessType.CreateConnectionPoint,
                MasterDataProcessType.CloseDownSupplyType
            ]);
        });

        it('should return only ModifyConnectionPoint for regular form types', () => {
            const result = getAvailableMasterDataProcessTypes([FormType.ChangeBranchLine]);
            expect(result).toEqual([MasterDataProcessType.ModifyConnectionPoint]);
        });

        it('should return only modify CP for Termination without scope', () => {
            const result = getAvailableMasterDataProcessTypes([FormType.Termination]);
            expect(result).toEqual([MasterDataProcessType.ModifyConnectionPoint]);
        });

        it('should return correct process types for EnergyProduction', () => {
            const result = getAvailableMasterDataProcessTypes([FormType.EnergyProduction]);
            expect(result).toEqual([
                MasterDataProcessType.ModifyConnectionPoint,
                MasterDataProcessType.AddConnectionPointNetSettlementGroup,
                MasterDataProcessType.ModifyConnectionPointNetSettlementGroup
            ]);
        });
    });

    describe('termination with scope scenarios', () => {
        it('should return correct process types for Termination with EntireInstallation scope', () => {
            const result = getAvailableMasterDataProcessTypes([FormType.Termination], TerminationScope.EntireInstallation);
            expect(result).toEqual([MasterDataProcessType.ModifyConnectionPoint, MasterDataProcessType.CloseDownSupplyType]);
        });

        it('should return correct process types for Termination with EnergyProduction scope', () => {
            const result = getAvailableMasterDataProcessTypes([FormType.Termination], TerminationScope.EnergyProduction);
            expect(result).toEqual([MasterDataProcessType.ModifyConnectionPoint, MasterDataProcessType.CloseDownProduction]);
        });
    });

    describe('multiple form types scenarios', () => {
        it('should return intersection of allowed process types for multiple form types', () => {
            const result = getAvailableMasterDataProcessTypes([FormType.NewInstallation, FormType.ChangeBranchLine]);
            expect(result).toEqual([MasterDataProcessType.ModifyConnectionPoint]);
        });

        it('should handle Termination and NewInstallation with entire installation scope', () => {
            const result = getAvailableMasterDataProcessTypes(
                [FormType.Termination, FormType.NewInstallation],
                TerminationScope.EntireInstallation
            );
            expect(result).toEqual([MasterDataProcessType.ModifyConnectionPoint, MasterDataProcessType.CloseDownSupplyType]);
        });
    });
});
