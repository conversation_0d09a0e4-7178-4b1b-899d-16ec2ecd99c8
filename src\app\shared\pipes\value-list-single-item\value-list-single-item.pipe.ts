import { Injectable, Pipe, PipeTransform } from '@angular/core';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { ValueListModel, ValueListType } from '../../../api/installation-forms-client';
import { ValueListsService } from '../../../core/services/value-lists/value-lists.service';

@Injectable()
@Pipe({
    name: 'valueListSingleItem',
    standalone: false
})
export class ValueListSingleItemPipe implements PipeTransform {
    constructor(private readonly valueListsService: ValueListsService) {}

    transform(id: string, type: ValueListType | undefined): Observable<string> {
        if (!type) return of('');
        return this.valueListsService.getValueList(type).pipe(
            map((valueList: ValueListModel | undefined) => {
                if (!valueList?.valueItems?.length) return '';
                return valueList.valueItems.find((item) => item.id === id)?.displayValue ?? '';
            })
        );
    }
}
