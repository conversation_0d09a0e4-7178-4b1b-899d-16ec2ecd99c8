import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AuthorizationService } from '@kmd-elements-ui/authorization';
import { KeyActions, KeyboardShortcuts, KeyboardShortcutsService } from '@kmd-elements-ui/shortcuts';
import { TabsService } from '@kmd-elements-ui/tabs';
import { TranslateService } from '@ngx-translate/core';
import { TableRowReorderEvent } from 'primeng/table';
import { BehaviorSubject, catchError, EMPTY, filter, finalize, of, Subscription, switchMap, tap } from 'rxjs';
import { InstallationFormsClient, InstructionText, ReorderModel } from 'src/app/api/installation-forms-client';
import { Permissions } from 'src/app/core/constants/permissions';
import { DefaultInstructionTextService } from 'src/app/core/services/default-instruction-text/default-instruction-text.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { ValueListsService } from 'src/app/core/services/value-lists/value-lists.service';
import { v4 as uuidv4 } from 'uuid';
import { RulesListColumn } from '../models/rules-list-column';
import { INSTRUCTION_TEXTS_COLUMNS } from './constants/columns';
import {
    defaultInstructionTextsMarkdownDaDK,
    defaultInstructionTextsMarkdownEnUs,
    defaultInstructionTextsTabName,
    defaultInstructionTextsTranslationPath
} from './constants/default-instruction-texts.consts';
import { DefaultInstructionTextsCreateComponent } from './default-instruction-texts-create/default-instruction-texts-create.component';
import { DefaultInstructionTextsEditComponent } from './default-instruction-texts-edit/default-instruction-texts-edit.component';
import { DefaultInstructionTextsListItem } from './models/default-instruction-texts-list-item';
import { DefaultInstructionTextsMapper } from './services/default-instruction-texts-mapper';

@Component({
    selector: 'app-default-instruction-texts',
    templateUrl: './default-instruction-texts.component.html',
    styleUrls: ['./default-instruction-texts.component.scss'],
    standalone: false
})
export class DefaultInstructionTextsComponent implements OnInit, OnDestroy {
    private widgetTranslations: any;

    private _isCreating: boolean = false;
    private _defaultInstructionTextInEdit?: InstructionText;
    private _isReordered: boolean = false;

    get isCreating(): boolean {
        return this._isCreating;
    }

    set isCreating(value: boolean) {
        this._isCreating = value;
        this.recalculateDirty();
    }

    get defaultInstructionTextInEdit(): InstructionText | undefined {
        return this._defaultInstructionTextInEdit;
    }

    set defaultInstructionTextInEdit(value: InstructionText | undefined) {
        this._defaultInstructionTextInEdit = value;
        this.recalculateDirty();
    }

    get isReordered(): boolean {
        return this._isReordered;
    }

    set isReordered(value: boolean) {
        this._isReordered = value;
        this.recalculateDirty();
    }

    defaultInstructionTexts: DefaultInstructionTextsListItem[] = [];
    rawDefaultInstructionTexts: InstructionText[] = [];

    subscription: Subscription = new Subscription();

    defaultInstructionTextsMarkdownEnUs = defaultInstructionTextsMarkdownEnUs;
    defaultInstructionTextsMarkdownDaDK = defaultInstructionTextsMarkdownDaDK;

    isProcessing: boolean = false;

    dirty$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

    defaultInstructionTextsTabName = defaultInstructionTextsTabName;

    hasUserFormConfigurationWritePermission: boolean = false;

    private _tabId?: string;

    columns: RulesListColumn[] = INSTRUCTION_TEXTS_COLUMNS;

    private keyEventsSubscription: Subscription = new Subscription();

    @ViewChild('defaultInstructionTextsCreate') defaultInstructionTextsCreate?: DefaultInstructionTextsCreateComponent;
    @ViewChild('defaultInstructionTextsEdit') defaultInstructionTextsEdit?: DefaultInstructionTextsEditComponent;

    keyActions: KeyActions = {
        [KeyboardShortcuts.ShiftPlusN]: () => {
            this.startCreating();
        },
        [KeyboardShortcuts.Escape]: () => {
            this.tabsService.close();
        },
        [KeyboardShortcuts.Enter]: () => {
            if (this.defaultInstructionTextInEdit) {
                this.defaultInstructionTextsEdit?.saveEditedClick();
                return;
            }

            if (this.isCreating) {
                this.defaultInstructionTextsCreate?.createClick();
                return;
            }

            if (this.isReordered) {
                this.saveReorder();
                return;
            }
        }
    };
    attemptedAction: (() => void) | null = null;
    private defaultInstructionTextToDelete?: DefaultInstructionTextsListItem;

    private originalOrder: string[] = [];

    constructor(
        private readonly client: InstallationFormsClient,
        private readonly translateService: TranslateService,
        private readonly messageServiceHelper: MessageServiceHelper,
        protected tabsService: TabsService,
        private readonly activatedRoute: ActivatedRoute,
        private readonly authService: AuthorizationService,
        private readonly keyboardShortcutsService: KeyboardShortcutsService,
        private readonly defaultInstructionTextsMapper: DefaultInstructionTextsMapper,
        private readonly valueListService: ValueListsService,
        private readonly defaultInstructionTextsService: DefaultInstructionTextService
    ) {
        this.addTab();
    }

    ngOnInit(): void {
        this.widgetTranslations = this.translateService.instant(defaultInstructionTextsTranslationPath);

        this.authService
            .hasPermissions([Permissions.formConfiguration.write])
            .subscribe((x) => (this.hasUserFormConfigurationWritePermission = x));

        this.registerRefreshButtonListener();
        this.loadScreeningWithTemplates();
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    subscribeKeyShortcuts(): void {
        this.keyEventsSubscription = this.keyboardShortcutsService.registerKeyboardShortcuts(this.keyActions).subscribe();
    }

    unsubscribeKeyShortcuts(): void {
        this.keyEventsSubscription.unsubscribe();
    }

    private loadScreeningWithTemplates() {
        this.isProcessing = true;
        this.subscription.add(
            this.client
                .getInstructionTexts(uuidv4())
                .pipe(
                    catchError((_) => {
                        this.messageServiceHelper.showError({
                            key: defaultInstructionTextsTabName,
                            detail: this.widgetTranslations['getInstructionTextsError']
                        });
                        return EMPTY;
                    })
                )
                .pipe(finalize(() => (this.isProcessing = false)))
                .subscribe({
                    next: (response) => {
                        const sortedRules = [...response.result];
                        sortedRules.sort((a, b) => a.order - b.order);
                        this.rawDefaultInstructionTexts = sortedRules;
                        this.defaultInstructionTexts = sortedRules.map((rule) =>
                            this.defaultInstructionTextsMapper.mapToInstructionTextsListItem(rule)
                        );
                        this.originalOrder = this.defaultInstructionTexts.map((x) => x.id);
                    }
                })
        );
    }

    protected editRule(event: MouseEvent, rule: DefaultInstructionTextsListItem) {
        event.stopPropagation();
        this.defaultInstructionTextInEdit = this.rawDefaultInstructionTexts.find((r) => r.id === rule.id);
        this.isCreating = false;
    }

    protected startCreating() {
        this.isCreating = true;
        this.defaultInstructionTextInEdit = undefined;
    }

    protected cancelCreating() {
        this.isCreating = false;
    }

    protected cancelEditing() {
        this.defaultInstructionTextInEdit = undefined;
    }

    protected onRuleCreated(rule: InstructionText) {
        this.isCreating = false;
        this.rawDefaultInstructionTexts.push(rule);
        this.defaultInstructionTexts.push(this.defaultInstructionTextsMapper.mapToInstructionTextsListItem(rule));
        this.originalOrder.push(rule.id);
        this.defaultInstructionTextsService.invalidateInstructionTextsCache();
    }

    protected onRuleEdited(updatedRule: InstructionText) {
        const editedIndex = this.defaultInstructionTexts.findIndex((r) => r.id === updatedRule.id);
        if (editedIndex === -1) {
            return;
        }

        this.rawDefaultInstructionTexts[editedIndex] = updatedRule;
        this.defaultInstructionTexts[editedIndex] = this.defaultInstructionTextsMapper.mapToInstructionTextsListItem(updatedRule);
        this.defaultInstructionTextInEdit = undefined;
        this.defaultInstructionTextsService.invalidateInstructionTextsCache();
    }

    private addTab() {
        this._tabId = this.tabsService.addOrUpdateTab(this.activatedRoute, {
            icon$: of('fas fa-th-list'),
            areaLabel$: of(this.translateService.instant(`${defaultInstructionTextsTranslationPath}.tabAreaLabel`)),
            label$: of(this.translateService.instant(`${defaultInstructionTextsTranslationPath}.tabLabel`)),
            tooltip$: of(this.translateService.instant(`${defaultInstructionTextsTranslationPath}.tabLabel`)),
            dirty$: this.dirty$,
            canReload$: of(true)
        });
    }

    registerRefreshButtonListener(): void {
        this.subscription.add(
            this.tabsService.reloadButtonClicked$
                .pipe(
                    filter((tabId) => tabId === this._tabId),
                    switchMap((tabId) => this.tabsService.reload(tabId)),
                    filter((canBeReloaded) => canBeReloaded),
                    tap(() => {
                        this.loadScreeningWithTemplates();
                        this.valueListService.fetchValueLists();
                    })
                )
                .subscribe(() => {
                    this.tabsService.tabReloaded(this._tabId as string);
                    this.undoChanges();
                })
        );
    }

    undoChanges() {
        this.cancelCreating();
        this.cancelEditing();
        this.cancelReorder();
    }

    onRowReorder(_: TableRowReorderEvent) {
        this.setIsReordered();
    }

    setIsReordered() {
        this.isReordered = !this.defaultInstructionTexts.every((rule, index) => rule.id === this.originalOrder[index]);
    }

    cancelReorder() {
        this.defaultInstructionTexts = this.originalOrder.map(
            (id) => this.defaultInstructionTexts.find((rule) => rule.id === id)!
        );
        this.setIsReordered();
    }

    protected deleteRule(event: MouseEvent, item: DefaultInstructionTextsListItem) {
        event.stopPropagation();
        this.defaultInstructionTextToDelete = item;
        this.attemptedAction = () => this.performDelete();
    }

    private performDelete() {
        if (!this.defaultInstructionTextToDelete) {
            return;
        }

        this.isProcessing = true;
        this.subscription.add(
            this.client
                .deleteInstructionTextById(this.defaultInstructionTextToDelete.id, uuidv4())
                .pipe(
                    finalize(() => {
                        this.isProcessing = false;
                        this.defaultInstructionTextToDelete = undefined;
                    })
                )
                .subscribe({
                    complete: () => {
                        this.messageServiceHelper.showSuccess({
                            key: defaultInstructionTextsTabName
                        });
                        this.defaultInstructionTexts = this.defaultInstructionTexts.filter(
                            (r) => r.id !== this.defaultInstructionTextToDelete?.id
                        );
                        this.originalOrder = this.originalOrder.filter((id) => id !== this.defaultInstructionTextToDelete?.id);
                        this.rawDefaultInstructionTexts = this.rawDefaultInstructionTexts.filter(
                            (r) => r.id !== this.defaultInstructionTextToDelete?.id
                        );
                        if (this.defaultInstructionTextInEdit?.id === this.defaultInstructionTextToDelete?.id) {
                            this.defaultInstructionTextInEdit = undefined;
                        }
                    },
                    error: (_error) => {
                        this.messageServiceHelper.showError({
                            key: defaultInstructionTextsTabName,
                            detail: this.widgetTranslations['instructionTextDeleteError']
                        });
                    }
                })
        );
        this.defaultInstructionTextsService.invalidateInstructionTextsCache();
    }

    onConfirmationDialogYesButtonClick() {
        if (this.attemptedAction) {
            this.attemptedAction();
        }
        this.attemptedAction = null;
    }

    onConfirmationDialogNoButtonClick() {
        this.attemptedAction = null;
        this.defaultInstructionTextToDelete = undefined;
    }

    saveReorder() {
        this.isProcessing = true;
        this.subscription.add(
            this.client
                .reorderInstructionTexts(
                    uuidv4(),
                    new ReorderModel({
                        reorderedIds: this.defaultInstructionTexts.map((x) => x.id)
                    })
                )
                .pipe(finalize(() => (this.isProcessing = false)))
                .subscribe({
                    complete: () => {
                        this.messageServiceHelper.showSuccess({
                            key: defaultInstructionTextsTabName
                        });
                        this.originalOrder = this.defaultInstructionTexts.map((x) => x.id);
                        this.isReordered = false;
                    },
                    error: (_error) => {
                        this.messageServiceHelper.showError({
                            key: defaultInstructionTextsTabName,
                            detail: this.widgetTranslations['instructionTextReorderError']
                        });
                    }
                })
        );
    }

    recalculateDirty() {
        this.dirty$.next(this.isCreating || this.isReordered || !!this.defaultInstructionTextInEdit);
    }
}
