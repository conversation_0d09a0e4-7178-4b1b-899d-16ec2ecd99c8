import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { MessageService, SelectItem } from 'primeng/api';
import { Subscription, catchError, finalize } from 'rxjs';
import { InstallationFormsClient, SendEmail } from 'src/app/api/installation-forms-client';
import { WidgetNames } from 'src/app/core/constants/widget-names';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { WidgetWithFormComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget-with-form.component';
import { v4 as uuidv4 } from 'uuid';
import { FormDataService } from '../../../../../../core/services/form-data/form-data.service';

@Component({
    selector: 'app-send-email',
    templateUrl: './send-email.component.html',
    standalone: false
})
export class SendEmailComponent extends WidgetWithFormComponent implements OnInit, OnDestroy {
    widgetName: string = WidgetNames.emailsWidget;
    private widgetTranslations: any;
    private commonTranslations: any;

    form!: FormGroup;

    @Input() templates: SelectItem[] = [];
    @Output() closePanelRequested = new EventEmitter<unknown>();
    @Output() emailSent = new EventEmitter<unknown>();

    subscription: Subscription = new Subscription();

    constructor(
        private readonly fb: FormBuilder,
        private readonly client: InstallationFormsClient,
        private readonly translateService: TranslateService,
        private readonly messageService: MessageService,
        private readonly messageServiceHelper: MessageServiceHelper,
        protected readonly formDataService: FormDataService
    ) {
        super(formDataService);
        this.form = this.fb.group({
            templateId: [null, [Validators.required]]
        });
    }

    ngOnInit(): void {
        this.widgetTranslations = this.translateService.instant('emailsWidget');
        this.commonTranslations = this.translateService.instant('common');
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    sendEmail() {
        if (this.form.valid) {
            this.processingStarted();
            this.client
                .sendInstallationFormEmail(
                    this.formDataService.formId!,
                    uuidv4(),
                    new SendEmail({
                        templateId: this.form.get('templateId')?.value
                    })
                )
                .pipe(
                    catchError((err) => {
                        throw err;
                    }),
                    finalize(() => {
                        this.processingFinished();
                    })
                )
                .subscribe({
                    next: (_) => {
                        this.messageServiceHelper.showSuccess({
                            key: this.formDataService.formId!
                        });
                        this.afterEmailSent();
                    },
                    error: (error) => {
                        this.messageServiceHelper.addTranslated(error, this.formDataService.formId!, WidgetNames.emailsWidget, {
                            path: 'enums.fieldNames',
                            key: 'FieldName'
                        });
                    }
                });
        } else {
            this.processingFinished();
            this.messageServiceHelper.showWarning({
                detail: this.commonTranslations['allRequiredFieldsShouldBeCompleted'],
                key: this.formDataService.formId!
            });
        }
    }

    cancelSend() {
        this.clearForm();
        this.closePanelRequested.emit(null);
    }

    afterEmailSent() {
        this.clearForm();
        this.emailSent.emit(null);
    }

    private clearForm() {
        this.form.reset({
            templateId: null
        });
    }
}
