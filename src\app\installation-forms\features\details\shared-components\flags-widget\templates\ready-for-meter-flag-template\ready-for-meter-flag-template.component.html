<ng-container [formGroup]="flagControl">
    <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
        <label for="readyForMeter">{{ 'flagsWidget.types.' + flagControl.value.type!.toString() | translate }} </label>
    </div>
    <div class="zebra-edit" fxFlex="20" fxFlex.lt-md="50">
        <p-checkbox id="readyForMeter" formControlName="isSet" [binary]="true"></p-checkbox>
    </div>
    <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
        <div>{{ 'flagsWidget.readyForMeterDate' | translate }}</div>
    </div>
    <div class="zebra-edit" fxFlex="20" fxFlex.lt-md="50">{{ visibleReadyForMeterDate | formatDateTime | noValue }}</div>
</ng-container>
