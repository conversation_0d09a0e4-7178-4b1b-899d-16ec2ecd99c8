import { FormType } from 'src/app/api/installation-forms-client';

export const VOLTAGE_LEVEL_FORM_TYPES = [FormType.NewInstallation, FormType.Extension] as const;

export const FORM_CATEGORY_FORM_TYPES = [FormType.NewInstallation, FormType.EnergyProduction] as const;

export const SCOPE_OF_DELIVERY_FORM_TYPES = [FormType.NewInstallation, FormType.Extension] as const;

export const TERMINATION_SCOPE_FORM_TYPES = [FormType.Termination] as const;

export const METER_RETURN_OPTIONS_FORM_TYPES = [FormType.Termination] as const;

export const METER_PLACEMENT_CODES_FORM_TYPES = [FormType.MoveMeter] as const;

export const METER_SIZE_FORM_TYPES = [FormType.MoveMeter] as const;

export const GROUNDING_METHODS_FORM_TYPES = [FormType.ChangeBranchLine] as const;

export const REASONS_FOR_CHANGE_FORM_TYPES = [FormType.ChangeMeter] as const;
