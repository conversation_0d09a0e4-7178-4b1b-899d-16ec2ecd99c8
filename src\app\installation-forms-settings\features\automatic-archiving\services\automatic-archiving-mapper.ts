import { Injectable } from '@angular/core';
import { AutomaticArchivingRule } from 'src/app/api/installation-forms-client';
import { AutomaticArchivingListItem } from '../models/automatic-archiving-list-item';

@Injectable({
    providedIn: 'root'
})
export class AutomaticArchivingMapper {
    constructor() {}

    mapToAutomaticArchivingListItem = (rule: AutomaticArchivingRule): AutomaticArchivingListItem => ({
        id: rule.id,
        order: rule.order,
        displayName: rule.displayName,
        formTypes: rule.formTypes,
        formStates: rule.formStates,
        responsibleForMeter: rule.responsibleForMeter,
        meterInstalled: rule.meterInstalled,
        startsAsConstruction: rule.startsAsConstruction,
        verifiedWorkOrderType: rule.verifiedWorkOrderType,
        verifiedWorkOrderDescription: rule.verifiedWorkOrderDescription,
        masterDataProcessTypes: rule.masterDataProcessTypes
    });
}
