import { Pipe, PipeTransform } from '@angular/core';
import { DateTimeService } from 'src/app/core/services/date-time/date-time.service';

@Pipe({
    name: 'formatDateTime',
    standalone: false
})
export class FormatDateTimePipe implements PipeTransform {
    constructor(private readonly dateTimeService: DateTimeService) {}

    transform(value: Date | undefined): string {
        return this.dateTimeService.transformDateTime(value);
    }
}
