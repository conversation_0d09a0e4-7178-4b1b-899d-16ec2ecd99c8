import { Injectable, Pipe, PipeTransform } from '@angular/core';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { InstructionText } from 'src/app/api/installation-forms-client';
import { DefaultInstructionTextService } from 'src/app/core/services/default-instruction-text/default-instruction-text.service';

@Injectable()
@Pipe({
    name: 'instructionTexts',
    standalone: false
})
export class InstructionTextsPipe implements PipeTransform {
    constructor(private readonly defaultInstructionTexts: DefaultInstructionTextService) {}

    transform(ids: string[]): Observable<string> {
        if (!ids || ids.length === 0) return of('');

        return this.defaultInstructionTexts.getInstructionTexts().pipe(
            map((instructionTexts: InstructionText[] | undefined) => {
                if (!instructionTexts) return '';

                return ids
                    .map((id) => {
                        const item = instructionTexts.find((item) => item.id === id);
                        return item?.text ?? '';
                    })
                    .filter((value) => value !== '') // Remove empty values
                    .join(', ');
            })
        );
    }
}
