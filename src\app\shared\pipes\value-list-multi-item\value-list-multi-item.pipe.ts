import { Injectable, Pipe, PipeTransform } from '@angular/core';
import { ValueListModel } from '@kmd-elements/core-kit';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { ValueListType } from 'src/app/api/installation-forms-client';
import { ValueListsService } from 'src/app/core/services/value-lists/value-lists.service';

@Injectable()
@Pipe({
    name: 'valueListMultiItem',
    standalone: false
})
export class ValueListMultiItemPipe implements PipeTransform {
    constructor(private valueListsService: ValueListsService) {}

    transform(ids: string[], type: ValueListType | undefined): Observable<string> {
        if (!type || !ids || ids.length === 0) return of('');

        return this.valueListsService.getValueList(type).pipe(
            map((valueList: ValueListModel | undefined) => {
                if (!valueList || !valueList.valueItems) return '';

                return ids
                    .map((id) => {
                        const item = valueList.valueItems.find((item) => item.id === id);
                        return item?.displayValue ?? '';
                    })
                    .filter((value) => value !== '') // Remove empty values
                    .join(', ');
            })
        );
    }
}
