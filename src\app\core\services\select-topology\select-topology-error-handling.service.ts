import { Injectable } from '@angular/core';
import { ErrorHandlingService } from '@kmd-elements-ui/topology-selector';
import { MessageServiceHelper } from '../message/message.service';

@Injectable()
export class SelectTopologyErrorHandlingService implements ErrorHandlingService {
    constructor(private readonly messageService: MessageServiceHelper) {}

    handleError(_error: any, messageKey?: string) {
        this.messageService.showError({ key: messageKey });
    }
}
