import { Component, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AuthorizationService } from '@kmd-elements-ui/authorization';
import {
    KeyActions,
    KeyboardShortcuts,
    KeyboardShortcutsService,
    KeyboardShortcutsSubscriptionManager
} from '@kmd-elements-ui/shortcuts';
import { TabsService } from '@kmd-elements-ui/tabs';
import { AttachmentsWidgetComponent } from '@kmd-elements/attachments';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, Observable, Subject, Subscription, filter, map, of, switchMap, take, tap } from 'rxjs';
import {
    CaseWorker,
    FormScreeningStatus,
    FormScreeningStatusUpdate,
    FormState,
    FormStateUpdate,
    IBaseForm,
    InstallationFormsApiResponse,
    InstallationFormsClient,
    PendingUpdateAreaType
} from 'src/app/api/installation-forms-client';
import { AWAITING_STATE_MAX_RETRY_COUNT, AWAITING_STATE_RETRY_TIMEOUT } from 'src/app/core/constants/constants';
import { Permissions } from 'src/app/core/constants/permissions';
import { WidgetNames } from 'src/app/core/constants/widget-names';
import { AutomaticFormRefreshService } from 'src/app/core/services/automatic-form-refresh/automatic-form-refresh.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { v4 as uuidv4 } from 'uuid';
import { CaseWorkerService } from '../../../../../core/services/case-worker/case-worker.service';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';
import { ValueListsService } from '../../../../../core/services/value-lists/value-lists.service';

@Component({
    template: '',
    standalone: false
})
export abstract class BaseDetailsComponent<TFormData extends IBaseForm>
    implements KeyboardShortcutsSubscriptionManager, OnInit, OnDestroy
{
    // assuming we have attachments in all form types
    @ViewChild(AttachmentsWidgetComponent) attachmentsWidget!: AttachmentsWidgetComponent;

    protected readonly dirty$ = new BehaviorSubject<boolean>(false);
    protected readonly widgetName = WidgetNames.coreInformationWidget;
    protected _tabId?: string;
    protected formReloadCompleted = new Subject<void>();

    dirtyFlags: { [key: string]: boolean } = {};
    subscription: Subscription = new Subscription();

    awaitingStateRetriesCount: number = 0;
    attachmentObjectType: string = 'InstallationForm';

    _formDetails!: TFormData;

    private keyEventsSubscription: Subscription = new Subscription();

    keyActions: KeyActions = {
        [KeyboardShortcuts.Escape]: () => {
            this.tabsService.close();
        }
    };

    get formDetails(): TFormData {
        return this._formDetails;
    }

    set formDetails(value: TFormData) {
        this._formDetails = value;
        this.formDataService.formLoaded(value);
    }

    get isAttachmentsReadonly() {
        return !this.formDataService.canCurrentUserEditData || this.formDataService.isProcessing;
    }

    constructor(
        protected tabsService: TabsService,
        protected client: InstallationFormsClient,
        protected route: ActivatedRoute,
        protected authService: AuthorizationService,
        protected messageServiceHelper: MessageServiceHelper,
        protected translateService: TranslateService,
        protected caseWorkerService: CaseWorkerService,
        protected formDataService: FormDataService,
        protected automaticFormRefreshService: AutomaticFormRefreshService,
        protected keyboardShortcutsService: KeyboardShortcutsService,
        protected valueListsService: ValueListsService
    ) {
        this.dirtyFlags[WidgetNames.coreInformationWidget] = false;
        this.dirtyFlags[WidgetNames.caseWorkerWidget] = false;
    }

    ngOnInit(): void {
        this.authService.hasPermissions([Permissions.installationForms.write]).subscribe((x) => {
            this.formDataService.hasUserWritePermission = x;
        });

        this.route.paramMap
            .pipe(
                map((param) => param.get('formId')),
                filter((formId): formId is string => formId !== null)
            )
            .subscribe((formId) => {
                this.reloadFormDetails(formId, true);
            });

        this.automaticFormRefreshService.subscribeForRefresh(PendingUpdateAreaType.FormData, () => {
            this.reloadFormDetails(this.formDetails.formId, false, true);
            this.attachmentsWidget.reloadAttachments();
        });

        this.formDataService.reloadMasterDataCompare$.subscribe(() => {
            this.reloadMasterDataToCompare(this.formDetails.formId);
        });

        this.registerRefreshButtonListener();
    }

    subscribeKeyShortcuts(): void {
        this.keyEventsSubscription = this.keyboardShortcutsService.registerKeyboardShortcuts(this.keyActions).subscribe();
    }

    unsubscribeKeyShortcuts(): void {
        this.keyEventsSubscription.unsubscribe();
    }

    reloadMasterDataToCompare = (formId: string) => {
        this.client.getMasterDataToCompare(formId, uuidv4()).subscribe({
            next: (x) => {
                this.formDataService.masterDataToCompareLoaded(x.result);
            },
            error: (_) => {
                this.messageServiceHelper.showError({
                    key: this.formDetails.formId,
                    detail: this.translateService.instant('masterDataCompare.getDataError')
                });
            }
        });
    };

    reloadFormDetails = (formId: string | null = null, isInit: boolean = false, isAutomaticRefresh: boolean = false) => {
        if (!isAutomaticRefresh) {
            this.formDataService.processingStarted();
        }
        this.subscription.add(
            this.getDetails((formId || this.formDataService.formId)!).subscribe((formDetails) => {
                this.formDetails = formDetails.result;
                this.formReloadCompleted.next();
                if (!isAutomaticRefresh) {
                    this.formDataService.processingFinished();
                }

                if (isInit) {
                    this.addTab(this.formDetails.formNumber);
                    this.automaticFormRefreshService.startRefreshingForm(this.formDataService.formId!, this._tabId!);
                }
                this.refreshCurrentUserAsCaseWorkerStatus(this.formDetails.caseWorker);
                if (
                    this.formDetails.state === FormState.AwaitingFormSystemUpdate &&
                    this.awaitingStateRetriesCount < AWAITING_STATE_MAX_RETRY_COUNT
                ) {
                    setTimeout(() => {
                        this.awaitingStateRetriesCount++;
                        this.reloadFormDetails(this.formDetails.formId);
                    }, AWAITING_STATE_RETRY_TIMEOUT);
                } else {
                    this.awaitingStateRetriesCount = 0;
                }
            })
        );
    };

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
        this.automaticFormRefreshService.unsubscribeForRefresh(PendingUpdateAreaType.FormData);
        this.automaticFormRefreshService.stopRefreshingForm();
    }

    protected refreshCurrentUserAsCaseWorkerStatus(value?: CaseWorker) {
        this.caseWorkerService.isCurrentUser$(value?.email).subscribe((value) => {
            this.formDataService.isCurrentUserAssignedAsCaseWorker = value;
            this.formDataService.editabilityChange$.next();
        });
    }

    protected registerRefreshButtonListener(): void {
        this.subscription.add(
            this.tabsService.reloadButtonClicked$
                .pipe(
                    filter((tabId) => tabId === this._tabId),
                    switchMap((tabId) => this.tabsService.reload(tabId)),
                    filter((canBeReloaded) => canBeReloaded),
                    tap(() => {
                        this.automaticFormRefreshService.forceRefresh();
                        this.valueListsService.fetchValueLists();
                    }),
                    switchMap(() => this.formReloadCompleted.pipe(take(1)))
                )
                .subscribe(() => {
                    this.tabsService.tabReloaded(this._tabId as string);
                })
        );
    }

    protected addTab(formNumber: string) {
        this._tabId = this.tabsService.addOrUpdateTab(this.route, {
            icon$: of('fas fa-th-list'),
            areaLabel$: of(this.translateService.instant('home.tabAreaLabel')),
            label$: of(formNumber),
            dirty$: this.dirty$,
            tooltip$: of(formNumber),
            canReload$: of(true)
        });
    }

    protected changeState(newState: string, comment?: string) {
        this.formDataService.processingStarted();
        const body = { formState: newState as FormState, comment } as FormStateUpdate;
        this.client
            .updateInstallationFormState(this.formDetails.formId, uuidv4(), this.formDetails.rowVersion, body)
            .pipe(take(1))
            .subscribe({
                error: (_) => {
                    this.formDataService.processingFinished();
                    this.messageServiceHelper.showError({
                        key: this.formDetails.formId
                    });
                },
                complete: () => {
                    this.formDataService.processingFinished();
                    this.reloadFormDetails(this.formDetails.formId);
                    this.messageServiceHelper.showSuccess({
                        key: this.formDetails.formId
                    });
                }
            });
    }

    protected changeScreeningStatus(newState: string) {
        this.formDataService.processingStarted();
        const body = { screeningStatus: newState as FormScreeningStatus } as FormScreeningStatusUpdate;
        this.client
            .updateInstallationFormScreeningStatus(this.formDetails.formId, uuidv4(), this.formDetails.rowVersion, body)
            .pipe(take(1))
            .subscribe({
                error: (_) => {
                    this.formDataService.processingFinished();
                    this.messageServiceHelper.showError({
                        key: this.formDetails.formId
                    });
                },
                complete: () => {
                    this.formDataService.processingFinished();
                    this.reloadFormDetails(this.formDetails.formId);
                    this.messageServiceHelper.showSuccess({
                        key: this.formDetails.formId
                    });
                }
            });
    }

    protected dirtyChanges(key: string, hasChanges: boolean) {
        this.dirtyFlags[key] = hasChanges;
        const anyWidgetHasChanges = Object.values(this.dirtyFlags).some((flag) => flag);
        this.dirty$.next(anyWidgetHasChanges);
    }

    protected abstract getDetails(formId: string): Observable<InstallationFormsApiResponse<TFormData>>;
}
