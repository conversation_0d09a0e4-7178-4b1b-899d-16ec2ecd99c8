<div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" [formGroup]="form">
    <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
        <label [labelRequired]="form.controls['scopeOfDeliverySize']" for="scopeOfDeliverySize">
            {{ 'technicalInformationWidget.connection.scopeOfDelivery' | translate }}
        </label>
        <app-icon-master-data-path [path]="formDataService.masterDataToCompareResult?.scopeOfDelivery?.path">
        </app-icon-master-data-path>
    </div>

    <div
        class="zebra-edit"
        [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70"
        fxFlex.lt-md="50"
        fxLayoutGap="0.5rem">
        <p-inputGroup>
            <p-inputNumber class="p-0" min="0" id="scopeOfDeliverySize" formControlName="scopeOfDeliverySize"></p-inputNumber>
            <p-inputGroupAddon>
                <button
                    [disabled]="!canUpdateConnectionRights()"
                    [pTooltip]="'connectionRights.updateButton' | translate"
                    pButton
                    icon="fa-solid fa-right-left"
                    (click)="onUpdateConnectionRightsClicked()"
                    class="p-button-outlined border-none p-2"></button>
            </p-inputGroupAddon>
        </p-inputGroup>
        <small [controlValidationErrors]="form.controls['scopeOfDeliverySize']" class="p-error mt-1 block"></small>
    </div>
    <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields">
        <input
            id="scopeOfDeliverySizeMD"
            type="text"
            pInputText
            [value]="formDataService.masterDataToCompareResult?.scopeOfDelivery?.value ?? ''"
            [disabled]="true" />
    </div>
</div>
<p-accordion [(value)]="accordionActiveValues">
    <p-accordion-panel [value]="transferConnectionRightsTabName">
        <p-accordion-header>
            <div class="flex align-items-center w-full">
                <span>
                    {{ 'connectionRights.sectionTitle' | translate }}
                </span>
                <span class="ml-auto">
                    {{
                        'connectionRights.summaryInfo'
                            | translate
                                : {
                                      TotalConnectionRightsTransfer: totalConnectionRightsTransfer
                                  }
                    }}
                </span>
            </div>
        </p-accordion-header>
        <p-accordion-content>
            <div fxFlexFill>
                <button
                    pButton
                    label="{{ 'connectionRights.addTransferButton' | translate }}"
                    class="p-button-outlined mb-20"
                    *ngIf="!isAddingOpened && !isReadOnly"
                    (click)="createTransfer()"></button>
                <div fxLayout="column" class="mb-20" *ngIf="isAddingOpened" [formGroup]="addTransferForm">
                    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
                        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                            <label [labelRequired]="addTransferForm.controls['meterFrame']" for="meterFrame">
                                {{ 'connectionRights.columnHeaders.meterFrameNumber' | translate }}
                            </label>
                        </div>
                        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                            <app-meter-frame-search
                                formControlName="meterFrame"
                                [options]="meterFrameOptions"
                                [showWithValidElectricityPurposeOnly]="true" />
                        </div>
                    </div>
                    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
                        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                            <label [labelRequired]="addTransferForm.controls['value']" for="value">
                                {{ 'connectionRights.columnHeaders.value' | translate }}
                            </label>
                        </div>
                        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                            <p-inputNumber id="value" formControlName="value" required min="0"></p-inputNumber>
                            <small [controlValidationErrors]="addTransferForm.controls['value']" class="p-error"></small>
                        </div>
                    </div>
                </div>
                <button
                    id="cancelButton"
                    type="button"
                    pButton
                    pRipple
                    class="mr-2 p-button-secondary mb-20"
                    (click)="cancelCreateTransfer()"
                    *ngIf="isAddingOpened">
                    {{ 'common.cancel' | translate | titlecase }}
                </button>
                <button
                    id="saveButton"
                    type="button"
                    pButton
                    pRipple
                    class="mb-20"
                    (click)="saveTransfer()"
                    *ngIf="isAddingOpened">
                    {{ 'common.create' | translate | titlecase }}
                </button>

                <p-table
                    [columns]="columns"
                    responsiveLayout="scroll"
                    [value]="gridItems"
                    [(rows)]="maxRowsPerPage"
                    [totalRecords]="form.get('transferConnectionRights')?.value"
                    [paginator]="form.get('transferConnectionRights')?.value.length > maxRowsPerPage"
                    currentPageReportTemplate="{first} - {last} {{ 'common.of' | translate }} {totalRecords}"
                    [showCurrentPageReport]="true"
                    dataKey="id"
                    [autoLayout]="true"
                    *ngIf="form.get('transferConnectionRights')?.value.length; else emptyMessage">
                    <ng-template pTemplate="header" let-columns>
                        <tr>
                            <th id="columnHeader" *ngFor="let col of columns" class="columnHeader">
                                <div fxLayout="row">
                                    {{ col.header | translate }}
                                </div>
                            </th>
                            <th id="connection-rights-actions"></th>
                        </tr>
                    </ng-template>

                    <ng-template pTemplate="body" let-rowData let-columns="columns" let-i="rowIndex">
                        <tr class="zebra-item table-row">
                            <td *ngFor="let col of columns">
                                <div *ngIf="col.field !== 'meterFrameNumber'">
                                    {{ rowData[col.field] }}
                                </div>
                                <div *ngIf="col.field === 'meterFrameNumber' && rowData.meterFrameId">
                                    <a
                                        class="add-point"
                                        (click)="onMeterFrameClicked(rowData.meterFrameId, rowData.connectionPointId)"
                                        >{{ rowData[col.field] }}<i class="fa-solid fa-up-right-from-square ml-1"></i
                                    ></a>
                                </div>
                                <div *ngIf="col.field === 'meterFrameNumber' && !rowData.meterFrameId">
                                    <p-tag severity="warn" value="{{ 'meterFrameSearch.notSelectedWarning' | translate }}">
                                    </p-tag>
                                </div>
                            </td>

                            <td>
                                <button
                                    pButton
                                    icon="fa-solid fa-trash-can"
                                    class="p-button-outlined border-none"
                                    [disabled]="isReadOnly"
                                    (click)="removeItem(i)"></button>
                            </td>
                        </tr>
                    </ng-template>
                </p-table>

                <ng-template #emptyMessage>
                    <p>{{ 'connectionRights.noTransfersFound' | translate }}</p>
                </ng-template>
            </div>
        </p-accordion-content>
    </p-accordion-panel>
</p-accordion>
