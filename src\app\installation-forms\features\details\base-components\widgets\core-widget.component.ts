import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { finalize, Observable, take } from 'rxjs';
import {
    ConnectionPoint,
    IBaseForm,
    InstallationFormsApiResponse,
    InstallationFormsClient,
    MeterFrame,
    PayerType,
    PaymentDetailsUpdate
} from 'src/app/api/installation-forms-client';
import { WidgetNames } from 'src/app/core/constants/widget-names';
import { AutomaticFormRefreshService } from 'src/app/core/services/automatic-form-refresh/automatic-form-refresh.service';
import { FormDataService } from 'src/app/core/services/form-data/form-data.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { MeterFrameSearchItemModel } from 'src/app/shared/components/meter-frame-search/meter-frame-search-item.model';
import { v4 as uuidv4 } from 'uuid';
import { WidgetWithChangesModelComponent } from './widget-with-changes-model.component';

@Component({
    template: '',
    standalone: false
})
export abstract class CoreWidgetComponent<TFormData extends IBaseForm, TFormDataUpdate>
    extends WidgetWithChangesModelComponent
    implements OnInit, OnDestroy
{
    protected commonTranslations: any;
    protected _formDetails!: TFormData;

    public meterFramesOptions: SelectItem[] = [];

    // used in form types where payer and contact person are nullable
    protected isPayerDataVisible: boolean = false;
    protected isInstallationContactPersonVisible: boolean = false;

    // relevant for specific form types only
    public branchLineMeterFrameOptions: MeterFrameSearchItemModel[] = [];

    protected abstract accordionActiveIndexes: number[];
    protected abstract payerPanelIndex: number;

    public form!: FormGroup;

    @Input() get formDetails(): TFormData {
        return this._formDetails;
    }
    set formDetails(formDetails: TFormData) {
        if (
            this._formDetails &&
            !this.isDuringForceRefresh &&
            this.hasConflictingChanges(
                this.convertToComparisonModel(this.formDetails, this.isReadOnly && this.canUpdatePaymentDetails),
                this.getFormComparisonModel(this.isReadOnly && this.canUpdatePaymentDetails),
                this.convertToComparisonModel(formDetails, this.isReadOnly && this.canUpdatePaymentDetails)
            )
        ) {
            this.automaticFormRefreshService.notifyConflictingChangeOccurred();
            return;
        }
        this.dataLoaded();
        this._formDetails = formDetails;
        if (this.canSupplyFormData) {
            this.supplyFormData();
        }
    }

    @Output() changesMade = new EventEmitter<{ key: string; hasChanges: boolean }>();

    get installationInformationForm(): FormGroup {
        return this.form.get('installationInformation') as FormGroup;
    }

    get consumptionMeteringPoint(): FormGroup {
        return this.installationInformationForm.get('consumptionMeteringPoint') as FormGroup;
    }

    get payerForm(): FormGroup {
        return this.form.get('payer') as FormGroup;
    }

    get contactPersonForm(): FormGroup {
        return this.form?.get('contactPerson') as FormGroup;
    }

    get technicalInformationForm(): FormGroup {
        return this.form.get('technicalInformation') as FormGroup;
    }

    get instructionDataForm(): FormGroup {
        return this.form.get('instructionData') as FormGroup;
    }

    get meterDeliveryOptionsForm(): FormGroup {
        return this.form.get('meterDeliveryOptions') as FormGroup;
    }

    get applicationsForm(): FormGroup {
        return this.form.get('applications') as FormGroup;
    }

    get relatedFormsForm(): FormGroup {
        return this.form.get('relatedForms') as FormGroup;
    }

    get connectionRightsForm(): FormGroup {
        return this.form.get('connectionRights') as FormGroup;
    }

    get voltageLevelForm(): FormGroup {
        return this.form?.get('voltageLevel') as FormGroup;
    }

    constructor(
        protected readonly formDataService: FormDataService,
        protected readonly automaticFormRefreshService: AutomaticFormRefreshService,
        protected readonly translateService: TranslateService,
        protected readonly messageServiceHelper: MessageServiceHelper,
        protected readonly client: InstallationFormsClient
    ) {
        super(formDataService, automaticFormRefreshService);
    }

    ngOnInit(): void {
        super.ngOnInit();
        this.commonTranslations = this.translateService.instant('common');

        this.supplyFormData();
        this.setFormEditability();
        this.formDataService.editabilityChange$.subscribe(() => {
            this.setFormEditability();
            this.onChange();
        });

        if (!this.formDetails.payer?.address?.carId) {
            this.accordionActiveIndexes.push(this.payerPanelIndex);
        }
    }

    ngOnDestroy(): void {
        super.ngOnDestroy();
    }

    override setSubFormEditability() {
        if (this.isCurrentUserAssignedAsCaseWorker && this.formDataService.canUpdatePaymentDetails) {
            this.contactPersonForm.enable();
            this.payerForm.enable();
        }
    }

    protected onCancelChangesClicked() {
        this.supplyFormData();
        this.onChange();
    }

    protected onChange() {
        if (this.isReadOnly && this.canUpdatePaymentDetails) {
            this.emitDirty(
                this.recalculateChanges(this.convertToComparisonModel(this.formDetails, true), this.getFormComparisonModel(true))
            );
            return;
        } else if (this.isReadOnly) {
            return;
        }
        this.emitDirty(
            this.recalculateChanges(this.convertToComparisonModel(this.formDetails, false), this.getFormComparisonModel(false))
        );
    }

    protected getPayerFieldForComparison(dataField: any, defaultValue: any = '') {
        return this.isPayerDataVisible ? dataField || defaultValue : defaultValue;
    }

    protected initDropDownOptions() {
        this.initMeterFramesOptions();
    }

    protected initMeterFramesOptions() {
        this.meterFramesOptions = this.formDetails.meterFrame?.id
            ? [
                  {
                      label: this.formDetails.meterFrame.meterFrameNumber,
                      value: {
                          id: this.formDetails.meterFrame.id,
                          meterFrameNumber: this.formDetails.meterFrame.meterFrameNumber
                      }
                  } as SelectItem
              ]
            : [];
    }

    protected observeFormUpdate(response: Observable<InstallationFormsApiResponse<void>>) {
        response
            .pipe(
                take(1),
                finalize(() => this.processingFinished())
            )
            .subscribe({
                error: (error) => {
                    if (error.status === 409) {
                        this.messageServiceHelper.showError({
                            detail: this.commonTranslations['concurrentModification'],
                            key: this.formDetails.formId
                        });
                        return;
                    }

                    this.messageServiceHelper.showError({
                        key: this.formDetails.formId
                    });
                },
                complete: () => {
                    this.messageServiceHelper.showSuccess({
                        key: this.formDetails.formId
                    });

                    this.automaticFormRefreshService.forceRefresh();
                }
            });
    }

    private emitDirty(hasChanges: boolean) {
        this.changesMade.emit({
            key: WidgetNames.coreInformationWidget,
            hasChanges: hasChanges
        });
    }

    isPayerTypeNotPublic = () => {
        return this.getSelectedPayerType() !== PayerType.Public;
    };

    isPayerTypeCompany = () => {
        return this.getSelectedPayerType() === PayerType.Company;
    };

    isPayerTypePrivate = () => {
        return this.getSelectedPayerType() === PayerType.Private;
    };

    isPayerTypePublic = () => {
        return this.getSelectedPayerType() === PayerType.Public;
    };

    getSelectedPayerType(): PayerType {
        return this.form?.get('payer.payerType')?.value;
    }

    onPayerDataVisibilityChanged(isVisible: boolean) {
        this.isPayerDataVisible = isVisible;
        this.onChange();
    }

    onInstallationContactPersonVisibilityChanged(isVisible: boolean) {
        this.isInstallationContactPersonVisible = isVisible;
        this.onChange();
    }

    onSaveClicked() {
        if (
            this.formDetails.isReadOnly &&
            this.formDetails.canUpdatePaymentDetails &&
            this.payerForm?.valid &&
            this.contactPersonForm?.valid
        ) {
            const updateBody = this.createPaymentDetailsUpdate();
            this.processingStarted();
            const update$ = this.client.updateFormPaymentDetails(
                this.formDetails.formId,
                uuidv4(),
                this.formDetails.rowVersion,
                updateBody
            );
            this.observeFormUpdate(update$);
        } else if (this.form.valid && !this.formDetails.isReadOnly) {
            const updateBody = this.createFormDataUpdate();
            this.processingStarted();
            const update$ = this.updateFormData(this.formDetails.formId, uuidv4(), this.formDetails.rowVersion, updateBody);
            this.observeFormUpdate(update$);
        } else {
            this.processingFinished();
            this.messageServiceHelper.showWarning({
                key: this.formDetails.formId,
                detail: this.commonTranslations['allRequiredFieldsShouldBeCompleted']
            });
        }
    }

    protected abstract initForm(): void;

    protected abstract setChangesModel(): void;

    protected abstract supplyFormData(): void;

    protected abstract createFormDataUpdate(): TFormDataUpdate;

    protected abstract createPaymentDetailsUpdate(): PaymentDetailsUpdate;

    protected abstract updateFormData(
        installationFormId: string,
        es_message_id: string,
        row_version: string,
        body: TFormDataUpdate
    ): Observable<InstallationFormsApiResponse<void>>;

    // methods used in tests, should be public
    abstract getFormComparisonModel(paymentDetailsOnly: boolean): any;

    abstract convertToComparisonModel(model: TFormData, paymentDetailsOnly: boolean): any;
}
