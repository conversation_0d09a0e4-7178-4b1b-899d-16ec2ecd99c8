import { TestBed } from '@angular/core/testing';
import { TranslateService } from '@ngx-translate/core';
import { BooleanToYesNoPipe } from './boolean-translate.pipe';

describe('BooleanToYesNoPipe', () => {
    let pipe: BooleanToYesNoPipe;
    let translateService: TranslateService;
    let translateInstantSpy: any;

    beforeEach(() => {
        const translateServiceSpy = {
            instant: jest.fn()
        };

        TestBed.configureTestingModule({
            providers: [BooleanToYesNoPipe, { provide: TranslateService, useValue: translateServiceSpy }]
        });

        pipe = TestBed.inject(BooleanToYesNoPipe);
        translateService = TestBed.inject(TranslateService);
        translateInstantSpy = translateService.instant as jest.Mock;
    });

    it('should create an instance', () => {
        expect(pipe).toBeTruthy();
    });

    it('should translate true to Yes', () => {
        translateInstantSpy.mockReturnValue('Yes');

        const result = pipe.transform(true);

        expect(translateInstantSpy).toHaveBeenCalledWith('common.yesOption');
        expect(result).toBe('Yes');
    });

    it('should translate false to No', () => {
        translateInstantSpy.mockReturnValue('No');

        const result = pipe.transform(false);

        expect(translateInstantSpy).toHaveBeenCalledWith('common.noOption');
        expect(result).toBe('No');
    });

    it('should handle null/undefined input', () => {
        expect(pipe.transform(null as any)).toBe('');
        expect(pipe.transform(undefined)).toBe('');
    });

    it('should use the correct translation keys', () => {
        translateInstantSpy.mockImplementation((key: string) => {
            if (key === 'common.yesOption') return 'Yes';
            if (key === 'common.noOption') return 'No';
            return key;
        });

        expect(pipe.transform(true)).toBe('Yes');
        expect(pipe.transform(false)).toBe('No');
    });
});
