import { Injectable } from '@angular/core';
import { ValueListHeaderModel, ValueListItemModel, ValueListModel } from '@kmd-elements/core-kit';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { catchError, filter, map, shareReplay, switchMap } from 'rxjs/operators';
import { v4 as uuidv4 } from 'uuid';
import { InstallationFormsClient, ValueListType } from '../../../api/installation-forms-client';
import { MessageServiceHelper } from '../message/message.service';

@Injectable({
    providedIn: 'root'
})
export class ValueListsService {
    private valueLists: { [key: string]: BehaviorSubject<ValueListModel> } = {};
    private allValuesFetched$?: Observable<void>;

    constructor(
        private client: InstallationFormsClient,
        private messageServiceHelper: MessageServiceHelper,
        private translateService: TranslateService
    ) {
        this.fetchValueLists();
    }

    private getValueListTypes(): ValueListType[] {
        return Object.values(ValueListType);
    }

    fetchValueLists(): void {
        this.allValuesFetched$ = this.client.getValueLists(uuidv4(), this.getValueListTypes()).pipe(
            map((response) => response.result.models),
            filter((valueLists) => !!valueLists),
            map((valueLists) => {
                // Store each type in the dictionary
                Object.keys(valueLists).forEach((key) => {
                    if (!this.valueLists[key]) {
                        this.valueLists[key] = new BehaviorSubject<ValueListModel>(valueLists[key]);
                    } else {
                        this.valueLists[key].next(valueLists[key]);
                    }
                });
            }),
            catchError((_) => {
                this.messageServiceHelper.showError({
                    detail: this.translateService.instant('valueListComponent.valueListsError')
                });
                return of();
            }),
            shareReplay(1)
        );

        this.allValuesFetched$.subscribe();
    }

    getValueList(type: ValueListType): Observable<ValueListModel | undefined> {
        return this.allValuesFetched$!.pipe(
            switchMap(() => (this.valueLists[type] ? this.valueLists[type] : of(undefined))),
            map((valueList) => {
                if (!valueList) {
                    this.messageServiceHelper.showError({
                        detail: `${type} - ${this.translateService.instant('valueListComponent.valueListsNotFoundError')}`
                    });
                }
                return valueList;
            })
        );
    }

    checkIfValueListItemAttributeMatch(
        type: ValueListType,
        valueListItemId: string | undefined,
        headerSystemIdentifier: string,
        allowedAttributeValues: string[]
    ): boolean {
        if (!valueListItemId) {
            return false;
        }

        let valueListItem: ValueListItemModel | undefined;
        let valueListHeader: ValueListHeaderModel | undefined;

        // Get the value list item synchronously
        this.getValueList(type).subscribe((valueList) => {
            if (valueList && valueList.valueItems) {
                valueListItem = valueList.valueItems.find((item) => item.id.toLowerCase() === valueListItemId.toLowerCase());
            }

            if (valueList && valueList.activeHeaders) {
                valueListHeader = valueList.activeHeaders.find(
                    (header) => header.systemIdentifier?.toLowerCase() === headerSystemIdentifier.toLowerCase()
                );
            }
        });

        if (valueListItem && valueListHeader && valueListItem.attributes) {
            // Normalize case by converting both to lowercase (or uppercase)
            const normalizedAttributeValues = allowedAttributeValues.map((value) => value.toLowerCase());

            const attribute = valueListItem.attributes.find(
                (attribute) =>
                    attribute.valueListHeaderId === valueListHeader?.id &&
                    normalizedAttributeValues.includes(attribute.value.toLowerCase())
            );
            return !!attribute; // If found, return true, otherwise false
        }

        return false;
    }
}
