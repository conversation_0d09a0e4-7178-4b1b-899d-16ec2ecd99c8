import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export default function addressLookupValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        let value = control.value;
        if (!!value === false || !value.text || value.text.trim() === '') {
            return { carIdIsEmpty: true };
        }

        return null;
    };
}
