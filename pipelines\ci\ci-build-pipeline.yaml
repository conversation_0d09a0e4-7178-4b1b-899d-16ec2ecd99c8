name: $(date:yyyyMMdd)$(rev:.r)_$(SourceBranchName)

trigger: none

pool:
  vmImage: "ubuntu-22.04"

resources:
  repositories:
    - repository: commonAks
      type: git
      name: COMBAS/KMD.Elements.Pipelines

extends:
  template: ci/templates/build-microfrontend.yaml@commonAks
  parameters:
    projectPath: $(System.DefaultWorkingDirectory)
    testCommand: $(testCommand)
    testResultPath: $(testResultPath)
    coverageResultPath: $(coverageResultPath)

variables:
  - template: ci-variables-template.yaml
