import { getValueOrDefault } from './get-value-or-default';

describe('getValueOrDefault', () => {
    it('should return the input value when truthy', () => {
        expect(getValueOrDefault('test')).toBe('test');
        expect(getValueOrDefault(42)).toBe(42);
        expect(getValueOrDefault(true)).toBe(true);

        const obj = { name: 'test' };
        expect(getValueOrDefault(obj)).toBe(obj);

        const arr = [1, 2, 3];
        expect(getValueOrDefault(arr)).toBe(arr);
    });

    it('should return null when input is falsy', () => {
        expect(getValueOrDefault(null)).toBeNull();
        expect(getValueOrDefault(undefined)).toBeNull();
        expect(getValueOrDefault(false)).toBeNull();
        expect(getValueOrDefault('')).toBeNull();
        expect(getValueOrDefault(0)).toBeNull();
        expect(getValueOrDefault(NaN)).toBeNull();
    });

    it('should handle edge cases correctly', () => {
        // Empty objects/arrays are truthy
        expect(getValueOrDefault({})).toEqual({});
        expect(getValueOrDefault([])).toEqual([]);

        // String '0' is truthy
        expect(getValueOrDefault('0')).toBe('0');

        // Function is truthy
        const fn = () => {};
        expect(getValueOrDefault(fn)).toBe(fn);
    });
});
