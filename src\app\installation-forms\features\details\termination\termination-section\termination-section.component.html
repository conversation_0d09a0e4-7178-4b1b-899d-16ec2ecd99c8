<div #overlayContainer></div>
<div [formGroup]="form">
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['scope']" for="scope">
                {{ 'terminationWidget.scope' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="scope"
                [options]="terminationScope"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="scope">
            </p-select>
            <small [controlValidationErrors]="form.controls['scope']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label for="terminationDateEod">
                {{ 'terminationWidget.terminationDateEod' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <cmbs-calendar
                inputId="terminationDateEod"
                formControlName="terminationDateEod"
                [showTime]="false"
                [appendTo]="overlayContainer">
            </cmbs-calendar>
            <small [controlValidationErrors]="form.controls['terminationDateEod']" class="p-error"></small>
        </div>
    </div>
</div>
