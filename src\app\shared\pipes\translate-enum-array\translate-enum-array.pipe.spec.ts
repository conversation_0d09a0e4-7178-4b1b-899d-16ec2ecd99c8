import { TestBed } from '@angular/core/testing';
import { TranslateService } from '@ngx-translate/core';
import { TranslateEnumArrayPipe } from './translate-enum-array.pipe';

describe('TranslateEnumArrayPipe', () => {
    let pipe: TranslateEnumArrayPipe;
    let translateService: TranslateService;
    let translateInstantSpy: jest.SpyInstance;

    const mockEnumsTranslations = {
        StatusEnum: {
            ACTIVE: 'Active',
            INACTIVE: 'Inactive',
            PENDING: 'Pending'
        },
        TypeEnum: {
            TYPE_A: 'Type A',
            TYPE_B: 'Type B'
        }
    };

    beforeEach(() => {
        const translateServiceMock = {
            instant: jest.fn((key: string) => {
                if (key === 'enums') {
                    return mockEnumsTranslations;
                }
                return key;
            })
        };

        TestBed.configureTestingModule({
            providers: [TranslateEnumArrayPipe, { provide: TranslateService, useValue: translateServiceMock }]
        });

        translateService = TestBed.inject(TranslateService);
        translateInstantSpy = jest.spyOn(translateService, 'instant');
        pipe = TestBed.inject(TranslateEnumArrayPipe);
    });

    it('should create an instance', () => {
        expect(pipe).toBeTruthy();
    });

    it('should load enums translations in constructor', () => {
        expect(translateService.instant).toHaveBeenCalledWith('enums');
        expect(pipe['enumsTranslations']).toEqual(mockEnumsTranslations);
    });

    it('should return empty string when enumArray is null or undefined', () => {
        expect(pipe.transform(null as any, 'namespace.StatusEnum')).toBe('');
        expect(pipe.transform(undefined as any, 'namespace.StatusEnum')).toBe('');
    });

    it('should return empty string when translationPath is null or undefined', () => {
        expect(pipe.transform(['ACTIVE'], null as any)).toBe('');
        expect(pipe.transform(['ACTIVE'], undefined as any)).toBe('');
        expect(pipe.transform(['ACTIVE'], '')).toBe('');
    });

    it('should translate enum array values and join them with comma', () => {
        const result = pipe.transform(['ACTIVE', 'INACTIVE'], 'namespace.StatusEnum');
        expect(result).toBe('Active, Inactive');
    });

    it('should translate different enum types correctly', () => {
        const statusResult = pipe.transform(['ACTIVE', 'PENDING'], 'namespace.StatusEnum');
        expect(statusResult).toBe('Active, Pending');

        const typeResult = pipe.transform(['TYPE_A', 'TYPE_B'], 'namespace.TypeEnum');
        expect(typeResult).toBe('Type A, Type B');
    });

    it('should extract enum name from the translation path', () => {
        pipe.transform(['ACTIVE'], 'namespace.StatusEnum');
        // Can't directly test the split logic, but we can verify the results match expectations
        expect(translateInstantSpy).toHaveBeenCalledWith('enums');
    });

    it('should handle single-item arrays correctly', () => {
        const result = pipe.transform(['ACTIVE'], 'namespace.StatusEnum');
        expect(result).toBe('Active');
    });

    it('should handle empty arrays correctly', () => {
        const result = pipe.transform([], 'namespace.StatusEnum');
        expect(result).toBe('');
    });

    it('should unsubscribe on destroy', () => {
        const unsubscribeSpy = jest.spyOn(pipe.subscription, 'unsubscribe');
        pipe.ngOnDestroy();
        expect(unsubscribeSpy).toHaveBeenCalled();
    });
});
