[[_TOC_]]

# Description

Installation forms are used for communicating changes to system installations in the electricity, water or heating grid.
When a new installation is being put in place, one is being changed or removed, the field technician fills in a form, which is forwarded to the DSO for processing.
The DSO perform the necessary registrations in the DHM and other systems and determine whether and how the installation can go ahead.

# Maintainer

See: [.maintainers](.maintainers)

# Documentation

- [InstallationForms HLD](https://dev.azure.com/kmddk/COMBAS/_wiki/wikis/COMBAS.wiki/8899/-00008-Installation-forms)

# Prerequisites

Application is dependent on `KMD.Elements.InstallationForms.BFF`.

# Development

## Local Environment

In order to run the setup with Installation Forms UI locally you need to :

1. Configure `appsettings.local.json` (or appsettings for your team) in `Tools.LocalEnvironment` project and run it. Required minimum is:

   ```json
     {
      "path": "/api/",
      "destination": "https://visue-dhm.dev.kmdelements.com"
     },
     {
       "path": "/mf/installation-forms/",
       "destination": "http://localhost:4207/"
     },
     {
       "path": "/mf/",
       "destination": "https://visue-dhm.dev.kmdelements.com"
     },
     {
       "path": "/",
       "destination": "https://visue-dhm.dev.kmdelements.com"
     }
   ```

2. Run command "npm run start-proxy" in KMD.Elements.InstallationForms.UI
3. Go to <https://localhost:4200/650> and after login, go to Installation Forms by clicking on menu item
4. If in step 3 you don't see Installation Forms as menu item, you need to add permissions for your user for current env (DHM DEV in example above).
5. To rebuild BFF client run `scripts\open-api-tools\generate.ps1` or `npm run generate-client`
   - NOTE: since we use bundling for API SPECs `Redocly CLI` is required (`npm install -g @redocly/cli@latest`)

## Build

Standard scripts for Angular applications (`ng build`).

## Test

Standard scripts for Angular applications (`ng test --watch`).

# Deployment

DHM/CS-VISUE

# Notes

_Add more notes if you want_
