import { TestBed } from '@angular/core/testing';
import { TranslateService } from '@ngx-translate/core';
import { NO_VALUE_STRING } from '../../../core/constants/constants';
import { NoValueEnumTranslatePipe } from './no-value-enum-translate.pipe';

describe('NoValueEnumTranslatePipe', () => {
    let pipe: NoValueEnumTranslatePipe;
    let translateService: TranslateService;
    let translateInstantSpy: jest.SpyInstance;

    const mockEnumTranslations = {
        StatusEnum: {
            ACTIVE: 'Active',
            INACTIVE: 'Inactive',
            PENDING: 'Pending'
        },
        TypeEnum: {
            TYPE_A: 'Type A',
            TYPE_B: 'Type B'
        }
    };

    beforeEach(() => {
        const translateServiceMock = {
            instant: jest.fn((key: string) => {
                if (key === 'enums') {
                    return mockEnumTranslations;
                }
                return key;
            })
        };

        TestBed.configureTestingModule({
            providers: [NoValueEnumTranslatePipe, { provide: TranslateService, useValue: translateServiceMock }]
        });

        translateService = TestBed.inject(TranslateService);
        translateInstantSpy = jest.spyOn(translateService, 'instant');
        pipe = TestBed.inject(NoValueEnumTranslatePipe);
    });

    it('should create an instance', () => {
        expect(pipe).toBeTruthy();
    });

    it('should load enum translations in constructor', () => {
        expect(translateService.instant).toHaveBeenCalledWith('enums');
        expect(pipe['enumsTranslations']).toEqual(mockEnumTranslations);
    });

    it('should return NO_VALUE_STRING when enumValue is null or undefined', () => {
        expect(pipe.transform(null, 'namespace.StatusEnum')).toBe(NO_VALUE_STRING);
        expect(pipe.transform(undefined, 'namespace.StatusEnum')).toBe(NO_VALUE_STRING);
    });

    it('should return NO_VALUE_STRING when enumPath is null or undefined', () => {
        expect(pipe.transform('ACTIVE', null as any)).toBe(NO_VALUE_STRING);
        expect(pipe.transform('ACTIVE', undefined as any)).toBe(NO_VALUE_STRING);
        expect(pipe.transform('ACTIVE', '')).toBe(NO_VALUE_STRING);
    });

    it('should translate enum values correctly', () => {
        const result1 = pipe.transform('ACTIVE', 'namespace.StatusEnum');
        expect(result1).toBe('Active');

        const result2 = pipe.transform('INACTIVE', 'namespace.StatusEnum');
        expect(result2).toBe('Inactive');

        const result3 = pipe.transform('TYPE_A', 'namespace.TypeEnum');
        expect(result3).toBe('Type A');
    });

    it('should extract enum name from the path', () => {
        pipe.transform('ACTIVE', 'namespace.StatusEnum');
        pipe.transform('TYPE_A', 'domain.TypeEnum');

        // We can't directly test the split logic, but we can verify the results
        expect(translateInstantSpy).toHaveBeenCalledWith('enums');
    });

    it('should unsubscribe on destroy', () => {
        const unsubscribeSpy = jest.spyOn(pipe.subscription, 'unsubscribe');
        pipe.ngOnDestroy();
        expect(unsubscribeSpy).toHaveBeenCalled();
    });
});
