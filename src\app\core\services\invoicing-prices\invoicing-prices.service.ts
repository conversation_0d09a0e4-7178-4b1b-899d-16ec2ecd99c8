import { Injectable } from '@angular/core';
import { map, Observable, shareReplay } from 'rxjs';
import { InstallationFormsClient, Price, SupplyType } from 'src/app/api/installation-forms-client';
import { v4 as uuidv4 } from 'uuid';

@Injectable({
    providedIn: 'root'
})
export class InvoicingPricesService {
    private prices: Observable<Price[] | undefined> | null = null;

    constructor(private readonly client: InstallationFormsClient) {}

    getAllPrices(): Observable<Price[] | undefined> {
        this.prices ??= this.client.getInvoicingPrices(uuidv4(), SupplyType.All).pipe(
            map((response) => response.result.results),
            shareReplay(1)
        );
        return this.prices;
    }

    getPricesBySupplyType(supplyType: SupplyType): Observable<Price[] | undefined> {
        if (supplyType === SupplyType.All) {
            return this.getAllPrices();
        }
        this.prices ??= this.client.getInvoicingPrices(uuidv4(), SupplyType.All).pipe(
            map((response) => response.result.results),
            shareReplay(1)
        );
        return this.prices.pipe(map((prices) => prices?.filter((price) => price.supplyType === supplyType)));
    }

    invalidatePricesCache(): void {
        this.prices = null;
    }
}
