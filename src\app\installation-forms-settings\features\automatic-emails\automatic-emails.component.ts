import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AuthorizationService } from '@kmd-elements-ui/authorization';
import {
    KeyActions,
    KeyboardShortcuts,
    KeyboardShortcutsService,
    KeyboardShortcutsSubscriptionManager
} from '@kmd-elements-ui/shortcuts';
import { TabsService } from '@kmd-elements-ui/tabs';
import { TranslateService } from '@ngx-translate/core';
import { TableRowReorderEvent } from 'primeng/table';
import { BehaviorSubject, catchError, EMPTY, filter, finalize, forkJoin, of, Subscription, switchMap, tap } from 'rxjs';
import {
    AutomaticEmailsRule,
    CommunicationTemplateModel,
    InstallationFormsClient,
    ReorderModel,
    SupplyType
} from 'src/app/api/installation-forms-client';
import { Permissions } from 'src/app/core/constants/permissions';
import { EmailTemplatesService } from 'src/app/core/services/email-templates/email-templates.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { v4 as uuidv4 } from 'uuid';
import { RulesListColumn } from '../models/rules-list-column';
import { AutomaticEmailsCreateComponent } from './automatic-emails-create/automatic-emails-create.component';
import { AutomaticEmailsEditComponent } from './automatic-emails-edit/automatic-emails-edit.component';
import {
    automaticEmailsMarkdownDaDK,
    automaticEmailsMarkdownEnUs,
    automaticEmailsTabName,
    automaticEmailsTranslationPath
} from './constants/automatic-emails.consts';
import { AUTOMATIC_EMAILS_COLUMNS } from './constants/columns';
import { AutomaticEmailsListItem } from './models/automatic-emails-list-item';

@Component({
    selector: 'app-automatic-emails',
    templateUrl: './automatic-emails.component.html',
    styleUrl: './automatic-emails.component.scss',
    standalone: false
})
export class AutomaticEmailsComponent implements KeyboardShortcutsSubscriptionManager, OnInit, OnDestroy {
    private widgetTranslations: any;
    private _isCreating: boolean = false;
    private _ruleInEdit?: AutomaticEmailsRule;
    private _isReordered: boolean = false;

    public emailTemplateOptions: CommunicationTemplateModel[] = [];

    get isCreating(): boolean {
        return this._isCreating;
    }

    set isCreating(value: boolean) {
        this._isCreating = value;
        this.recalculateDirty();
    }

    get ruleInEdit(): AutomaticEmailsRule | undefined {
        return this._ruleInEdit;
    }

    set ruleInEdit(value: AutomaticEmailsRule | undefined) {
        this._ruleInEdit = value;
        this.recalculateDirty();
    }

    get isReordered(): boolean {
        return this._isReordered;
    }

    set isReordered(value: boolean) {
        this._isReordered = value;
        this.recalculateDirty();
    }

    automaticEmailsRules: AutomaticEmailsListItem[] = [];
    rawAutomaticEmailsRules: AutomaticEmailsRule[] = [];

    subscription: Subscription = new Subscription();

    automaticEmailsMarkdownEnUs = automaticEmailsMarkdownEnUs;
    automaticEmailsMarkdownDaDK = automaticEmailsMarkdownDaDK;

    isProcessing: boolean = false;

    dirty$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

    automaticEmailsTabName = automaticEmailsTabName;

    hasUserFormConfigurationWritePermission: boolean = false;

    private _tabId?: string;

    columns: RulesListColumn[] = AUTOMATIC_EMAILS_COLUMNS;

    private keyEventsSubscription: Subscription = new Subscription();

    @ViewChild('automaticEmailsCreate') automaticEmailsCreate?: AutomaticEmailsCreateComponent;
    @ViewChild('automaticEmailsEdit') automaticEmailsEdit?: AutomaticEmailsEditComponent;

    keyActions: KeyActions = {
        [KeyboardShortcuts.ShiftPlusN]: () => {
            this.startCreating();
        },
        [KeyboardShortcuts.Escape]: () => {
            this.tabsService.close();
        },
        [KeyboardShortcuts.Enter]: () => {
            if (this.ruleInEdit) {
                this.automaticEmailsEdit?.saveEditedClick();
                return;
            }

            if (this.isCreating) {
                this.automaticEmailsCreate?.createClick();
                return;
            }

            if (this.isReordered) {
                this.saveReorder();
                return;
            }
        }
    };
    attemptedAction: (() => void) | null = null;
    private ruleToDelete?: AutomaticEmailsListItem;

    private originalRuleOrder: string[] = [];
    public alreadyUsedDisplayNames: string[] = [];

    constructor(
        private readonly client: InstallationFormsClient,
        private readonly emailTemplatesService: EmailTemplatesService,
        private readonly translateService: TranslateService,
        private readonly messageServiceHelper: MessageServiceHelper,
        protected tabsService: TabsService,
        private readonly activatedRoute: ActivatedRoute,
        private readonly authService: AuthorizationService,
        private readonly keyboardShortcutsService: KeyboardShortcutsService
    ) {
        this.addTab();
    }

    ngOnInit(): void {
        this.widgetTranslations = this.translateService.instant(automaticEmailsTranslationPath);

        this.authService
            .hasPermissions([Permissions.formConfiguration.write])
            .subscribe((x) => (this.hasUserFormConfigurationWritePermission = x));

        this.registerRefreshButtonListener();
        this.loadEmailsRulesWithTemplates();
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    subscribeKeyShortcuts(): void {
        this.keyEventsSubscription = this.keyboardShortcutsService.registerKeyboardShortcuts(this.keyActions).subscribe();
    }

    unsubscribeKeyShortcuts(): void {
        this.keyEventsSubscription.unsubscribe();
    }

    private loadEmailsRulesWithTemplates() {
        this.isProcessing = true;
        this.subscription.add(
            forkJoin({
                rules: this.client.getAutomaticEmailsRules(uuidv4()).pipe(
                    catchError((_) => {
                        this.messageServiceHelper.showError({
                            key: automaticEmailsTabName,
                            detail: this.widgetTranslations['getEmailsRulesError']
                        });
                        return EMPTY;
                    })
                ),
                templates: this.emailTemplatesService.getEmailTemplates(SupplyType.Electricity as SupplyType).pipe(
                    catchError((_) => {
                        this.messageServiceHelper.showError({
                            key: automaticEmailsTabName,
                            detail: this.translateService.instant('common.loadEmailTemplatesError')
                        });
                        return EMPTY;
                    })
                )
            })
                .pipe(finalize(() => (this.isProcessing = false)))
                .subscribe({
                    next: (response) => {
                        this.emailTemplateOptions = response.templates.result ?? [];
                        const sortedRules = [...response.rules.result];
                        sortedRules.sort((a, b) => a.order - b.order);
                        this.rawAutomaticEmailsRules = sortedRules;
                        this.automaticEmailsRules = sortedRules.map((rule) =>
                            this.mapToAutomaticEmailsListItem(rule, this.emailTemplateOptions)
                        );
                        this.originalRuleOrder = this.automaticEmailsRules.map((x) => x.id);
                        this.alreadyUsedDisplayNames = this.automaticEmailsRules.map((x) => x.displayName);
                    }
                })
        );
    }

    protected editRule(event: MouseEvent, rule: AutomaticEmailsListItem) {
        event.stopPropagation();
        this.ruleInEdit = this.rawAutomaticEmailsRules.find((r) => r.id === rule.id);
        this.isCreating = false;
    }

    protected startCreating() {
        this.isCreating = true;
        this.ruleInEdit = undefined;
    }

    protected cancelCreating() {
        this.isCreating = false;
    }

    protected cancelEditing() {
        this.ruleInEdit = undefined;
    }

    protected onRuleCreated(rule: AutomaticEmailsRule) {
        this.isCreating = false;
        this.rawAutomaticEmailsRules.push(rule);
        this.automaticEmailsRules.push(this.mapToAutomaticEmailsListItem(rule, this.emailTemplateOptions));
        this.originalRuleOrder.push(rule.id);
        this.alreadyUsedDisplayNames.push(rule.displayName);
    }

    protected onRuleEdited(updatedRule: AutomaticEmailsRule) {
        const editedIndex = this.automaticEmailsRules.findIndex((r) => r.id === updatedRule.id);
        if (editedIndex === -1) {
            return;
        }

        this.alreadyUsedDisplayNames.splice(
            this.alreadyUsedDisplayNames.indexOf(this.ruleInEdit!.displayName),
            1,
            updatedRule.displayName
        );
        this.rawAutomaticEmailsRules[editedIndex] = updatedRule;
        this.automaticEmailsRules[editedIndex] = this.mapToAutomaticEmailsListItem(updatedRule, this.emailTemplateOptions);
        this.ruleInEdit = undefined;
    }

    private addTab() {
        this._tabId = this.tabsService.addOrUpdateTab(this.activatedRoute, {
            icon$: of('fas fa-th-list'),
            areaLabel$: of(this.translateService.instant(`${automaticEmailsTranslationPath}.tabAreaLabel`)),
            label$: of(this.translateService.instant(`${automaticEmailsTranslationPath}.tabLabel`)),
            tooltip$: of(this.translateService.instant(`${automaticEmailsTranslationPath}.tabLabel`)),
            dirty$: this.dirty$,
            canReload$: of(true)
        });
    }

    registerRefreshButtonListener(): void {
        this.subscription.add(
            this.tabsService.reloadButtonClicked$
                .pipe(
                    filter((tabId) => tabId === this._tabId),
                    switchMap((tabId) => this.tabsService.reload(tabId)),
                    filter((canBeReloaded) => canBeReloaded),
                    tap(() => {
                        this.loadEmailsRulesWithTemplates();
                    })
                )
                .subscribe(() => {
                    this.tabsService.tabReloaded(this._tabId as string);
                    this.undoChanges();
                })
        );
    }

    undoChanges() {
        this.cancelCreating();
        this.cancelEditing();
        this.cancelReorder();
    }

    onRowReorder(_: TableRowReorderEvent) {
        this.setIsReordered();
    }

    setIsReordered() {
        this.isReordered = !this.automaticEmailsRules.every((rule, index) => rule.id === this.originalRuleOrder[index]);
    }

    cancelReorder() {
        this.automaticEmailsRules = this.originalRuleOrder.map((id) => this.automaticEmailsRules.find((rule) => rule.id === id)!);
        this.setIsReordered();
    }

    protected deleteRule(event: MouseEvent, rule: AutomaticEmailsListItem) {
        event.stopPropagation();
        this.ruleToDelete = rule;
        this.attemptedAction = () => this.performDelete();
    }

    private performDelete() {
        if (!this.ruleToDelete) {
            return;
        }

        this.isProcessing = true;
        this.subscription.add(
            this.client
                .deleteAutomaticEmailsRuleById(this.ruleToDelete.id, uuidv4())
                .pipe(
                    finalize(() => {
                        this.isProcessing = false;
                        this.ruleToDelete = undefined;
                    })
                )
                .subscribe({
                    complete: () => {
                        this.messageServiceHelper.showSuccess({
                            key: automaticEmailsTabName
                        });
                        this.automaticEmailsRules = this.automaticEmailsRules.filter((r) => r.id !== this.ruleToDelete?.id);
                        this.originalRuleOrder = this.originalRuleOrder.filter((id) => id !== this.ruleToDelete?.id);
                        this.rawAutomaticEmailsRules = this.rawAutomaticEmailsRules.filter((r) => r.id !== this.ruleToDelete?.id);
                        if (this.ruleInEdit?.id === this.ruleToDelete?.id) {
                            this.ruleInEdit = undefined;
                        }

                        this.alreadyUsedDisplayNames = this.alreadyUsedDisplayNames.filter(
                            (name) => name !== this.ruleToDelete?.displayName
                        );
                    },
                    error: (_error) => {
                        this.messageServiceHelper.showError({
                            key: automaticEmailsTabName,
                            detail: this.widgetTranslations['automaticEmailsRuleDeleteError']
                        });
                    }
                })
        );
    }

    onConfirmationDialogYesButtonClick() {
        if (this.attemptedAction) {
            this.attemptedAction();
        }
        this.attemptedAction = null;
    }

    onConfirmationDialogNoButtonClick() {
        this.attemptedAction = null;
        this.ruleToDelete = undefined;
    }

    saveReorder() {
        this.isProcessing = true;
        this.subscription.add(
            this.client
                .reorderAutomaticEmailsRules(
                    uuidv4(),
                    new ReorderModel({
                        reorderedIds: this.automaticEmailsRules.map((x) => x.id)
                    })
                )
                .pipe(finalize(() => (this.isProcessing = false)))
                .subscribe({
                    complete: () => {
                        this.messageServiceHelper.showSuccess({
                            key: automaticEmailsTabName
                        });
                        this.originalRuleOrder = this.automaticEmailsRules.map((x) => x.id);
                        this.isReordered = false;
                    },
                    error: (_error) => {
                        this.messageServiceHelper.showError({
                            key: automaticEmailsTabName,
                            detail: this.widgetTranslations['automaticEmailsRuleReorderError']
                        });
                    }
                })
        );
    }

    recalculateDirty() {
        this.dirty$.next(this.isCreating || this.isReordered || !!this.ruleInEdit);
    }

    mapToAutomaticEmailsListItem(
        rule: AutomaticEmailsRule,
        emailTemplateOptions: CommunicationTemplateModel[]
    ): AutomaticEmailsListItem {
        return {
            id: rule.id,
            order: rule.order,
            displayName: rule.displayName,
            formTypes: rule.formTypes,
            formStates: rule.formStates,
            formCategories: rule.formCategories,
            emailTemplateName: emailTemplateOptions.find((template) => template.id === rule.templateId)?.name ?? rule.templateId
        };
    }
}
