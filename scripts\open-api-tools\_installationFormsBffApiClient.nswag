{"runtime": "<PERSON><PERSON><PERSON>", "defaultVariables": null, "documentGenerator": {"fromDocument": {"url": "$(ApiSpecificationRepoRootPath)/api/KMD.Elements.InstallationForms.BFF/openapi/v1/kmd-elements-installation-forms-bff.bundled.yaml", "output": null, "newLineBehavior": "CRLF"}}, "codeGenerators": {"openApiToTypeScriptClient": {"className": "InstallationFormsClient", "exceptionClass": "InstallationFormsApiException", "responseClass": "InstallationFormsApiResponse", "baseUrlTokenName": "INSTALLATIONFORMS_BASE_URL", "output": "../../src/app/api/installation-forms-client.ts", "templateDirectory": "$(ApiSpecificationRepoRootPath)/nswag/templates/TypeScript", "moduleName": "", "typeScriptVersion": 4.6, "template": "Angular", "promiseType": "Promise", "httpClass": "HttpClient", "withCredentials": false, "useSingletonProvider": false, "injectionTokenType": "InjectionToken", "rxJsVersion": 7.5, "dateTimeType": "Date", "nullValue": "Undefined", "generateClientClasses": true, "generateClientInterfaces": false, "generateOptionalParameters": true, "exportTypes": true, "wrapDtoExceptions": true, "clientBaseClass": null, "wrapResponses": true, "wrapResponseMethods": [], "generateResponseClasses": true, "protectedMethods": [], "configurationClass": null, "useTransformOptionsMethod": false, "useTransformResultMethod": false, "generateDtoTypes": true, "operationGenerationMode": "SingleClientFromOperationId", "markOptionalProperties": true, "generateCloneMethod": false, "typeStyle": "Class", "enumStyle": "Enum", "useLeafType": false, "classTypes": [], "extendedClasses": [], "extensionCode": null, "generateDefaultValues": true, "excludedTypeNames": [], "excludedParameterNames": [], "handleReferences": false, "generateTypeCheckFunctions": false, "generateConstructorInterface": true, "convertConstructorInterfaceData": false, "importRequiredTypes": true, "useGetBaseUrlMethod": false, "queryNullValue": "", "useAbortSignal": false, "inlineNamedDictionaries": false, "inlineNamedAny": false, "includeHttpContext": false, "typeNameGeneratorType": null, "propertyNameGeneratorType": null, "enumNameGeneratorType": null, "serviceHost": null, "serviceSchemes": null, "newLineBehavior": "CRLF"}}}