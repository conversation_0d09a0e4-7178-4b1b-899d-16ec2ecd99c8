import { MissingTranslationHand<PERSON>, MissingTranslationHandlerParams } from '@ngx-translate/core';

export class MpmMissingTranslationHandler extends MissingTranslationHandler {
    static isArrayItemPathRegex: RegExp = /propertyPath\..*\[\d*\]/;
    static propertyPathSubstr = 'propertyPath.';

    handle(params: MissingTranslationHandlerParams): any {
        //currently we do not have a smart way to translate property path when it is an array item
        //we should find a good solution for it, but until then - lets show the property path as translation
        if (MpmMissingTranslationHandler.isArrayItemPathRegex.test(params.key)) {
            return params.key.substring(MpmMissingTranslationHandler.propertyPathSubstr.length);
        }
        return `Key - "${params.key}" - Not Found`;
    }
}
