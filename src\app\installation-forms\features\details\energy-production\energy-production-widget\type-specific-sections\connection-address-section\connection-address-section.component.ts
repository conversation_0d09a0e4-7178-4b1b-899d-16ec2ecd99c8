import { Component, EventEmitter, Input, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { Subscription } from 'rxjs';
import { FormCategory, LocationType } from 'src/app/api/installation-forms-client';
import { FormDataService } from 'src/app/core/services/form-data/form-data.service';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import { WidgetWithFormComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget-with-form.component';

@Component({
    selector: 'app-connection-address-section',
    templateUrl: './connection-address-section.component.html',
    styleUrls: ['./connection-address-section.component.scss'],
    standalone: false
})
export class ConnectionAddressSectionComponent extends WidgetWithFormComponent implements <PERSON>Ini<PERSON>, OnD<PERSON>roy {
    @Input() form!: FormGroup;
    @Input() type!: FormCategory;

    subscription: Subscription = new Subscription();
    locationTypeOptions: SelectItem[] = [];

    constructor(
        private readonly translateService: TranslateService,
        readonly formDataService: FormDataService
    ) {
        super(formDataService);
    }

    ngOnInit(): void {
        this.getEnumTranslations();

        this.form.get('locationType')?.valueChanges.subscribe((value) => {
            if (value === LocationType.Sea) {
                this.form.get('cadastralRegistrationNumber')?.setValue(null);
            }
        });
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    getEnumTranslations() {
        this.locationTypeOptions = enumMapper.map(this.translateService.instant('enums.locationType'), LocationType);
    }

    isCadastralRegistrationNumberRequired() {
        return this.type === FormCategory.Wind && this.form.get('locationType')?.value === LocationType.Land;
    }
}
