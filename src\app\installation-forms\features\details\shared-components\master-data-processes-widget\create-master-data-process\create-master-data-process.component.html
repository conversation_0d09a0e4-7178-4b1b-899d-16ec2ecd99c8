<div #overlayContainer></div>

<div fxLayout="column" class="mt-10 zebra-container" [formGroup]="form">
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['masterDataProcessType']" for="masterDataProcessType">
                {{ 'masterDataProcessesWidget.masterDataProcessType' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="masterDataProcessType"
                [options]="masterDataProcessTypeOptions"
                (onChange)="onMasterDataProcessTypeChange()"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="masterDataProcessType">
            </p-select>
            <small [controlValidationErrors]="form.controls['masterDataProcessType']" class="p-error"></small>
        </div>
    </div>

    <div
        class="zebra-item"
        fxLayout="row"
        fxLayout.lt-sm="column"
        fxLayoutGap="15px"
        *ngIf="isMasterDataProcessTemplateVisible()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['masterDataProcessTemplate']" for="masterDataProcessTemplate">
                {{ 'masterDataProcessesWidget.masterDataProcessTemplate' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="masterDataProcessTemplate"
                [options]="masterDataProcessTemplateOptions"
                optionValue="id"
                optionLabel="name"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="masterDataProcessTemplate">
            </p-select>
            <small [controlValidationErrors]="form.controls['masterDataProcessTemplate']" class="p-error"></small>
        </div>
    </div>
</div>

<div class="mt-10" fxLayout="row" fxLayoutAlign="flex-end center" style="margin: 10px">
    <div>
        <button
            id="cancelButton"
            [disabled]="isProcessing"
            type="button"
            pButton
            pRipple
            class="mr-2 p-button-secondary"
            (click)="onCancelClicked()">
            {{ 'common.cancel' | translate | titlecase }}
        </button>
        <button
            id="saveButton"
            [disabled]="isProcessing || !this.form.valid"
            type="button"
            pButton
            pRipple
            (click)="onCreateClicked()">
            <span *ngIf="isProcessing" class="fa fa-spin fa-spinner"></span>
            {{ 'common.create' | translate | titlecase }}
        </button>
    </div>
</div>
