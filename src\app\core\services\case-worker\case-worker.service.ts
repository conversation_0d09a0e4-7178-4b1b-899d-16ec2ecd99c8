import { Injectable } from '@angular/core';
import { AuthorizationService } from '@kmd-elements-ui/authorization';
import { Observable, map, of } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class CaseWorkerService {
    constructor(private readonly authService: AuthorizationService) {}

    isCurrentUser$(email?: string): Observable<boolean> {
        return !email
            ? of(false)
            : this.authService.getUser().pipe(map((currentUser) => email.toLowerCase() === currentUser?.email?.toLowerCase()));
    }
}
