{"common": {"emptyList": "Ingen resultater fundet", "of": "af", "from": "fra", "searchResults": "Søgeresultater", "singleChangedFieldInformation": "Du har foretaget {{amount}} ændring", "pluralChangedFieldsInformation": "Du har foretaget {{amount}} ænd<PERSON>er", "add": "Tilføj", "create": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "saveChanges": "<PERSON><PERSON>", "cancelChanges": "<PERSON><PERSON><PERSON>", "successTitle": "Succes", "validationTitle": "<PERSON><PERSON><PERSON>", "errorTitle": "<PERSON><PERSON><PERSON>", "errorRefreshingDetail": "Fejl under opdatering af data", "staleDataTitle": "<PERSON><PERSON><PERSON>", "staleDataDetail": "Data blev ændret af en anden bruger eller et system. Du skal genindlæse blanketten og vil miste ikke gemte ændringer.", "concurrentModification": "<PERSON>, du forsøgte at redigere, blev ændret af en anden bruger, efter du hentede den oprindelige værdi. Genindlæs venligst siden.", "allRequiredFieldsShouldBeCompleted": "Alle obligatoriske felter skal udfyldes", "fieldIsRequired": "Feltet er krævet", "fieldIsInWrongFormat": "Værdien har forkert format", "fieldIsOutsideCorrectMinRange": "Den korrekte minimumsværdi er {{min}}", "fieldIsOutsideCorrectMaxRange": "Den korrekte maksimumværdi er {{max}}", "selectValue": "<PERSON><PERSON><PERSON><PERSON>", "showMore": "Vis mere", "showLess": "Vis mindre", "lackOfPermissions": "Du har ikke rettigheder til dette område. Kontakt venligst din brugeradministrator.", "yesOption": "<PERSON>a", "noOption": "<PERSON><PERSON>", "emptyValue": "<PERSON> v<PERSON>", "addressSearchError": "Der opstod en fejl ved søgning efter adresser.", "meterFramesSearchError": "Der opstod en fejl ved søgning efter målerrammer.", "formNotValid": "<PERSON><PERSON> <PERSON> kan ikke <PERSON>, da <PERSON>ten ikke er gyldig", "columnsReset": "Nulstil kolo<PERSON>", "filterLabelContains": "Indeholder", "filterLabelNotContains": "Indeholder Ikke", "filterLabelStartsWith": "Starter <PERSON>", "filterLabelEndsWith": "<PERSON><PERSON>", "filterLabelEquals": "Lige Til", "filterLabelNotEquals": "Ikke Li<PERSON>", "filterLabelDateIs": "<PERSON>to er", "filterLabelDateIsNot": "Dato er ikke", "filterLabelDateIsBefore": "<PERSON><PERSON> er før", "filterLabelDateIsAfter": "Dato er efter", "filterLabelGreaterThanOrEqualTo": "<PERSON><PERSON><PERSON> end eller lig med", "filterLabelGreaterThan": "Større end", "filterLabelLessThanOrEqualTo": "Mindre end eller lig med", "filterLabelLessThan": "Mindre end", "yesNoPlaceholder": "<PERSON><PERSON>/<PERSON><PERSON>", "loadMasterDataProcessTemplatesError": "Der opstod en fejl under indlæsning af stamdataprocesskabeloner.", "loadEmailTemplatesError": "Der opstod en fejl under indlæsning af e-mailskabeloner", "dateMustNotBePast": "Datoen må ikke ligge i fortiden", "displayNameAlreadyUsed": "Visningsnavnet er allerede i brug.", "multipleWhitespaces": "Der må ikke være flere mellemrum."}, "home": {"tabAreaLabel": "<PERSON><PERSON><PERSON><PERSON>", "tabLabel": "<PERSON><PERSON><PERSON>", "columnHeaders": {"tags": "Tags", "formNumber": "Blanketnummer", "category": "<PERSON><PERSON><PERSON>", "installationAddress": "Installationsadresse", "state": "Status", "type": "Blankettype", "problemCategories": "Problemkategorier", "formRequiresAttention": "Kræver opmærksomhed", "createdDate": "Oprettelsesdato", "hasUnreadMessages": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>der", "installerName": "Installatør navn", "installerAuthorizationNumber": "Autorisationsnummer", "caseWorkerName": "Sagsbehandlerens navn", "caseWorkerEmail": "Sagsbehandler e-mail", "scopeOfDeliverySize": "Leveringsomfang (A)", "installInConstructionPhase": "Starter <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "processLevelIndicators": "Procesniveauindikatorer", "latestRegistrationDate": "Seneste tilmeldingsdato"}}, "details": {"form": "Blanket", "formNumber": "Blanketnummer", "category": "<PERSON><PERSON><PERSON>", "state": "Status", "type": "Type", "installationAddress": "Installationsadresse", "typeAndState": "Type og status", "creationDateTime": "Oprettelsesdato:", "lastChanged": "Sidst ændret:", "importantDates": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>er", "meterNumber": "Målernummer", "screeningStatus": "Tillad automatisering", "problemCategories": "Problemkategorier", "formRequiresAttention": "Kræver opmærksomhed"}, "address": {"darId": "DAR adresse ID", "country": "Land", "city": "By", "postalCode": "Postnummer", "buildingNumber": "Husnummer", "streetName": "Vejnavn", "floor": "Etage", "door": "<PERSON><PERSON><PERSON>", "addressDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "notValidWarning": "<PERSON><PERSON><PERSON> er ikke valid", "pattern": "Gade og nummer, etage og dør, postnummer og by"}, "caseWorkerWidget": {"email": "E-mail", "name": "Navn", "phoneNumber": "Telefonnummer", "assignMe": "Tildel til mig", "unassign": "<PERSON><PERSON><PERSON> tildeling", "title": "Tildelt til", "unassignSuccessDetails": "Sagsbehandler fjernet", "assignSuccessDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON> til<PERSON>t", "takeOverFormQuestion": "Er du sikker på du vil ændre sagsbehandler?", "onlyAssignedUserCanEditFormInformation": "Redigering kun tilladt for den tildelte sagsbehandler"}, "workOrdersWidget": {"title": "Arbejdsordrer", "workOrderType": "Arbejdsordre type", "workOrderDescription": "Arbejdsbeskrivelse", "workOrderPurpose": "<PERSON><PERSON><PERSON>", "meterFrameNumber": "Målerrammenummer", "createWorkOrder": "<PERSON><PERSON>", "createWorkOrderFailed": "Arbejdsordren kunne ikke oprettes", "getConfigError": "Der opstod en fejl ved indlæsning af konfigurationen for arbejdsordrer.", "noWorkOrdersFound": "Ingen arbejdsordrer fundet.", "columnNames": {"workOrderType": "Type", "workOrderPurpose": "<PERSON><PERSON><PERSON>", "status": "Status", "workOrderStarted": "Startet"}, "enums": {"workOrderEntryState": {"Creating": "Opretter", "ValidationFailed": "Kunne ikke oprettes", "AwaitingValidation": "Afventer validering", "Created": "Oprettet", "Draft": "Kladde", "Unassigned": "<PERSON><PERSON><PERSON> tildel", "Assigned": "<PERSON><PERSON><PERSON>", "Active": "I gang", "OnHold": "<PERSON><PERSON><PERSON><PERSON>", "ReceivedCompleted": "Modtaget udført", "ClosedByTechnician": "Lukket af tekniker", "Cancelled": "<PERSON><PERSON><PERSON><PERSON>", "Done": "<PERSON><PERSON><PERSON><PERSON>"}}}, "masterDataProcessesWidget": {"title": "Stamdataprocesser", "masterDataProcessType": "Type", "masterDataProcessTemplate": "Skabelon", "createMasterDataProcess": "<PERSON><PERSON>", "createMasterDataProcessFailed": "Oprettelse af stamdataproces fejlet", "noMasterDataProcessesFound": "Ingen stamdataprocesser fundet.", "postMasterDataProcessesMeterTransformerSetToOther": "Stamdataprocessen kan ikke startes, da feltet Målertransformer skal udfyldes med en anden værdi fra værdilisten end \"Andet\".", "columnNames": {"masterDataProcessType": "Type", "status": "Status", "masterDataProcessStarted": "Startet"}, "enums": {"masterDataProcessEntryState": {"Creating": "Opretter", "AwaitingForProcessCreation": "<PERSON><PERSON><PERSON><PERSON> pro<PERSON>", "FailedToCreateProcess": "Oprettelse af stamdataproces fejlet", "New": "Ny", "InProgress": "I gang", "Failed": "<PERSON><PERSON><PERSON>", "RequiresManualHandling": "<PERSON><PERSON><PERSON><PERSON> manuel behandling", "ManuallyHandled": "<PERSON><PERSON> behandlet", "Completed": "Afsluttet", "Cancelled": "<PERSON><PERSON><PERSON><PERSON>", "Awaiting": "<PERSON><PERSON><PERSON><PERSON>"}}}, "chatAndNotesWidget": {"title": "Kommunikation", "chat": {"title": "Cha<PERSON>", "sendMessage": "Send", "markAllAsRead": "<PERSON><PERSON><PERSON> alle som læst", "getChatMessagesError": "Der opstod en fejl ved indlæsning af chatbeskeder.", "createNewChatMessageError": "Der opstod en fejl ved oprettelse af ny chatbesked.", "markAllAsReadError": "Der opstod en fejl under markeringen af alle som læst.", "markAllAsReadSuccess": "Alle beskeder er markeret som læst.", "syncFailedAt": "Synkronisering af chatbeskeden mislykkedes."}, "notes": {"title": "Noter", "content": "Indhold", "modifiedBy": "Senest redigeret af", "add": "Tilføj", "cancel": "<PERSON><PERSON><PERSON>", "save": "Gem", "edit": "<PERSON><PERSON><PERSON>", "getNotesError": "Der opstod en fejl ved indlæsning af noter.", "createNewNoteError": "Der opstod en fejl ved oprettelse af ny note.", "modifyNoteError": "Der opstod en fejl under rettelsen af noten.", "deleteNoteError": "Der opstod en fejl ved sletning af noten.", "noNotesFound": "Ingen noter fundet.", "deleteConfirmationTitle": "Slet note", "deleteConfirmationMessage": "<PERSON>r du sikker på, at du vil slette denne note?"}}, "problemsWidget": {"title": "<PERSON><PERSON><PERSON>", "errors": "<PERSON><PERSON><PERSON>", "warnings": "<PERSON><PERSON><PERSON>", "info": "Info", "acknowledged": "Anerkendt", "markAsAcknowledged": "Anerkend", "markAsAcknowledgedError": "Fejl under anerkendelse af alarm", "markAsAcknowledgedConfirmationTitle": "Anerkend", "markAsAcknowledgedConfirmationMessage": "Er du sikker på, at du vil anerkende denne alarm? Anerkendelse kan have uventede konsek<PERSON>, da automatiseringer kan starte med forkert eller mangler data og bør anvendes med forsigtighed."}, "installerWidget": {"title": "Installatør", "companyName": "Virksomhed", "companyPhoneNumber": "Firma telefonnummer", "companyEmail": "Firma e-mail", "cvr": "CVR", "companyAuthorizationNumber": "Autorisationsnummer", "name": "Installatørnavn", "phoneNumber": "Telefonnummer", "email": "E-mail", "secondaryContact": {"title": "Alternativ Kontaktperson", "name": "Navn", "phoneNumber": "Telefonnummer", "email": "E-mail"}}, "installationWidget": {"title": "Installation", "installationAddress": {"title": "<PERSON><PERSON><PERSON>"}, "connectionPoint": {"title": "Forbindelsespunkt", "navigateLink": "Oplysninger om forbindelsespunkt", "notSelectedWarning": "Forbindelsespunkt ikke valgt", "connectionStateNotValid": "Forbindelsesstatus ikke gyldig"}, "meterFrame": {"title": "Målerramme", "navigateLink": "Oplysninger om målerramme", "notSelectedWarning": "Målerramme ikke valgt"}, "gridAreaId": {"title": "Netområde"}, "consumptionMeteringPointId": {"navigateLink": "Oplysninger om målepunkt", "title": "ForbrugsmålepunktsID"}, "productionMeteringPointId": {"navigateLink": "Oplysninger om målepunkt", "title": "Produktionsmålepunktsid"}, "remarksToInstallation": "Bemærkninger", "temporaryInstallationDuration": "<PERSON><PERSON><PERSON><PERSON> af mid<PERSON>g installation", "contactPerson": {"title": "Kontaktperson", "companyName": "Virksomhed", "name": "Navn", "phoneNumber": "Telefonnummer", "email": "E-mail", "addContactPersonData": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>ak<PERSON>", "deleteContactPersonData": "<PERSON><PERSON> kontaktperson"}, "tags": "Tags", "loadMeteringPointsError": "Der opstod en fejl under indlæsning af målepunktdata", "noMatchingMeteringPointsWarning": "Der blev ikke fundet noget matchende målepunkt", "multipleMeteringPointsWarning": "Flere matchende målepunkter fundet (forkert konfiguration)", "meterFramesSearchError": "Der opstod en fejl ved søgning efter målerrammer.", "connectionPointsSearchError": "Der opstod en fejl ved søgning efter forbindelsespunkter.", "loadGridAreasError": "Der opstod en fejl under indlæsning af netområder."}, "meterFrameSearch": {"navigateLink": "Oplysninger om målerramme", "notSelectedWarning": "Målerramme ikke valgt"}, "payerWidget": {"title": "<PERSON><PERSON>", "payer": {"title": "<PERSON><PERSON>", "type": "Betalertype", "name": "Navn", "email": "E-mail", "addPayerData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "deletePayerData": "Slet betaler"}, "payerAddress": {"title": "Betaleradresse"}, "financialInformation": {"title": "Finansiel information", "cvrOrSeNumber": "CVR eller SE-nummer", "requisition": "Rekvisitionsnummer", "eanNumber": "EAN-nummer"}, "contactPerson": {"title": "Kontaktperson", "name": "Navn", "phoneNumber": "Telefonnummer", "email": "E-mail"}}, "meterDeliveryWidget": {"title": "Målerlevering", "requestedConnectionDateEod": "Ønsket tilslutningsdato", "installedDuringConstruction": "Starter som bygg<PERSON><PERSON><PERSON><PERSON>", "deliveryOption": "Opsætningsvalg", "meterPlacementId": "<PERSON><PERSON><PERSON> placering", "deliveryInformation": {"title": "Leveringsinformation", "name": "Modtagerens navn", "attention": "Modtagerens att.", "address": "Modtageradresse"}}, "meterReturnWidget": {"title": "<PERSON><PERSON><PERSON> returnering", "returnOption": "Nedtagningsvalg", "name": "Lager navn", "attention": "Lager att.", "address": "Lageradresse"}, "terminationWidget": {"title": "Afmelding", "scope": "Omfang af afmelding", "terminationDateEod": "Dato for afmelding"}, "technicalInformationWidget": {"title": "Teknisk information", "branchLine": {"title": "Stikledning", "type": "Stikledningstype", "fuseTypeOfMainProtection": "Beskyttelse, sikringstype", "fuseSizeOfMainProtection": "Be<PERSON><PERSON>lse, sikringsstørrelse (A)", "protectionTypeOfMainProtection": "Beskyttelse", "cableDimensionId": "Kabeldimension", "numberOfPairs": "Par antal", "protectiveEarthingDimension": "PE (mm2)", "meterFrame": "Målerramme"}, "connection": {"title": "Tilslutning", "scopeOfDelivery": "Leveringsomfang (A)", "transformerStationNumber": "Transformer st. nr.", "cabinetNumber": "Kabelskabsnr.", "groundingMethod": "Jordingsmetode", "fuseSizeOfPreProtection": "Forsikring, sikringsstørrelse (A)", "protectionTypeOfPreProtection": "Forsikring", "phaseCount": "Fase antal"}, "meterType": {"title": "Målertype", "meterPlacementId": "<PERSON><PERSON><PERSON> placering", "installInConstructionPhase": "Starter <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "meterSize": "M<PERSON>lerstø<PERSON><PERSON>", "connectionTypeId": "Tilslutningstype", "connectionTypeChange": "<PERSON><PERSON><PERSON> til<PERSON>", "meterTransformerId": "<PERSON><PERSON><PERSON> transformer", "meterTransformerRemark": "Bemærkninger"}}, "instructionDataWidget": {"title": "Anvisningsinformation", "remark": "Bemærkninger", "meterResponsible": "M<PERSON><PERSON>h<PERSON>ndtering", "branchLineResponsible": "Stikledningshåndtering", "connectionFee": {"validUntilEod": "Gældende til og med", "fee": "Tilslutningsbidrag ekskl. moms [DKK]", "currency": "Valuta"}, "instructionTextsPicker": {"openPicker": "Åbn instruktionstekstvælgeren", "closePicker": "Luk instruktionstekstvælger", "apply": "Tilføj valgte instruktionstekster", "selectInstructionTexts": "Vælg instruktionstekster"}, "getInstructionTextsError": "Der opstod en fejl ved indlæsning af instruktionstekster."}, "connectionRights": {"title": "Tilslutningsrettigheder", "updateButton": "Opdater tilslutningsrettigheder", "addTransferButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>f<PERSON><PERSON>l", "sectionTitle": "Overførsel af tilslutningsrettigheder", "summaryInfo": "Total overførsel {{TotalConnectionRightsTransfer}}A", "noTransfersFound": "Ingen overførsler fundet.", "columnHeaders": {"meterFrameNumber": "Målerrammenummer", "value": "<PERSON><PERSON><PERSON><PERSON> (A)"}, "updateConnectionRightsSucceded": "Forbindelsesrettigheder opdateret på målerammen.", "updateConnectionRightsFailed": "Der opstod en fejl under opdateringen af forbindelsesrettighederne."}, "commonReadings": {"sectionTitle": "Kollektiv målinger", "commonReadingId": "Kollektiv måling", "columnHeaders": {"type": "Type", "number": "<PERSON><PERSON>"}}, "sealWidget": {"title": "Plombe", "meterPlacementId": "<PERSON><PERSON><PERSON> placering", "responsibleForSeal": "<PERSON><PERSON><PERSON><PERSON><PERSON> for genplombering", "reasonForChange": "<PERSON><PERSON><PERSON>"}, "moveMeterWidget": {"title": "Målerflytning", "reasonForChange": "<PERSON><PERSON><PERSON>"}, "changeOfMeterWidget": {"title": "Målerskifte", "reasonForChange": "<PERSON><PERSON><PERSON>", "responsibleForSeal": "<PERSON><PERSON><PERSON><PERSON><PERSON> for genplombering"}, "changeBranchLineWidget": {"title": "Stikledning", "newMeterRequested": "<PERSON>y måleranmodning"}, "extensionWidget": {"title": "Udvidelse", "requestedDeliveryExtensionDateEod": "<PERSON><PERSON><PERSON> udvidelse", "newMeterRequested": "<PERSON>y måleranmodning"}, "flagsWidget": {"types": {"MeterInstalled": "M<PERSON>ler installeret", "SupplierSelected": "Lev<PERSON><PERSON>ø<PERSON> valgt", "ConnectionFeePaid": "Tilslutningsbidrag betalt", "ReadyForMeter": "<PERSON><PERSON> til m<PERSON>", "SubmittedForProjectPlanning": "Sendt til projektering"}, "manuallySetDate": "<PERSON><PERSON> sat d.", "readyForMeterDate": "<PERSON><PERSON> til måler dato", "title": "Flag"}, "usageSection": {"title": "Anvendel<PERSON>", "applicationType": "Anvendel<PERSON>", "effect": "Effekt [kW]", "phaseCount": "Fase antal", "remark": "Bemærkning", "addApplication": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noApplicationsFound": "Ingen anvendelse fundet."}, "voltageSection": {"voltageLevelId": "Underkategori", "currentTransformer": "Valg af strømtransformer", "voltageTransformer": "Valg af spændingstransformer"}, "emailsWidget": {"create": "<PERSON><PERSON>", "template": "Skabelon", "title": "E-mails", "cancel": "<PERSON><PERSON><PERSON>", "sendEmail": "Send", "sendEmailError": "Der opstod en fejl under afsendelse af e-mail ", "noEmailsFound": "Ingen e-mails fundet.", "postSendEmailMissingData": "<PERSON><PERSON> ikke sende e-mail på grund af manglende data: {{FieldName}}.", "columnNames": {"templateName": "Skabelon navn", "status": "Status", "createdDate": "Oprettelsesdato", "recipientAddress": "E-mail"}, "emailStatuses": {"Failed": "<PERSON><PERSON><PERSON>", "InProgress": "I gang", "Completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "invoicesWidget": {"title": "<PERSON><PERSON><PERSON><PERSON>", "columnNames": {"number": "<PERSON><PERSON><PERSON>", "createdDate": "Oprettelsesdato", "createdInErpDate": "Fakturadato", "status": "Status", "totalSum": "Samlet sum", "isRequired": "Krævet"}, "invoiceStatuses": {"Draft": "Under oprettelse", "Created": "Oprettet", "Calculated": "<PERSON><PERSON><PERSON><PERSON>", "CalculationFailed": "Beregning fejlet", "Transferred": "<PERSON><PERSON><PERSON><PERSON>", "TransferStarted": "Overførsel startet", "Paid": "<PERSON>lt", "CreationFailed": "<PERSON><PERSON><PERSON><PERSON> fejlet"}, "createInvoice": "<PERSON><PERSON>", "invoiceType": "Fakturatype", "fee": "Gebyr", "loadPricesError": "Der opstod en fejl ved indlæsning af prisdefinitioner.", "postInvoicesError": "Der opstod en fejl under oprettelse af fakturaen.", "postInvoicesFormNotValid": "Det er kun muligt at oprette en faktura fra  en gyldig blanket.", "postInvoicesSuccess": "Ny faktura er blevet oprettet.", "getInvoicesError": "Der opstod en fejl ved indlæsning af fakturaer.", "noInvoicesFound": "Ingen fakturaer fundet.", "transferButton": "<PERSON><PERSON><PERSON><PERSON>", "transferInvoiceSingleInvoicingProblem": "Der opstod en fejl under overførslen af fakturaen. Fakturaen blev ikke overført."}, "tasksWidget": {"title": "Opgaver", "columnNames": {"name": "Opgave", "state": "Status", "startedDate": "Startdato", "completionDate": "Afslutningsdato", "isManual": ""}, "taskStatuses": {"NotStarted": "<PERSON>kke startet", "InProgress": "I gang", "Completed": "Afsluttet", "Failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "taskGroups": {"AllowAutomatization": "Tillad automatisering", "CreateInvoice": "Oprette faktura", "AutomaticEmails": "Automatiske e-mails", "CreateWorkOrder": "Oprette arbejdsordre", "CreateMasterDataProcess": "<PERSON>ret stamdataproces", "CreateMasterDataProcessWithCloseDown": "<PERSON><PERSON> stamdataproces med lukning", "Archiving": "Arkivering", "AutomaticInstructionText": "Automatisk instruktionstekst"}, "getTasksError": "Der opstod en fejl under hent<PERSON> af opgaver.", "markAsCompleted": "A<PERSON>lut", "noTasksFound": "Ingen opgaver fundet.", "reExecute": "Genkør", "manualTask": "<PERSON>", "automaticTask": "Autotamisk opgave"}, "relatedFormsSection": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "searchError": "Der opstod en fejl under søgning efter blanketter ved hjælp af blanketnummer.", "formAlreadyHasRelationWarning": "Den valgte blanket har allerede en relation til den aktuelle blanket.", "formNumber": "Blanketnummer", "state": "Status", "formType": "Type", "relationType": "Relationstype", "addRelatedForm": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> man<PERSON>t", "origin": "Relationsoprindelse", "direction": "Relationssretning", "noRelatedFormsFound": "Ingen relaterede blanketter fundet.", "createdDate": "Modtaget"}, "energyProductionSection": {"title": "Elproduceren<PERSON>", "common": {"energyProductionType": "Anlægstype", "energyProductionConnectionType": "Tilslutningspunkt", "owner": {"name": "<PERSON><PERSON><PERSON> navn", "address": "<PERSON><PERSON><PERSON> adress<PERSON>", "identificationType": "Ejers identifikationstype", "identifier": "<PERSON><PERSON><PERSON> identifikator"}, "moreThan125kW": "Over 125kW", "includesStorage": "<PERSON><PERSON><PERSON>g med energilager", "commissioningDateSod": "Idriftsættelsesdato"}, "connectionAddress": {"title": "Tilslutningsadresse", "address": "<PERSON><PERSON><PERSON>", "consumptionUnitType": "Forbrugsenhedstype", "locationType": "Placeringstype", "cadastralDistrictIdentifier": "<PERSON><PERSON><PERSON>identifi<PERSON>", "cadastralRegistrationNumber": "Matrikelregistreringsnummer", "locationUtmX": "Placering [Utm X]", "locationUtmY": "Placering [Utm Y]", "buildingNumber": "Bygningsnummer i BBR-register", "propertyNumber": "Ejendomsnummer i BBR-register", "connectionPointUtmX": "Tilslutningspunkt [Utm X]", "connectionPointUtmY": "Tilslutningspunkt [Utm Y]", "foundationElevation": "Kote på fundament som ref."}, "windPlant": {"certificate": "Typegodkendelse", "certificationProvider": "Godkendelsesorgan", "rotorDiameterInMeters": "Rotordiameter", "hubHeightInMeters": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "powerPlant": {"primaryEnergySource": "Hovedenergitype", "primaryEnergyShareInPercent": "Hovedenergi andel (%)", "secondaryEnergySource": "Sekundærenergitype", "secondaryEnergyShareInPercent": "Sekundærenergitype andel (%)", "plantTypeName": "Anlægstype"}, "plants": {"plant": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON>", "phaseCount": "Fase antal", "totalPowerInKw": "Installeret effekt [kW]", "commissioningDateSod": "Idriftsættelsesdato", "brandName": "Panelfabrikat", "modelName": "Panelmodel", "typeName": "Inverter", "solarSystemAreaInM2": "Areal for anlæg [m^2]", "addPlant": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "calculatedTotalPowerInKw": "Beregnet installeret effekt [kW]", "totalCount": "Samlet effekt:"}, "calculatedTotalProductionCapacity": "Beregnet samlet produktionskapacitet [kW]"}, "actionBar": {"stateHandling": {"Returned": "<PERSON><PERSON><PERSON><PERSON>", "Instructed": "<PERSON><PERSON>", "Cancelled": "<PERSON><PERSON><PERSON> og arkiver", "Rejected": "Afvis og arkiver", "Archived": "<PERSON><PERSON>", "SaveAndContinue": "Gem og forsæt", "stateChangeComment": "Ændringskommentar"}, "screeningStatusHandling": {"HandleManually": "<PERSON><PERSON><PERSON> manuelt", "Screened": "Tillad automatisering", "screeningStatusDialogTitle": "Tillad automatisering", "revertToHandleManuallyQuestion": "Automatization er allerede tilladt. <PERSON>r du sikker på, du ønsker at behandl form manuelt?"}}, "valueListComponent": {"valueListsNotFoundError": "Værdilisten blev ikke fundet.", "valueListsError": "Der opstod en fejl ved indlæsning af værdilister."}, "enums": {"fieldNames": {"contactPerson": "Kontaktperson", "contactPersonName": "Kontaktperson Navn", "contactPersonEmail": "Kontaktperson E-mail", "consumptionMeteringPointId": "ForbrugsmålepunktsID", "templateId": "SkabelonID"}, "pipeDimension": {"Pe6CU": "6 CU", "Pe10CU": "10 CU", "Pe16CU": "16 CU", "Pe25CU": "25 CU", "Pe35CU": "35 CU", "Pe50CU": "50 CU", "Pe70CU": "70 CU", "Pe95CU": "95 CU", "Pe120CU": "120 CU", "Pe150CU": "150 CU", "Pe185CU": "185 CU", "Pe240CU": "240 CU", "Pe300CU": "300 CU", "Pe16AL": "16 AL", "Pe25AL": "25 AL", "Pe35AL": "35 AL", "Pe50AL": "50 AL", "Pe70AL": "70 AL", "Pe95AL": "95 AL", "Pe120AL": "120 AL", "Pe150AL": "150 AL", "Pe185AL": "185 AL", "Pe240AL": "240 AL", "Pe300AL": "300 AL"}, "connectionGroundingMethod": {"Tt": "TT", "TnS": "TN-S", "TnC": "TN-C", "TnCS": "TN-C-S", "It": "IT", "Other": "And<PERSON>"}, "branchLineType": {"Existing": "Eksisterende delt", "New": "Ny delt", "Own": "Egen"}, "preProtectionType": {"Fuse": "<PERSON><PERSON><PERSON>", "MaxBreaker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "None": "Ingen"}, "mainProtectionType": {"Fuse": "Stikledningssikring", "MaxBreaker": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "supplyType": {"Electricity": "El", "Water": "<PERSON><PERSON>", "Heating": "Varme"}, "commonReadingType": {"LargeApartment": "<PERSON><PERSON> le<PERSON>", "SmallApartment": "<PERSON> lejlighed", "TerracedHouse": "Rækkehus", "Common": "<PERSON><PERSON><PERSON>", "Business": "Forretning"}, "type": {"NewInstallation": "Ny installation", "SealBreach": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Termination": "Afmelding", "ChangeMeter": "Målerskifte", "MoveMeter": "Målerflytning", "ChangeBranchLine": "Stikledning", "Extension": "Udvidelse", "EnergyProduction": "Elproduceren<PERSON>"}, "state": {"InTransit": "I transit", "Registered": "Tilmeldt", "Returned": "Returneret", "Instructed": "<PERSON><PERSON>", "Completed": "Færdigmeldt", "Cancelled": "<PERSON><PERSON><PERSON><PERSON>", "Rejected": "<PERSON><PERSON><PERSON>", "Archived": "Arkiveret", "AwaitingFormSystemUpdate": "Afventer blanketsystem"}, "screeningStatus": {"HandleManually": "<PERSON><PERSON>", "Screened": "<PERSON>a"}, "category": {"BusinessOrOther": "Øvrige/Erhverv", "ParcelOrCottage": "<PERSON><PERSON><PERSON><PERSON>", "TerracedHouse": "Rækkehus (tæt/lav)", "StandardApartment": "Standard lejlighed", "LargeApartment": "<PERSON><PERSON> le<PERSON>", "YouthOrRetirementOrNursingHome": "Ungdoms-, ældre- og plejebolig", "Allotment": "Kolonihave", "SmallInstallation": "Små installationer", "TemporaryInstallation": "Midlertidig", "Solar": "Solcelleanlæg & -delanlæg", "Wind": "Vindmølleanlæg", "Consumption": "Forbrugsenheder & -anlæg", "PowerPlant": "Kraftværker og delanlæg"}, "problemCategory": {"IntegrationIssue": "Integrationsproblem", "DataIssue": "Data problem", "ProcessingIssue": "Processeringsproblem"}, "meterDeliveryOption": {"GridCompanyWillHandle": "Netselskab håndterer", "InstallerWillHandlePickUp": "Installtør <PERSON>, afhentning", "InstallerWillHandleSendMeter": "<PERSON><PERSON><PERSON><PERSON><PERSON>, send m<PERSON><PERSON>"}, "meterConnectionTypeChange": {"PinToWire": "Ændring fra ledning til stikben", "WireToPin": "Ændring fra stikben til ledning"}, "meterReturnOption": {"GridCompanyWillHandle": "Netselskab håndterer", "InstallerWillHandleDropOff": "<PERSON><PERSON><PERSON><PERSON><PERSON>, indlevering", "InstallerWillHandleSendMeter": "<PERSON><PERSON><PERSON><PERSON><PERSON>, send m<PERSON><PERSON>"}, "meterSize": {"Below63A": "Under 63A", "Above63A": "Over 63A"}, "payerType": {"Company": "Virksomhed", "Private": "Privat", "Public": "Offentlig"}, "terminationScope": {"EntireInstallation": "<PERSON><PERSON>", "EnergyProduction": "Elproduceren<PERSON>"}, "meterTransformerId": {"C300_5": "300/5", "C600_5": "600/5", "C1000_5": "1000/5", "C1200_5": "1200/5", "Other": "And<PERSON>"}, "responsibleForSeal": {"GridCompany": "Netvirksomhed", "Installer": "Installatør"}, "responsibleForMeter": {"GridCompany": "Netvirksomhed", "Installer": "Installatør"}, "reasonForChange": {"TroubleshootingOrMaintenance": "<PERSON><PERSON><PERSON><PERSON><PERSON>ng eller vedligehold", "NewBranchlineOrSupply": "<PERSON>y stikledning/forsyning", "ChangeOfDashboardOrElectricalPanel": "Udskiftning af målertavle/gruppetavle", "Thermography": "Termografering", "Other": "And<PERSON>", "Remodeling": "Ombygning", "MoveMeter": "<PERSON><PERSON> m<PERSON>ler", "NewBranchline": "<PERSON><PERSON> sti<PERSON>ning", "ChangeMeterBoard": "Udskiftning af målertavle", "MeterBurned": "<PERSON><PERSON><PERSON> bræ<PERSON>", "MeterDefect": "<PERSON><PERSON><PERSON> defekt", "ChangeToWire": "<PERSON><PERSON><PERSON> til ledning", "ChangeToPin": "<PERSON><PERSON><PERSON> til stikben"}, "meterResponsible": {"SetupByGridCompany": "Netselskab opsætter", "PickedUpAndSetupByInstaller": "El-installatør afhenter og opsætter", "SendByGridCompanySetupByInstaller": "Netselskab sender, el-<PERSON><PERSON><PERSON><PERSON>sætter", "TakenDownAndReturnedByInstaller": "El-installatør nedtager og afleverer", "SetupByGridCompanyReturnedByInstaller": "Netselskab ops<PERSON><PERSON>, El-installatør afleverer", "PickedUpSetupAndReturnedByInstaller": "El-<PERSON><PERSON><PERSON><PERSON> a<PERSON>, opsætter og afleverer"}, "branchLineResponsible": {"GridCompanyConnects": "Netselskab tilslutter", "InstallerConnects": "El-installatør tilslutter", "GridCompanyDisconnects": "Netselskab demonterer", "InstallerDisconnects": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "InstallerConnectsAndDisconnects": "El-installatør tilslutter og demonterer", "GridCompanyConnectsAndDisconnects": "Netselskab tilslutter og demonterer"}, "applicationType": {"Motor": "Motor", "ThermalLoad": "Termisk belastning", "PowerPlant": "El-<PERSON><PERSON><PERSON>", "BatterySystem": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ChargingStand": "Lades<PERSON>der", "HeatPump": "Varmepumpe", "PhaseCompensationSystem": "Fasekompenseringsanlæg", "EmergencyFacility": "N<PERSON>danl<PERSON>g", "MachinePlant": "Maskinanlæg", "FieldIrrigationSystem": "Mark<PERSON>dingsanlæg", "StreetLights": "Gadelys/stibelysning", "Sign": "<PERSON><PERSON>", "CommonInstallation": "Fællesinstallation", "PowerGasWaterHeat": "El, Gas, Vand & varneforsyning", "Telecommunications": "Telekommunikation", "Other": "And<PERSON>", "ElectricFence": "Elhegn", "AntennaAmplifier": "Antenneforstærk<PERSON>", "TrafficCounter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PressureSensor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "WarningSiren": "Varslingssirene", "Defibrillator": "<PERSON><PERSON><PERSON><PERSON>r", "StandardResidentialInstallation": "Standard boliginstallation", "ConstructionSite": "Byggeplads", "CulturalEvent": "Kulturel begivenhed (marked, koncert, festival)"}, "energyProductionConnectionType": {"ConnectedByInstallation": "Installationstilsluttet (egenproducent)", "ConnectedDirectly": "Direkte tilsluttet (producent)"}, "ownerIdentificationType": {"Cvr": "CVR", "DateOfBirth": "Fødselsdato", "CommitmentIdentifier": "Tilsagnsid"}, "locationType": {"Land": "Land", "Sea": "Hav"}, "formsRelationType": {"Manual": "<PERSON>", "Automatic": "Automatisk", "AutoDetected": "Auto-detekteret"}, "formsRelationOrigin": {"ManualEntry": "<PERSON><PERSON>", "ConnectionPointMatch": "Match på forbindelsespunkt", "Eltilmelding": "Eltilmelding"}, "formsRelationDirection": {"NoDirection": "-", "Parent": "Parent-relation", "Child": "Child-relation"}, "temporaryInstallationDurationType": {"UpTo12Months": "Begr<PERSON><PERSON><PERSON> periode (maks. 12 måneder)", "Over12Months": "Byggeperiode over 12 måneder"}, "connectionRightsCategories": {"Allotment": "Kolonihavehus", "ExtensionAHigh": "Udvidelse A-høj", "ExtensionALow": "Udvidelse A-lav", "ExtensionBHigh": "Udvidelse B-høj", "ExtensionBLow": "Udvidelse B-lav", "ExtensionCLevel": "Udvidelse C-niveau", "KWMaxBHigh": "Maks. kW B-høj", "KWMaxBLow": "Maks. kW B-lav", "KWMaxCLevel": "Maks. kW C-niveau", "LargeApartment": "<PERSON><PERSON> le<PERSON>", "Other": "<PERSON><PERSON><PERSON><PERSON>", "SmallApartment": "Standard lejlighed", "SmallInstallation": "Små installationer", "Parcel": "Parcelhus/fritidshus", "YoungOldAndNursery": "Ungdoms-, ældre- og plejebolig", "TerracedHouse": "Rækkehus"}, "automationVoltageLevel": {"LowVoltage": "C", "HighVoltageBLow": "B-Lav", "HighVoltageBHigh": "B-Høj", "HighVoltageALow": "A-Lav", "HighVoltageAHigh": "A-Høj"}, "masterDataProcessType": {"ModifyConnectionPoint": "<PERSON><PERSON><PERSON>", "CreateConnectionPoint": "<PERSON><PERSON>bin<PERSON>pu<PERSON>", "CloseDownSupplyType": "<PERSON><PERSON><PERSON><PERSON>", "CloseDownProduction": "Ned<PERSON>æg elproduktion"}, "masterDataProcessAutomationLevel": {"InitiateProcessOnly": "Opret proces. kør ikke", "InitiateAndRunProcess": "<PERSON>ret og kør proces"}, "masterDataProcessWorkOrderAutomationLevel": {"Manual": "<PERSON>", "PrimaryMeterFrameOnly": "<PERSON>n primær m<PERSON>", "AllMeterFrames": "Alle måler<PERSON>mer"}, "transferConnectionRightUom": {"Ampere": "A", "Kilowatt": "kW", "KilovoltAmpere": "kVA"}, "processLevelIndicators": {"None": "Ingen", "MeterInstalled": "M<PERSON>ler installeret", "SupplierSelected": "Lev<PERSON><PERSON>ø<PERSON> valgt", "ReadyForMeter": "<PERSON><PERSON> til m<PERSON>", "NoWorkOrderCreated": "Ingen arbejdsordre oprettet", "MasterDataCreated": "Stamdata oprettet", "MasterDataUpdated": "Stamdata opdateret", "SupplyTypeClosedDown": "Forsyningsart lukket", "NoInvoiceCreated": "Der er ikke oprettet nogen faktura", "NoInvoiceTransferred": "Ingen faktura overført", "InvoicePaid": "Faktura betalt", "WelcomeLetterSent": "Velkomstbrev sendt", "EnergyProductionClosedDown": "Elproducerende anlæg lukket"}, "workOrderType": {"InstallMeter": "<PERSON>s<PERSON><PERSON> måler", "ReplaceMeter": "<PERSON><PERSON> måler", "RemoveMeter": "<PERSON><PERSON>", "General": "Generel"}, "invoiceType": {"ConnectionRights": "tilslutningsrettigheder", "StandardFee": "Standard gebyr", "CreatedExternally": "Oprettet eksternt"}}, "problemCodes": {"Unknown": "Ukendt. Årsag: '{{Message}}'", "CarUpdateChangedDarId": "{{[propertyPath]PropertyPath}} i det centrale adresseregister (CAR) har fået nyt DAR ID efter en opdatering. CAR ID: '{{carId}}'. Tidligere DAR ID: '{{oldDarId}}'. Nyt DAR ID: '{{newDarId}}'.", "NullValidator": "'{{<PERSON><PERSON><PERSON>}}' skal være tom.", "NotNullValidator": "'{{<PERSON><PERSON><PERSON>}}' er p<PERSON><PERSON><PERSON><PERSON><PERSON>.", "EanNumberValidator": "'{{PropertyName}}' skal være et gyldig EAN-13 nummer. Aktuel værdi '{{PropertyValue}}'.", "CvrNumberValidator": "'{{PropertyName}}' skal være et gyldigt <PERSON> (SE) nummer. Du har indtastet '{{PropertyValue}}'.", "NotEmptyValidator": "'{{<PERSON><PERSON><PERSON>}}' må ikke være tom.", "LengthValidator": "'{{<PERSON>N<PERSON>}}' skal være mellem '{{<PERSON>}}' og '{{<PERSON>}}' tegn. Du har indtastet '{{TotalLength}}' tegn.", "EmailValidator": "'{{<PERSON><PERSON><PERSON>}}' er ikke en gyldig e-mailadresse.", "PhoneValidator": "", "WorkOrderValidationFailed": "Arbejdsordre baseret på Proces ID: '{{WorkOrderProcessId}}' kunne ikke oprettes.", "WorkOrderCreationProblem": "Kunne ikke oprette arbejdsordre på grund af teknisk fejl", "CarNotFound": "{{[propertyPath]PropertyPath}} ikke fundet i det centrale adresseregister (CAR). {{IdName}}: '{{Id}}', Forsyningsart: '{{[enums.supplyType]SupplyType}}'.", "CarInvalidSupplyType": "{{[propertyPath]PropertyPath}} findes i det centrale adresseregister (CAR), men den har ikke den korrekte forsyningsart. {{IdName}}: '{{Id}}', Forsyningsart: '{{[enums.supplyType]SupplyType}}'.", "CarHasNoDetails": "{{[propertyPath]PropertyPath}} i det centrale adresseregister (CAR) har ingen detaljer. {{IdName}}: '{{Id}}', Forsyningsart: '{{[enums.supplyType]SupplyType}}'.", "CarNoDarIdAssigned": "{{[propertyPath]PropertyPath}} i det centrale adresseregister (CAR) mangler obligatorisk DarId. {{IdName}}: '{{Id}}', Forsyningsart: '{{[enums.supplyType]SupplyType}}'.", "CarInvalidDarStatus": "{{[propertyPath]PropertyPath}} i det centrale adresseregister (CAR) skal have DAR status sat til 'Ja'. {{IdName}}: '{{Id}}', Forsyningsart: '{{[enums.supplyType]SupplyType}}'.", "CarError": "Fejl under hentning af {{[propertyPath]PropertyPath}} fra det centrale adresseregister (CAR). Statuskode: '{{StatusCode}}'. {{IdName}}: '{{Id}}', Forsyningsart: '{{[enums.supplyType]SupplyType}}'.", "CarNotActive": "{{[propertyPath]PropertyPath}} findes i det centrale adresseregister (CAR), men er ikke aktiv. {{IdName}}: '{{Id}}', Forsyningsart: '{{[enums.supplyType]SupplyType}}'.", "CarMultipleMatching": "Flere aktive matches for {{[propertyPath]PropertyPath}} fundet i det centrale adresseregister (CAR). {{IdName}}: '{{Id}}', Forsyningsart: '{{[enums.supplyType]SupplyType}}'.", "ConnectionPointNotFound": "Forbindelsespunkt ikke fundet.", "ConnectionPointInvalidSupplyType": "Forbindelsespunktet findes i Forbindelsespunktservicen, men det har ikke den korrekte forsyningsart. {{IdName}}: '{{Id}}', forsyningsart: '{{[enums.supplyType]SupplyType}}'.", "ConnectionPointNotActive": "Forbindelsespunktet findes i Forbindelsespunktservicen, men adressen er ikke aktiv. {{IdName}}: '{{Id}}', forsyningsart: '{{[enums.supplyType]SupplyType}}'.", "ConnectionPointMultipleMatching": "Søgningen resulterede i mere end ét forbindelsespunkt. {{IdName}}: '{{Id}}', forsyningsart: '{{[enums.supplyType]SupplyType}}', Forbindelsespunktnummer: '{{ConnectionPointNumber}}'.", "ConnectionPointError": "Der opstod en fejl ved hentning af {{[propertyPath]PropertyPath}} fra forbindelsespunktsservicen. Statuskode '{{StatusCode}}'. {{IdName}}: '{{Id}}', forsyningsart: '{{[enums.supplyType]SupplyType}}'.", "MeterFrameNotFound": "{{[propertyPath]PropertyPath}} ikke fundet. {{IdName}}: '{{Id}}', Forsyningsart: '{{[enums.supplyType]SupplyType}}'.", "MeterFrameInvalidSupplyType": "{{[propertyPath]PropertyPath}} findes, men ikke med den korrekte forsyningsart. ID: '{{Id}}', målerrammenummer: '{{MeterFrameNumber}}', forsyningsart: '{{[enums.supplyType]SupplyType}}'.", "MeterFrameMeterMissing": "<PERSON><PERSON><PERSON> mangler for målerramme med id '{{Id}}' og målerrammenummer '{{MeterFrameNumber}}'.", "MeteringPointMultipleMatching": "Flere matchende målepunkter.", "MeteringPointNotFound": "Målepunkt ikke fundet.", "MeteringPointError": "Der opstod en fejl ved adgang til målepunktsservicen. Statuskode '{{StatusCode}}'.", "InvoicingError": "Der opstod en fejl under oprettelse af fakturaen.", "AddressDetailsValidator": "{{[propertyPath]PropertyPath}} mangler adressedetaljer. DAR ID: '{{DarID}}'.", "MasterDataByRowIdNotFound": "Installationsblanket med Id '{{FormId}}' er ikke relateret til stamdataproces med række-Id '{{MasterDataProcessRowId}}'.", "MasterDataValidationFailed": "Stamdataproces baseret på række-Id '{{MasterDataProcessRowId}}' fejlede i forbindelse med oprettelse. Stamdatafejl: '{{MasterDataErrors}}'.", "MasterDataCreationProblem": "Kunne ikke oprette stamdata grundet en teknisk fejl.", "AttachmentFromFormSystemHasInvalidFileName": "Vedhæftning fra installatør opfylder ikke krav til filnavn: '{{FileName}}'.", "AttachmentFromFormSystemHasNoFileName": "Vedhæftning fra installatør mangler filnavn.", "AttachmentFromFormSystemExceedsSizeLimit": "<PERSON>edhæftning fra installatør, med filnavnet '{{FileName}}', overskrider den tilladte filstørrelse.", "AttachmentFromFormSystemWithoutFileNameExceedsSizeLimit": "Vedhæftning fra installatør overskrider den tilladte filstørrelse.", "AttachmentFromFormSystemAlreadyExists": "<PERSON>edh<PERSON><PERSON>ning fra installatør, med filnavnet '{{FileName}}', eksisterer allerede.", "AttachmentFromFormSystemWithoutFileNameAlreadyExists": "Vedhæftning fra installatør eksisterer allerede.", "AttachmentSyncFailedAccessForbidden": "Håndtering af vedhæftninger til installationsblanketter mangler korrekt opsatte rettigheder.", "AttachmentSyncFailedAttachmentTypeNotEnabled": "Håndtering af vedhæftninger til installationsblanketter er ikke aktiveret.", "AttachmentFromFormSystemFailed": "Tilføjelse af vedhæftning med filnavnet '{{FileName}}' fra installatøren fejlede. Årsag: '{{Message}}'", "AttachmentFromFormSystemWithoutFileNameFailed": "Tilføjelse af vedhæftning fra installatøren fejlede. Årsag: '{{Message}}'", "AttachmentFromElementsFailed": "Tilføjelse af vedhæftning med filnavnet '{{FileName}}' fra sagsbehandleren fejlede. Årsag: '{{Message}}'", "AttachmentFromElementsWithoutFileNameFailed": "Tilføjelse af vedhæftning fra sagsbehandleren fejlede. Årsag: '{{Message}}'", "FormSystemValidationFailed": "Validering fejlede i {{FormSystemName}}. Årsag: '{{Message}}'", "FormSystemError": "Fejl i kommunikation med {{FormSystemName}}. Årsag: '{{Message}}'", "FlagMeterSetupCannotBeUnsetByDso": "Flaget '<PERSON><PERSON>ler installeret' kan ikke deaktiveres af sagsbehander/DSO", "FlagReadyForMeterCannotBeUnsetByDso": "Flaget '<PERSON><PERSON> til måler' kan ikke deaktiveres af sagsbehander/DSO", "MeterInstalledFlagFailedToUpdateInFormSystem": "Opdatering af flaget '<PERSON><PERSON>ler installeret' fejlede i {{FormSystemName}}", "SupplierSelectedFlagFailedToUpdateInFormSystem": "Opdatering af flaget '<PERSON><PERSON><PERSON><PERSON><PERSON> valgt' fejlede i {{FormSystemName}}", "ConnectionFeePaidFlagFailedToUpdateInFormSystem": "Opdatering af flaget 'Tilslutningsbidrag betalt' fejlede i {{FormSystemName}}", "ReadyForMeterFlagFailedToUpdateInFormSystem": "Opdatering af flaget 'Klar til måler' fejlede i {{FormSystemName}}", "SubmittedForProjectPlanningFlagFailedToUpdateInFormSystem": "Opdatering af flaget 'Sendt til projektering' fejlede i {{FormSystemName}}", "FormDataUpdateFailedInFormSystemDuringArchive": "Blanketdata blev ikke opdateret i {{FormSystemName}} inden arkivering", "FormInstructionDataUpdateFailedInFormSystemDuringArchive": "Anvisningsdata blev ikke opdateret i {{FormSystemName}} inden arkivering", "ChatMessageSyncFailed": "Chatbeskeden blev ikke leveret til {{FormSystemName}}. Årsag: '{{Message}}'", "ValueListItemMappingFromEltilmeldingCodeFailed": "Kunne ikke finde en matchende værdi i værdilisten '{{ValueListName}}', for Eltilmeldingskoden: '{{ExternalCode}}'.", "ValueListItemMappingToEltilmeldingCodeFailed": "Kunne ikke finde matchende Eltilmeldingkode for værdien '{{ValueListItemDisplayValue}}' i værdilisten '{{ValueListName}}'", "CommunicationTemplatesNoTemplateFound": "Der blev ikke fundet en kommunikationsskabelon.", "CommunicationTemplatesMoreThanOneTemplateFound": "Der blev fundet mere end en kommunikationsskabelon.", "RangeValidator": "'{{PropertyName}}' skal være i området {{<PERSON>}} og {{Max}}, men {{ActualValue}} blev angivet.", "CarEmptyAddress": "{{[propertyPath]PropertyPath}} mangler obligatoriske adressedetaljer. {{IdName}}: '{{Id}}', Forsyningsart: '{{[enums.supplyType]SupplyType}}'.", "MarketParticipantsError": "Der opstod en fejl ved adgang til markedsdeltagereservicen. Statuskode '{{StatusCode}}'.", "MarketParticipantsNoGridAreaFound": "Intet gitterområde med id '{{GridAreaId}}' fundet.", "MarketParticipantsMoreThanOneGridAreaFound": "Mere end ét gitterområde med id '{{GridAreaId}}' fundet.", "MarketParticipantsCouldNotSetDefaultGridArea": "Kunne ikke indstille standardnetområde. Service returnerede {{GridAreasCount}} netområder. Vælg et gyldigt netområde.", "ApplicationsMultipleWithSameType": "Anvendelser indeholder {{ApplicationTypeCount}} rækker af typen '{{[enums.applicationType]ApplicationTypeName}}', der må max være en af hver.", "MeterTransformerIdSetToOther": "Feltet Målertransformer skal udfyldes med en anden værdi fra værdilisten end \"Andet\".", "FlagDoesNotExist": "Flag eksisterer ikke.", "MeterFrameMultipleMatching": "Sø<PERSON>ngen resulterede i mere end en {{[propertyPath]PropertyPath}}. {{IdName}}: '{{Id}}', Forsyningsart: '{{[enums.supplyType]SupplyType}}'.", "MDPConnectionPointTemplateError": "Der opstod en fejl under indlæsning af skabelon til stamdataproces. SkabelonID: '{{Id}}'", "MDPConnectionPointTemplateNotFound": "Den valgte stamdataprocesskabelon findes ikke. SkabelonID: '{{Id}}'", "MeterFrameNotFoundByConnectionPointAndMeterNumber": "{{[propertyPath]PropertyPath}} ikke fundet. Forbindelsespunktsnummer: '{{ConnectionPointNumber}}', Målernummer: '{{MeterNumber}}', Forsyningsart: '{{[enums.supplyType]SupplyType}}'.", "ConnectionPointErrorSearchMeterFrame": "Der opstod en fejl ved hentning af {{[propertyPath]PropertyPath}} fra forbindelsespunktsservicen. Statuskode '{{StatusCode}}'. Forbindelsespunktsnummer: '{{ConnectionPointNumber}}', Målernummer: '{{MeterNumber}}', Forsyningsart: '{{[enums.supplyType]SupplyType}}'.", "MeterFrameMultipleMatchingByConnectionPointAndMeterNumber": "Søgningen resulterede i mere end en {{[propertyPath]PropertyPath}}. Forbindelsespunktsnummer: '{{ConnectionPointNumber}}', Målernummer: '{{MeterNumber}}'.", "ConnectionRightsReadError": "Der opstod en fejl ved læsning af tilslutningsrettigheder.", "InvoiceCreationFailedNoConnectionRightsToInvoice": "Der er ingen ufakturerende tilslutningsrettigheder. Der er ikke oprettet en faktura.", "InvoiceCreationFailedPricesDefinitionFetchError": "Der opstod en fejl ved oprettelse af faktura. Priser kunne ikke hentes.", "InvoiceCreationFailedPriceElementForConnectionRightsCategoryIsMissing": "Priselement for tilslutningsrettighedskategorien {{Category}} mangler. Der er ikke oprettet faktura.", "InvoiceCreationFailedPriceElementForConnectionRightsCategoryIsDuplicated": "Priselement for tilslutningsrettighedskategorien {{Category}} er duplikeret. Der er ikke oprettet faktura.", "InvoiceCreationFailedSingleInvoicingCreateInvoiceError": "Der opstod en fejl ved oprettelse af faktura. Kunne ikke oprette faktura.", "FormShouldNotHaveMeterNumberUponReceival": "Skemaet må ikke indeholde målernummer ved tilmelding.", "MeterNeedsReconfiguration": "Måleren skal omkonfigureres.", "MeterNeedsChange": "Målerbehov ændres.", "ConnectionFeeUpdateErrorInvalidInvoiceCurrency": "Fejl ved opdatering af tilslutningsbidrag under anvisningsinformation. Forkert valuta for relateret faktura: {{InvoiceIds}}. Forsyningsart: '{{[enums.supplyType]SupplyType}}'.", "ManuallySetConnectionFeeDiffersFromInvoiceTotals": "Manuelt indtastet tilslutningsbidrag, under anvisningsinformation, er forskellig fra den totale sum af faktura. Forsyningsart: '{{[enums.supplyType]SupplyType}}', Beregnet tilslutningsbidrag: {{CalculatedConnectionFee}}, Manualt indtastet tilslutningsbidrag: {{PreviouslyManuallySetConnectionFee}}.", "ConnectionFeeUpdatedAfterInstruction": "Tilslutningsbidrag blev opdateret efter anvisning. Forsyningsart: '{{[enums.supplyType]SupplyType}}', Beregnet tilslutningsbidrag: {{CalculatedConnectionFee}}, Tilslutningsbidrag under anvisningsinformation: {{PreviouslySetConnectionFee}}.", "ConnectionPointSetButItWasNotExpected": "Der er et eksisterende tilslutningspunkt. Automatisk behandling standses indtil manuel verifikation af en formular.", "SharedBranchLineMeterFrameMissingOwnedMainBranchLine": "Den målerramme der er valgt til delt stikledning skal have en hovedstikledning af typen 'egen'. Målerramme-id: '{{MeterFrameId}}', Målerrammenummer: '{{MeterFrameNumber}}', Forsyningsart: '{{[enums.supplyType]SupplyType}}'.", "SharedBranchLineNestingCorrected": "Målerrammen for delt stikledning blev ændret for benytte hovedstikledning af typen 'egen'. '{{ConnectedMeterFrameNumber}}' målerrammens stikledning vil blive benyttet i stedet for den forespurgte målerramme '{{MeterFrameNumber}}'. Målerramme-id: '{{MeterFrameId}}', Målerrammenummer: '{{MeterFrameNumber}}', Forbundet målerramme-id: '{{ConnectedMeterFrameId}}', Forbundet målerrammenummer: '{{ConnectedMeterFrameNumber}}', Forsyningsart: '{{[enums.supplyType]SupplyType}}'.", "SharedBranchLineInfo": "Stikledningstype er delt, så ændringer vil ikke automatisk blive sendt til hovedstikledningen og skal håndteres manuelt.", "AddressEmpty": "{{[propertyPath]PropertyPath}} har hverken DAR Id eller CAR Id tilknyttet.", "MeterFrameInvalidElectricityPurpose": "Elmålerrammens formål er ikke gyldigt. Forventet værdi er '<PERSON><PERSON>ling'.", "NoConnectionPointFoundOnTheAddress": "Der blev ikke fundet et forbindelsespunkt på adressen, vælg manuelt det korrekte forbindelsespunkt. Adressen: '{{Address}}', CAR ID: '{{CarId}}', DAR ID: '{{DarId}}', Forsyningsart: '{{[enums.supplyType]SupplyType}}'.", "MeterNumberDoesNotMatchConnectionPoint": "Målernummeret '{{MeterNumber}}' matcher ikke det forbindelsespunkt der er fundet via adressen. Adressen: '{{Address}}', Målernummeret: '{{MeterNumber}}', Forsyningsart: '{{[enums.supplyType]SupplyType}}'.", "MeteringPointIdDoesNotMatchConnectionPoint": "Aftagernummeret '{{MeteringPointId}}' matcher ikke det forbindelsespunkt der er fundet via adressen '{{Address}}'. Adressen: '{{Address}}', Aftagernummeret: '{{MeteringPointId}}', Forsyningsart: '{{[enums.supplyType]SupplyType}}'.", "ConnectionPointNumberDoesNotMatchConnectionPoint": "Forbindelsespunktsnummeret '{{ConnectionPointNumber}}' matcher ikke det forbindelsespunkt der er fundet via adressen '{{Address}}'. Adressen: '{{Address}}', Forbindelsespunktsnummeret: '{{ConnectionPointNumber}}', Forsyningsart: '{{[enums.supplyType]SupplyType}}'.", "NotPossibleToMatchUniqueConnectionPoint": "Det var ikke muligt at finde et unikt forbindelsespunkt baseret på adressen '{{Address}}', målernummeret '{{MeterNumber}}', aftagernummeret '{{MeteringPointId}}' eller forbindelsespunktsnummeret '{{ConnectionPointNumber}}'. Adressen: '{{Address}}', Målernummeret: '{{MeterNumber}}', Aftagernummeret: '{{MeteringPointId}}', Forbindelsespunktsnummer: '{{ConnectionPointNumber}}', Forsyningsart: '{{[enums.supplyType]SupplyType}}'.", "MeterNumberWasNotProvidedByFormSystemButRequired": "Målernummeret blev ikke angivet af blanketsystemet, men det er påkrævet. Forsyningsart: '{{[enums.supplyType]SupplyType}}'.", "MeterFramesError": "Der opstod en fejl ved adgang til målerrammeservicen. Statuskode '{{StatusCode}}'.", "MeterFramesNotFound": "Målerrammer blev ikke fundet: {{Ids}}.", "MeterRemovalNotStarted": "Arbejdsordre skal startes manuelt for at nedtage måler i målerramme med ID: '{{MeterFrameNumber}}'.", "ScopeOfDeliveryMissingError": "Leveringsomfang mangler i formularen. Opgaven om oprettelse af forbindelsesrettigheder kan ikke fuldføres.", "MatchingConnectionPointWithNewlyCreatedElectricityConnectionStatusExistsInfo": "Matchende tilslutningspunkt med nyoprettet elforbindelsesstatus for den tilsvarende adresse findes allerede. Bekræft det, og vælg, hvis det er korrekt.", "ConnectionRightsBelowScopeOfDeliveryAfterConnectionFeePaidFlagSet": "Effektgrænsen på målerrammen er blevet ændret og er nu lavere end leveringsomfanget på blanketten, men flaget for 'Tilslutningsbidrag betalt' er allerede blevet sat.", "InvoiceCreationFailedPriceElementForStandardFeeIsMissing": "Den valgte pris '{{InvoicingPriceName}}' har ikke en version for dato ({{PriceDate}}). Faktura ikke oprettet.", "InvoicingPriceDoesNotHavePriceDefinition": "Den valgte pris '{{InvoicingPriceName}}' peger ikke på en eksisterende prisdefinition. Faktura ikke oprettet."}, "formsSettings": {"invoice": {"tabLabel": "<PERSON><PERSON><PERSON><PERSON>", "tabAreaLabel": "Fakturering", "displayName": "Navn", "formTypes": "<PERSON><PERSON><PERSON><PERSON>", "voltageLevels": "Spændingsniveauer", "scopeOfDelivery": "Leveringsomfang", "scopeOfDeliveryRange": "Leveringsomfang", "scopeOfDeliveryMin": "Leveringsomfang min.", "scopeOfDeliveryMax": "Leveringsomfang max.", "canTransferInvoiceAutomatically": "Overfør faktura automatisk", "getInvoiceRulesError": "Der opstod en fejl ved indlæsning af reglerne for automatisk fakturering.", "createRule": "<PERSON><PERSON>gel", "invoiceRuleCreationError": "Der opstod en fejl under oprettelse af regel til automatisk fakturering.", "invoiceRuleWarningMinGreaterThanMax": "Leveringsomfang min. skal være mindre end leveringsomfang max.", "invoiceRuleEditError": "Der opstod en fejl ved redigering af regel til automatisk fakturering.", "invoiceRuleDeleteError": "Der opstod en fejl ved sletning af regel til automatisk fakturering.", "invoiceRuleReorderError": "Der opstod en fejl under æ<PERSON><PERSON> af rækkefølgen af regler for automatisk fakturering.", "deleteConfirmationTitle": "<PERSON><PERSON> regel", "deleteConfirmationMessage": "Er du sikker på, at du vil slette denne regel til automatisk fakturering?"}, "automaticEmails": {"tabLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabAreaLabel": "E-mails", "displayName": "Navn", "formTypes": "<PERSON><PERSON><PERSON><PERSON>", "formStates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "formCategories": "Blanketkategorier", "emailTemplateName": "E-mailskabelon navn", "createRule": "<PERSON><PERSON>gel", "deleteConfirmationMessage": "Er du sikker på, at du vil slette denne regel for automatiske e-mails?", "deleteConfirmationTitle": "<PERSON><PERSON> regel", "automaticEmailsRuleCreationError": "Der opstod en fejl under oprettelse af regel for automatiske e-mails.", "automaticEmailsRuleEditError": "Der opstod en fejl under redigering af regel for automatiske e-mails.", "automaticEmailsRuleDeleteError": "Der opstod en fejl under sletning af regel for automatiske e-mails.", "automaticEmailsRuleReorderError": "Der opstod en fejl under æ<PERSON><PERSON> af rækkefølgen af regler for automatiske e-mails."}, "masterData": {"tabLabel": "<PERSON><PERSON><PERSON><PERSON>", "tabAreaLabel": "Stamdata", "displayName": "Navn", "formTypes": "<PERSON><PERSON><PERSON><PERSON>", "formStates": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "voltageLevels": "Spændingsniveauer", "scopeOfDelivery": "Leveringsomfang", "scopeOfDeliveryMin": "Leveringsomfang min.", "scopeOfDeliveryMax": "Leveringsomfang max.", "scopeOfDeliveryUom": "Leveringsomfang enhed", "totalCapacity": "Total kapacitet", "totalCapacityMin": "Total kapacitet min. [kW]", "totalCapacityMax": "Total kapacitet max. [kW]", "startsAsConstruction": "Starter som bygg<PERSON><PERSON><PERSON><PERSON>", "terminationScope": "Afmeldingsomfang", "masterDataProcessType": "Procestype", "masterDataProcessTemplate": "Processkabelon", "masterDataProcessAutomationLevel": "Automatiseringsniveau", "masterDataProcessWorkOrderAutomationLevel": "Automatiseringsniveau for arbejdsordre", "getMasterDataRulesError": "Der opstod en fejl ved indlæsning af reglerne for stamdata.", "createRule": "<PERSON><PERSON>gel", "masterDataRuleCreationError": "Der opstod en fejl under oprettelse af regel til stamdata.", "masterDataRuleEditError": "Der opstod en fejl ved redigering af regel til stamdata.", "masterDataRuleDeleteError": "Der opstod en fejl ved sletning af regel til stamdata.", "masterDataRuleReorderError": "Der opstod en fejl under ænd<PERSON> af rækkefølgen af regler for stamdata processer.", "scopeOfDeliveryMinMaxEmptyWarning": "<PERSON><PERSON><PERSON> enhed for leveringsomfang eller sæt min/max værdier", "scopeOfDeliveryMinGreaterThanMaxWarning": "Leveringsomfang min. skal være mindre end leveringsomfang max.", "totalCapacityMinGreaterThanMaxWarning": "Total kapacitet min. skal være mindre end total kapacitet max.", "deleteConfirmationTitle": "Slet stamdata regel", "deleteConfirmationMessage": "<PERSON>r du sikker på, at du vil slette denne stamdata regel?", "workOrderType": "Arbejdsordretype", "workOrderDescriptionId": "Arbejdsbeskrivelse", "workOrderPurposeId": "<PERSON><PERSON><PERSON>"}, "workOrders": {"tabLabel": "<PERSON><PERSON><PERSON><PERSON>", "tabAreaLabel": "Arbejdsordre", "displayName": "Navn", "formTypes": "<PERSON><PERSON><PERSON><PERSON>", "formStates": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sealResponsibles": "<PERSON><PERSON><PERSON><PERSON><PERSON> for genplombering", "meterResponsibles": "<PERSON><PERSON><PERSON><PERSON><PERSON> for målerlevering", "meterNeedsChange": "<PERSON><PERSON><PERSON> skal skiftes", "startsAsConstruction": "Starter som bygg<PERSON><PERSON><PERSON><PERSON>", "invoicePaid": "Faktura betalt", "readyForMeter": "<PERSON><PERSON><PERSON> klar", "supplierSelected": "Lev<PERSON><PERSON>ø<PERSON> valgt", "workOrderType": "Arbejdsordretype", "workOrderDescriptionId": "Arbejdsbeskrivelse", "workOrderPurposeId": "<PERSON><PERSON><PERSON>", "getWorkOrderRulesError": "Der opstod en fejl ved indlæsning af reglerne for arbejdsordre.", "createRule": "<PERSON><PERSON>gel", "workOrderRuleCreationError": "Der opstod en fejl under oprettelse af regel til arbejdsordre.", "workOrderRuleEditError": "Der opstod en fejl ved redigering af regel til arbejdsordre.", "workOrderRuleDeleteError": "Der opstod en fejl ved sletning af regel til arbejdsordre.", "workOrderRuleReorderError": "Der opstod en fejl under æ<PERSON><PERSON> af rækkefølgen af regler for arbejdsordre.", "deleteConfirmationTitle": "<PERSON><PERSON> regel for arbejdsordre", "deleteConfirmationMessage": "Er du sikker på, at du vil slette denne regel for arbejdsordre?"}, "automaticScreening": {"tabLabel": "<PERSON><PERSON><PERSON><PERSON>", "tabAreaLabel": "Tillad automatisering", "displayName": "Navn", "formTypes": "<PERSON><PERSON><PERSON><PERSON>", "formStates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "formCategories": "Blanketkategorier", "voltageLevels": "Spændingsniveauer", "scopeOfDelivery": "Leveringsomfang", "scopeOfDeliveryMin": "Leveringsomfang min.", "scopeOfDeliveryMax": "Leveringsomfang max.", "scopeOfDeliveryUom": "Leveringsomfang enhed", "terminationScope": "Afmeldingsomfang", "meterReturnOptionTypes": "Målerreturneringsvalg", "meterPlacementCodes": "Målerplaceringskoder", "meterSize": "M<PERSON>lerstø<PERSON><PERSON>", "groundingMethods": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reasonsForChange": "Årsag til ændring", "getAutomaticScreeningRulesError": "Der opstod en fejl ved indlæsning af reglerne for automatisk godkendelse af automatisering.", "createRule": "<PERSON><PERSON>gel", "automaticScreeningRuleCreationError": "Der opstod en fejl under oprettelse af regel til automatisk godkendelse af automatisering.", "automaticScreeningRuleEditError": "Der opstod en fejl ved redigering af regel til automatisk godkendelse af automatisering.", "automaticScreeningRuleDeleteError": "Der opstod en fejl ved sletning af regel til automatisk godkendelse af automatisering.", "automaticScreeningRuleReorderError": "Der opstod en fejl under æ<PERSON><PERSON> af rækkefølgen af regler for automatisk godkendelse af automatisering.", "scopeOfDeliveryMinMaxEmptyWarning": "<PERSON><PERSON><PERSON> leveringsomfang enhed eller sæt min/max værdier", "scopeOfDeliveryMinGreaterThanMaxWarning": "Leveringsomfang min. skal være mindre end leveringsomfang max.", "totalCapacityMinGreaterThanMaxWarning": "Total kapacitet min. skal være mindre end total kapacitet max.", "deleteConfirmationTitle": "<PERSON><PERSON> regel", "deleteConfirmationMessage": "Er du sikker på, at du vil slette denne regel til automatisk godkendelse af automatisering?"}, "defaultInstructionTexts": {"tabLabel": "<PERSON><PERSON><PERSON><PERSON>", "tabAreaLabel": "Standard anvisningstekster", "text": "Tekst", "getInstructionTextsError": "Der opstod en fejl under hentning af standardanvisningstekster.", "addText": "Tilføj tekst", "instructionTextCreationError": "Der opstod en fejl under oprettelse af standardanvisningstekst.", "instructionTextEditError": "Der opstod en fejl under redigering af standardanvisningstekst.", "instructionTextDeleteError": "Der opstod en fejl under sletning af standardanvisningstekst.", "instructionTextReorderError": "Der opstod en fejl under ændring af rækkefølgen af standardinstruktionstekster.", "deleteConfirmationTitle": "Slet tekst", "deleteConfirmationMessage": "Er du sikker på, at du vil slette denne standardanvisningstekst?"}, "automaticArchiving": {"tabLabel": "<PERSON><PERSON><PERSON><PERSON>", "tabAreaLabel": "Arkivering", "displayName": "Navn", "formTypes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "formStates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "responsibleForMeter": "<PERSON><PERSON><PERSON><PERSON><PERSON> for målerlevering", "meterInstalled": "M<PERSON>ler installeret", "startsAsConstruction": "Starter som bygg<PERSON><PERSON><PERSON><PERSON>", "verifiedWorkOrderType": "Verificeret arbejdsordretype", "verifiedWorkOrderDescription": "Verificeret arbejdsordrebeskrivelse", "masterDataProcessTypes": "Stamdata process types", "getAutomaticArchivingRulesError": "Der opstod en fejl ved indlæsning af reglerne for automatisk godkendelse af arkivering.", "createRule": "<PERSON><PERSON>gel", "automaticArchivingRuleCreationError": "Der opstod en fejl under oprettelse af regel til automatisk godkendelse af arkivering.", "automaticArchivingRuleEditError": "Der opstod en fejl ved redigering af regel til automatisk godkendelse af arkivering.", "automaticArchivingRuleDeleteError": "Der opstod en fejl ved sletning af regel til automatisk godkendelse af arkivering.", "automaticArchivingRuleReorderError": "Der opstod en fejl under æ<PERSON><PERSON> af rækkefølgen af regler for automatisk godkendelse af arkivering.", "verifiedWorkOrderDescriptionRequiredWarning": "Verificeret arbejdsorderbeskrivelse er påkrævet værdi, hvis arbejdsordretypen blev valgt.", "deleteConfirmationTitle": "<PERSON><PERSON> regel", "deleteConfirmationMessage": "Er du sikker på, at du vil slette denne regel til automatisk godkendelse af automatisering?"}, "automaticInstructionTexts": {"tabLabel": "<PERSON><PERSON><PERSON><PERSON>", "tabAreaLabel": "Anvisningstekster", "displayName": "Navn", "formTypes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "formCategories": "Blanketkategorier", "meterNeedsChange": "Målerbehov ænd<PERSON>", "meterNeedsReconfiguration": "Måleren skal omkonfigureres", "formIsScreened": "Blanket tillader automatisering", "defaultInstructionTexts": "Standard anvisningstekster", "getAutomaticArchivingRulesError": "Der opstod en fejl ved indlæsning af reglerne for automatisk godkendelse af anvisningstekst.", "createRule": "<PERSON><PERSON>gel", "automaticInstructionTextRuleCreationError": "Der opstod en fejl under oprettelse af regel til automatisk godkendelse af anvisningstekst.", "automaticInstructionTextRuleEditError": "Der opstod en fejl ved redigering af regel til automatisk godkendelse af anvisningstekst.", "automaticInstructionTextRuleDeleteError": "Der opstod en fejl ved sletning af regel til automatisk godkendelse af anvisningstekst.", "automaticInstructionTextRuleReorderError": "Der opstod en fejl ved genbestilling af regler for automatisk godkendelse af anvisningstekst.", "deleteConfirmationTitle": "<PERSON><PERSON> regel", "deleteConfirmationMessage": "Er du sikker på, at du vil slette denne regel til automatisk godkendelse af automatisering?"}, "priceDefinitions": {"tabLabel": "<PERSON><PERSON><PERSON><PERSON>", "tabAreaLabel": "<PERSON><PERSON><PERSON><PERSON>", "addPriceDefinition": "<PERSON><PERSON><PERSON><PERSON><PERSON> prise<PERSON>", "displayName": "Navn", "displayNameNotUnique": "Navn skal være unikt inden for forsyningsart.", "externalPriceName": "Ekstern prisnavn", "supplyType": "Forsyningsart", "addError": "Der opstod en fejl ved oprettelse af prisdefinition.", "updateError": "Der opstod en fejl ved opdatering af prisdefinition.", "deleteConfirmationTitle": "Slet priselement", "deleteConfirmationMessage": "<PERSON>r du sikker på, at du vil slette denne priselement?", "deleteError": "Der opstod en fejl ved sletning af prisdefinition.", "loadError": "Der opstod en fejl ved indlæsning af prisdefinitioner.", "validation": {"requiredFields": "Alle felter er påkrævet."}, "externalPriceMissing": "P<PERSON><PERSON><PERSON> findes ikke."}, "internalResource": {"tabLabel": "<PERSON><PERSON><PERSON><PERSON>", "tabAreaLabel": "Intern ressource", "defaultSenderAddress": "<PERSON><PERSON><PERSON>", "defaultReceiverAddress": "Modtager", "defaultSubject": "<PERSON><PERSON>", "template": "Skabelon", "getError": "Der opstod en fejl ved indlæsning af indstillinger for intern ressourc.", "updateError": "Der opstod en fejl ved opdatering af indstillinger for intern ressourc.", "noPermissionToReadEmailTemplates": "Du har ikke tilladelse til at læse e-mailskabeloner."}}, "propertyPath": {"Address": "Installationsadresse", "BranchLine": {"MeterFrame": "Delt stikledningsmålerramme"}, "ConnectionAddress": {"Address": "Tilslutningsadresse"}, "MeterDelivery": {"DeliveryInformation": {"Address": "Modtageradresse"}}, "MeterFrame": "Målerramme", "MeterReturn": {"Information": {"Address": "Lageradresse"}}, "Owner": {"Address": "<PERSON><PERSON><PERSON> adress<PERSON>"}, "Payer": {"Address": "Betaleradresse"}}, "masterDataCompare": {"path": {"MeterFrame.PlacementCode": "Målerramme -> Placeringskode", "MeterFrame.ElectricityAttributes.ConnectionType": "Målerramme -> Egenskaber electricitet -> Tilslutningstype", "MeterFrame.ElectricityAttributes.RatioCt": "Målerramme -> Egenskaber electricitet -> Omsætningsforhold CT", "MeterFrame.ElectricityAttributes.MeterFrameFuse": "Målerramme -> Egenskaber electricitet -> Målerramme forsikring", "MeterFrame.GisPropertiesElectricity.BranchLineFuseType": "Målerramme -> GIS egenskaber -> Stikledningssikringstype", "MeterFrame.GisPropertiesElectricity.BranchLineFuseAmps": "Målerramme -> GIS egenskaber -> Stikledningssikring Ampere", "MeterFrame.MainBranchLineElectricity.NumberOfCables": "Målerramme -> Stikledninger -> Hovedstikledning -> Antal <PERSON>le kabler", "MeterFrame.GisPropertiesElectricity.CabinetNumber": "Målerramme -> GIS egenskaber -> Kabelskabsnummer", "MeterFrame.GisPropertiesElectricity.StationNumber": "Målerramme -> GIS egenskaber -> Stationsnummer", "MeterFrame.ElectricityAttributes.TarifConnectionPoint": "Målerramme -> Egenskaber electricitet -> <PERSON><PERSON><PERSON> til<PERSON>ningspunkt", "MeterFrame.CommonReading": "Målerramme -> <PERSON><PERSON><PERSON> m<PERSON>", "MeterFrame.ElectricityAttributes.BreakerBeforeMeter": "Målerramme -> Egenskaber electricitet -> Afbryder før måler", "MeterFrame.MainBranchLineElectricity.BranchLineType": "Målerramme -> Stikledninger -> Hovedstikledning -> Stikledningstype", "MeterFrame.MainBranchLineElectricity.SystemGrounding": "Målerramme -> Stikledninger -> Hovedstikledning -> System jording", "ConnectionPoint.ElectricityAttributes.GridAreaId": "Forbindelsespunkt -> Egenskaber el -> Netområde", "ConnectionRight.Value": "Målerramme -> Tilslutningsrettigheder -> <PERSON><PERSON><PERSON>"}, "tooltipPrefix": "Stamdata sti: ", "toggleLabel": "Stamdata sammenligning", "getDataError": "Fejl ved indlæsning af stamdata til sammenligning"}}