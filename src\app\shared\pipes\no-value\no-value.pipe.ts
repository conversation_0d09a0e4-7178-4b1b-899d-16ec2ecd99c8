import { Pipe, PipeTransform } from '@angular/core';
import { NO_VALUE_STRING } from 'src/app/core/constants/constants';

@Pipe({
    name: 'noValue',
    standalone: false
})
export class NoValuePipe implements PipeTransform {
    transform(value: Date | number | string | null, ...args: unknown[]): string {
        if (value) {
            return value.toString();
        }

        return NO_VALUE_STRING;
    }
}
