import { Component, Input, OnInit } from '@angular/core';
import { AbstractControl, FormBuilder, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { finalize } from 'rxjs/operators';
import {
    AutomaticMasterDataProcessRule,
    AutomaticMasterDataProcessRuleCreateOrUpdate,
    AutomationVoltageLevel,
    ConnectionPointTemplateDetails,
    FormState,
    FormType,
    InstallationFormsClient,
    MasterDataProcessAutomationLevel,
    MasterDataProcessWorkOrderAutomationLevel,
    ScopeOfDeliveryRange,
    TerminationScope,
    TotalCapacityRange,
    TransferConnectionRightUom,
    ValueListType
} from 'src/app/api/installation-forms-client';
import { SETTINGS_DISPLAY_NAME_MAX_LENGTH } from 'src/app/core/constants/constants';
import { BooleanOptionsService } from 'src/app/core/services/boolean-options/boolean-options.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { ValueListsService } from 'src/app/core/services/value-lists/value-lists.service';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import conditionallyRequiredValidator from 'src/app/core/utils/validators/conditionally-required/conditionally-required.validator';
import { multipleWhitespaceValidator } from 'src/app/core/utils/validators/multiple-whitespace-validator/multiple-whitespace-validator';
import { v4 as uuidv4 } from 'uuid';
import { alreadyUsedDisplayNameValidator } from '../../../../core/utils/validators/already-used-display-name-validator/already-used-display-name-validator';
import { BaseRuleCreateComponent } from '../../base-components/base-rule-create.components';
import { masterDataTabName, masterDataTranslationPath } from '../constants/master-data.consts';
import { MasterDataFormService } from '../services/master-data-form.service';
import { MasterDataOptionsService } from '../services/master-data-options.service';

@Component({
    selector: 'app-master-data-create',
    templateUrl: './master-data-create.component.html',
    styleUrls: ['./master-data-create.component.scss'],
    standalone: false
})
export class MasterDataCreateComponent extends BaseRuleCreateComponent<AutomaticMasterDataProcessRule> implements OnInit {
    @Input() masterDataProcessTemplateOptions: ConnectionPointTemplateDetails[] = [];

    formTypeOptions: SelectItem[] = [];
    formStateOptions: SelectItem[] = [];
    voltageLevelOptions: SelectItem[] = [];
    masterDataProcessTypeOptions: SelectItem[] = [];
    masterDataProcessAutomationLevelOptions: SelectItem[] = [];
    masterDataProcessWorkOrderAutomationLevelOptions: SelectItem[] = [];
    terminationScopeOptions: SelectItem[] = [];
    scopeOfDeliveryUomOptions: SelectItem[] = [];
    workOrderTypeOptions: SelectItem[] = [];

    constructor(
        private readonly client: InstallationFormsClient,
        protected readonly fb: FormBuilder,
        protected readonly translateService: TranslateService,
        protected readonly messageServiceHelper: MessageServiceHelper,
        protected readonly booleanOptionsService: BooleanOptionsService,
        protected readonly valueListsService: ValueListsService,
        protected readonly masterDataOptionsService: MasterDataOptionsService,
        protected readonly masterDataFormService: MasterDataFormService
    ) {
        super(fb, translateService, messageServiceHelper, masterDataTranslationPath, masterDataTabName);
    }

    ngOnInit(): void {
        super.ngOnInit();
        this.addFormControlsValueChanges();
    }

    override initForm(): void {
        this.form = this.fb.group({
            displayName: [
                null,
                [
                    Validators.required,
                    Validators.maxLength(SETTINGS_DISPLAY_NAME_MAX_LENGTH),
                    alreadyUsedDisplayNameValidator(this.alreadyUsedDisplayNames),
                    multipleWhitespaceValidator()
                ]
            ],
            formTypes: [null, Validators.required],
            formStates: [null, Validators.required],
            voltageLevels: [null, Validators.required],
            totalCapacityMin: [null, [Validators.min(0), Validators.max(2147483647)]],
            totalCapacityMax: [null, [Validators.min(0), Validators.max(2147483647)]],
            scopeOfDeliveryMin: [null, [Validators.min(0), Validators.max(2147483647)]],
            scopeOfDeliveryMax: [null, [Validators.min(0), Validators.max(2147483647)]],
            scopeOfDeliveryUom: [
                null,
                [(control: AbstractControl) => conditionallyRequiredValidator(control, () => this.masterDataFormService.isScopeOfDeliveryUomRequired(this.form))]
            ],
            startsAsConstruction: [null],
            terminationScope: [null],
            masterDataProcessType: [null, Validators.required],
            masterDataProcessTemplateId: [
                null,
                [(control: AbstractControl) => conditionallyRequiredValidator(control, () => this.masterDataFormService.isProcessTemplateRequired(this.form))]
            ],
            masterDataProcessAutomationLevel: [null, Validators.required],
            masterDataProcessWorkOrderAutomationLevel: [null],
            workOrderType: [
                null,
                [(control: AbstractControl) => conditionallyRequiredValidator(control, () => this.masterDataFormService.isAnyWorkOrderTypeAvailable(this.workOrderTypeOptions))]
            ],
            workOrderDescriptionId: [
                null,
                [(control: AbstractControl) => conditionallyRequiredValidator(control, () => this.masterDataFormService.isAnyWorkOrderTypeAvailable(this.workOrderTypeOptions))]
            ],
            workOrderPurposeId: [
                null,
                [(control: AbstractControl) => conditionallyRequiredValidator(control, () => this.masterDataFormService.isAnyWorkOrderTypeAvailable(this.workOrderTypeOptions))]
            ]
        });
    }

    override getEnumTranslations(): void {
        this.formTypeOptions = enumMapper.map(this.translateService.instant('enums.type'), FormType);
        this.formStateOptions = enumMapper.map(this.translateService.instant('enums.state'), [
            FormState.Registered,
            FormState.Instructed,
            FormState.Completed
        ]);
        this.voltageLevelOptions = enumMapper.map(
            this.translateService.instant('enums.automationVoltageLevel'),
            AutomationVoltageLevel
        );
        this.masterDataProcessAutomationLevelOptions = enumMapper.map(
            this.translateService.instant('enums.masterDataProcessAutomationLevel'),
            MasterDataProcessAutomationLevel
        );
        this.masterDataProcessWorkOrderAutomationLevelOptions = enumMapper.map(
            this.translateService.instant('enums.masterDataProcessWorkOrderAutomationLevel'),
            MasterDataProcessWorkOrderAutomationLevel
        );
        this.terminationScopeOptions = enumMapper.map(this.translateService.instant('enums.terminationScope'), TerminationScope);
        this.scopeOfDeliveryUomOptions = enumMapper.map(
            this.translateService.instant('enums.transferConnectionRightUom'),
            TransferConnectionRightUom
        );
    }

    override createClick() {
        if (this.form.invalid) {
            return;
        }

        const formValue = this.form.getRawValue();

        let hasValidationErrors = false;

        if (
            formValue.totalCapacityMin &&
            formValue.totalCapacityMax &&
            formValue.totalCapacityMin >= formValue.totalCapacityMax
        ) {
            this.showWarningMessage(this.widgetTranslations['totalCapacityMinGreaterThanMaxWarning']);
            hasValidationErrors = true;
        }

        if (formValue.scopeOfDeliveryUom && formValue.scopeOfDeliveryMin === null && formValue.scopeOfDeliveryMax === null) {
            this.showWarningMessage(this.widgetTranslations['scopeOfDeliveryMinMaxEmptyWarning']);
            hasValidationErrors = true;
        }

        if (
            formValue.scopeOfDeliveryUom &&
            formValue.scopeOfDeliveryMin !== null &&
            formValue.scopeOfDeliveryMax !== null &&
            formValue.scopeOfDeliveryMin >= formValue.scopeOfDeliveryMax
        ) {
            this.showWarningMessage(this.widgetTranslations['scopeOfDeliveryMinGreaterThanMaxWarning']);
            hasValidationErrors = true;
        }

        if (hasValidationErrors) {
            return;
        }

        const rule: AutomaticMasterDataProcessRuleCreateOrUpdate = new AutomaticMasterDataProcessRuleCreateOrUpdate({
            displayName: formValue.displayName,
            formTypes: formValue.formTypes,
            formStates: formValue.formStates,
            scopeOfDeliveryRange: formValue.scopeOfDeliveryUom
                ? new ScopeOfDeliveryRange({
                      min: formValue.scopeOfDeliveryMin,
                      max: formValue.scopeOfDeliveryMax,
                      uom: formValue.scopeOfDeliveryUom
                  })
                : undefined,
            voltageLevels: formValue.voltageLevels ?? [],
            totalCapacityRange:
                formValue.totalCapacityMin || formValue.totalCapacityMax
                    ? new TotalCapacityRange({
                          min: formValue.totalCapacityMin,
                          max: formValue.totalCapacityMax
                      })
                    : undefined,
            startsAsConstruction: formValue.startsAsConstruction ?? null,
            terminationScope: formValue.terminationScope ?? null,
            masterDataProcessType: formValue.masterDataProcessType,
            masterDataProcessTemplateId: formValue.masterDataProcessTemplateId ?? null,
            masterDataProcessAutomationLevel: formValue.masterDataProcessAutomationLevel,
            masterDataProcessWorkOrderAutomationLevel: formValue.masterDataProcessWorkOrderAutomationLevel ?? null,
            workOrderType: formValue.workOrderType ?? null,
            workOrderDescriptionId: formValue.workOrderDescriptionId ?? null,
            workOrderPurposeId: formValue.workOrderPurposeId ?? null
        });

        this.isProcessing = true;
        this.subscription.add(
            this.client
                .createAutomaticMasterDataRule(uuidv4(), rule)
                .pipe(finalize(() => (this.isProcessing = false)))
                .subscribe({
                    next: (response) => {
                        this.ruleCreated.emit(response.result);
                        this.showSuccessMessage();
                    },
                    error: (_) => {
                        this.showErrorMessage(this.widgetTranslations['masterDataRuleCreationError']);
                    }
                })
        );
    }

    addFormControlsValueChanges() {
        this.subscription.add(
            this.form.get('masterDataProcessType')?.valueChanges.subscribe((_) => {
                const controlToReValidate = this.form.get('masterDataProcessTemplateId');
                if (controlToReValidate) {
                    controlToReValidate.updateValueAndValidity();
                }
            })
        );

        this.subscription.add(
            this.form.get('scopeOfDeliveryMin')?.valueChanges.subscribe((_) => {
                const uomControl = this.form.get('scopeOfDeliveryUom');
                if (uomControl) {
                    uomControl.updateValueAndValidity();
                }
            })
        );

        this.subscription.add(
            this.form.get('scopeOfDeliveryMax')?.valueChanges.subscribe((_) => {
                const uomControl = this.form.get('scopeOfDeliveryUom');
                if (uomControl) {
                    uomControl.updateValueAndValidity();
                }
            })
        );

        this.subscription.add(
            this.form.get('formTypes')?.valueChanges.subscribe(() => {
                this.masterDataFormService.updateTerminationScopeValidation(this.form);
                this.masterDataFormService.updateVoltageLevelsValidation(this.form);
                this.masterDataFormService.updateTotalCapacityValidation(this.form);
                this.masterDataFormService.updateScopeOfDeliveryValidation(this.form);
                this.masterDataFormService.updateStartsAsConstructionValidation(this.form);
                this.masterDataFormService.updateWorkOrderAutomationLevelValidation(this.form);
                this.updateMasterDataProcessTypesOptions();
                this.workOrderTypeOptions = this.masterDataFormService.setWorkOrderTypeOptions(this.form);
            })
        );

        this.subscription.add(
            this.form.get('masterDataProcessWorkOrderAutomationLevel')?.valueChanges.subscribe(() => {
                this.workOrderTypeOptions = this.masterDataFormService.setWorkOrderTypeOptions(this.form);
            })
        );

        this.subscription.add(
            this.form.get('terminationScope')?.valueChanges.subscribe(() => {
                this.updateMasterDataProcessTypesOptions();
                if (!this.masterDataProcessTypeOptions.some((x) => x.value === this.form.get('masterDataProcessType')?.value)) {
                    this.form.get('masterDataProcessType')?.setValue(null);
                }
            })
        );
    }

    updateMasterDataProcessTypesOptions = () => {
        this.masterDataProcessTypeOptions = this.masterDataFormService.updateMasterDataProcessTypesOptions(this.form, this.masterDataProcessTypeOptions);
    };




    isWorkOrderTypeSelected = (): boolean => {
        return this.masterDataFormService.isWorkOrderTypeSelected(this.form);
    };

    getWorkOrderDescriptionValueListType = (): ValueListType => {
        return this.masterDataFormService.getWorkOrderDescriptionValueListType(this.form);
    };
}
