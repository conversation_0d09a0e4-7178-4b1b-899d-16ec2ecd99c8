import { Component, Input } from '@angular/core';
import { WorkOrderEntryState } from 'src/app/api/installation-forms-client';
import { IconState } from 'src/app/core/enums/icon-state';

@Component({
    selector: 'app-work-order-status',
    templateUrl: './work-order-status.component.html',
    standalone: false
})
export class WorkOrderStatusComponent {
    @Input() workOrderStatus?: WorkOrderEntryState;

    mapToIconState(): IconState {
        switch (this.workOrderStatus) {
            case WorkOrderEntryState.ValidationFailed:
            case WorkOrderEntryState.ClosedByTechnician:
            case WorkOrderEntryState.Cancelled:
                return IconState.Failed;
            case WorkOrderEntryState.Done:
                return IconState.Completed;
            default:
                return IconState.InProgress;
        }
    }
}
