import { TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { InstallationFormsClient, SupplyType } from 'src/app/api/installation-forms-client';
import { InvoicingPricesService } from './invoicing-prices.service';

describe('InvoicingPricesService', () => {
    let invoicingPricesService: InvoicingPricesService;
    let mockClient: InstallationFormsClient;

    const mockResponse = of([
        { id: 'item1', displayName: 'Text One', externalPriceId: 1, supplyType: SupplyType.Electricity },
        { id: 'item2', displayName: 'Text Two', externalPriceId: 2, supplyType: SupplyType.Electricity },
        { id: 'item3', displayName: 'Text Three', externalPriceId: 3, supplyType: SupplyType.Heating }
    ]);

    beforeEach(() => {
        // Create a simple mock for the client
        mockClient = {
            getInvoicingPrices: jest.fn().mockReturnValue(
                of({
                    results: [
                        { id: 'item1', displayName: 'Text One', externalPriceId: 1, supplyType: SupplyType.Electricity },
                        { id: 'item2', displayName: 'Text Two', externalPriceId: 2, supplyType: SupplyType.Electricity },
                        { id: 'item3', displayName: 'Text Three', externalPriceId: 3, supplyType: SupplyType.Heating }
                    ]
                })
            )
        } as Partial<InstallationFormsClient> as InstallationFormsClient;

        TestBed.configureTestingModule({
            providers: [InvoicingPricesService, { provide: InstallationFormsClient, useValue: mockClient }]
        });

        invoicingPricesService = TestBed.inject(InvoicingPricesService);
        mockClient = TestBed.inject(InstallationFormsClient);
    });

    it('should be created', () => {
        expect(invoicingPricesService).toBeTruthy();
    });

    it('should call client with correct parameters', () => {
        invoicingPricesService.getAllPrices();
        expect(mockClient.getInvoicingPrices).toHaveBeenCalledWith(expect.any(String), SupplyType.All);
    });

    it('should return invoicing prices', () => {
        invoicingPricesService.getAllPrices().subscribe((prices) => {
            expect(prices).toEqual(mockResponse);
        });
    });

    it('should return invoicing prices by supply type', () => {
        invoicingPricesService.getPricesBySupplyType(SupplyType.Electricity).subscribe((prices) => {
            expect(prices).toEqual([
                { id: 'item1', displayName: 'Text One', externalPriceId: 1, supplyType: SupplyType.Electricity },
                { id: 'item2', displayName: 'Text Two', externalPriceId: 2, supplyType: SupplyType.Electricity }
            ]);
        });
    });

    it('should cache invoicing prices', () => {
        // First call
        invoicingPricesService.getAllPrices().subscribe();
        expect(mockClient.getInvoicingPrices).toHaveBeenCalledTimes(1);

        // Second call - should use cached result
        invoicingPricesService.getAllPrices().subscribe();
        expect(mockClient.getInvoicingPrices).toHaveBeenCalledTimes(1);
    });

    it('should handle empty invoicing prices', () => {
        (mockClient.getInvoicingPrices as jest.Mock).mockReturnValue(of([]));
        invoicingPricesService.getAllPrices().subscribe((prices) => {
            expect(prices).toEqual([]);
        });
    });
});
