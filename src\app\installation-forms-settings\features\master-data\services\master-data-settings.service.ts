import { Injectable } from '@angular/core';
import { FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { FormType, MasterDataProcessWorkOrderAutomationLevel, ValueListType } from 'src/app/api/installation-forms-client';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import { getAvailableMasterDataProcessTypes } from 'src/app/core/utils/master-data-processes-per-form-types';
import { findMatchingWorkOrderTypes } from '../constants/properties-per-for-type';
import { MasterDataOptionsService } from './master-data-options.service';

@Injectable({
    providedIn: 'root'
})
export class MasterDataSettingsService {
    constructor(
        private readonly translateService: TranslateService,
        private readonly masterDataOptionsService: MasterDataOptionsService
    ) {}

    // Form type validation methods
    isNewInstallationSelected(form: FormGroup): boolean {
        const selectedTypes = form?.get('formTypes')?.value || [];
        return selectedTypes.includes(FormType.NewInstallation);
    }

    isTerminationFormSelected(form: FormGroup): boolean {
        const selectedTypes = form?.get('formTypes')?.value || [];
        return selectedTypes.includes(FormType.Termination);
    }

    isOnlyTerminationFormSelected(form: FormGroup): boolean {
        const selectedTypes = form?.get('formTypes')?.value || [];
        return selectedTypes.length === 1 && selectedTypes.includes(FormType.Termination);
    }

    isNewInstallationOrExtensionSelected(form: FormGroup): boolean {
        const selectedTypes = form?.get('formTypes')?.value || [];
        return selectedTypes.some((type: FormType) => type === FormType.NewInstallation || type === FormType.Extension);
    }

    isEnergyProductionFormSelected(form: FormGroup): boolean {
        const selectedTypes = form?.get('formTypes')?.value || [];
        return selectedTypes.includes(FormType.EnergyProduction);
    }

    // Conditional validation methods
    isProcessTemplateRequired = (form: FormGroup) => {
        const value = form?.get('masterDataProcessType')?.value;
        return this.masterDataOptionsService.isProcessTemplateRequired(value);
    };

    isScopeOfDeliveryUomRequired = (form: FormGroup) => {
        const minValue = form?.get('scopeOfDeliveryMin')?.value;
        const maxValue = form?.get('scopeOfDeliveryMax')?.value;
        return minValue !== null || maxValue !== null;
    };

    isWorkOrderTypeSelected = (form: FormGroup): boolean => {
        return form?.get('workOrderType')?.value !== null;
    };

    getWorkOrderDescriptionValueListType = (form: FormGroup): ValueListType => {
        return this.masterDataOptionsService.getWorkOrderDescriptionValueListType(form?.get('workOrderType')?.value);
    };

    // Field validation update methods
    updateTerminationScopeValidation(form: FormGroup): void {
        const terminationScope = form.get('terminationScope');
        if (this.isTerminationFormSelected(form)) {
            terminationScope?.setValidators(Validators.required);
        } else {
            terminationScope?.clearValidators();
            terminationScope?.setValue(null);
        }
        terminationScope?.updateValueAndValidity();
    }

    updateVoltageLevelsValidation(form: FormGroup): void {
        const voltageLevels = form.get('voltageLevels');
        if (this.isNewInstallationOrExtensionSelected(form)) {
            voltageLevels?.setValidators(Validators.required);
        } else {
            voltageLevels?.clearValidators();
            voltageLevels?.setValue(null);
        }
        voltageLevels?.updateValueAndValidity();
    }

    updateTotalCapacityValidation(form: FormGroup): void {
        const totalCapacityMin = form.get('totalCapacityMin');
        const totalCapacityMax = form.get('totalCapacityMax');

        if (!this.isEnergyProductionFormSelected(form)) {
            totalCapacityMin?.setValue(null);
            totalCapacityMax?.setValue(null);
        }
    }

    updateScopeOfDeliveryValidation(form: FormGroup): void {
        const scopeOfDeliveryMin = form.get('scopeOfDeliveryMin');
        const scopeOfDeliveryMax = form.get('scopeOfDeliveryMax');
        const scopeOfDeliveryUom = form.get('scopeOfDeliveryUom');

        if (!this.isNewInstallationOrExtensionSelected(form)) {
            scopeOfDeliveryMin?.setValue(null);
            scopeOfDeliveryMax?.setValue(null);
            scopeOfDeliveryUom?.setValue(null);
        }
    }

    updateStartsAsConstructionValidation(form: FormGroup): void {
        const startsAsConstruction = form.get('startsAsConstruction');
        if (!this.isNewInstallationSelected(form)) {
            startsAsConstruction?.setValue(null);
        }
    }

    updateWorkOrderAutomationLevelValidation(form: FormGroup): void {
        const workOrderAutomationLevel = form.get('masterDataProcessWorkOrderAutomationLevel');
        if (this.isOnlyTerminationFormSelected(form)) {
            workOrderAutomationLevel?.enable();
            workOrderAutomationLevel?.setValidators(Validators.required);
        } else {
            workOrderAutomationLevel?.clearValidators();
            workOrderAutomationLevel?.setValue(null);
        }

        const selectedFormTypes = form?.get('formTypes')?.value;

        if (selectedFormTypes?.length > 1 && selectedFormTypes.includes(FormType.Termination)) {
            workOrderAutomationLevel?.clearValidators();
            workOrderAutomationLevel?.setValue(MasterDataProcessWorkOrderAutomationLevel.Manual);
            workOrderAutomationLevel?.disable();
        }

        workOrderAutomationLevel?.updateValueAndValidity();
    }

    updateMasterDataProcessTypesOptions(form: FormGroup, masterDataProcessTypeOptions: SelectItem[]): SelectItem[] {
        const formTypes = form?.get('formTypes')?.value || [];
        const updatedOptions = enumMapper.mapArray(
            this.translateService.instant('enums.masterDataProcessType'),
            getAvailableMasterDataProcessTypes(formTypes, form.get('terminationScope')?.value)
        );

        if (!updatedOptions.some(option => option.value === form?.get('masterDataProcessType')?.value)) {
            form?.get('masterDataProcessType')?.setValue(null);
        }

        return updatedOptions;
    }

    setWorkOrderTypeOptions(form: FormGroup): SelectItem[] {
        const automationLevel = form?.get('masterDataProcessWorkOrderAutomationLevel')?.value;
        if (!automationLevel || automationLevel === MasterDataProcessWorkOrderAutomationLevel.Manual) {
            form.get('workOrderType')?.setValue(null);
            form.get('workOrderDescriptionId')?.setValue(null);
            form.get('workOrderPurposeId')?.setValue(null);
            return [];
        }

        const selectedFormTypes = form?.get('formTypes')?.value;
        const workOrderTypes = findMatchingWorkOrderTypes(selectedFormTypes);
        if (workOrderTypes) {
            const options = enumMapper.map(this.translateService.instant('enums.workOrderType'), workOrderTypes);
            if (options.length === 1) {
                form.get('workOrderType')?.setValue(options[0].value);
                form.get('workOrderType')?.disable();
            }
            if (options.length > 1) {
                form.get('workOrderType')?.enable();
            }
            return options;
        } else {
            form.get('workOrderType')?.setValue(null);
            form.get('workOrderDescriptionId')?.setValue(null);
            form.get('workOrderPurposeId')?.setValue(null);
            return [];
        }
    }

    isAnyWorkOrderTypeAvailable = (workOrderTypeOptions: SelectItem[]): boolean => {
        return workOrderTypeOptions.length > 0;
    };
}
