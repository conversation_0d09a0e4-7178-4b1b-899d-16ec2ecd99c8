<div #overlayContainer></div>
<div fxLayout="column" class="mt-10 zebra-container" [formGroup]="form">
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['displayName']" for="displayName">
                {{ 'formsSettings.workOrders.displayName' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input id="displayName" type="text" pInputText maxlength="100" formControlName="displayName" autoTrimWhitespaces />
            <small [controlValidationErrors]="form.controls['displayName']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['formTypes']" for="formTypes">
                {{ 'formsSettings.workOrders.formTypes' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-multiSelect
                id="formTypes"
                [options]="formTypeOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="formTypes"
                [maxSelectedLabels]="formTypeOptions.length">
            </p-multiSelect>
            <small [controlValidationErrors]="form.controls['formTypes']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['formStates']" for="formStates">
                {{ 'formsSettings.workOrders.formStates' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-multiSelect
                id="formStates"
                [options]="formStateOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="formStates"
                [maxSelectedLabels]="formStateOptions.length">
            </p-multiSelect>
            <small [controlValidationErrors]="form.controls['formStates']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isSealResponsiblesRequired()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['sealResponsibles']" for="sealResponsibles">
                {{ 'formsSettings.workOrders.sealResponsibles' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-multiSelect
                id="sealResponsibles"
                [options]="responsibleForSealOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="sealResponsibles"
                [maxSelectedLabels]="responsibleForSealOptions.length">
            </p-multiSelect>
            <small [controlValidationErrors]="form.controls['sealResponsibles']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isMeterResponsiblesRequired()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['meterResponsibles']" for="meterResponsibles">
                {{ 'formsSettings.workOrders.meterResponsibles' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-multiSelect
                id="meterResponsibles"
                [options]="responsibleForMeterOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="meterResponsibles"
                [maxSelectedLabels]="responsibleForMeterOptions.length">
            </p-multiSelect>
            <small [controlValidationErrors]="form.controls['meterResponsibles']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isMeterNeedsChangeRequired()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['meterNeedsChange']" for="meterNeedsChange">
                {{ 'formsSettings.workOrders.meterNeedsChange' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="meterNeedsChange"
                formControlName="meterNeedsChange"
                [options]="(booleanOptionsService.getBooleanOptions() | async) ?? []"
                [appendTo]="overlayContainer"
                [showClear]="true"
                [placeholder]="'common.selectValue' | translate">
            </p-select>
            <small [controlValidationErrors]="form.controls['meterNeedsChange']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isInvoicePaidRequired()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['invoicePaid']" for="invoicePaid">
                {{ 'formsSettings.workOrders.invoicePaid' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="invoicePaid"
                formControlName="invoicePaid"
                [options]="(booleanOptionsService.getBooleanOptions() | async) ?? []"
                [appendTo]="overlayContainer"
                [showClear]="true"
                [placeholder]="'common.selectValue' | translate">
            </p-select>
            <small [controlValidationErrors]="form.controls['invoicePaid']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isReadyForMeterRequired()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['readyForMeter']" for="readyForMeter">
                {{ 'formsSettings.workOrders.readyForMeter' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="readyForMeter"
                formControlName="readyForMeter"
                [options]="(booleanOptionsService.getBooleanOptions() | async) ?? []"
                [appendTo]="overlayContainer"
                [showClear]="true"
                [placeholder]="'common.selectValue' | translate">
            </p-select>
            <small [controlValidationErrors]="form.controls['readyForMeter']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isSupplierSelectedRequired()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['supplierSelected']" for="supplierSelected">
                {{ 'formsSettings.workOrders.supplierSelected' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="supplierSelected"
                formControlName="supplierSelected"
                [options]="(booleanOptionsService.getBooleanOptions() | async) ?? []"
                [appendTo]="overlayContainer"
                [showClear]="true"
                [placeholder]="'common.selectValue' | translate">
            </p-select>
            <small [controlValidationErrors]="form.controls['supplierSelected']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isStartsAsConstructionRequired()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['startsAsConstruction']" for="startsAsConstruction">
                {{ 'formsSettings.workOrders.startsAsConstruction' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="startsAsConstruction"
                formControlName="startsAsConstruction"
                [options]="(booleanOptionsService.getBooleanOptions() | async) ?? []"
                [appendTo]="overlayContainer"
                [showClear]="true"
                [placeholder]="'common.selectValue' | translate">
            </p-select>
            <small [controlValidationErrors]="form.controls['startsAsConstruction']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['workOrderType']" for="workOrderType">
                {{ 'formsSettings.workOrders.workOrderType' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="workOrderType"
                [options]="workOrderTypeOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="workOrderType">
            </p-select>
            <small [controlValidationErrors]="form.controls['workOrderType']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['workOrderPurposeId']" for="workOrderPurposeId">
                {{ 'formsSettings.workOrders.workOrderPurposeId' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <cmbs-value-list-dropdown
                id="workOrderPurposeId"
                formControlName="workOrderPurposeId"
                [valueListType]="getWorkOrderPurposeValueListType()"
                [messageKey]="this.tabName"
                [valueList]="valueListsService.getValueList(getWorkOrderPurposeValueListType()) | async">
            </cmbs-value-list-dropdown>
            <small [controlValidationErrors]="form.controls['workOrderPurposeId']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="isWorkOrderTypeSelected()">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['workOrderDescriptionId']" for="workOrderDescriptionId">
                {{ 'formsSettings.workOrders.workOrderDescriptionId' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <cmbs-value-list-dropdown
                id="workOrderDescriptionId"
                formControlName="workOrderDescriptionId"
                [valueListType]="getWorkOrderDescriptionValueListType()"
                [messageKey]="this.tabName"
                [valueList]="valueListsService.getValueList(getWorkOrderDescriptionValueListType()) | async">
            </cmbs-value-list-dropdown>
            <small [controlValidationErrors]="form.controls['workOrderDescriptionId']" class="p-error"></small>
        </div>
    </div>

    <div class="mb-2 mt-2 flex justify-content-end">
        <button
            id="cancelButton"
            [disabled]="isProcessing"
            type="button"
            pButton
            pRipple
            class="mr-2 p-button-secondary"
            (click)="cancelCreateClick()">
            {{ 'common.cancel' | translate }}
        </button>
        <button id="saveButton" type="button" pButton pRipple (click)="createClick()" [disabled]="form.invalid || isProcessing">
            {{ 'common.saveChanges' | translate }}
        </button>
    </div>
</div>
