import { OnD<PERSON>roy, <PERSON><PERSON>, PipeTransform } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import { NO_VALUE_STRING } from '../../../core/constants/constants';

@Pipe({
    name: 'noValueEnumTranslate',
    standalone: false
})
export class NoValueEnumTranslatePipe implements PipeTransform, OnDestroy {
    subscription: Subscription = new Subscription();
    enumsTranslations: any;

    constructor(private readonly translateService: TranslateService) {
        this.enumsTranslations = this.translateService.instant('enums');
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    transform(enumValue: any, enumPath: string): string {
        if (!enumValue || !enumPath) {
            return NO_VALUE_STRING;
        }
        let enumName = enumPath.split('.')[1];
        return this.enumsTranslations[enumName][enumValue];
    }
}
