// list.component.spec.ts
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router, provideRouter } from '@angular/router';
import { RouterTestingHarness } from '@angular/router/testing';
import { TabsService } from '@kmd-elements-ui/tabs';
import { TranslateModule } from '@ngx-translate/core';
import { of } from 'rxjs';
import { InstallationFormsClient } from '../../../api/installation-forms-client';
import { ValueListsService } from '../../../core/services/value-lists/value-lists.service';
import { InstallationFormsListComponent } from './list.component';
import { InstallationFormsSearchStoreService } from './store/list-store.service';
jest.mock('uuid', () => ({ v4: () => '123456789' }));

describe('InstallationFormsListComponent', () => {
    let component: InstallationFormsListComponent;
    let fixture: ComponentFixture<InstallationFormsListComponent>;
    let mockTabsService: Partial<TabsService>;
    let mockInstallationFormsClient: Partial<InstallationFormsClient>;
    let mockSearchStore: Partial<InstallationFormsSearchStoreService>;
    let mockValueListsService: Partial<ValueListsService>;
    let router: Router;
    let harness: RouterTestingHarness;

    beforeEach(async () => {
        mockTabsService = {
            reloadButtonClicked$: of('someTabId'),
            reload: jest.fn().mockReturnValue(of(true)),
            tabReloaded: jest.fn(),
            addOrUpdateTab: jest.fn().mockReturnValue('someTabId'),
            isTabActiveListener: jest.fn(() => of(true))
        };

        mockInstallationFormsClient = {
            searchInstallationForms: jest.fn().mockReturnValue(
                of({
                    result: {
                        results: [],
                        totalCount: 0
                    }
                })
            )
        };

        mockValueListsService = {
            fetchValueLists: jest.fn()
        };

        mockSearchStore = {
            loading$: of(false),
            setLazyLoadEvent: jest.fn(),
            setLoading: jest.fn(),
            onSearchResultChange: jest.fn()
        };

        await TestBed.configureTestingModule({
            imports: [TranslateModule.forRoot()],
            declarations: [InstallationFormsListComponent],
            providers: [
                provideRouter([]), // No routes need to be provided for this component test
                { provide: TabsService, useValue: mockTabsService },
                { provide: InstallationFormsClient, useValue: mockInstallationFormsClient },
                { provide: InstallationFormsSearchStoreService, useValue: mockSearchStore },
                { provide: ValueListsService, useValue: mockValueListsService }
            ],
            schemas: [CUSTOM_ELEMENTS_SCHEMA]
        }).compileComponents();

        fixture = TestBed.createComponent(InstallationFormsListComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();

        harness = await RouterTestingHarness.create();
        router = TestBed.inject(Router);
    });

    afterEach(async () => {
        await harness?.navigateByUrl('/'); // Clean up harness state after each test
    });

    it('should create the component', () => {
        expect(component).toBeTruthy();
    });

    it('should have default values set for pagination and date formats', () => {
        expect(component.first).toBe(0);
        expect(component.maxRowsPerPage).toBe(50);
    });

    it('should initialize the component with correct observables', () => {
        expect(component.isLoading$).toBeDefined();
        expect(component.searchResult$).toBeDefined();
    });

    it('should call addTab on creation', () => {
        expect(mockTabsService.addOrUpdateTab).toHaveBeenCalled();
    });

    it('should unsubscribe on destroy', () => {
        const unsubscribeSpy = jest.spyOn(component.subscription, 'unsubscribe');
        component.ngOnDestroy();
        expect(unsubscribeSpy).toHaveBeenCalled();
    });

    it('should call client searchInstallationForms with correct parameters', () => {
        component.search();
        expect(mockInstallationFormsClient.searchInstallationForms).toHaveBeenCalled();
    });
});
