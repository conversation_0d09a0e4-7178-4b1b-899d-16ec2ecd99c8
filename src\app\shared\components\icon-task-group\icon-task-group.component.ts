import { Component, Input } from '@angular/core';
import { TaskGroup } from 'src/app/api/installation-forms-client';

@Component({
    selector: 'app-icon-task-group',
    templateUrl: './icon-task-group.component.html',
    standalone: false,
    host: { hostID: crypto.randomUUID().toString() }
})
export class IconTaskGroupComponent {
    @Input() taskGroup!: TaskGroup;

    readonly AllowAutomatizationTaskGroup: TaskGroup = TaskGroup.AllowAutomatization;
    readonly CreateInvoiceTaskGroup: TaskGroup = TaskGroup.CreateInvoice;
    readonly CreateMasterDataProcessTaskGroup: TaskGroup = TaskGroup.CreateMasterDataProcess;
    readonly CreateMasterDataProcessWithCloseDownTaskGroup: TaskGroup = TaskGroup.CreateMasterDataProcessWithCloseDown;
    readonly CreateWorkOrderTaskGroup: TaskGroup = TaskGroup.CreateWorkOrder;
    readonly AutomaticEmailsTaskGroup: TaskGroup = TaskGroup.AutomaticEmails;
    readonly ArchivingTaskGroup: TaskGroup = TaskGroup.Archiving;
    readonly AutomaticInstructionTextTaskGroup: TaskGroup = TaskGroup.AutomaticInstructionText;
}
