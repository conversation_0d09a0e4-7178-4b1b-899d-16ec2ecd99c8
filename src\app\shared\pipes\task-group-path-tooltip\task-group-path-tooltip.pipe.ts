import { Pipe, PipeTransform } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Pipe({
    name: 'taskGroupPathTooltip',
    standalone: false
})
export class TaskGroupPathTooltipPipe implements PipeTransform {
    constructor(private readonly translateService: TranslateService) {}

    transform(path?: string): string {
        if (!path) {
            return '';
        }

        return this.translateService.instant('tasksWidget.taskGroups.' + path);
    }
}
