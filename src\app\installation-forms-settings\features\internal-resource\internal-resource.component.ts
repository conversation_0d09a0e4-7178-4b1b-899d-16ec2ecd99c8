import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { AuthorizationService } from '@kmd-elements-ui/authorization';
import { KeyActions, KeyboardShortcuts, KeyboardShortcutsService } from '@kmd-elements-ui/shortcuts';
import { TabsService } from '@kmd-elements-ui/tabs';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { BehaviorSubject, filter, finalize, map, of, Subscription, switchMap, tap } from 'rxjs';
import { InstallationFormsClient, InternalResourceSettings, SupplyType } from 'src/app/api/installation-forms-client';
import { Permissions } from 'src/app/core/constants/permissions';
import { EmailTemplatesService } from 'src/app/core/services/email-templates/email-templates.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { getValueOrDefault } from 'src/app/core/utils/get-value-or-default';
import { EmailValidator } from 'src/app/core/utils/validators/email/email.validator';
import { v4 as uuidv4 } from 'uuid';
import {
    internalResourceMarkdownDaDK,
    internalResourceMarkdownEnUs,
    internalResourceSubjectTooltipDaDK,
    internalResourceSubjectTooltipEnUs,
    internalResourceTabName,
    internalResourceTranslationPath
} from './internal-resource.consts';

@Component({
    selector: 'app-internal-resource',
    templateUrl: './internal-resource.component.html',
    styleUrl: './internal-resource.component.scss',
    standalone: false
})
export class InternalResourceComponent implements OnInit, OnDestroy {
    widgetTranslations: any;
    internalResource?: InternalResourceSettings;
    form: FormGroup = new FormGroup({});
    templateOptions: SelectItem[] = [];

    private _tabId?: string;
    private keyEventsSubscription: Subscription = new Subscription();
    private _isProcessing: boolean = false;

    internalResourceMarkdownEnUs = internalResourceMarkdownEnUs;
    internalResourceMarkdownDaDK = internalResourceMarkdownDaDK;
    internalResourceSubjectTooltipEnUs = internalResourceSubjectTooltipEnUs;
    internalResourceSubjectTooltipDaDK = internalResourceSubjectTooltipDaDK;
    internalResourceTabName = internalResourceTabName;
    internalResourceTranslationPath = internalResourceTranslationPath;
    dirty$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
    hasUserFormConfigurationWritePermission: boolean = false;
    hasUserCommunicationTemplatesReadPermission: boolean = false;

    get isProcessing() {
        return this._isProcessing;
    }
    set isProcessing(value: boolean) {
        this._isProcessing = value;
        if (this.hasUserCommunicationTemplatesReadPermission) {
            value ? this.form.disable() : this.form.enable();
        }
    }

    subscription: Subscription = new Subscription();

    keyActions: KeyActions = {
        [KeyboardShortcuts.Escape]: () => {
            this.tabsService.close();
        },
        [KeyboardShortcuts.Enter]: () => {
            this.updateInternalResource();
        }
    };

    constructor(
        private readonly installationFormsClient: InstallationFormsClient,
        private readonly messageServiceHelper: MessageServiceHelper,
        private readonly translateService: TranslateService,
        private readonly fb: FormBuilder,
        private readonly tabsService: TabsService,
        private readonly keyboardShortcutsService: KeyboardShortcutsService,
        private readonly activatedRoute: ActivatedRoute,
        private readonly emailTemplatesService: EmailTemplatesService,
        private readonly authService: AuthorizationService
    ) {
        this.initForm();
        this.addTab();
        this.authService
            .hasPermissions([Permissions.formConfiguration.write])
            .subscribe((x) => (this.hasUserFormConfigurationWritePermission = x));
        this.authService.hasPermissions([Permissions.communicationTemplates.read]).subscribe((x) => {
            this.hasUserCommunicationTemplatesReadPermission = x;
            if (!x) {
                this.form.disable();
            }
        });
    }

    ngOnInit(): void {
        this.widgetTranslations = this.translateService.instant(internalResourceTranslationPath);
        this.getInternalResource();
        this.registerRefreshButtonListener();
        this.loadTemplates();
        this.subscription.add(
            this.form.valueChanges.subscribe((x) => {
                this.recalculateDirty();
            })
        );
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    initForm(): void {
        this.form = this.fb.group({
            defaultSenderAddress: [null, [Validators.required, EmailValidator(), Validators.maxLength(320)]],
            defaultReceiverAddress: [null, [Validators.required, EmailValidator(), Validators.maxLength(320)]],
            defaultSubject: [
                null,
                [
                    Validators.required,
                    Validators.maxLength(200),
                    Validators.pattern(
                        /^(?:[^<]*|.*<(FormNumber|FormAddress|CaseWorkerName|CaseWorkerEmail|CaseWorkerPhoneNumber)>.*)*$/
                    )
                ]
            ],
            templateId: [null, [Validators.required]]
        });
    }

    subscribeKeyShortcuts(): void {
        this.keyEventsSubscription = this.keyboardShortcutsService.registerKeyboardShortcuts(this.keyActions).subscribe();
    }

    unsubscribeKeyShortcuts(): void {
        this.keyEventsSubscription.unsubscribe();
    }

    getInternalResource(): void {
        this.isProcessing = true;
        this.subscription.add(
            this.installationFormsClient
                .getInternalResourceSettings(uuidv4())
                .pipe(
                    finalize(() => {
                        this.isProcessing = false;
                    }),
                    map((response) => response.result)
                )
                .subscribe({
                    next: (result) => {
                        this.internalResource = result;
                        this.form.patchValue({
                            defaultSenderAddress: result.sender,
                            defaultReceiverAddress: result.receiver,
                            defaultSubject: result.subject,
                            templateId: result.templateId
                        });
                    },
                    error: (_) => {
                        this.messageServiceHelper.showError({
                            detail: this.widgetTranslations['getError'],
                            key: internalResourceTabName
                        });
                    }
                })
        );
    }

    updateInternalResource(): void {
        if (this.form.invalid) {
            this.form.markAllAsTouched();
            this.messageServiceHelper.showWarning({
                detail: this.translateService.instant('common.allRequiredFieldsShouldBeCompleted'),
                key: internalResourceTabName
            });
            return;
        }

        const updateBody = new InternalResourceSettings({
            sender: this.form.value.defaultSenderAddress,
            receiver: this.form.value.defaultReceiverAddress,
            subject: this.form.value.defaultSubject,
            templateId: this.form.value.templateId
        });
        this.isProcessing = true;
        this.subscription.add(
            this.installationFormsClient
                .updateInternalResourceSettings(uuidv4(), updateBody)
                .pipe(
                    finalize(() => {
                        this.isProcessing = false;
                    }),
                    map((response) => response.result)
                )
                .subscribe({
                    next: (result) => {
                        this.internalResource = result;
                        this.form.patchValue({
                            defaultSenderAddress: result.sender,
                            defaultReceiverAddress: result.receiver,
                            defaultSubject: result.subject,
                            templateId: result.templateId
                        });
                        this.messageServiceHelper.showSuccess({
                            key: internalResourceTabName
                        });
                    },
                    error: (_) => {
                        this.messageServiceHelper.showError({
                            detail: this.widgetTranslations['updateError'],
                            key: internalResourceTabName
                        });
                    }
                })
        );
    }

    private loadTemplates() {
        if (!this.hasUserCommunicationTemplatesReadPermission) {
            this.messageServiceHelper.showWarning({
                detail: this.widgetTranslations['noPermissionToReadEmailTemplates'],
                key: internalResourceTabName
            });
            return;
        }
        this.subscription.add(
            this.emailTemplatesService
                .getEmailTemplates(SupplyType.Electricity)
                .pipe(
                    filter((response) => response.result !== undefined),
                    map((response) =>
                        response.result!.map(
                            (result) =>
                                ({
                                    value: result.id,
                                    label: result.name
                                }) as SelectItem
                        )
                    )
                )
                .subscribe({
                    next: (mappedResult) => {
                        this.templateOptions = mappedResult;
                    },
                    error: (_) => {
                        this.messageServiceHelper.showError({
                            detail: this.translateService.instant('common.loadEmailTemplatesError'),
                            key: internalResourceTabName
                        });
                    }
                })
        );
    }

    private addTab() {
        this._tabId = this.tabsService.addOrUpdateTab(this.activatedRoute, {
            icon$: of('fas fa-th-list'),
            areaLabel$: of(this.translateService.instant(`${internalResourceTranslationPath}.tabAreaLabel`)),
            label$: of(this.translateService.instant(`${internalResourceTranslationPath}.tabLabel`)),
            tooltip$: of(this.translateService.instant(`${internalResourceTranslationPath}.tabLabel`)),
            dirty$: this.dirty$,
            canReload$: of(true)
        });
    }

    registerRefreshButtonListener(): void {
        this.subscription.add(
            this.tabsService.reloadButtonClicked$
                .pipe(
                    filter((tabId) => tabId === this._tabId),
                    switchMap((tabId) => this.tabsService.reload(tabId)),
                    filter((canBeReloaded) => canBeReloaded),
                    tap(() => {
                        this.getInternalResource();
                        this.loadTemplates();
                    })
                )
                .subscribe(() => {
                    this.tabsService.tabReloaded(this._tabId as string);
                })
        );
    }

    onCancelChangesClicked(): void {
        this.form.patchValue({
            defaultSenderAddress: this.internalResource?.sender,
            defaultReceiverAddress: this.internalResource?.receiver,
            defaultSubject: this.internalResource?.subject,
            templateId: this.internalResource?.templateId
        });
    }

    recalculateDirty(): void {
        const isDirty =
            getValueOrDefault(this.form.value.defaultSenderAddress) !== getValueOrDefault(this.internalResource?.sender) ||
            getValueOrDefault(this.form.value.defaultReceiverAddress) !== getValueOrDefault(this.internalResource?.receiver) ||
            getValueOrDefault(this.form.value.defaultSubject) !== getValueOrDefault(this.internalResource?.subject) ||
            getValueOrDefault(this.form.value.templateId) !== getValueOrDefault(this.internalResource?.templateId);
        this.dirty$.next(isDirty);
    }
}
