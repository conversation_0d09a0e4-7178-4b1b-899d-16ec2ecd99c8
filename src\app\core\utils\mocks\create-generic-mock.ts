/**
 * Generic factory function to create a typed mock for any class.
 * @param overrides Properties to set on the mock object
 * @returns A Proxy-based mock that returns empty objects for undefined properties
 */
export function createGenericMock<T>(overrides: Partial<T> = {}): T {
    const handler = {
        get: (target: any, prop: string) => {
            // If property exists on the mock object, return it
            if (prop in target) {
                return target[prop];
            }

            // If property is a standard object function, return undefined
            if (['then', 'catch', 'finally'].includes(prop)) {
                return undefined;
            }

            // Create and return empty objects for undefined properties
            if (typeof prop === 'string' && !prop.startsWith('_')) {
                target[prop] = {};
                return target[prop];
            }

            return undefined;
        }
    };

    return new Proxy(overrides as any, handler) as T;
}
