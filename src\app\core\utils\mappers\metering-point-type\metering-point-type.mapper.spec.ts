import { MeteringPointType, MeteringPointTypeParam } from 'src/app/api/installation-forms-client';
import { convertMeteringPointTypeToEnum } from './metering-point-type.mapper';

describe('MeteringPointTypeMapper', () => {
    describe('convertMeteringPointTypeToEnum', () => {
        it('should convert MeteringPointType.E17 to MeteringPointTypeParam.E17', () => {
            const result = convertMeteringPointTypeToEnum(MeteringPointType.E17);
            expect(result).toBe(MeteringPointTypeParam.E17);
        });

        it('should convert MeteringPointType.E18 to MeteringPointTypeParam.E18', () => {
            const result = convertMeteringPointTypeToEnum(MeteringPointType.E18);
            expect(result).toBe(MeteringPointTypeParam.E18);
        });

        // Test undefined/missing handling - current implementation would return undefined
        it('should return undefined for any other value', () => {
            // Use a non-existent value for testing (type assertion to bypass TypeScript)
            const result = convertMeteringPointTypeToEnum('OTHER_VALUE' as MeteringPointType);
            expect(result).toBeUndefined();
        });
    });
});
