import { AfterViewInit, Component, EventEmitter, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import {
    KeyActions,
    KeyboardShortcuts,
    KeyboardShortcutsService,
    KeyboardShortcutsSubscriptionManager
} from '@kmd-elements-ui/shortcuts';
import { TabsService } from '@kmd-elements-ui/tabs';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { Table, TableLazyLoadEvent, TableRowSelectEvent } from 'primeng/table';
import {
    BehaviorSubject,
    EMPTY,
    Observable,
    Subscription,
    catchError,
    combineLatestWith,
    filter,
    map,
    of,
    shareReplay,
    switchMap,
    take,
    tap
} from 'rxjs';
import {
    IInstallationFormsSearchFilter,
    InstallationFormsApiResponse,
    InstallationFormsClient,
    InstallationFormsPagedResult,
    InstallationFormsSearchFilter,
    PaginationParameters,
    SearchCompareOperator,
    SearchExpression,
    SortBy,
    SortingDirection
} from 'src/app/api/installation-forms-client';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import { v4 as uuidv4 } from 'uuid';
import { SearchOperator } from '../../../core/enums/search-operator';
import { BooleanOptionsService } from '../../../core/services/boolean-options/boolean-options.service';
import { ValueListsService } from '../../../core/services/value-lists/value-lists.service';
import { InstallationFormsListColumn } from './models/list-column';
import { InstallationFormsListItem } from './models/list-item';
import { InstallationFormsSearchResult } from './models/search-result';
import { InstallationFormsSearchStoreService } from './store/list-store.service';
import { InstallationFormsColumnsService } from './utils/columns.service';

@Component({
    selector: 'app-installation-forms-list',
    templateUrl: './list.component.html',
    styleUrls: ['./list.component.scss'],
    providers: [InstallationFormsSearchStoreService],
    standalone: false
})
export class InstallationFormsListComponent implements KeyboardShortcutsSubscriptionManager, OnInit, OnDestroy, AfterViewInit {
    private commonTranslations: any;
    private enumsTranslations: any;
    public operatorsTranslations: SelectItem[] = [];

    allColumns: InstallationFormsListColumn[] = [];
    selectedColumns: InstallationFormsListColumn[] = [];
    selectedRow?: InstallationFormsListItem;

    tableId = 'InstallationFormsList';
    get defaultPage(): { pageNumber: number; pageSize: number } {
        return { pageNumber: 1, pageSize: 50 };
    }

    get defaultSort(): { field: string | undefined; order: number | undefined } {
        return { field: undefined, order: undefined };
    }

    @ViewChild('installationFormsTable') private installationFormsTable!: Table;
    filters$: BehaviorSubject<IInstallationFormsSearchFilter> = new BehaviorSubject<IInstallationFormsSearchFilter>(
        this.initFilters()
    );

    isLoading$!: Observable<boolean>;
    searchResult$!: Observable<InstallationFormsSearchResult>;
    first = 0;
    maxRowsPerPage = 50;
    subscription: Subscription = new Subscription();
    sortableColumns = [
        'formNumber',
        'formRequiresAttention',
        'createdDate',
        'hasUnreadMessages',
        'caseWorkerName',
        'caseWorkerEmail',
        'installerName',
        'installerAuthorizationNumber',
        'scopeOfDeliverySize',
        'installationAddress',
        'installInConstructionPhase',
        'latestRegistrationDate'
    ];

    private isSearchTriggeredManually: boolean = true;
    private _tabId?: string;

    private keyEventsSubscription: Subscription = new Subscription();

    keyActions: KeyActions = {
        [KeyboardShortcuts.AltPlusL]: () => {
            this.focusOnFirstRow();
        },
        [KeyboardShortcuts.Escape]: () => {
            this.tabsService.close();
        }
    };

    @Output()
    itemClicked: EventEmitter<InstallationFormsListItem> = new EventEmitter();

    constructor(
        private readonly client: InstallationFormsClient,
        private readonly searchStore: InstallationFormsSearchStoreService,
        private readonly activatedRoute: ActivatedRoute,
        private readonly tabsService: TabsService,
        private readonly translateService: TranslateService,
        private readonly router: Router,
        private readonly keyboardShortcutsService: KeyboardShortcutsService,
        private readonly valueListsService: ValueListsService,
        private readonly columnsService: InstallationFormsColumnsService,
        protected readonly booleanOptionsService: BooleanOptionsService
    ) {
        this.addTab();
        this.allColumns = this.columnsService.getColumns();
        this.selectedColumns = structuredClone(this.allColumns);
    }

    ngOnDestroy(): void {
        this.filters$?.unsubscribe();
        this.subscription.unsubscribe();
    }

    ngOnInit(): void {
        this.isLoading$ = this.searchStore.loading$;

        this.search();

        this.searchStore.onSearchResultChange(this.searchResult$);

        this.registerRefreshButtonListener();

        this.commonTranslations = this.translateService.instant('common');
        this.enumsTranslations = this.translateService.instant('enums');
        this.getOperatorsTranslations();
    }

    ngAfterViewInit() {
        this.keyboardShortcutsService.registerTableRowFocus(this._tabId as string);
    }

    subscribeKeyShortcuts(): void {
        this.keyEventsSubscription = this.keyboardShortcutsService.registerKeyboardShortcuts(this.keyActions).subscribe();
    }

    unsubscribeKeyShortcuts(): void {
        this.keyEventsSubscription.unsubscribe();
    }

    focusOnFirstRow() {
        const tableFirstRow = document.querySelector('.p-datatable-tbody')?.children[0] as HTMLElement;
        if (tableFirstRow) {
            tableFirstRow.focus();
        }
    }

    registerRefreshButtonListener(): void {
        this.subscription.add(
            this.tabsService.reloadButtonClicked$
                .pipe(
                    filter((tabId) => tabId === this._tabId),
                    switchMap((tabId) => this.tabsService.reload(tabId)),
                    filter((canBeReloaded) => canBeReloaded),
                    tap(() => {
                        this.refreshData();
                        this.valueListsService.fetchValueLists();
                    }),
                    switchMap(() =>
                        this.isLoading$.pipe(
                            filter((loading) => !loading),
                            take(1)
                        )
                    )
                )
                .subscribe(() => {
                    this.tabsService.tabReloaded(this._tabId as string);
                })
        );
    }

    refreshData(): void {
        this.filters$.next(this.filters$.value);
    }

    loadItemsLazy(event: TableLazyLoadEvent) {
        this.filters$.next({
            paginationParameters: new PaginationParameters({
                pageNumber: 1,
                pageSize: 50
            }),
            searchExpressions: this.prepareFiltersFromColumns(event.filters),
            sortBy: this.prepareSortFromColumns(event)
        });

        this.searchStore.setLazyLoadEvent(event);
    }

    onMatchModeChange() {
        const hasEmptyMatchMode = Object.values(this.installationFormsTable.filters).some((filter: any) => {
            return filter.matchMode === SearchOperator.Empty;
        });

        if (hasEmptyMatchMode) {
            Object.values(this.installationFormsTable.filters).forEach((filter: any) => {
                if (filter.matchMode === SearchOperator.Empty) {
                    filter.value = '';
                }
            });
            const lazyLoadMetadata = this.installationFormsTable.createLazyLoadMetadata();
            this.loadItemsLazy(lazyLoadMetadata);
        }
    }

    onRowSelect(selectedItem: TableRowSelectEvent) {
        const selection = window.getSelection();
        if (selection && selection.toString().length > 0) {
            return;
        }
        this.router.navigate(['details', selectedItem.data.type, selectedItem.data.formId], {
            relativeTo: this.activatedRoute
        });
    }

    search(): void {
        this.searchStore.setLoading(true);
        this.searchResult$ = this.searchStore.lazyLoadEvent$.pipe(
            combineLatestWith(this.filters$.asObservable()),
            switchMap(([loadGridDataEvent, filters]) => {
                const request = new InstallationFormsSearchFilter({
                    ...filters,
                    paginationParameters: new PaginationParameters({
                        pageNumber: this.isSearchTriggeredManually
                            ? filters.paginationParameters.pageNumber
                            : Math.floor((loadGridDataEvent.first ?? 0) / (loadGridDataEvent.rows ?? 50)) + 1,
                        pageSize: this.isSearchTriggeredManually ? filters.paginationParameters.pageSize : loadGridDataEvent.rows!
                    })
                });
                this.isSearchTriggeredManually = false;
                return this.client.searchInstallationForms(uuidv4(), request).pipe(
                    catchError((x) => {
                        this.searchStore.setLoading(false);
                        return EMPTY;
                    })
                );
            }),
            map((result: InstallationFormsApiResponse<InstallationFormsPagedResult>) => {
                return {
                    items:
                        result.result.items?.map((x) => {
                            return {
                                formId: x.formId,
                                formNumber: x.formNumber,
                                state: x.state,
                                type: x.type,
                                category: x.category,
                                installationAddress: x.installationAddress || '',
                                formRequiresAttention: x.formRequiresAttention,
                                problemCategory: x.problemCategories,
                                createdDate: x.createdDate,
                                hasUnreadMessages: x.hasUnreadMessages,
                                caseWorkerEmail: x.caseWorkerEmail,
                                caseWorkerName: x.caseWorkerName,
                                installerName: x.installerName,
                                installerAuthorizationNumber: x.installerAuthorizationNumber,
                                scopeOfDeliverySize: x.scopeOfDeliverySize,
                                tags: x.tags,
                                installInConstructionPhase: x.installInConstructionPhase,
                                processLevelIndicators: x.processLevelIndicators,
                                latestRegistrationDate: x.latestRegistrationDate
                            } as InstallationFormsListItem;
                        }) ?? [],
                    totalRecords: result.result.totalCount
                } as InstallationFormsSearchResult;
            }),
            tap((_) => this.searchStore.setLoading(false)),
            shareReplay(1)
        );
    }

    private initFilters(): IInstallationFormsSearchFilter {
        return {
            paginationParameters: new PaginationParameters({
                pageNumber: 1,
                pageSize: 50
            })
        };
    }

    private addTab() {
        this._tabId = this.tabsService.addOrUpdateTab(this.activatedRoute, {
            icon$: of('fas fa-th-list'),
            areaLabel$: of(this.translateService.instant('home.tabAreaLabel')),
            label$: of(this.translateService.instant('home.tabLabel')),
            tooltip$: of(this.translateService.instant('home.tabLabel')),
            dirty$: of(false),
            canReload$: of(true)
        });
    }

    getTranslationsForEnum(column: InstallationFormsListColumn): SelectItem[] {
        if (!this.enumsTranslations) return [];
        return enumMapper.map(this.enumsTranslations[column.field], column.enumReference);
    }

    getOperatorsTranslations() {
        this.operatorsTranslations = [
            { label: this.commonTranslations['filterLabelContains'], value: 'contains' },
            { label: this.commonTranslations['filterLabelNotContains'], value: 'notContains' },
            { label: this.commonTranslations['filterLabelStartsWith'], value: 'startsWith' },
            { label: this.commonTranslations['filterLabelEndsWith'], value: 'endsWith' },
            { label: this.commonTranslations['filterLabelEquals'], value: 'equals' },
            { label: this.commonTranslations['filterLabelNotEquals'], value: 'notEquals' },
            { label: this.commonTranslations['filterLabelDateIs'], value: 'dateIs' },
            { label: this.commonTranslations['filterLabelDateIsNot'], value: 'dateIsNot' },
            { label: this.commonTranslations['filterLabelDateIsBefore'], value: 'dateBefore' },
            { label: this.commonTranslations['filterLabelDateIsAfter'], value: 'dateAfter' },
            { label: this.commonTranslations['filterLabelGreaterThanOrEqualTo'], value: 'gte' },
            { label: this.commonTranslations['filterLabelGreaterThan'], value: 'gt' },
            { label: this.commonTranslations['filterLabelLessThanOrEqualTo'], value: 'lte' },
            { label: this.commonTranslations['filterLabelLessThan'], value: 'lt' },
            { label: this.commonTranslations['emptyValue'], value: 'empty' }
        ];
    }

    getAllowedOperators(operators: SearchOperator[]): SelectItem[] | undefined {
        if (operators == undefined) {
            return undefined;
        }
        const allowedOperators: SelectItem[] = [];
        operators.forEach((allowedOperator: SearchOperator) => {
            this.operatorsTranslations.forEach((translated: SelectItem) => {
                if (translated.value == allowedOperator) {
                    allowedOperators.push(translated);
                }
            });
        });
        return allowedOperators;
    }

    isSortableColumn(columnName: string): boolean {
        return this.sortableColumns.includes(columnName);
    }

    private prepareSortFromColumns(event: TableLazyLoadEvent): SortBy | undefined {
        if (!event.sortField || !this.isSortableColumn(event.sortField as string)) {
            return undefined;
        }
        return new SortBy({
            propertyName: event.sortField as string,
            sortingDirection: event.sortOrder === 1 ? SortingDirection.Asc : SortingDirection.Desc
        });
    }

    private prepareFiltersFromColumns(filters: any): SearchExpression[] | undefined {
        if (filters == null || Object.keys(filters).length === 0) {
            return undefined;
        }
        const columnsFilter: SearchExpression[] = [];
        Object.getOwnPropertyNames(filters).forEach((name) => {
            const filter = filters[name];

            if (filter.value === null || (Array.isArray(filter.value) && !filter.value.length)) {
                return;
            }

            const compareOperator = this.mapOperator(filter.matchMode) as SearchCompareOperator;
            const isValueLessOperator =
                compareOperator === SearchCompareOperator.Empty || compareOperator === SearchCompareOperator.NotEmpty;
            const value = isValueLessOperator ? null : filter.value;

            columnsFilter.push(
                new SearchExpression({
                    propertyName: name as string,
                    compareOperator: compareOperator,
                    valueToCompare: value
                })
            );
        });

        return columnsFilter.length > 0 ? columnsFilter : undefined;
    }

    private mapOperator(filter: any): string {
        switch (filter) {
            case 'in':
                return 'In';
            case 'equals':
            case 'dateIs':
                return 'Equal';
            case 'notEquals':
            case 'dateIsNot':
                return 'NotEqual';
            case 'gt':
            case 'dateAfter':
                return 'Greater';
            case 'gte':
                return 'GreaterOrEqual';
            case 'lt':
            case 'dateBefore':
                return 'Lesser';
            case 'lte':
                return 'LesserOrEqual';
            case 'notContains':
                return 'NotContains';
            case 'startsWith':
                return 'StartsWith';
            case 'contains':
                return 'Contains';
            case 'endsWith':
                return 'EndsWith';
            case 'empty':
                return 'Empty';
        }

        return 'Like';
    }

    selectedColumnsChange(event: any) {
        this.selectedColumns = structuredClone(event);
    }

    settingsRevertedOrReset(event: any) {
        this.selectedColumns =
            (event?.displayColumnsPropertyNames as InstallationFormsListColumn[]) ?? structuredClone(this.allColumns);
    }

    handleOnColumnReorder(event: any) {
        if (event.columns) {
            this.selectedColumns = structuredClone(event.columns);
        }
    }
}
