import { TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { InstallationFormsClient, SupplyType } from 'src/app/api/installation-forms-client';
import { ApplicationsConfigService } from './applications-config.service';

// Mock UUID to make testing deterministic
jest.mock('uuid', () => ({
    v4: () => 'test-uuid'
}));

describe('ApplicationsConfigService', () => {
    let service: ApplicationsConfigService;
    let mockClient: InstallationFormsClient;

    // Simplified mock response - structure doesn't matter for caching tests
    const mockResponse = {
        result: { someData: 'test-data' }
    };

    beforeEach(() => {
        // Create a simple mock for the client
        mockClient = {
            getApplicationTypesConfiguration: jest.fn().mockReturnValue(of(mockResponse))
        } as Partial<InstallationFormsClient> as InstallationFormsClient;

        TestBed.configureTestingModule({
            providers: [ApplicationsConfigService, { provide: InstallationFormsClient, useValue: mockClient }]
        });

        service = TestBed.inject(ApplicationsConfigService);
        mockClient = TestBed.inject(InstallationFormsClient);
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    it('should call client with correct parameters', () => {
        service.getConfigData('ELECTRICITY');

        expect(mockClient.getApplicationTypesConfiguration).toHaveBeenCalledWith('test-uuid', 'ELECTRICITY' as SupplyType);
    });

    it('should cache the response and only call API once', () => {
        // First call
        service.getConfigData('ELECTRICITY').subscribe();
        expect(mockClient.getApplicationTypesConfiguration).toHaveBeenCalledTimes(1);

        // Second call - should use cached result
        service.getConfigData('ELECTRICITY').subscribe();
        expect(mockClient.getApplicationTypesConfiguration).toHaveBeenCalledTimes(1);
    });

    it('should return the result property from the response', (done) => {
        service.getConfigData('ELECTRICITY').subscribe((result) => {
            expect(result).toBe(mockResponse.result);
            done();
        });
    });
});
