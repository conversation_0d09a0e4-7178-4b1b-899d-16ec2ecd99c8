import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { FormDataService } from 'src/app/core/services/form-data/form-data.service';
import { WidgetWithFormComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget-with-form.component';

@Component({
    selector: 'app-wind-plant-section',
    templateUrl: './wind-plant-section.component.html',
    standalone: false
})
export class WindPlantSectionComponent extends WidgetWithFormComponent {
    @Input() form!: FormGroup;
    @Output() changesMade = new EventEmitter<void>();

    constructor(protected readonly formDataService: FormDataService) {
        super(formDataService);
    }

    onValueChange() {
        this.changesMade.emit();
    }
}
