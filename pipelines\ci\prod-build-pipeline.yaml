name: $(date:yyyyMMdd)$(rev:.r)_$(SourceBranchName)

trigger:
  batch: true
  branches:
    include:
      - "release/*"
    exclude:
      - "master"
      - "temp-release/*"

pool:
  vmImage: "ubuntu-22.04"

variables:
  - name: registryName
    value: "kmdelementsprodeuwacr001"
  - template: ci-variables-template.yaml
  - template: ../global-variables-template.yaml

resources:
  repositories:
  - repository: commonAks
    type: git
    name: COMBAS/KMD.Elements.Pipelines

extends:
  template: ci/templates/build-microfrontend-image-template.yaml@commonAks
  parameters:
    registryName: ${{ variables.registryName }}
    testCommand: $(testCommand)
    testResultPath: $(testResultPath)
    coverageResultPath: $(coverageResultPath)
