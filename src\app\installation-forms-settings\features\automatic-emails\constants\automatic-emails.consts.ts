export const automaticEmailsTabName: string = 'AutomaticEmails';
export const automaticEmailsTranslationPath: string = 'formsSettings.automaticEmails';

export const automaticEmailsMarkdownEnUs: string = `
## Configuration of automatic emails for form management

**Rules:** Adding a rule below will cause the system to automatically send an email to the contact persons email address,
using the selected template, when all criteria defined in the rule are met.

**Trigger:** The form will be setup to send an email with a chosen template when all the below conditions are met in a rule:
- The form allows automatization.
- The form has no problems with the severity type "Error".
- The Connection Point and the primary Meter Frame have been selected on the form.
- No email with the same template has been sent before.
- The form matches every enabled criteria in the rule.
`;

export const automaticEmailsMarkdownDaDK: string = `
## Konfiguration af automatiske e-mails fra blanketter

**Regler:** Tilføjelse af en regel nedenfor vil få systemet til automatisk at sende en e-mail, til kontaktpersonens e-mailadresse,
med en valgt skabelon, når alle kriterier defineret i reglen er opfyldt.

**Trigger:** Blanketten vil blive opsat til at sende en e-mail med en valgt skabelon, når alle nedenstående betingelser i en regel er opfyldt:
- Blanketten tillader automatisering.
- Blanketten har ingen problemer af alvorlighedsgraden "fejl".
- Blanketten har et valgt forbindelsespunkt og en primær målerramme.
- Der er ikke tidligere sendt en e-mail med samme skabelon fra blanketten.
- Blanketten opfylder alle opsatte kriterier fra reglen.
`;
