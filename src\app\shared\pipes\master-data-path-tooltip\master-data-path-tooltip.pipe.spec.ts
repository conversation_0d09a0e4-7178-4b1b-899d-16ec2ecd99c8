import { TestBed } from '@angular/core/testing';
import { TranslateService } from '@ngx-translate/core';
import { MasterDataPathTooltipPipe } from './master-data-path-tooltip.pipe';

describe('MasterDataPathTooltipPipe', () => {
    let pipe: MasterDataPathTooltipPipe;
    let translateService: TranslateService;
    let translateInstantSpy: jest.SpyInstance;

    beforeEach(() => {
        const translateServiceMock = {
            instant: jest.fn((key: string) => {
                if (key === 'masterDataCompare.tooltipPrefix') {
                    return 'Tooltip Prefix:';
                }
                return 'Translated ' + key;
            })
        };

        TestBed.configureTestingModule({
            providers: [MasterDataPathTooltipPipe, { provide: TranslateService, useValue: translateServiceMock }]
        });

        translateService = TestBed.inject(TranslateService);
        translateInstantSpy = jest.spyOn(translateService, 'instant');
        pipe = TestBed.inject(MasterDataPathTooltipPipe);
    });

    it('should create an instance', () => {
        expect(pipe).toBeTruthy();
    });

    it('should fetch the tooltip prefix in constructor', () => {
        expect(translateService.instant).toHaveBeenCalledWith('masterDataCompare.tooltipPrefix');
        expect(pipe['tooltipPrefix']).toBe('Tooltip Prefix:');
    });

    it('should return empty string for undefined path', () => {
        const result = pipe.transform(undefined);
        expect(result).toBe('');
    });

    it('should return empty string for null path', () => {
        const result = pipe.transform(null as any);
        expect(result).toBe('');
    });

    it('should return empty string for empty path', () => {
        const result = pipe.transform('');
        expect(result).toBe('');
    });

    it('should combine prefix with translated path', () => {
        const path = 'somePath';
        const result = pipe.transform(path);

        expect(translateService.instant).toHaveBeenCalledWith('masterDataCompare.path.' + path);
        expect(result).toBe('Tooltip Prefix: Translated masterDataCompare.path.somePath');
    });

    it('should handle different paths correctly', () => {
        const paths = ['path1', 'path2', 'path3'];

        paths.forEach((path) => {
            translateInstantSpy.mockImplementation((key: string) => {
                if (key === 'masterDataCompare.tooltipPrefix') {
                    return 'Tooltip Prefix:';
                }
                if (key === 'masterDataCompare.path.' + path) {
                    return `Translation for ${path}`;
                }
                return 'Translated ' + key;
            });

            const result = pipe.transform(path);
            expect(result).toBe(`Tooltip Prefix: Translation for ${path}`);
        });
    });
});
