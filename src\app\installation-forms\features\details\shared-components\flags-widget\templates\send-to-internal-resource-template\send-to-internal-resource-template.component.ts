import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { AuthorizationService } from '@kmd-elements-ui/authorization';
import { ParentProcessDetails, RelatedObjectType, SendEmailRequest, SendEmailService } from '@kmd-elements/send-message';
import { TranslateService } from '@ngx-translate/core';
import { finalize, map, Subscription, switchMap } from 'rxjs';
import { EmailType, InstallationFormsClient, InternalResourceSettings } from 'src/app/api/installation-forms-client';
import { Permissions } from 'src/app/core/constants/permissions';
import { FormDataService } from 'src/app/core/services/form-data/form-data.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { v4 as uuidv4 } from 'uuid';

@Component({
    selector: 'app-send-to-internal-resource-template',
    templateUrl: './send-to-internal-resource-template.component.html',
    standalone: false
})
export class SendToInternalResourceTemplateComponent implements OnInit, OnDestroy {
    @Input()
    flagControl!: FormGroup;

    isSending = false;

    subscription: Subscription = new Subscription();

    hasUserSendEmailPermission = false;

    constructor(
        private readonly client: InstallationFormsClient,
        private readonly sendEmailService: SendEmailService,
        private readonly formDataService: FormDataService,
        private readonly messageServiceHelper: MessageServiceHelper,
        private readonly translateService: TranslateService,
        private readonly authService: AuthorizationService
    ) {}

    ngOnInit(): void {
        this.authService
            .hasPermissions([
                Permissions.meteringPoints.read,
                Permissions.communicationChannelEmail.read,
                Permissions.communicationChannelEmail.write,
                Permissions.communicationTemplates.read
            ])
            .subscribe((x) => (this.hasUserSendEmailPermission = x));
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    get canBeSendToInternalResource(): boolean {
        return this.hasUserSendEmailPermission && !this.flagControl.value.isSet && this.formDataService.canCurrentUserEditData;
    }

    onSendToInternalResourceClicked() {
        this.isSending = true;
        this.subscription.add(
            this.client
                .getInternalResourceSettings(uuidv4())
                .pipe(
                    map((settings) => this.prepareSendEmailData(settings.result)),
                    switchMap((preparedRequest) => this.sendEmailService.sendEmailData(preparedRequest)),
                    finalize(() => {
                        this.isSending = false;
                    })
                )
                .subscribe({
                    next: (_) => {},
                    error: (_) => {
                        this.messageServiceHelper.showError({
                            detail: this.translateService.instant('flagsWidget.sendToInternalResourceError')
                        });
                    }
                })
        );
    }

    private prepareSendEmailData(settings: InternalResourceSettings): SendEmailRequest {
        return {
            templateId: settings.templateId,
            recipients: [settings.receiver],
            mergeDataObject: this.prepareMergeDataObject(),
            subject: this.replaceInternalResourcePlaceholders(settings.subject, {
                FormNumber: this.formDataService.formNumber,
                FormAddress: this.formDataService.installationAddress?.formattedAddress,
                CaseWorkerName: this.formDataService.caseWorker?.name,
                CaseWorkerEmail: this.formDataService.caseWorker?.email,
                CaseWorkerPhoneNumber: this.formDataService.caseWorker?.phoneNumber
            }),
            relatedObjectId: this.formDataService.formId,
            relatedObjectType: RelatedObjectType.InstallationForm,
            parentProcessDetails: new ParentProcessDetails({
                id: this.formDataService.formId,
                source: 'InstallationForms',
                action: this.formDataService.type?.toString(),
                onBehalfOf: 'InstallationForms'
            }),
            attachments: [],
            autoRedirect: true,
            responseExpected: true,
            customProperties: {
                emailId: uuidv4(),
                emailType: EmailType.InternalResource
            }
        } as SendEmailRequest;
    }

    private replaceInternalResourcePlaceholders(
        subject: string,
        values: {
            FormNumber?: string;
            FormAddress?: string;
            CaseWorkerName?: string;
            CaseWorkerEmail?: string;
            CaseWorkerPhoneNumber?: string;
        }
    ): string {
        const regex = /<(FormNumber|FormAddress|CaseWorkerName|CaseWorkerEmail|CaseWorkerPhoneNumber)>/g;
        return subject.replace(regex, (match, key: keyof typeof values) => values[key] ?? match);
    }

    private prepareMergeDataObject(): any {
        return {
            FormNumber: this.formDataService.formNumber,
            InstallationAddress: this.formDataService.installationAddress?.formattedAddress,
            CaseWorkerName: this.formDataService.caseWorker?.name,
            CaseWorkerEmail: this.formDataService.caseWorker?.email,
            CaseWorkerPhoneNumber: this.formDataService.caseWorker?.phoneNumber
        };
    }
}
