/**
 * Converts falsy values (empty string, 0, false, null, undefined) to null.
 * Returns the original value for all truthy values.
 *
 * @example
 * getValueOrDefault("")        // returns null
 * getValueOrDefault(0)         // returns null
 * getValueOrDefault(false)     // returns null
 * getValueOrDefault(null)      // returns null
 * getValueOrDefault(undefined) // returns null
 * getValueOrDefault("hello")   // returns "hello"
 *
 * @param obj - The value to check
 * @returns The original value if truthy, null otherwise
 */
export function getValueOrDefault(obj: any): any {
    return !!obj ? obj : null;
}
