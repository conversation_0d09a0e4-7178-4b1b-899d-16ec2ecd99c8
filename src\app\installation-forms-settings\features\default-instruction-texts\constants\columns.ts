import { ColumnTransformationType } from 'src/app/core/enums/column-transformation-type';
import { RulesListColumn } from '../../models/rules-list-column';
import { defaultInstructionTextsTranslationPath } from './default-instruction-texts.consts';

export const INSTRUCTION_TEXTS_COLUMNS: RulesListColumn[] = [
    {
        field: 'text',
        header: `${defaultInstructionTextsTranslationPath}.text`,
        columnTransformationType: ColumnTransformationType.None,
        isDefault: true
    }
];
