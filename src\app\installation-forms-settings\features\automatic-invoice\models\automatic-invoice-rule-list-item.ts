import { AutomationVoltageLevel, FormCategory, FormType, InvoiceType, ValueRange } from 'src/app/api/installation-forms-client';

export interface AutomaticInvoiceRuleListItem {
    id: string;
    order: number;
    displayName: string;
    voltageLevels: AutomationVoltageLevel[];
    formTypes: FormType[];
    formCategories?: FormCategory[];
    scopeOfDelivery?: string;
    canTransferInvoiceAutomatically: boolean;
    productionCapacityRange?: string;
    hadExistingProduction?: boolean;
    invoiceType: InvoiceType;
    tariff?: string;
}
