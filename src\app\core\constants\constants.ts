import { SelectItem } from 'primeng/api';

export const applicationRootUrl = '/';

export const forbiddenUrl = '/error/forbidden';

export const AWAITING_STATE_MAX_RETRY_COUNT = 10;

export const AWAITING_STATE_RETRY_TIMEOUT = 5000;

export const NO_VALUE_STRING = '-';

export const PhaseCountOptions: SelectItem[] = [
    { value: 1, label: '1' },
    { value: 2, label: '2' },
    { value: 3, label: '3' }
];

export const AUTOCOMPLETE_ITEMS_LIMIT = 5;

export const SMALL_TABLE_ROWS_PER_PAGE = 3;

export const MEDIUM_TABLE_ROWS_PER_PAGE = 5;

export const LARGE_TABLE_ROWS_PER_PAGE = 10;

export const SETTINGS_DISPLAY_NAME_MAX_LENGTH = 100;

//Value Lists consts
export const VALUE_LIST_CODE_ATTRIBUTE = 'Code';

//Meter transformer VL values
export const METER_TRANSFORMER_OTHER_VALUE = 'Other';

//Voltage level VL values
export const VOLTAGE_LEVEL_B_LOW_VALUE = 'BLow';
export const VOLTAGE_LEVEL_C_VALUE = 'C';

//Meter transformer values
export const METER_TRANSFORMER_OTHER_VALUE_ID = '5a3b1f8d-2e6c-4d7b-9c4e-3a5b2d8f1c7e';

//Voltage level values
export const VOLTAGE_LEVEL_A_HIGH_VALUE_ID = '36b5e382-4c39-4a7a-bb37-333c5b7d6bdd'; //high voltage

export const VOLTAGE_LEVEL_A_LOW_VALUE_ID = 'db36b79e-ab74-4464-aaa9-675a36ce03c6'; //high voltage

export const VOLTAGE_LEVEL_B_HIGH_VALUE_ID = '097d7ed4-edbb-4396-b7cf-f4eaf7ce04f2'; //high voltage

export const VOLTAGE_LEVEL_B_LOW_VALUE_ID = 'c5a693c5-f072-4bba-b862-9aa301745656'; //high voltage

export const VOLTAGE_LEVEL_C_VALUE_ID = 'e63116c7-088b-4c46-b62c-6cdc4c28e261'; //low voltage
