import { Component, Input } from '@angular/core';
import { FormGroup } from '@angular/forms';

@Component({
    selector: 'app-default-flag-template',
    templateUrl: './default-flag-template.component.html',
    standalone: false
})
export class DefaultFlagTemplateComponent {
    @Input()
    flagControl!: FormGroup;

    @Input()
    canBeSetAutomatically!: boolean;

    shouldShowManuallySetDate() {
        const flagValue = this.flagControl?.value;
        return flagValue && this.canBeSetAutomatically && flagValue.manuallySetDate;
    }
}
