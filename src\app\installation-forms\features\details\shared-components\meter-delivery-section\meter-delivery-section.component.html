<div #overlayContainer></div>
<div [formGroup]="form">
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['requestedConnectionDateEod']" for="requestedConnectionDateEod">
                {{ 'meterDeliveryWidget.requestedConnectionDateEod' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <cmbs-calendar
                inputId="requestedConnectionDateEod"
                formControlName="requestedConnectionDateEod"
                [showTime]="false"
                [appendTo]="overlayContainer">
            </cmbs-calendar>
            <small [controlValidationErrors]="form.controls['requestedConnectionDateEod']" class="p-error"></small>
        </div>
    </div>
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['deliveryOption']" for="deliveryOption">
                {{ 'meterDeliveryWidget.deliveryOption' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="deliveryOption"
                [options]="deliveryOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="deliveryOption"></p-select>
            <small [controlValidationErrors]="form.controls['deliveryOption']" class="p-error"></small>
        </div>
    </div>
    <div *ngIf="shouldShowDelivery()">
        <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label [labelRequired]="form.controls['name']" for="name">
                    {{ 'meterDeliveryWidget.deliveryInformation.name' | translate }}
                </label>
            </div>
            <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                <input id="name" type="text" pInputText maxlength="100" formControlName="name" />
                <small [controlValidationErrors]="form.controls['name']" class="p-error"></small>
            </div>
        </div>
        <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label [labelRequired]="form.controls['attention']" for="attention">
                    {{ 'meterDeliveryWidget.deliveryInformation.attention' | translate }}
                </label>
            </div>
            <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                <input id="attention" type="text" pInputText maxlength="100" formControlName="attention" />
                <small [controlValidationErrors]="form.controls['attention']" class="p-error"></small>
            </div>
        </div>
        <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label [labelRequired]="form.controls['meterDeliveryAddress']" for="meterDeliveryAddress">
                    {{ 'meterDeliveryWidget.deliveryInformation.address' | translate }}
                </label>
            </div>
            <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                <app-address-search id="meterDeliveryAddress" formControlName="meterDeliveryAddress"> </app-address-search>
                <small [controlValidationErrors]="form.controls['meterDeliveryAddress']" class="p-error"></small>
            </div>
        </div>
    </div>
</div>
