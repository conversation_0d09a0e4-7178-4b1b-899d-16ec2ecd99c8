<ng-container *ngIf="workOrders && workOrders.length; else emptyMessage"
    ><p-table
        [columns]="columns"
        [value]="workOrders"
        [rowHover]="true"
        responsiveLayout="scroll"
        [paginator]="workOrders.length > rowsPerPage"
        [rows]="rowsPerPage"
        [loading]="loading"
        [customSort]="false"
        [sortField]="'workOrderStarted'"
        [sortOrder]="-1">
        <ng-template pTemplate="header" let-columns>
            <tr>
                <th *ngFor="let col of columns" [pSortableColumn]="col" [id]="col">
                    <div fxLayout="row">
                        {{ 'workOrdersWidget.columnNames.' + col | translate }}
                    </div>
                </th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rowData let-columns="columns">
            <tr class="zebra-item" [ngClass]="{ 'cursor-auto': !isRowClickable(rowData) }" (click)="handleRowClick(rowData)">
                <td>
                    {{ 'enums.workOrderType.' + rowData['workOrderType'] | translate }}
                    <i
                        class="fa-regular fa-circle-info pl-1"
                        aria-hidden="true"
                        [pTooltip]="`${'workOrdersWidget.meterFrameNumber' | translate}: ${rowData['meterFrameNumber']}`"
                        tooltipPosition="top"
                        *ngIf="rowData['workOrderType'] === workOrderRemoveMeter">
                    </i>
                </td>
                <td>{{ rowData['workOrderPurpose'] }}</td>
                <td><app-work-order-status [workOrderStatus]="rowData['state']"></app-work-order-status></td>
                <td>{{ rowData['workOrderStarted'] | formatDateTime }}</td>
            </tr>
        </ng-template>
        <ng-template pTemplate="paginatorright"> </ng-template>
    </p-table>
</ng-container>

<ng-template #emptyMessage>
    <p>{{ 'workOrdersWidget.noWorkOrdersFound' | translate }}</p>
</ng-template>
