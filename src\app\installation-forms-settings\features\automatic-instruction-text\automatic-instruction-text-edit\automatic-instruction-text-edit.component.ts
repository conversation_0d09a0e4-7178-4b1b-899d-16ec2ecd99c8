import { Component } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, Valida<PERSON> } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { finalize, take } from 'rxjs';
import {
    AutomaticInstructionTextRule,
    AutomaticInstructionTextRuleCreateOrUpdate,
    FormCategory,
    FormType,
    InstallationFormsClient
} from 'src/app/api/installation-forms-client';
import { SETTINGS_DISPLAY_NAME_MAX_LENGTH } from 'src/app/core/constants/constants';
import {
    energyProductionFormCategories,
    newInstallationFormCategories
} from 'src/app/core/constants/form-categories-per-form-type';
import { BooleanOptionsService } from 'src/app/core/services/boolean-options/boolean-options.service';
import { FormDataService } from 'src/app/core/services/form-data/form-data.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { isAnyOfRequiredFormTypesSelected } from 'src/app/core/utils/form-type-checkers';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import { v4 as uuidv4 } from 'uuid';
import { BaseRuleEditComponent } from '../../base-components/base-rule-edit.component';
import { DefaultInstructionTextsListItem } from '../../default-instruction-texts/models/default-instruction-texts-list-item';
import {
    automaticInstructionTextTabName,
    automaticInstructionTextTranslationPath
} from '../constants/automatic-instruction-text.consts';
import { FORM_CATEGORY_FORM_TYPES, FORM_TYPES_FOR_INSTRUCTION } from '../constants/properties-per-form-types';

@Component({
    selector: 'app-automatic-instruction-text-edit',
    templateUrl: './automatic-instruction-text-edit.component.html',
    styleUrl: './automatic-instruction-text-edit.component.scss',
    standalone: false
})
export class AutomaticInstructionTextEditComponent extends BaseRuleEditComponent<AutomaticInstructionTextRule> {
    formTypeOptions: SelectItem[] = [];
    formCategoriesOptions: SelectItem[] = [];
    meterNeedsChangeOptions: SelectItem[] = [];
    meterNeedsReconfigurationOptions: SelectItem[] = [];
    formIsScreenedOptions: SelectItem[] = [];
    defaultInstructionTextsOptions: DefaultInstructionTextsListItem[] = [];
    isDuringInstructionTextLoading = false;
    relevantCategories: FormCategory[] = [];

    constructor(
        protected readonly fb: FormBuilder,
        protected readonly translateService: TranslateService,
        protected readonly client: InstallationFormsClient,
        protected readonly messageServiceHelper: MessageServiceHelper,
        protected readonly formDataService: FormDataService,
        protected readonly booleanOptionsService: BooleanOptionsService
    ) {
        super(
            fb,
            translateService,
            messageServiceHelper,
            automaticInstructionTextTabName,
            automaticInstructionTextTranslationPath
        );
        this.initForm();
        this.loadDefaultInstructionTexts();
        this.updateFormCategoriesOptions();
        this.addFormControlsValueChanges();
    }

    override initForm(): void {
        this.form = this.fb.group({
            id: [null, Validators.required],
            displayName: [null, [Validators.required, Validators.maxLength(SETTINGS_DISPLAY_NAME_MAX_LENGTH)]],
            formTypes: [null],
            formCategories: [null],
            meterNeedsChange: [null],
            meterNeedsReconfiguration: [null],
            formIsScreened: [null],
            defaultInstructionTexts: [null, [Validators.required]]
        });
    }

    override getEnumTranslations(): void {
        this.formTypeOptions = enumMapper.map(this.translateService.instant('enums.type'), FORM_TYPES_FOR_INSTRUCTION);
        this.formCategoriesOptions = enumMapper.map(this.translateService.instant('enums.categories'), FormCategory);
    }

    override setFormValue(rule: AutomaticInstructionTextRule) {
        this.form.patchValue({
            id: rule.id,
            displayName: rule.displayName,
            formTypes: rule.formTypes,
            formCategories: rule.formCategories,
            meterNeedsChange: rule.meterNeedsChange,
            meterNeedsReconfiguration: rule.meterNeedsReconfiguration,
            formIsScreened: rule.formIsScreened,
            defaultInstructionTexts: rule.defaultInstructionTexts
        });
    }

    override saveEditedClick() {
        if (this.form.invalid) {
            return;
        }

        const formValue = this.form.value;

        const rule: AutomaticInstructionTextRuleCreateOrUpdate = new AutomaticInstructionTextRuleCreateOrUpdate({
            displayName: formValue.displayName,
            formTypes: formValue.formTypes ?? [],
            formCategories: formValue.formCategories ?? [],
            meterNeedsChange: formValue.meterNeedsChange ?? null,
            meterNeedsReconfiguration: formValue.meterNeedsReconfiguration ?? null,
            formIsScreened: formValue.formIsScreened ?? null,
            defaultInstructionTexts: formValue.defaultInstructionTexts ?? null
        });

        this.isProcessing = true;
        this.subscription.add(
            this.client
                .updateAutomaticInstructionTextRuleById(formValue.id, uuidv4(), rule)
                .pipe(finalize(() => (this.isProcessing = false)))
                .subscribe({
                    next: (response) => {
                        this.ruleEdited.emit(response.result);
                        this.showSuccessMessage();
                    },
                    error: (_) => {
                        this.showErrorMessage(this.widgetTranslations['automaticInstructionTextRuleEditError']);
                    }
                })
        );
    }

    loadDefaultInstructionTexts() {
        this.isDuringInstructionTextLoading = true;
        this.subscription.add(
            this.client
                .getInstructionTexts(uuidv4())
                .pipe(
                    take(1),
                    finalize(() => (this.isDuringInstructionTextLoading = false))
                )
                .subscribe({
                    next: (response) => {
                        this.defaultInstructionTextsOptions = response.result;
                    },
                    error: (_error) => {
                        this.messageServiceHelper.showError({
                            detail: this.widgetTranslations['getInstructionTextsError'],
                            key: this.formDataService.formId!
                        });
                    }
                })
        );
    }

    isFormCategoryRelevant = (): boolean => {
        const formTypes = this.form?.get('formTypes')?.value || [];
        return isAnyOfRequiredFormTypesSelected(formTypes, FORM_CATEGORY_FORM_TYPES);
    };

    addFormControlsValueChanges() {
        this.subscription.add(
            this.form.get('formTypes')?.valueChanges.subscribe(() => {
                this.updateFormCategoriesOptions();
                const selectedFormCategories = this.form.get('formCategories')?.value;
                const filteredFormCategories = selectedFormCategories.filter((category: FormCategory) =>
                    this.relevantCategories.includes(category)
                );
                this.form.get('formCategories')?.setValue(filteredFormCategories);
            })
        );
    }
    updateFormCategoriesOptions() {
        const formTypes = this.form?.get('formTypes')?.value || [];
        this.relevantCategories = [...newInstallationFormCategories, ...energyProductionFormCategories];

        if (formTypes.length !== 0 && !formTypes.includes(FormType.NewInstallation)) {
            this.relevantCategories = this.relevantCategories.filter(
                (category) => !newInstallationFormCategories.includes(category)
            );
        }
        if (formTypes.length !== 0 && !formTypes.includes(FormType.EnergyProduction)) {
            this.relevantCategories = this.relevantCategories.filter(
                (category) => !energyProductionFormCategories.includes(category)
            );
        }

        this.formCategoriesOptions = enumMapper.map(this.translateService.instant('enums.category'), this.relevantCategories);
    }
}
