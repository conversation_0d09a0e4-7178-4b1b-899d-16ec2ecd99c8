import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
    providedIn: 'root'
})
export class TranslateServiceHelper {
    constructor(private readonly translateService: TranslateService) {}

    public getTranslationOrNull(translationKey: string, substitutions: object | undefined = undefined): string | null {
        return this.hasTranslation(translationKey) ? this.translateService.instant(translationKey, substitutions) : null;
    }

    public hasTranslation(translationKey: string): boolean {
        const translation: object | string = this.translateService.translations[this.translateService.currentLang];
        const value = translationKey.split('.').reduce((translation: any, current) => translation?.[current], translation);
        return value !== undefined;
    }
}
