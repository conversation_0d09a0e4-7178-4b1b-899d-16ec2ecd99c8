<div #overlayContainer></div>
<div [formGroup]="form">
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label for="reasonForChange">
                {{ 'moveMeterWidget.reasonForChange' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="reasonForChange"
                [options]="reasonForChangeOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                [showClear]="true"
                formControlName="reasonForChange">
            </p-select>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['meterPlacementId']" for="meterPlacementId">
                {{ 'technicalInformationWidget.meterType.meterPlacementId' | translate }}
            </label>
            <app-icon-master-data-path [path]="formDataService.masterDataToCompareResult?.meterPlacementId?.path">
            </app-icon-master-data-path>
        </div>
        <div class="zebra-edit" [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70" fxFlex.lt-md="50">
            <cmbs-value-list-dropdown
                id="meterPlacementId"
                formControlName="meterPlacementId"
                [valueListType]="meterFramePlacementValueListType"
                [messageKey]="this.formDataService.formId"
                [valueList]="valueListsService.getValueList(meterFramePlacementValueListType) | async">
            </cmbs-value-list-dropdown>
            <small [controlValidationErrors]="form.controls['meterPlacementId']" class="p-error"></small>
        </div>
        <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields">
            <input
                id="meterPlacementIdMD"
                type="text"
                pInputText
                [value]="
                    formDataService.masterDataToCompareResult?.meterPlacementId?.value ?? ''
                        | valueListSingleItem: meterFramePlacementValueListType
                        | async
                "
                [disabled]="true" />
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['responsibleForSeal']" for="responsibleForSeal">
                {{ 'sealWidget.responsibleForSeal' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="responsibleForSeal"
                [options]="responsibleForSealOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="responsibleForSeal">
            </p-select>
            <small [controlValidationErrors]="form.controls['responsibleForSeal']" class="p-error"></small>
        </div>
    </div>
</div>
<app-meter-type-section [form]="meterTypeForm" nextZebraContainer></app-meter-type-section>
