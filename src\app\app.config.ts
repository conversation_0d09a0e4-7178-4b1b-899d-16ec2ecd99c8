import { ApplicationConfig, provideZoneChangeDetection } from '@angular/core';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideRouter } from '@angular/router';
import { provideElementsLocalDevelopmentHost } from '@kmd-elements-ui/host-sdk';
import { appRoutes } from './app.routes';

export const appConfig: ApplicationConfig = {
    providers: [
        provideZoneChangeDetection({ eventCoalescing: true }),
        provideRouter(appRoutes),
        provideAnimations(),
        provideElementsLocalDevelopmentHost({
            appConfig: {
                Frontend: { Version: '0.0.1', LogLevel: 5, UseTelemetry: false, InstrumentationKey: '' },
                Environment: {
                    WebApp: {
                        SystemManagementBff: { Url: '/api/system-management-bff' },
                        AuthorizationBff: { Url: '/api/authorization-bff' }
                    }
                },
                featureToggleConfigurations: [],
                microfrontends: {}
            },
            menuItems: [
                {
                    label: 'Installation Forms',
                    icon: 'fa fa-map-marker',
                    routerLink: '/'
                },
                {
                    label: 'Installation Forms Settings',
                    icon: 'fa fa-map-marker',
                    routerLink: '/settings'
                }
            ],
            title: 'Installation Forms'
        })
    ]
};
