<div #overlayContainer></div>
<ng-container [formGroup]="form">
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['installationAddress']" for="installationAddress">
                {{ 'installationWidget.installationAddress.title' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <app-address-search
                id="installationAddress"
                formControlName="installationAddress"
                (newAddressSelected)="newAddressSelected()">
            </app-address-search>
            <small [controlValidationErrors]="form.controls['installationAddress']" class="p-error"></small>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px" *ngIf="showGridArea">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['gridAreaId']" for="gridAreaId">
                {{ 'installationWidget.gridAreaId.title' | translate }}
            </label>
            <app-icon-master-data-path [path]="formDataService.masterDataToCompareResult?.gridAreaId?.path">
            </app-icon-master-data-path>
        </div>
        <div class="zebra-edit" [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70" fxFlex.lt-md="50">
            <p-select
                id="gridAreaId"
                [options]="gridAreasOptions"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                [dropdownIcon]="isGridAreasOptionsLoading ? 'pi pi-spinner pi-spin' : 'pi pi-chevron-down'"
                formControlName="gridAreaId"
                (onShow)="onGridAreasDropdownShow()"
                (onHide)="onGridAreasDropdownHide()"
                (onChange)="onGridAreaChange()">
            </p-select>
            <small [controlValidationErrors]="form.controls['gridAreaId']" class="p-error"></small>
        </div>
        <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields">
            <input
                id="gridAreaIdMD"
                type="text"
                pInputText
                [value]="formDataService.masterDataToCompareResult?.gridAreaId?.value ?? ''"
                [disabled]="true" />
        </div>
    </div>

    <div
        class="zebra-item"
        fxLayout="row"
        fxLayout.lt-sm="column"
        fxLayoutGap="15px"
        *ngIf="!!form.get('installationAddress')?.value?.id">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['connectionPoint']" for="connectionPoint">
                {{ 'installationWidget.connectionPoint.title' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <div fxLayoutGap="0.5rem" fxLayout="row">
                <div fxFlex>
                    <p-select
                        id="connectionPoint"
                        [options]="connectionPointsOptions"
                        [appendTo]="overlayContainer"
                        [placeholder]="'common.selectValue' | translate"
                        [dropdownIcon]="isConnectionPointsOptionsLoading ? 'pi pi-spinner pi-spin' : 'pi pi-chevron-down'"
                        formControlName="connectionPoint"
                        (onShow)="onConnectionPointsDropdownShow()"
                        (onHide)="onConnectionPointsDropdownHide()"
                        (onChange)="onConnectionPointChange()">
                    </p-select>

                    <small [controlValidationErrors]="form.controls['connectionPoint']" class="p-error"></small>
                </div>
                <div *ngIf="!form.get('connectionPoint')?.value?.id" fxFlex="nogrow" fxLayoutAlign="start center">
                    <p-tag severity="warn" value="{{ 'installationWidget.connectionPoint.notSelectedWarning' | translate }}">
                    </p-tag>
                </div>
                <div *ngIf="!!form.get('connectionPoint')?.value?.id" fxFlex="nogrow" fxLayoutAlign="start center">
                    <a class="add-point" (click)="navigateToConnectionPoint()"
                        ><i class="fa-solid fa-up-right-from-square"></i
                    ></a>
                </div>
            </div>
        </div>
    </div>

    <div
        class="zebra-item"
        fxLayout="row"
        fxLayout.lt-sm="column"
        fxLayoutGap="15px"
        *ngIf="!!form.get('connectionPoint')?.value?.id">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label for="meterFrame">{{ 'installationWidget.meterFrame.title' | translate }} </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <div fxLayoutGap="0.5rem" fxLayout="row">
                <div fxFlex>
                    <p-select
                        id="meterFrame"
                        [options]="meterFramesOptions"
                        [appendTo]="overlayContainer"
                        [placeholder]="'common.selectValue' | translate"
                        [dropdownIcon]="isMeterFramesOptionsLoading ? 'pi pi-spinner pi-spin' : 'pi pi-chevron-down'"
                        formControlName="meterFrame"
                        (onShow)="onMeterFramesDropdownShow()"
                        (onHide)="onMeterFramesDropdownHide()">
                    </p-select>
                    <small [controlValidationErrors]="form.controls['meterFrame']" class="p-error"></small>
                </div>
                <div *ngIf="!form.get('meterFrame')?.value?.id" fxFlex="nogrow" fxLayoutAlign="start center">
                    <p-tag severity="warn" value="{{ 'installationWidget.meterFrame.notSelectedWarning' | translate }}"> </p-tag>
                </div>
                <div *ngIf="!!form.get('meterFrame')?.value?.id" fxFlex="nogrow" fxLayoutAlign="start center">
                    <a class="add-point" (click)="navigateToMeterFrame()"><i class="fa-solid fa-up-right-from-square"></i></a>
                </div>
            </div>
        </div>
    </div>
    <div
        class="zebra-item"
        fxLayout="row"
        fxLayout.lt-sm="column"
        fxLayoutGap="15px"
        *ngIf="!!form.get('connectionPoint')?.value?.id">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label for="consumptionMeteringPointId">{{
                'installationWidget.consumptionMeteringPointId.title' | translate
            }}</label>
        </div>

        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <div fxLayoutGap="0.5rem" fxLayout="row">
                <div fxFlex>
                    <input
                        id="consumptionMeteringPointId"
                        type="text"
                        pInputText
                        maxlength="100"
                        [disabled]="true"
                        value="{{ form.get('consumptionMeteringPoint')?.value?.meteringPointId || '' }}" />
                    <small [controlValidationErrors]="form.controls['consumptionMeteringPointId']" class="p-error"></small>
                </div>
                <div
                    *ngIf="shouldDisplayConsumptionNoMatchingMeteringPointsWarning()"
                    fxFlex="nogrow"
                    fxLayoutAlign="start center">
                    <p-tag severity="warn" value="{{ 'installationWidget.noMatchingMeteringPointsWarning' | translate }}">
                    </p-tag>
                </div>
                <div *ngIf="shouldDisplayConsumptionMultipleMeteringPointsWarning()" fxFlex="nogrow" fxLayoutAlign="start center">
                    <p-tag severity="warn" value="{{ 'installationWidget.multipleMeteringPointsWarning' | translate }}"> </p-tag>
                </div>
                <div
                    *ngIf="!!form.get('consumptionMeteringPoint')?.value?.meteringPointVersionId"
                    fxFlex="nogrow"
                    fxLayoutAlign="start center">
                    <a
                        class="add-point"
                        (click)="
                            navigateToMeteringPoint(this.form.get('consumptionMeteringPoint')?.value?.meteringPointVersionId)
                        "
                        ><i class="fa-solid fa-up-right-from-square"></i
                    ></a>
                </div>
            </div>
        </div>
    </div>
    <div
        class="zebra-item"
        fxLayout="row"
        fxLayout.lt-sm="column"
        fxLayoutGap="15px"
        *ngIf="!!productionMeteringPointType && !!form.get('connectionPoint')?.value?.id">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label for="productionMeteringPointId">{{ 'installationWidget.productionMeteringPointId.title' | translate }}</label>
        </div>

        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <div fxLayoutGap="0.5rem" fxLayout="row">
                <div fxFlex>
                    <input
                        id="productionMeteringPointId"
                        type="text"
                        pInputText
                        maxlength="100"
                        [disabled]="true"
                        value="{{ form.get('productionMeteringPoint')?.value?.meteringPointId || '' }}" />
                    <small [controlValidationErrors]="form.controls['productionMeteringPointId']" class="p-error"></small>
                </div>
                <div
                    *ngIf="shouldDisplayProductionNoMatchingMeteringPointsWarning()"
                    fxFlex="nogrow"
                    fxLayoutAlign="start center">
                    <p-tag severity="warn" value="{{ 'installationWidget.noMatchingMeteringPointsWarning' | translate }}">
                    </p-tag>
                </div>
                <div *ngIf="shouldDisplayProductionMultipleMeteringPointsWarning()" fxFlex="nogrow" fxLayoutAlign="start center">
                    <p-tag severity="warn" value="{{ 'installationWidget.multipleMeteringPointsWarning' | translate }}"> </p-tag>
                </div>
                <div
                    *ngIf="!!form.get('productionMeteringPoint')?.value?.meteringPointVersionId"
                    fxFlex="nogrow"
                    fxLayoutAlign="start center">
                    <a
                        class="add-point"
                        (click)="navigateToMeteringPoint(this.form.get('productionMeteringPoint')?.value?.meteringPointVersionId)"
                        ><i class="fa-solid fa-up-right-from-square"></i
                    ></a>
                </div>
            </div>
        </div>
    </div>
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label for="remarksToInstallation">{{ 'installationWidget.remarksToInstallation' | translate }}</label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <textarea
                id="remarksToInstallation"
                pTextarea
                maxlength="1000"
                [autoResize]="true"
                formControlName="remarksToInstallation"></textarea>
        </div>
    </div>
    <div *ngIf="durationType" class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label for="temporaryInstallationDuration">{{
                'installationWidget.temporaryInstallationDuration' | translate
            }}</label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input
                id="temporaryInstallationDuration"
                type="text"
                pInputText
                [disabled]="true"
                [value]="'enums.temporaryInstallationDurationType.' + durationType | translate" />
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label for="tags">{{ 'installationWidget.tags' | translate }}</label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <app-value-list-multiselect
                id="tags"
                formControlName="tags"
                [valueListType]="tagsValueListType"
                [appendTo]="overlayContainer">
            </app-value-list-multiselect>
        </div>
    </div>
</ng-container>
