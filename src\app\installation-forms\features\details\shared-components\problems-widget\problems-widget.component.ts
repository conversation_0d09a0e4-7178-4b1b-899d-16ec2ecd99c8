import { Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { finalize, map, Subscription } from 'rxjs';
import { FormProblem, FormProblemSeverity, InstallationFormsClient } from 'src/app/api/installation-forms-client';
import { FormDataService } from 'src/app/core/services/form-data/form-data.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { v4 as uuidv4 } from 'uuid';
import { GroupedTranslatedFormErrors } from './grouped-translated-form-errors.model';
import { TranslatedFormProblem } from './translated-form-problem.model';

@Component({
    selector: 'app-problems-widget',
    templateUrl: './problems-widget.component.html',
    styleUrls: ['./problems-widget.component.scss'],
    standalone: false
})
export class ProblemsWidgetComponent implements OnInit, OnD<PERSON>roy {
    private _allProblems: FormProblem[] = [];
    public errors: GroupedTranslatedFormErrors[] = [];
    public warnings: GroupedTranslatedFormErrors[] = [];
    public infos: GroupedTranslatedFormErrors[] = [];
    public acknowledged: TranslatedFormProblem[] = [];
    public errorsCount = 0;
    public warningsCount = 0;
    public infosCount = 0;

    widgetTranslations: any;

    isProcessing = false;

    subscription: Subscription = new Subscription();

    attemptedAction: (() => void) | null = null;

    constructor(
        private readonly translateService: TranslateService,
        private readonly client: InstallationFormsClient,
        private readonly formDataService: FormDataService,
        protected messageServiceHelper: MessageServiceHelper
    ) {
        this.groupAndTranslateProblems();
    }

    ngOnInit(): void {
        this.widgetTranslations = this.translateService.instant('problemsWidget');
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    @Input()
    set problems(problems: FormProblem[]) {
        this._allProblems = problems;
        this.groupAndTranslateProblems();
    }

    get problems(): FormProblem[] {
        return this._allProblems;
    }

    protected markProblemAsAcknowledgedConfirmation(id: string) {
        this.attemptedAction = () => this.markProblemAsAcknowledged(id);
    }

    onConfirmationDialogYesButtonClick() {
        if (this.attemptedAction) {
            this.attemptedAction();
        }
        this.attemptedAction = null;
    }

    onConfirmationDialogNoButtonClick() {
        this.attemptedAction = null;
    }

    public markProblemAsAcknowledged(id: string) {
        this.isProcessing = true;
        this.subscription.add(
            this.client
                .markFormProblemAsAcknowledged(this.formDataService.formId!, id, uuidv4())
                .pipe(
                    finalize(() => {
                        this.isProcessing = false;
                    }),
                    map((x) => x.result)
                )
                .subscribe({
                    next: (x) => {
                        this.problems = this.problems.map((problem) => (problem.id === id ? x : problem));
                    },
                    error: () => {
                        this.messageServiceHelper.showError({
                            detail: this.widgetTranslations['markAsAcknowledgedError'],
                            key: this.formDataService.formId!
                        });
                    }
                })
        );
    }

    private groupAndTranslateProblems() {
        this.addTranslatedProblemDetails();

        // Group problems by relationId
        const errors = this.problems
            .filter((x) => x.severity === FormProblemSeverity.Error && !x.isAcknowledged)
            .map((error) => this.createTranslatedProblem(error));

        this.errorsCount = errors.length;
        this.errors = this.groupProblemsByRelationId(errors);

        const warnings = this.problems
            .filter((x) => x.severity === FormProblemSeverity.Warning && !x.isAcknowledged)
            .map((warning) => this.createTranslatedProblem(warning));

        this.warningsCount = warnings.length;
        this.warnings = this.groupProblemsByRelationId(warnings);

        const infos = this.problems
            .filter((x) => x.severity === FormProblemSeverity.Info && !x.isAcknowledged)
            .map((info) => this.createTranslatedProblem(info));

        this.infosCount = infos.length;
        this.infos = this.groupProblemsByRelationId(infos);

        this.acknowledged = this.problems
            .filter((x) => x.isAcknowledged)
            .sort((a, b) => (b.acknowledgedTimestamp?.getTime() ?? 0) - (a.acknowledgedTimestamp?.getTime() ?? 0))
            .map((acknowledged) => this.createTranslatedProblem(acknowledged));
    }

    private groupProblemsByRelationId(problems: TranslatedFormProblem[]): GroupedTranslatedFormErrors[] {
        const grouped = new Map<string | null, TranslatedFormProblem[]>();

        problems.forEach((problem) => {
            const relationId = problem.relationId || null;
            if (!grouped.has(relationId)) {
                grouped.set(relationId, []);
            }
            grouped.get(relationId)!.push(problem);
        });

        return [...grouped].map(([relationId, problems]) => ({
            relationId,
            problems
        }));
    }

    private createTranslatedProblem(problem: FormProblem): TranslatedFormProblem {
        return {
            id: problem.id,
            translatedProblem: this.translateService.instant('problemCodes.' + problem.code, problem.details),
            acknowledgedBy: problem.acknowledgedBy,
            acknowledgedTimestamp: problem.acknowledgedTimestamp,
            relationId: problem.relationId
        };
    }

    private addTranslatedProblemDetails() {
        const substitutionsRegex = /\{\{(?:\[([^[\]]*)\])([^}]*)\}\}/g;

        this.problems.forEach((problem) => {
            if (Object.keys(problem.details).length == 0) return;

            const problemTextWithoutSubstitutions = this.translateService.instant('problemCodes.' + problem.code);

            let substitutionMatches;
            while ((substitutionMatches = substitutionsRegex.exec(problemTextWithoutSubstitutions)) !== null) {
                const translationPath = substitutionMatches[1];
                const substitutionValueKey = substitutionMatches[2];

                const substitutionKey = `[${translationPath}]${substitutionValueKey}`;

                const substitutionValue = problem.details[substitutionValueKey];
                const fullTranslationPath = translationPath ? translationPath + '.' + substitutionValue : substitutionValue;
                const translatedSubstitutionValue = this.translateService.instant(fullTranslationPath);

                problem.details[substitutionKey] = translatedSubstitutionValue;
            }
        });
    }
}
