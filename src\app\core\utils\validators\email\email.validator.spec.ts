import { FormControl } from '@angular/forms';
import { EmailValidator } from './email.validator';

describe('EmailValidator', () => {
    it('should return null for valid email addresses', () => {
        const validator = EmailValidator();
        const validEmails = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            // Test Nordic characters as mentioned in the regex
            'testæøå@example.com',
            'test@exampleæøå.com',
            'test.ÆØÅ@example.com'
        ];

        validEmails.forEach((email) => {
            const control = new FormControl(email);
            expect(validator(control)).toBeNull();
        });
    });

    it('should return error for invalid email addresses', () => {
        const validator = EmailValidator();
        const invalidEmails = [
            'test',
            'test@',
            '@example.com',
            'test@example',
            'test@.com',
            'test@example.',
            'test@exam ple.com',
            'test@exam\nple.com'
        ];

        invalidEmails.forEach((email) => {
            const control = new FormControl(email);
            expect(validator(control)).toEqual({ emailFormatError: true });
        });
    });

    it('should return null when control value is null or empty', () => {
        const validator = EmailValidator();
        const control1 = new FormControl(null);
        const control2 = new FormControl('');

        expect(validator(control1)).toBeNull();
        expect(validator(control2)).toBeNull();
    });
});
