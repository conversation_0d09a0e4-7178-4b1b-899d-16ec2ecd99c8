import { AbstractControl, ValidationErrors, Validators } from '@angular/forms';

export default function conditionallyRequiredValidator(
    control: AbstractControl,
    shouldBeRequired: () => boolean,
    complexObjectValueExtractionFunc?: (control: AbstractControl) => any
): ValidationErrors | null {
    if (shouldBeRequired()) {
        if (complexObjectValueExtractionFunc) {
            const val = complexObjectValueExtractionFunc(control);
            if (!val) {
                return {
                    required: true
                };
            }

            return null;
        }
        return Validators.required(control);
    }
    return null;
}
