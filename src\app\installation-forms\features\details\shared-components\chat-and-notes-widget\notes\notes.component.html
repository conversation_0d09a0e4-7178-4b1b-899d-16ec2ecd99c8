<p-scrollPanel styleClass="chat-scroll-panel" #scrollPanel>
    <div class="pr-20 pb-20">
        <form [formGroup]="notesForm">
            <div formArrayName="notes">
                <div class="flex flex-column gap-2 pr-3" *ngIf="notesFormControls.length > 0; else emptyMessage">
                    <ng-container *ngFor="let note of notesFormControls; let i = index" [formGroupName]="i">
                        <div class="flex justify-content-end">
                            <p-card [header]="note.get('author')?.value" class="flex note">
                                <ng-container *ngIf="!(note.get('id')?.value === currentlyEditedNoteId); else editMode">
                                    <p class="break-word" [innerText]="note.get('content')?.value"></p>
                                </ng-container>
                                <ng-template #editMode>
                                    <textarea
                                        pTextarea
                                        [autoResize]="true"
                                        [maxlength]="1000"
                                        formControlName="content"></textarea>
                                </ng-template>
                                <div class="flex align-items-center justify-content-between mt-2">
                                    <h4>{{ note.get('timestamp')?.value | formatDateTime }}</h4>
                                    <div class="flex">
                                        <ng-container
                                            *ngIf="!(note.get('id')?.value === currentlyEditedNoteId); else editButtons">
                                            <button
                                                [disabled]="isProcessing || currentlyEditedNoteId"
                                                pButton
                                                icon="fa-solid fa-pen"
                                                class="p-button-outlined border-none"
                                                (click)="startModifyNote(note)"></button>
                                            <button
                                                [disabled]="isProcessing || currentlyEditedNoteId"
                                                pButton
                                                icon="fa-solid fa-trash-can"
                                                class="p-button-outlined border-none"
                                                (click)="deleteNote($event, note)"></button>
                                        </ng-container>
                                        <ng-template #editButtons>
                                            <button
                                                [disabled]="isProcessing"
                                                pButton
                                                icon="fa-solid fa-xmark"
                                                class="p-button-outlined border-none py-1"
                                                (click)="cancelModifyNote(note)"></button>
                                            <button
                                                [disabled]="isProcessing"
                                                pButton
                                                icon="fa-solid fa-check"
                                                class="p-button-outlined border-none py-1"
                                                (click)="modifyNote(note)"></button>
                                        </ng-template>
                                    </div>
                                </div>
                            </p-card>
                        </div>
                    </ng-container>
                </div>
            </div>
        </form>
    </div>
</p-scrollPanel>

<div>
    <p-divider />
    <div class="flex flex-row gap-2" [formGroup]="form">
        <textarea
            pTextarea
            class="add-chat-or-note-textarea"
            [autoResize]="true"
            [maxlength]="1000"
            formControlName="content"
            [rows]="1"></textarea>
        <div>
            <button
                id="sendChatMessageButton"
                type="button"
                pButton
                pRipple
                [disabled]="isProcessing || formNoteIsEmpty()"
                class="p-button-primary"
                (click)="createNewNote()">
                <span *ngIf="isProcessing" class="fa fa-spin fa-spinner"></span>
                {{ 'chatAndNotesWidget.notes.add' | translate }}
            </button>
        </div>
    </div>
</div>

<app-confirmation-dialog
    [headerKey]="'chatAndNotesWidget.notes.deleteConfirmationTitle'"
    [messageKey]="'chatAndNotesWidget.notes.deleteConfirmationMessage'"
    [visible]="!!attemptedAction"
    (confirmEvent)="onConfirmationDialogYesButtonClick()"
    (cancelEvent)="onConfirmationDialogNoButtonClick()">
</app-confirmation-dialog>

<ng-template #emptyMessage>
    <p class="mt-20">{{ 'chatAndNotesWidget.notes.noNotesFound' | translate }}</p>
</ng-template>
