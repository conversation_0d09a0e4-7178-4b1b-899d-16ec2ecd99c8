import { TestBed } from '@angular/core/testing';
import {
    AddressLookup,
    ContactPerson,
    Installer,
    NewInstallation,
    Payer,
    PayerType
} from 'src/app/api/installation-forms-client';
import { createGenericMock } from '../../utils/mocks/create-generic-mock';
import { FormDataService } from './form-data.service';

describe('FormDataService', () => {
    let service: FormDataService;

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [FormDataService]
        });

        service = TestBed.inject(FormDataService);
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    it('should have installation contact details ', () => {
        const testForm = createGenericMock<NewInstallation>({
            contactPerson: new ContactPerson({
                companyName: undefined,
                name: 'Installation contact name',
                email: '<EMAIL>',
                phoneNumber: '12312313'
            })
        });
        service.formLoaded(testForm);

        expect(service.installationContact).toEqual({
            companyName: undefined,
            name: 'Installation contact name',
            email: '<EMAIL>',
            phoneNumber: '12312313'
        });
    });

    it('should return installer main contact details even when secondary contact is supplied', () => {
        const testForm = createGenericMock<NewInstallation>({
            installer: new Installer({
                companyName: 'KMD-DEV-INST-1',
                companyEmail: '<EMAIL>',
                companyPhoneNumber: '12345897',
                cvr: '26911745',
                companyAuthorizationNumber: 'EFUL-13064',
                secondaryContact: new ContactPerson({
                    name: 'Installer secondary contact name',
                    email: '<EMAIL>',
                    phoneNumber: '12312312'
                }),
                name: 'Inst Dev1',
                email: '<EMAIL>',
                phoneNumber: '12345678'
            })
        });
        service.formLoaded(testForm);

        expect(service.installerContact).toEqual({
            name: 'Inst Dev1',
            email: '<EMAIL>',
            phoneNumber: '12345678'
        });
    });

    it('should return installer private contact details when not empty', () => {
        const testForm = createGenericMock<NewInstallation>({
            installer: new Installer({
                companyName: 'KMD-DEV-INST-1',
                companyEmail: '<EMAIL>',
                companyPhoneNumber: '12345897',
                cvr: '26911745',
                companyAuthorizationNumber: 'EFUL-13064',
                name: 'Inst Dev1',
                email: '<EMAIL>',
                phoneNumber: '12345678'
            })
        });
        service.formLoaded(testForm);

        expect(service.installerContact).toEqual({
            name: 'Inst Dev1',
            email: '<EMAIL>',
            phoneNumber: '12345678'
        });
    });

    it('should return installer company details when no private data', () => {
        const testForm = createGenericMock<NewInstallation>({
            installer: new Installer({
                companyName: 'KMD-DEV-INST-1',
                companyEmail: '<EMAIL>',
                companyPhoneNumber: '12345897',
                cvr: '26911745',
                companyAuthorizationNumber: 'EFUL-13064',
                name: undefined,
                email: undefined,
                phoneNumber: undefined
            })
        });
        service.formLoaded(testForm);

        expect(service.installerContact).toEqual({
            name: 'KMD-DEV-INST-1',
            email: '<EMAIL>',
            phoneNumber: '12345897'
        });
    });

    it('should return payer address is valid when it is correct car id', () => {
        const testForm = createGenericMock<NewInstallation>({
            payer: new Payer({
                address: new AddressLookup({
                    carId: '12345678'
                }),
                type: PayerType.Private,
                name: 'Payer Dev1'
            })
        });
        service.formLoaded(testForm);

        expect(service.hasValidPayerAddress).toBeTruthy();
    });

    it('should return payer address is not valid when it has no car id', () => {
        const testForm = createGenericMock<NewInstallation>({
            payer: new Payer({
                address: new AddressLookup({}),
                type: PayerType.Private,
                name: 'Payer Dev1'
            })
        });
        service.formLoaded(testForm);

        expect(service.hasValidPayerAddress).toBeFalsy();
    });
});
