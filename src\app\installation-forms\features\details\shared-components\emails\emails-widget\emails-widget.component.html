<div #overlayContainer></div>
<p-panel [header]="'emailsWidget.title' | translate" [toggleable]="true">
    <ng-template pTemplate="icons">
        <button
            type="button"
            pButton
            pRipple
            class="p-button-outlined"
            *ngIf="showSendEmailButton()"
            [disabled]="isProcessing"
            (click)="openSendEmail()"
            size="small">
            {{ 'emailsWidget.create' | translate }}
        </button>
    </ng-template>

    <app-send-email
        *ngIf="activeAction.sendEmail"
        [templates]="templateOptions"
        (closePanelRequested)="onClosePanelRequested()"
        (emailSent)="onEmailSent()"></app-send-email>

    <app-emails-list-widget *ngIf="hasUserReadEmailPermission"> </app-emails-list-widget>
</p-panel>
