import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { Subscription } from 'rxjs';
import { ValueListsService } from 'src/app/core/services/value-lists/value-lists.service';
import { WidgetWithFormComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget-with-form.component';
import { ResponsibleForSeal, ValueListType } from '../../../../../api/installation-forms-client';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';
import { enumMapper } from '../../../../../core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';

@Component({
    selector: 'app-change-branch-line-section',
    templateUrl: './change-branch-line-section.component.html',
    styleUrls: ['./change-branch-line-section.component.scss'],
    standalone: false
})
export class ChangeBranchLineSectionComponent extends WidgetWithFormComponent implements OnInit, OnDestroy {
    @Input() form!: FormGroup;

    subscription: Subscription = new Subscription();
    responsibleForSealOptions: SelectItem[] = [];
    readonly meterFramePlacementValueListType: ValueListType = ValueListType.MeterFramePlacementCode;

    constructor(
        readonly formDataService: FormDataService,
        private readonly translateService: TranslateService,
        protected readonly valueListsService: ValueListsService
    ) {
        super(formDataService);
    }

    ngOnInit(): void {
        this.responsibleForSealOptions = enumMapper.map(
            this.translateService.instant('enums.responsibleForSeal'),
            ResponsibleForSeal
        );
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }
}
