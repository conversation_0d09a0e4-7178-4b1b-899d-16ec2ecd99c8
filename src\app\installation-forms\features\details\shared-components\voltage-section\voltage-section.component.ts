import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { SelectItem } from 'primeng/api';
import { Subscription, pairwise, startWith } from 'rxjs';
import { ValueListType } from 'src/app/api/installation-forms-client';
import { VOLTAGE_LEVEL_B_LOW_VALUE_ID, VOLTAGE_LEVEL_C_VALUE_ID } from 'src/app/core/constants/constants';
import { ValueListsService } from 'src/app/core/services/value-lists/value-lists.service';
import { WidgetWithFormComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget-with-form.component';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';

@Component({
    selector: 'app-voltage-section',
    templateUrl: './voltage-section.component.html',
    standalone: false
})
export class VoltageSectionComponent extends WidgetWithFormComponent implements OnIni<PERSON>, OnD<PERSON>roy {
    @Input() form!: FormGroup;

    voltageLevelSubcategories: SelectItem[] = [];
    subscription: Subscription = new Subscription();
    readonly meterFrameElectricityTarifConnectionPointFormsValuesValueListType: ValueListType =
        ValueListType.MeterFrameElectricityTarifConnectionPointFormsValues;
    readonly meterFrameElectricityTarifConnectionPointValueListType: ValueListType =
        ValueListType.MeterFrameElectricityTarifConnectionPoint;
    readonly currentTransformerValueListType: ValueListType = ValueListType.MeterFrameElectricityAttributesRatioCT;
    readonly voltageTransformerValueListType: ValueListType = ValueListType.MeterFrameElectricityAttributesRatioVT;

    constructor(
        protected readonly formDataService: FormDataService,
        protected readonly valueListsService: ValueListsService
    ) {
        super(formDataService);
    }

    ngOnInit(): void {
        this.subscription.add(
            this.form.valueChanges.pipe(startWith(null), pairwise()).subscribe(([prevFormValue, currentFormValue]) => {
                let shouldUpdateForm = false;

                if (currentFormValue?.currentTransformerId !== prevFormValue?.currentTransformerId) {
                    shouldUpdateForm = true;
                }

                if (currentFormValue?.voltageTransformerId !== prevFormValue?.voltageTransformerId) {
                    shouldUpdateForm = true;
                }

                if (currentFormValue?.voltageLevelId !== prevFormValue?.voltageLevelId) {
                    if (!this.isCurrentTransformerIncluded(currentFormValue)) {
                        this.form.patchValue({
                            currentTransformerId: null
                        });
                    }

                    if (!this.isVoltageTransformerIncluded(currentFormValue)) {
                        this.form.patchValue({
                            voltageTransformerId: null
                        });
                    }

                    shouldUpdateForm = true;
                }

                if (shouldUpdateForm) {
                    this.form.updateValueAndValidity();
                }
            })
        );
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    isCurrentTransformerIncluded = (overridenFormValue: any = null) => {
        const formValue = overridenFormValue || this.form.value;
        const voltageLevelId = formValue.voltageLevelId;
        return voltageLevelId && voltageLevelId !== VOLTAGE_LEVEL_C_VALUE_ID;
    };

    isVoltageTransformerIncluded = (overridenFormValue: any = null) => {
        const formValue = overridenFormValue || this.form.value;
        const voltageLevelId = formValue.voltageLevelId;
        return voltageLevelId && voltageLevelId !== VOLTAGE_LEVEL_C_VALUE_ID && voltageLevelId !== VOLTAGE_LEVEL_B_LOW_VALUE_ID;
    };
}
