import { ChangeField, ChangeFieldType } from 'src/app/core/constants/changes-details';

export class MoveMeterChanges {
    installationAddress: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };
    connectionPoint: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };
    meterFrame: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };
    consumptionMeteringPoint: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };
    contactPersonCompanyName: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    contactPersonName: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    contactPersonEmail: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    contactPersonPhoneNumber: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    remarksToInstallation: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    tags: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };

    payerName: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    payerType: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    payerEmail: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    payerContactPersonName: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    payerContactPersonEmail: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    payerContactPersonPhoneNumber: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    payerAddress: ChangeField = { change: false, type: ChangeFieldType.Array, dtoFieldName: undefined };
    requisition: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    cvrOrSeNumber: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    eanNumber: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };

    // instructionData
    instructionDataMeterResponsible: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    instructionDataBranchLineResponsible: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    instructionDataConnectionFeeFee: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    instructionDataConnectionFeeValidUntilEod: ChangeField = {
        change: false,
        type: ChangeFieldType.Date,
        dtoFieldName: undefined
    };
    instructionDataRemark: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };

    // Meter Delivery
    meterDeliveryOptionsRequestedConnectionDateEod: ChangeField = {
        change: false,
        type: ChangeFieldType.Date,
        dtoFieldName: undefined
    };

    // Move meter information
    moveMeterInformationReasonForChange: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    moveMeterInformationMeterTypeMeterSize: ChangeField = {
        change: false,
        type: ChangeFieldType.Default,
        dtoFieldName: undefined
    };
    moveMeterInformationMeterTypeConnectionType: ChangeField = {
        change: false,
        type: ChangeFieldType.Default,
        dtoFieldName: undefined
    };
    moveMeterInformationMeterTypeMeterTransformerId: ChangeField = {
        change: false,
        type: ChangeFieldType.Default,
        dtoFieldName: undefined
    };
    moveMeterInformationMeterTypeMeterTransformerRemark: ChangeField = {
        change: false,
        type: ChangeFieldType.Default,
        dtoFieldName: undefined
    };

    // Seal information
    sealInformationMeterPlacementId: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
    sealInformationResponsibleForSeal: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };

    // related forms
    hasRelatedFormsChanged: ChangeField = { change: false, type: ChangeFieldType.Default, dtoFieldName: undefined };
}
