module.exports = {
    preset: 'jest-preset-angular',
    setupFilesAfterEnv: ['<rootDir>/setup-jest.ts'],
    testEnvironment: '@happy-dom/jest-environment',
    testPathIgnorePatterns: ['<rootDir>/node_modules/', '<rootDir>/dist/', '<rootDir>/src/environments/environment.test.ts'],
    reporters: [
        'default',
        [
            './node_modules/jest-html-reporter',
            {
                pageTitle: 'Test Report',
                includeFailureMsg: true
            }
        ],
        ['jest-junit', { outputDirectory: './coverage', outputName: 'junit.xml' }]
    ],
    coverageReporters: ['cobertura', 'lcov', 'text'],
    coveragePathIgnorePatterns: [
        'node_modules',
        '<rootDir>/src/app/api/',
        '<rootDir>/environments/*.ts',
        '<rootDir>/src/app/shared/components/',
        '<rootDir>/src/app/installation-forms/features/details/',
        '<rootDir>/src/app/installation-forms-settings/features/'
    ],
    maxWorkers: 4,
    roots: ['<rootDir>'],
    modulePaths: ['<rootDir>'],
    moduleDirectories: ['node_modules']
};
