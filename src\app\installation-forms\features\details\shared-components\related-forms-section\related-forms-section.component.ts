import { Component, Input, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { Subject, Subscription, catchError, map, of, switchMap, takeUntil } from 'rxjs';
import {
    FormSimpleListItem,
    FormsRelationDirection,
    FormsRelationOrigin,
    FormsRelationType,
    InstallationFormsClient,
    RelatedForm
} from 'src/app/api/installation-forms-client';
import { AUTOCOMPLETE_ITEMS_LIMIT, SMALL_TABLE_ROWS_PER_PAGE } from 'src/app/core/constants/constants';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { WidgetWithFormComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget-with-form.component';
import { v4 as uuidv4 } from 'uuid';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';

@Component({
    selector: 'app-related-forms-section',
    templateUrl: './related-forms-section.component.html',
    styleUrl: './related-forms-section.component.scss',
    standalone: false
})
export class RelatedFormsSectionComponent extends WidgetWithFormComponent implements OnInit, OnDestroy {
    private _form!: FormGroup;

    @Input()
    get form(): FormGroup {
        return this._form;
    }

    set form(value: FormGroup) {
        this._form = value;
        this.setAccordionActiveValues();
    }

    addForm: FormGroup;

    private sectionTranslations: any;

    private searchSubject$ = new Subject<string>();
    private destroy$ = new Subject<void>();
    filtered: FormSimpleListItem[] = [];

    subscription: Subscription = new Subscription();

    hasNewRelatedFormPicked: boolean = false;

    rowsPerPage = SMALL_TABLE_ROWS_PER_PAGE;

    accordionActiveValues: string = '';
    relatedFormsTabName: string = 'relatedForms';

    constructor(
        readonly formDataService: FormDataService,
        private readonly client: InstallationFormsClient,
        private readonly messageServiceHelper: MessageServiceHelper,
        private readonly translateService: TranslateService,
        private readonly router: Router
    ) {
        super(formDataService);
        this.addForm = new FormGroup({
            newRelatedForm: new FormControl()
        });
    }

    ngOnInit(): void {
        this.registerActions();

        this.sectionTranslations = this.translateService.instant('relatedFormsSection');
    }

    ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }

    async handleRowClick(clickedItem: RelatedForm) {
        const selection = window.getSelection();
        if (selection && selection.toString().length > 0) {
            return;
        }
        await this.router.navigate([`installation-forms/details`, clickedItem.type, clickedItem.formId]);
    }

    removeItem(item: any, event: MouseEvent): void {
        event.stopPropagation();
        let relatedForms = this.form.get('relatedForms')?.value as RelatedForm[];
        const index = relatedForms.indexOf(item);
        if (index !== -1) {
            relatedForms.splice(index, 1);
        }
        this.form.patchValue({
            relatedForms: relatedForms,
            hasRelatedFormsChanged: true
        });
    }

    addNewRelatedForm(): void {
        let relatedForms = this.form.get('relatedForms')?.value as RelatedForm[];

        const selected = this.addForm.get('newRelatedForm')?.value as FormSimpleListItem;

        if (relatedForms.some((x) => x.formId === selected.formId)) {
            this.messageServiceHelper.showWarning({
                detail: this.sectionTranslations['formAlreadyHasRelationWarning'],
                key: this.formDataService.formId
            });
            return;
        }

        const newRelatedForm = new RelatedForm({
            formId: selected.formId,
            formNumber: selected.formNumber,
            state: selected.state,
            type: selected.type,
            relationType: FormsRelationType.Manual,
            origin: FormsRelationOrigin.ManualEntry,
            direction: FormsRelationDirection.NoDirection,
            createdDate: selected.createdDate
        });

        relatedForms.push(newRelatedForm);

        this.form.patchValue({
            relatedForms: [...relatedForms],
            hasRelatedFormsChanged: true
        });

        this.addForm.get('newRelatedForm')?.reset();
        this.hasNewRelatedFormPicked = false;
    }

    private registerActions() {
        this.searchSubject$
            .pipe(
                takeUntil(this.destroy$),
                switchMap((query) => {
                    query = query.trim();
                    if (!query || query.length === 0) {
                        return of([]);
                    }
                    return this.client
                        .filterInstallationForms(
                            uuidv4(),
                            0,
                            AUTOCOMPLETE_ITEMS_LIMIT,
                            undefined,
                            undefined,
                            undefined,
                            query,
                            this.formDataService.formId
                        )
                        .pipe(
                            catchError((_error) => {
                                this.messageServiceHelper.showError({
                                    detail: this.sectionTranslations['searchError'],
                                    key: this.formDataService.formId
                                });
                                return of(undefined);
                            }),
                            map((response) => {
                                return response?.result.results || [];
                            })
                        );
                })
            )
            .subscribe((x) => {
                this.filtered = x;
            });
    }

    search(event: any) {
        this.searchSubject$.next(event.query);
    }

    onClear() {
        this.hasNewRelatedFormPicked = false;
    }

    onSelect() {
        this.hasNewRelatedFormPicked = true;
    }

    setAccordionActiveValues() {
        this.accordionActiveValues = this.form.get('relatedForms')?.value.length ? this.relatedFormsTabName : '';
    }
}
