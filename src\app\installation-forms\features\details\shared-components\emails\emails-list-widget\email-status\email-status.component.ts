import { Component, Input } from '@angular/core';
import { EmailStatus, EmailType } from 'src/app/api/installation-forms-client';
import { IconState } from 'src/app/core/enums/icon-state';

@Component({
    selector: 'app-email-status',
    templateUrl: './email-status.component.html',
    standalone: false
})
export class EmailStatusComponent {
    @Input() emailStatus?: EmailStatus;
    @Input() emailType?: EmailType;

    mapToIconState(): IconState {
        if (!this.emailStatus) {
            return IconState.InProgress;
        }

        switch (this.emailType) {
            case EmailType.WelcomeLetter:
                return this.mapWelcomeLetterToIconState();
            case EmailType.InternalResource:
                return this.mapInternalResourceToIconState();
            default:
                return IconState.Unknown;
        }
    }

    mapInternalResourceToIconState(): IconState {
        switch (this.emailStatus) {
            case EmailStatus.Unknown:
            case EmailStatus.New:
            case EmailStatus.Sending:
            case EmailStatus.Processed:
            case EmailStatus.Deferred:
                return IconState.InProgress;
            case EmailStatus.Delivered:
                return IconState.Sent;
            case EmailStatus.Dropped:
            case EmailStatus.Bounce:
            case EmailStatus.Error:
            case EmailStatus.RecipientNotWhitelisted:
                return IconState.Failed;
            case EmailStatus.ResponseReceived:
                return IconState.ResponseReceived;
            default:
                return IconState.Unknown;
        }
    }

    mapWelcomeLetterToIconState(): IconState {
        switch (this.emailStatus) {
            case EmailStatus.Unknown:
            case EmailStatus.New:
            case EmailStatus.Sending:
            case EmailStatus.Processed:
            case EmailStatus.Deferred:
                return IconState.InProgress;
            case EmailStatus.Delivered:
                return IconState.Completed;
            case EmailStatus.Dropped:
            case EmailStatus.Bounce:
            case EmailStatus.Error:
            case EmailStatus.RecipientNotWhitelisted:
                return IconState.Failed;
            default:
                return IconState.Unknown;
        }
    }
}
