import { Component, Input } from '@angular/core';
import { EmailState } from 'src/app/api/installation-forms-client';
import { IconState } from 'src/app/core/enums/icon-state';

@Component({
    selector: 'app-email-status',
    templateUrl: './email-status.component.html',
    styleUrls: ['./email-status.component.scss'],
    standalone: false
})
export class EmailStatusComponent {
    @Input() emailState?: EmailState;

    mapToIconState(): IconState {
        if (!this.emailState) {
            return IconState.InProgress;
        }

        switch (this.emailState) {
            case EmailState.Bounced:
            case EmailState.RemovedBySecurityScan:
            case EmailState.TechnicalError:
            case EmailState.ExceededSendingLimit:
                return IconState.Failed;
            case EmailState.UnreadEmail:
            case EmailState.ConfirmedAccepted:
                return IconState.InProgress;
            case EmailState.EmailRead:
            case EmailState.ConfirmedDelivered:
                return IconState.Completed;
            default:
                return IconState.Unknown;
        }
    }
}
