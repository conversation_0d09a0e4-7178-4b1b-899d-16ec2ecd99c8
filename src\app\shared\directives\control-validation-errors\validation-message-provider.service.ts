import { Injectable } from '@angular/core';
import { TranslateParser, TranslateService } from '@ngx-translate/core';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { ValidationMessageTranslationKeysService } from './validation-message-translation-keys.service';

@Injectable()
export class ValidationMessageProviderService {
    constructor(
        private readonly translateService: TranslateService,
        private readonly parser: TranslateParser,
        private readonly mappingsService: ValidationMessageTranslationKeysService
    ) {}

    getMessages(errors: { [key: string]: any } | null): Observable<string[]> {
        if (errors === null) {
            return of(['']);
        }

        const mappedKeys = Object.keys(errors)
            .map((key) => {
                if (this.mappingsService.keys.hasOwnProperty(key)) {
                    return {
                        errorKey: key,
                        errorValue: errors[key],
                        // @ts-ignore
                        translationKey: this.mappingsService.keys[key]
                    };
                }
                return;
            })
            .filter((element) => element !== undefined);

        if (mappedKeys && mappedKeys.length > 0) {
            return this.translateService.get(mappedKeys.map((el) => el?.translationKey)).pipe(
                map((messages) => {
                    const results: any[] = [];

                    Object.keys(messages).forEach((el) => {
                        const err = mappedKeys.find((o) => o?.translationKey === el);
                        if (err) {
                            results.push({ ...err, translation: messages[el] });
                        }
                    });

                    return results.map((el) => this.parser.interpolate(el.translation, el.errorValue) ?? '');
                })
            );
        } else {
            return of(['']);
        }
    }
}
