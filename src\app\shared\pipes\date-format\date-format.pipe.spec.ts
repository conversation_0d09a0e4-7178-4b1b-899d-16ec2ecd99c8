import { TestBed } from '@angular/core/testing';
import { DateTimeService } from 'src/app/core/services/date-time/date-time.service';
import { FormatDatePipe } from './date-format.pipe';

describe('FormatDatePipe', () => {
    let pipe: FormatDatePipe;
    let dateTimeService: DateTimeService;
    let transformDateSpy: jest.SpyInstance;

    beforeEach(() => {
        const dateTimeServiceMock = {
            transformDate: jest.fn()
        };

        TestBed.configureTestingModule({
            providers: [FormatDatePipe, { provide: DateTimeService, useValue: dateTimeServiceMock }]
        });

        pipe = TestBed.inject(FormatDatePipe);
        dateTimeService = TestBed.inject(DateTimeService);
        transformDateSpy = jest.spyOn(dateTimeService, 'transformDate');
    });

    it('should create an instance', () => {
        expect(pipe).toBeTruthy();
    });

    it('should delegate to the DateTimeService transformDate method', () => {
        const testDate = new Date(2023, 0, 15);
        transformDateSpy.mockReturnValue('mocked-date-string');

        const result = pipe.transform(testDate);

        expect(dateTimeService.transformDate).toHaveBeenCalledWith(testDate);
        expect(result).toBe('mocked-date-string');
    });

    it('should pass undefined to the service when input is undefined', () => {
        transformDateSpy.mockReturnValue('');

        pipe.transform(undefined);

        expect(dateTimeService.transformDate).toHaveBeenCalledWith(undefined);
    });
});
