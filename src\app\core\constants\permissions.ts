export const InstallationForms = {
    read: 'InstallationForms.Read',
    write: 'InstallationForms.Write'
} as const;

export const FormConfiguration = {
    read: 'FormConfiguration.Read',
    write: 'FormConfiguration.Write'
} as const;

export const CommunicationTemplates = {
    read: 'CommunicationTemplates.Read',
    write: 'CommunicationTemplates.Write'
} as const;

export const CommunicationChannelEmail = {
    read: 'CommunicationChannelEmail.Read',
    write: 'CommunicationChannelEmail.Write'
} as const;

export const WorkOrders = {
    read: 'PCWorkOrders.Read',
    write: 'PCWorkOrders.Write'
} as const;

export const MeteringPoints = {
    read: 'MeteringPoints.Read'
} as const;

export const MasterData = {
    read: 'PCMasterData.Read',
    write: 'PCMasterData.Write'
} as const;

export const SingleInvoicing = {
    write: 'InvoiceBases.Write'
} as const;

export const Permissions = {
    installationForms: InstallationForms,
    communicationTemplates: CommunicationTemplates,
    communicationChannelEmail: CommunicationChannelEmail,
    workOrders: WorkOrders,
    meteringPoints: MeteringPoints,
    masterData: MasterData,
    singleInvoicing: SingleInvoicing,
    formConfiguration: FormConfiguration
};

export type PermissionKey = (typeof Permissions)[keyof typeof Permissions][keyof (typeof Permissions)[keyof typeof Permissions]];
