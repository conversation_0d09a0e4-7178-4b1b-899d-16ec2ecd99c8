import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function alreadyUsedDisplayNameValidator(alreadyUsedDisplayNames: string[]): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        const value = control.value;
        if (value && alreadyUsedDisplayNames.includes(value)) {
            return { displayNameAlreadyUsed: true };
        }
        return null;
    };
}
