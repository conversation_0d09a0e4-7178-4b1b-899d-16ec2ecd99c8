import { Component } from '@angular/core';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';

@Component({
    selector: 'app-chat-and-notes-widget',
    templateUrl: './chat-and-notes-widget.component.html',
    standalone: false
})
export class ChatAndNotesWidgetComponent {
    notesCount = 0;
    constructor(private readonly formDataService: FormDataService) {}

    protected hasUnreadMessages = () => !!this.formDataService.hasUnreadMessages;

    protected setNotesCount(count: number) {
        this.notesCount = count;
    }
}
