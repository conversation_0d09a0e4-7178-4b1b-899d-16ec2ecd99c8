import { Injectable } from '@angular/core';
import { MasterDataProcessType, ValueListType, WorkOrderType } from 'src/app/api/installation-forms-client';

@Injectable({
    providedIn: 'root'
})
export class MasterDataOptionsService {
    constructor() {}

    isProcessTemplateRequired = (masterDataProcessType?: MasterDataProcessType) => {
        return (
            masterDataProcessType !== MasterDataProcessType.ModifyConnectionPoint &&
            masterDataProcessType !== MasterDataProcessType.CloseDownSupplyType
        );
    };

    getWorkOrderPurposeValueListType = (): ValueListType => {
        return ValueListType.WorkOrderElectricityPurposeList;
    };

    getWorkOrderDescriptionValueListType = (workOrderType: WorkOrderType): ValueListType => {
        switch (workOrderType) {
            case WorkOrderType.InstallMeter:
                return ValueListType.WorkOrderInstallMeterElectricityWorkDescriptionList;
            case WorkOrderType.ReplaceMeter:
                return ValueListType.WorkOrderReplaceMeterElectricityWorkDescriptionList;
            case WorkOrderType.RemoveMeter:
                return ValueListType.WorkOrderRemoveMeterElectricityWorkDescriptionList;
            case WorkOrderType.General:
                return ValueListType.WorkOrderGeneralElectricityWorkDescriptionList;
            default:
                return ValueListType.WorkOrderGeneralElectricityWorkDescriptionList;
        }
    };
}
