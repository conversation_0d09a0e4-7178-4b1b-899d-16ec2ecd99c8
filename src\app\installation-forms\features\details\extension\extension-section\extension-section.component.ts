import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { Subscription } from 'rxjs';
import { ValueListsService } from 'src/app/core/services/value-lists/value-lists.service';
import { WidgetWithFormComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget-with-form.component';
import { ResponsibleForSeal, ValueListType } from '../../../../../api/installation-forms-client';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';
import { enumMapper } from '../../../../../core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';

@Component({
    selector: 'app-extension-section',
    templateUrl: './extension-section.component.html',
    styleUrls: ['./extension-section.component.scss'],
    standalone: false
})
export class ExtensionSectionComponent extends WidgetWithFormComponent implements OnInit, OnDestroy {
    @Input() form!: FormGroup;

    subscription: Subscription = new Subscription();
    responsibleForSealOptions: SelectItem[] = [];
    readonly meterFramePlacementValueListType: ValueListType = ValueListType.MeterFramePlacementCode;

    constructor(
        readonly formDataService: FormDataService,
        private readonly translateService: TranslateService,
        protected readonly valueListsService: ValueListsService
    ) {
        super(formDataService);
    }

    ngOnInit(): void {
        this.getEnumTranslations();
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    getEnumTranslations() {
        this.responsibleForSealOptions = enumMapper.map(
            this.translateService.instant('enums.responsibleForSeal'),
            ResponsibleForSeal
        );
    }
}
