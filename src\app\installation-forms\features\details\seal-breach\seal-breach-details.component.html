<div *ngIf="formDetails" fxLayoutGap="20px" fxLayout="column">
    <app-details-header [formNumber]="formDetails.formNumber"></app-details-header>

    <app-identification-bar
        [connectionPoint]="formDetails.connectionPoint"
        [meterNumber]="formDetails.meterNumber"
        [installationAddress]="formDetails.installationAddress">
    </app-identification-bar>

    <div fxLayoutGap="20px" fxLayout="row" fxLayout.lt-lg="column" stop-key-events-propagation>
        <div fxLayout="column" fxFlexFill fxLayoutGap="20px" fxLayoutAlign="center" fxFlex="66.6">
            <app-problems-widget [problems]="formDetails.problems" *ngIf="formDetails.problems.length > 0"></app-problems-widget>
            <app-seal-breach-widget
                [formDetails]="formDetails"
                (changesMade)="dirtyChanges($event.key, $event.hasChanges)"
                (refreshRequested)="reloadFormDetails()">
            </app-seal-breach-widget>
        </div>
        <div fxLayout="column" fxFlexFill fxLayoutGap="20px" fxLayoutAlign="center" fxFlex="33.4">
            <app-chat-and-notes-widget></app-chat-and-notes-widget>

            <app-flags-widget
                [flags]="formDetails.flags"
                (changesMade)="dirtyChanges($event.key, $event.hasChanges)"
                (refreshRequested)="reloadFormDetails()">
            </app-flags-widget>

            <cmbs-attachments-widget
                [objectType]="attachmentObjectType"
                [objectId]="formDetails.formId"
                [isReadOnly]="isAttachmentsReadonly"></cmbs-attachments-widget>

            <app-case-worker-widget
                [caseWorker]="formDetails.caseWorker"
                (changesMade)="dirtyChanges($event.key, $event.hasChanges)"></app-case-worker-widget>

            <app-tasks-widget> </app-tasks-widget>

            <app-master-data-processes-widget></app-master-data-processes-widget>

            <app-emails-widget></app-emails-widget>

            <app-invoices-widget> </app-invoices-widget>

            <app-work-orders-widget></app-work-orders-widget>
        </div>
    </div>

    <app-action-bar
        [possibleStateChanges]="formDetails.possibleStateChanges"
        (stateChangeEvent)="changeState($event.state, $event.comment)"
        (screeningStatusChangeEvent)="changeScreeningStatus($event.state)">
    </app-action-bar>

    <p-toast [key]="formDetails.formId"></p-toast>
</div>
<p-toast #genericMessagesToast></p-toast>
