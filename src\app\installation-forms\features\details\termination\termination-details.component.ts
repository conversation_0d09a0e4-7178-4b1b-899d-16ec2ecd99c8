import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AuthorizationService } from '@kmd-elements-ui/authorization';
import { KeyboardShortcutsService } from '@kmd-elements-ui/shortcuts';
import { TabsService } from '@kmd-elements-ui/tabs';
import { TranslateService } from '@ngx-translate/core';
import { Observable } from 'rxjs';
import { InstallationFormsApiResponse, InstallationFormsClient, Termination } from 'src/app/api/installation-forms-client';
import { AutomaticFormRefreshService } from 'src/app/core/services/automatic-form-refresh/automatic-form-refresh.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { v4 as uuidv4 } from 'uuid';
import { CaseWorkerService } from '../../../../core/services/case-worker/case-worker.service';
import { FormDataService } from '../../../../core/services/form-data/form-data.service';
import { ValueListsService } from '../../../../core/services/value-lists/value-lists.service';
import { BaseDetailsComponent } from '../base-components/details/base-details.component';

@Component({
    selector: 'app-termination-details',
    templateUrl: './termination-details.component.html',
    styleUrls: ['./termination-details.component.scss'],
    providers: [AutomaticFormRefreshService, FormDataService],
    standalone: false
})
export class TerminationDetailsComponent extends BaseDetailsComponent<Termination> implements OnInit {
    constructor(
        override readonly tabsService: TabsService,
        override readonly client: InstallationFormsClient,
        override readonly route: ActivatedRoute,
        override readonly authService: AuthorizationService,
        override readonly messageServiceHelper: MessageServiceHelper,
        override readonly translateService: TranslateService,
        override readonly caseWorkerService: CaseWorkerService,
        override readonly formDataService: FormDataService,
        override readonly automaticFormRefreshService: AutomaticFormRefreshService,
        override readonly keyboardShortcutsService: KeyboardShortcutsService,
        override readonly valueListsService: ValueListsService
    ) {
        super(
            tabsService,
            client,
            route,
            authService,
            messageServiceHelper,
            translateService,
            caseWorkerService,
            formDataService,
            automaticFormRefreshService,
            keyboardShortcutsService,
            valueListsService
        );
    }

    override getDetails(formId: string): Observable<InstallationFormsApiResponse<Termination>> {
        return this.client.getTerminationById(formId, uuidv4());
    }
}
