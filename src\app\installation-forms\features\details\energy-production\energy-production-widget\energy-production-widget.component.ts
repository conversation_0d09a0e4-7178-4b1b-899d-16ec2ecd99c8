import { Component, ViewChild } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { Observable, pairwise, startWith } from 'rxjs';
import {
    AddressLookup,
    BranchLineResponsible,
    ConnectionAddress,
    ConnectionFee,
    ContactPerson,
    EnergyProduction,
    EnergyProductionCommon,
    EnergyProductionConnectionType,
    EnergyProductionInstallationInformationUpdate,
    EnergyProductionPowerPlant,
    EnergyProductionUpdate,
    EnergyProductionWindPlant,
    FormCategory,
    FormsRelationType,
    InstallationFormsApiResponse,
    InstallationFormsClient,
    InstructionDataUpdate,
    LocationType,
    MeterResponsible,
    Owner,
    OwnerIdentification,
    OwnerIdentificationType,
    PayerType,
    PayerUpdate,
    PaymentDetailsUpdate,
    Plant,
    RelatedForm,
    RelatedFormUpdate,
    UtmCoordinate
} from 'src/app/api/installation-forms-client';
import { ChangesModel } from 'src/app/core/constants/changes-details';
import { RemarkLength } from 'src/app/core/constants/field-lengths';
import { AutomaticFormRefreshService } from 'src/app/core/services/automatic-form-refresh/automatic-form-refresh.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { createMeteringPoint } from 'src/app/core/utils/create-metering-point';
import { getValueOrDefault } from 'src/app/core/utils/get-value-or-default';
import addressLookupValidator from 'src/app/core/utils/validators/address-lookup/address-lookup.validator';
import conditionallyRequiredValidator from 'src/app/core/utils/validators/conditionally-required/conditionally-required.validator';
import dateMustNotBePastValidator from 'src/app/core/utils/validators/date-time/date-must-not-be-past.validator';
import { EmailValidator } from 'src/app/core/utils/validators/email/email.validator';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';
import { CoreWidgetComponent } from '../../base-components/widgets/core-widget.component';
import { EnergyProductionChanges } from './energy-production-changes.model';
import { PlantsSectionComponent } from './type-specific-sections/plants-section/plants-section.component';

@Component({
    selector: 'app-energy-production-widget',
    templateUrl: './energy-production-widget.component.html',
    standalone: false
})
export class EnergyProductionWidgetComponent extends CoreWidgetComponent<EnergyProduction, EnergyProductionUpdate> {
    override accordionActiveIndexes: number[] = [0, 1, 2, 3, 4];
    override payerPanelIndex: number = 6;

    @ViewChild('plantsSection') plantsSection!: PlantsSectionComponent;

    get productionMeteringPoint(): FormGroup {
        return this.installationInformationForm.get('productionMeteringPoint') as FormGroup;
    }

    get energyProductionCommonForm(): FormGroup {
        return this.form?.get('energyProductionCommon') as FormGroup;
    }

    get connectionAddressForm(): FormGroup {
        return this.form?.get('connectionAddress') as FormGroup;
    }

    get windPlantForm(): FormGroup {
        return this.form?.get('windPlant') as FormGroup;
    }

    get powerPlantForm(): FormGroup {
        return this.form?.get('powerPlant') as FormGroup;
    }

    get plantsForm(): FormGroup {
        return this.form?.get('plants') as FormGroup;
    }

    constructor(
        protected readonly fb: FormBuilder,
        protected readonly messageServiceHelper: MessageServiceHelper,
        protected readonly translateService: TranslateService,
        protected readonly client: InstallationFormsClient,
        protected readonly formDataService: FormDataService,
        protected readonly automaticFormRefreshService: AutomaticFormRefreshService
    ) {
        super(formDataService, automaticFormRefreshService, translateService, messageServiceHelper, client);
        this.initForm();
        this.setChangesModel();
    }

    protected override initForm() {
        this.form = this.fb.group({
            installationInformation: this.fb.group({
                installationAddress: [{ id: '', text: '' }, [addressLookupValidator()]],
                remarksToInstallation: ['', [Validators.maxLength(1000)]],
                connectionPoint: [{ id: '', connectionPointNumber: '' }],
                meterFrame: [{ id: '', meterFrameNumber: '' }],
                consumptionMeteringPoint: [{ meteringPointId: '', meteringPointVersionId: '' }],
                productionMeteringPoint: [{ meteringPointId: '', meteringPointVersionId: '' }],
                tags: []
            }),
            payer: this.fb.group({
                payerName: ['', [Validators.maxLength(100), Validators.required]],
                payerType: ['', Validators.required],
                payerEmail: [
                    '',
                    [
                        (control: AbstractControl) => conditionallyRequiredValidator(control, this.isPayerTypeNotPublic),
                        EmailValidator()
                    ]
                ],
                payerContactPersonName: ['', [Validators.maxLength(100)]],
                payerContactPersonEmail: ['', [EmailValidator()]],
                payerContactPersonPhoneNumber: ['', [Validators.maxLength(20)]],
                requisition: '',
                cvrOrSeNumber: [
                    '',
                    [
                        (control: AbstractControl) => conditionallyRequiredValidator(control, this.isPayerTypeCompany),
                        Validators.pattern('^[0-9]{8}$')
                    ]
                ],
                eanNumber: [
                    '',
                    [
                        (control: AbstractControl) => conditionallyRequiredValidator(control, this.isPayerTypePublic),
                        Validators.pattern('^[0-9]{13}$')
                    ]
                ],
                payerAddress: [{ id: '', text: '' }, [addressLookupValidator()]]
            }),
            contactPerson: this.fb.group({
                contactPersonCompanyName: ['', [Validators.maxLength(100)]],
                contactPersonName: ['', [Validators.maxLength(100)]],
                contactPersonEmail: ['', [EmailValidator()]],
                contactPersonPhoneNumber: ['', [Validators.maxLength(20)]]
            }),
            instructionData: this.fb.group({
                meterResponsible: '',
                branchLineResponsible: '',
                connectionFeeValidUntilEod: ['', [(control: AbstractControl) => dateMustNotBePastValidator(control)]],
                connectionFeeFee: [null, [Validators.min(-*********), Validators.max(*********)]], // Connection fee can be negative, that's not a mistake
                remark: ['', [Validators.maxLength(RemarkLength)]]
            }),
            energyProductionCommon: this.fb.group({
                energyProductionConnectionType: [null, [Validators.required]],
                ownerName: ['', [Validators.maxLength(100), Validators.required]],
                ownerIdentificationType: ['', [Validators.required]],
                ownerIdentifier: ['', [Validators.required]],
                ownerAddress: [{ id: '', text: '' }, [addressLookupValidator()]],
                moreThan125kW: false,
                includesStorage: false,
                commissioningDateSod: [
                    '',
                    [Validators.required, (control: AbstractControl) => dateMustNotBePastValidator(control)]
                ]
            }),
            connectionAddress: this.fb.group({
                connectionAddress: [{ id: '', text: '' }, [addressLookupValidator()]],
                consumptionUnitType: [
                    '',
                    [
                        Validators.maxLength(50),
                        (control: AbstractControl) =>
                            conditionallyRequiredValidator(control, this.isEnergyProductionTypeConsumption)
                    ]
                ],
                locationType: [
                    '',
                    [(control: AbstractControl) => conditionallyRequiredValidator(control, this.isEnergyProductionTypeWind)]
                ],
                cadastralDistrictIdentifier: [
                    '',
                    [
                        Validators.maxLength(50),
                        (control: AbstractControl) => conditionallyRequiredValidator(control, this.isEnergyProductionTypeWind)
                    ]
                ],
                cadastralRegistrationNumber: [
                    '',
                    [
                        Validators.maxLength(50),
                        (control: AbstractControl) =>
                            conditionallyRequiredValidator(control, this.isCadastralRegistrationNumberRequired)
                    ]
                ],
                locationUtmX: [
                    null,
                    [
                        Validators.min(1),
                        Validators.max(*********),
                        (control: AbstractControl) => conditionallyRequiredValidator(control, this.isEnergyProductionTypeWind)
                    ]
                ],
                locationUtmY: [
                    null,
                    [
                        Validators.min(1),
                        Validators.max(*********),
                        (control: AbstractControl) => conditionallyRequiredValidator(control, this.isEnergyProductionTypeWind)
                    ]
                ],
                foundationElevation: [
                    '',
                    [
                        Validators.maxLength(50),
                        (control: AbstractControl) => conditionallyRequiredValidator(control, this.isEnergyProductionTypeWind)
                    ]
                ],
                buildingNumber: [
                    '',
                    [
                        Validators.maxLength(50),
                        (control: AbstractControl) =>
                            conditionallyRequiredValidator(control, this.isEnergyProductionTypePowerPlant)
                    ]
                ],
                propertyNumber: [
                    '',
                    [
                        Validators.maxLength(50),
                        (control: AbstractControl) =>
                            conditionallyRequiredValidator(control, this.isEnergyProductionTypePowerPlant)
                    ]
                ],
                connectionPointUtmX: [
                    null,
                    [
                        Validators.max(*********),
                        (control: AbstractControl) =>
                            conditionallyRequiredValidator(control, this.isConnectionPointCoordinateNotEmpty)
                    ]
                ],
                connectionPointUtmY: [
                    null,
                    [
                        Validators.max(*********),
                        (control: AbstractControl) =>
                            conditionallyRequiredValidator(control, this.isConnectionPointCoordinateNotEmpty)
                    ]
                ]
            }),
            windPlant: this.fb.group({
                certificate: [
                    '',
                    [
                        Validators.maxLength(100),
                        (control: AbstractControl) => conditionallyRequiredValidator(control, this.isEnergyProductionTypeWind)
                    ]
                ],
                certificationProvider: [
                    '',
                    [
                        Validators.maxLength(100),
                        (control: AbstractControl) => conditionallyRequiredValidator(control, this.isEnergyProductionTypeWind)
                    ]
                ],
                rotorDiameterInMeters: [null, [Validators.min(1), Validators.max(*********)]],
                hubHeightInMeters: [null, [Validators.min(1), Validators.max(*********)]]
            }),
            powerPlant: this.fb.group({
                primaryEnergySource: [
                    '',
                    [
                        Validators.maxLength(100),
                        (control: AbstractControl) =>
                            conditionallyRequiredValidator(control, this.isEnergyProductionTypePowerPlant)
                    ]
                ],
                primaryEnergyShareInPercent: [
                    '',
                    [
                        Validators.maxLength(100),
                        (control: AbstractControl) =>
                            conditionallyRequiredValidator(control, this.isEnergyProductionTypePowerPlant)
                    ]
                ],
                secondaryEnergySource: [
                    '',
                    [
                        Validators.maxLength(100),
                        (control: AbstractControl) =>
                            conditionallyRequiredValidator(control, this.isEnergyProductionTypePowerPlant)
                    ]
                ],
                secondaryEnergyShareInPercent: [
                    '',
                    [
                        Validators.maxLength(100),
                        (control: AbstractControl) =>
                            conditionallyRequiredValidator(control, this.isEnergyProductionTypePowerPlant)
                    ]
                ],
                plantTypeName: [
                    '',
                    [
                        Validators.maxLength(100),
                        (control: AbstractControl) =>
                            conditionallyRequiredValidator(control, this.isEnergyProductionTypePowerPlant)
                    ]
                ]
            }),
            plants: this.fb.group({
                plants: this.fb.array([]),
                hasPlantsChanged: false
            }),
            relatedForms: this.fb.group({
                relatedForms: [],
                hasRelatedFormsChanged: false
            })
        });
    }

    protected override setChangesModel() {
        this.changesModel = new EnergyProductionChanges() as unknown as ChangesModel;
    }

    protected override supplyFormData() {
        this.form.patchValue({
            installationInformation: {
                installationAddress: {
                    id: this.formDetails.installationAddress?.carId || '',
                    text: this.formDetails.installationAddress?.formattedAddress || ''
                },
                remarksToInstallation: this.formDetails.remarksToInstallation || '',
                connectionPoint: {
                    id: this.formDetails.connectionPoint?.id || '',
                    connectionPointNumber: this.formDetails.connectionPoint?.connectionPointNumber || ''
                },
                meterFrame: {
                    id: this.formDetails.meterFrame?.id || '',
                    meterFrameNumber: this.formDetails.meterFrame?.meterFrameNumber || ''
                },
                consumptionMeteringPoint: {
                    meteringPointId: this.formDetails.consumptionMeteringPoint?.meteringPointId || '',
                    meteringPointVersionId: this.formDetails.consumptionMeteringPoint?.meteringPointVersionId || ''
                },
                productionMeteringPoint: {
                    meteringPointId: this.formDetails.productionMeteringPoint?.meteringPointId || '',
                    meteringPointVersionId: this.formDetails.productionMeteringPoint?.meteringPointVersionId || ''
                },
                tags: this.formDetails.tags.map((t) => t.id) || []
            },
            contactPerson: {
                contactPersonCompanyName: this.formDetails.contactPerson?.companyName || '',
                contactPersonName: this.formDetails.contactPerson?.name || '',
                contactPersonEmail: this.formDetails.contactPerson?.email || '',
                contactPersonPhoneNumber: this.formDetails.contactPerson?.phoneNumber || ''
            },
            payer: {
                payerName: this.formDetails.payer?.name || '',
                payerEmail: this.formDetails.payer?.email || '',
                payerType: this.formDetails.payer?.type || PayerType.Private,
                payerContactPersonName: this.formDetails.payer?.contactPerson?.name || '',
                payerContactPersonEmail: this.formDetails.payer?.contactPerson?.email || '',
                payerContactPersonPhoneNumber: this.formDetails.payer?.contactPerson?.phoneNumber || '',
                requisition: this.formDetails.payer?.requisition || '',
                cvrOrSeNumber: this.formDetails.payer?.cvrOrSeNumber || '',
                eanNumber: this.formDetails.payer?.eanNumber || '',
                payerAddress: {
                    id: this.formDetails.payer?.address?.carId || '',
                    text: this.formDetails.payer?.address?.formattedAddress || ''
                }
            },
            instructionData: {
                meterResponsible: this.formDetails.instructionData?.meterResponsible || null,
                branchLineResponsible: this.formDetails.instructionData?.branchLineResponsible || null,
                connectionFeeFee: this.formDetails.instructionData?.connectionFee?.fee || null,
                connectionFeeValidUntilEod: this.formDetails.instructionData?.connectionFee?.validUntilEod || null,
                remark: this.formDetails.instructionData?.remark || null
            },
            energyProductionCommon: {
                energyProductionConnectionType: this.formDetails.energyProductionCommon.energyProductionConnectionType || null,
                ownerName: this.formDetails.energyProductionCommon.owner?.name || null,
                ownerIdentificationType:
                    this.formDetails.energyProductionCommon.owner?.ownerIdentification?.ownerIdentificationType || null,
                ownerIdentifier: this.formDetails.energyProductionCommon.owner?.ownerIdentification?.identifier || null,
                ownerAddress: {
                    id: this.formDetails.energyProductionCommon.owner?.address?.carId || '',
                    text: this.formDetails.energyProductionCommon.owner?.address?.formattedAddress || ''
                },
                moreThan125kW: this.formDetails.energyProductionCommon.moreThan125kW || false,
                includesStorage: this.formDetails.energyProductionCommon.includesStorage || false,
                commissioningDateSod: this.formDetails.energyProductionCommon.commissioningDateSod || null
            },
            connectionAddress: {
                connectionAddress: {
                    id: this.formDetails.energyProductionCommon.connectionAddress?.address?.carId || '',
                    text: this.formDetails.energyProductionCommon.connectionAddress?.address?.formattedAddress || ''
                },
                consumptionUnitType: this.formDetails.energyProductionCommon.connectionAddress?.consumptionUnitType || null,
                locationType: this.formDetails.energyProductionCommon.connectionAddress?.locationType || null,
                cadastralDistrictIdentifier:
                    this.formDetails.energyProductionCommon.connectionAddress?.cadastralDistrictIdentifier || null,
                cadastralRegistrationNumber:
                    this.formDetails.energyProductionCommon.connectionAddress?.cadastralRegistrationNumber || null,
                locationUtmX: this.formDetails.energyProductionCommon.connectionAddress?.location?.utmX || null,
                locationUtmY: this.formDetails.energyProductionCommon.connectionAddress?.location?.utmY || null,
                foundationElevation: this.formDetails.energyProductionCommon.connectionAddress?.foundationElevation || null,
                buildingNumber: this.formDetails.energyProductionCommon.connectionAddress?.buildingNumber || null,
                propertyNumber: this.formDetails.energyProductionCommon.connectionAddress?.propertyNumber || null,
                connectionPointUtmX: this.formDetails.energyProductionCommon.connectionAddress?.connectionPoint?.utmX,
                connectionPointUtmY: this.formDetails.energyProductionCommon.connectionAddress?.connectionPoint?.utmY
            },
            windPlant: {
                certificate: this.formDetails.energyProductionWindPlant?.certificate || null,
                certificationProvider: this.formDetails.energyProductionWindPlant?.certificationProvider || null,
                rotorDiameterInMeters: this.formDetails.energyProductionWindPlant?.rotorDiameterInMeters || null,
                hubHeightInMeters: this.formDetails.energyProductionWindPlant?.hubHeightInMeters || null
            },
            powerPlant: {
                primaryEnergySource: this.formDetails.energyProductionPowerPlant?.primaryEnergySource || null,
                primaryEnergyShareInPercent: this.formDetails.energyProductionPowerPlant?.primaryEnergyShareInPercent || null,
                secondaryEnergySource: this.formDetails.energyProductionPowerPlant?.secondaryEnergySource || null,
                secondaryEnergyShareInPercent: this.formDetails.energyProductionPowerPlant?.secondaryEnergyShareInPercent || null,
                plantTypeName: this.formDetails.energyProductionPowerPlant?.plantTypeName || null
            },
            plants: {
                hasPlantsChanged: false
            },
            relatedForms: {
                relatedForms: [...this.formDetails.relatedForms],
                hasRelatedFormsChanged: false
            }
        });
        this.populatePlantsArray(this.formDetails.energyProductionCommon.plants || []);

        if (this.plantsSection) {
            this.plantsSection.calculateTotalPower();
        }

        this.initDropDownOptions();
    }

    private populatePlantsArray(items: Plant[]) {
        const control = <FormArray>this.plantsForm.controls['plants'];
        control.clear();
        const formDisabled = this.form.disabled;
        items.forEach((item) => {
            control.push(this.initPlant(item));
        });
        if (formDisabled && !this.form.disabled) {
            this.form.disable();
        }
    }

    private initPlant(item: Plant) {
        return this.fb.group({
            phaseCount: [item.phaseCount, [Validators.required, Validators.min(1), Validators.max(3)]],
            totalPowerInKw: [item.totalPowerInKw, [Validators.required]],
            commissioningDateSod: [
                this.isEnergyProductionTypeNotWind() ? item.commissioningDateSod : null,
                [
                    (control: AbstractControl) => conditionallyRequiredValidator(control, this.isEnergyProductionTypeNotWind),
                    (control: AbstractControl) => dateMustNotBePastValidator(control)
                ]
            ],
            brandName: [
                item.brandName,
                [(control: AbstractControl) => conditionallyRequiredValidator(control, this.isEnergyProductionTypeNotConsumption)]
            ],
            modelName: [
                item.modelName,
                [(control: AbstractControl) => conditionallyRequiredValidator(control, this.isEnergyProductionTypeNotConsumption)]
            ],
            typeName: [
                item.typeName,
                [(control: AbstractControl) => conditionallyRequiredValidator(control, this.isEnergyProductionTypeSolar)]
            ],
            solarSystemAreaInM2: [
                item.solarSystemAreaInM2,
                [(control: AbstractControl) => conditionallyRequiredValidator(control, this.isEnergyProductionTypeSolar)]
            ]
        });
    }

    override onChange() {
        super.onChange();
        const controlX = this.connectionAddressForm.get('connectionPointUtmX')!;

        const controlY = this.connectionAddressForm.get('connectionPointUtmY')!;

        controlX?.valueChanges.pipe(startWith(controlX), pairwise()).subscribe(([prev, next]) => {
            if (prev != next) {
                controlY?.updateValueAndValidity({
                    onlySelf: true,
                    emitEvent: true
                });
            }
        });

        controlY?.valueChanges.pipe(startWith(controlY), pairwise()).subscribe(([prev, next]) => {
            if (prev != next) {
                controlX?.updateValueAndValidity({
                    onlySelf: true,
                    emitEvent: true
                });
            }
        });
    }

    getFormComparisonModelPaymentDetailsOnly(): any {
        return {
            // Contact person
            contactPersonCompanyName: this.contactPersonForm.get('contactPersonCompanyName')?.value,
            contactPersonName: this.contactPersonForm.get('contactPersonName')?.value,
            contactPersonEmail: this.contactPersonForm.get('contactPersonEmail')?.value,
            contactPersonPhoneNumber: this.contactPersonForm.get('contactPersonPhoneNumber')?.value,

            //  Payer
            payerName: this.payerForm.get('payerName')?.value,
            payerEmail: this.payerForm.get('payerEmail')?.value,
            payerType: this.payerForm.get('payerType')?.value,
            payerContactPersonName: this.payerForm.get('payerContactPersonName')?.value,
            payerContactPersonEmail: this.payerForm.get('payerContactPersonEmail')?.value,
            payerContactPersonPhoneNumber: this.payerForm.get('payerContactPersonPhoneNumber')?.value,
            requisition: this.payerForm.get('requisition')?.value,
            cvrOrSeNumber: this.payerForm.get('cvrOrSeNumber')?.value,
            eanNumber: this.payerForm.get('eanNumber')?.value,
            payerAddress: [this.payerForm.get('payerAddress')?.value?.id, this.payerForm.get('payerAddress')?.value?.text]
        };
    }

    getFormComparisonModel(): any {
        return {
            installationAddress: [
                this.installationInformationForm.get('installationAddress')?.value?.id,
                this.installationInformationForm.get('installationAddress')?.value?.text
            ],
            connectionPoint: [
                this.installationInformationForm.get('connectionPoint')?.value?.id || '',
                this.installationInformationForm.get('connectionPoint')?.value?.connectionPointNumber || ''
            ],
            meterFrame: [
                this.installationInformationForm.get('meterFrame')?.value?.id || '',
                this.installationInformationForm.get('meterFrame')?.value?.meterFrameNumber || ''
            ],
            consumptionMeteringPoint: [
                this.consumptionMeteringPoint?.value?.meteringPointId || '',
                this.consumptionMeteringPoint?.value?.meteringPointVersionId || ''
            ],
            productionMeteringPoint: [
                this.productionMeteringPoint?.value?.meteringPointId || '',
                this.productionMeteringPoint?.value?.meteringPointVersionId || ''
            ],
            contactPersonCompanyName: this.contactPersonForm.get('contactPersonCompanyName')?.value,
            contactPersonName: this.contactPersonForm.get('contactPersonName')?.value,
            contactPersonEmail: this.contactPersonForm.get('contactPersonEmail')?.value,
            contactPersonPhoneNumber: this.contactPersonForm.get('contactPersonPhoneNumber')?.value,
            remarksToInstallation: this.installationInformationForm.get('remarksToInstallation')?.value,
            tags: this.installationInformationForm.get('tags')?.value,

            payerName: this.payerForm.get('payerName')?.value,
            payerEmail: this.payerForm.get('payerEmail')?.value,
            payerType: this.payerForm.get('payerType')?.value,
            payerContactPersonName: this.payerForm.get('payerContactPersonName')?.value,
            payerContactPersonEmail: this.payerForm.get('payerContactPersonEmail')?.value,
            payerContactPersonPhoneNumber: this.payerForm.get('payerContactPersonPhoneNumber')?.value,
            requisition: this.payerForm.get('requisition')?.value,
            cvrOrSeNumber: this.payerForm.get('cvrOrSeNumber')?.value,
            eanNumber: this.payerForm.get('eanNumber')?.value,
            payerAddress: [this.payerForm.get('payerAddress')?.value?.id, this.payerForm.get('payerAddress')?.value?.text],

            // instructionData
            instructionDataMeterResponsible: this.instructionDataForm.get('meterResponsible')?.value || null,
            instructionDataBranchLineResponsible: this.instructionDataForm.get('branchLineResponsible')?.value || null,
            instructionDataConnectionFeeFee: this.instructionDataForm.get('connectionFeeFee')?.value || null,
            instructionDataConnectionFeeValidUntilEod: this.instructionDataForm.get('connectionFeeValidUntilEod')?.value || null,
            instructionDataRemark: this.instructionDataForm.get('remark')?.value || null,

            //energy production common
            energyProductionConnectionType: this.energyProductionCommonForm.get('energyProductionConnectionType')?.value || null,
            ownerName: this.energyProductionCommonForm.get('ownerName')?.value || null,
            ownerIdentificationType: this.energyProductionCommonForm.get('ownerIdentificationType')?.value || null,
            ownerIdentifier: this.energyProductionCommonForm.get('ownerIdentifier')?.value || null,
            ownerAddress: [
                this.energyProductionCommonForm.get('ownerAddress')?.value?.id || '',
                this.energyProductionCommonForm.get('ownerAddress')?.value?.text || ''
            ],
            moreThan125kW: this.energyProductionCommonForm.get('moreThan125kW')?.value || false,
            includesStorage: this.energyProductionCommonForm.get('includesStorage')?.value || false,
            commissioningDateSod: this.energyProductionCommonForm.get('commissioningDateSod')?.value || null,

            //connection address
            connectionAddress: [
                this.connectionAddressForm.get('connectionAddress')?.value?.id || '',
                this.connectionAddressForm.get('connectionAddress')?.value?.text || ''
            ],
            consumptionUnitType: this.connectionAddressForm.get('consumptionUnitType')?.value || null,
            locationType: this.connectionAddressForm.get('locationType')?.value || null,
            cadastralDistrictIdentifier: this.connectionAddressForm.get('cadastralDistrictIdentifier')?.value || null,
            cadastralRegistrationNumber: this.connectionAddressForm.get('cadastralRegistrationNumber')?.value || null,
            locationUtmX: this.connectionAddressForm.get('locationUtmX')?.value || null,
            locationUtmY: this.connectionAddressForm.get('locationUtmY')?.value || null,
            foundationElevation: this.connectionAddressForm.get('foundationElevation')?.value || null,
            buildingNumber: this.connectionAddressForm.get('buildingNumber')?.value || null,
            propertyNumber: this.connectionAddressForm.get('propertyNumber')?.value || null,
            connectionPointUtmX: this.connectionAddressForm.get('connectionPointUtmX')?.value?.toString() || null,
            connectionPointUtmY: this.connectionAddressForm.get('connectionPointUtmY')?.value?.toString() || null,

            //plants
            hasPlantsChanged: this.plantsForm.get('hasPlantsChanged')?.value || false,

            //related forms
            hasRelatedFormsChanged: this.relatedFormsForm.get('hasRelatedFormsChanged')?.value || false
        };
    }

    convertDetailsToComparisonModelPaymentDetailsOnly(model: EnergyProduction): any {
        return {
            // Contact person
            contactPersonCompanyName: model.contactPerson?.companyName || '',
            contactPersonName: model.contactPerson?.name || '',
            contactPersonEmail: model.contactPerson?.email || '',
            contactPersonPhoneNumber: model.contactPerson?.phoneNumber || '',

            //  Payer
            payerName: model.payer?.name || '',
            payerType: model.payer?.type || '',
            payerEmail: model.payer?.email || '',
            payerContactPersonName: model.payer?.contactPerson?.name || '',
            payerContactPersonEmail: model.payer?.contactPerson?.email || '',
            payerContactPersonPhoneNumber: model.payer?.contactPerson?.phoneNumber || '',
            requisition: model.payer?.requisition || '',
            cvrOrSeNumber: model.payer?.cvrOrSeNumber || '',
            eanNumber: model.payer?.eanNumber || '',
            payerAddress: [model.payer?.address?.carId || '', model.payer?.address?.formattedAddress || '']
        };
    }

    convertDetailsToComparisonModel(model: EnergyProduction): any {
        return {
            installationAddress: [model.installationAddress?.carId || '', model.installationAddress?.formattedAddress || ''],
            connectionPoint: [model.connectionPoint?.id || '', model.connectionPoint?.connectionPointNumber || ''],
            meterFrame: [model.meterFrame?.id || '', model.meterFrame?.meterFrameNumber || ''],
            consumptionMeteringPoint: [
                model.consumptionMeteringPoint?.meteringPointId || '',
                model.consumptionMeteringPoint?.meteringPointVersionId || ''
            ],
            productionMeteringPoint: [
                model.productionMeteringPoint?.meteringPointId || '',
                model.productionMeteringPoint?.meteringPointVersionId || ''
            ],
            contactPersonCompanyName: model.contactPerson?.companyName || '',
            contactPersonName: model.contactPerson?.name || '',
            contactPersonEmail: model.contactPerson?.email || '',
            contactPersonPhoneNumber: model.contactPerson?.phoneNumber || '',
            remarksToInstallation: model.remarksToInstallation || '',
            tags: model.tags.map((t) => t.id),

            //  Payer
            payerName: model.payer?.name || '',
            payerType: model.payer?.type || '',
            payerEmail: model.payer?.email || '',
            payerContactPersonName: model.payer?.contactPerson?.name || '',
            payerContactPersonEmail: model.payer?.contactPerson?.email || '',
            payerContactPersonPhoneNumber: model.payer?.contactPerson?.phoneNumber || '',
            requisition: model.payer?.requisition || '',
            cvrOrSeNumber: model.payer?.cvrOrSeNumber || '',
            eanNumber: model.payer?.eanNumber || '',
            payerAddress: [model.payer?.address?.carId || '', model.payer?.address?.formattedAddress || ''],
            // instructionData
            instructionDataMeterResponsible: model.instructionData?.meterResponsible || null,
            instructionDataBranchLineResponsible: model.instructionData?.branchLineResponsible || null,
            instructionDataConnectionFeeFee: model.instructionData?.connectionFee?.fee || null,
            instructionDataConnectionFeeValidUntilEod: model.instructionData?.connectionFee?.validUntilEod || null,
            instructionDataRemark: model.instructionData?.remark || null,

            //energy production common
            energyProductionConnectionType: model.energyProductionCommon.energyProductionConnectionType || null,
            ownerName: model.energyProductionCommon.owner?.name || null,
            ownerIdentificationType: model.energyProductionCommon.owner?.ownerIdentification?.ownerIdentificationType || null,
            ownerIdentifier: model.energyProductionCommon.owner?.ownerIdentification?.identifier || null,
            ownerAddress: [
                model.energyProductionCommon.owner?.address?.carId || '',
                model.energyProductionCommon.owner?.address?.formattedAddress || ''
            ],
            moreThan125kW: model.energyProductionCommon.moreThan125kW || false,
            includesStorage: model.energyProductionCommon.includesStorage || false,
            commissioningDateSod: model.energyProductionCommon.commissioningDateSod || null,

            //connection address
            connectionAddress: [
                model.energyProductionCommon.connectionAddress?.address?.carId || '',
                model.energyProductionCommon.connectionAddress?.address?.formattedAddress || ''
            ],
            consumptionUnitType: model.energyProductionCommon.connectionAddress?.consumptionUnitType || null,
            locationType: model.energyProductionCommon.connectionAddress?.locationType || null,
            cadastralDistrictIdentifier: model.energyProductionCommon.connectionAddress?.cadastralDistrictIdentifier || null,
            cadastralRegistrationNumber: model.energyProductionCommon.connectionAddress?.cadastralRegistrationNumber || null,
            locationUtmX: model.energyProductionCommon.connectionAddress?.location?.utmX || null,
            locationUtmY: model.energyProductionCommon.connectionAddress?.location?.utmY || null,
            foundationElevation: model.energyProductionCommon.connectionAddress?.foundationElevation || null,
            buildingNumber: model.energyProductionCommon.connectionAddress?.buildingNumber || null,
            propertyNumber: model.energyProductionCommon.connectionAddress?.propertyNumber || null,
            connectionPointUtmX: model.energyProductionCommon.connectionAddress?.connectionPoint?.utmX?.toString() || null,
            connectionPointUtmY: model.energyProductionCommon.connectionAddress?.connectionPoint?.utmY?.toString() || null,

            //plants
            hasPlantsChanged: false,

            // related forms
            hasRelatedFormsChanged: false
        };
    }

    protected override updateFormData(
        installationFormId: string,
        es_message_id: string,
        row_version: string,
        body: EnergyProductionUpdate
    ): Observable<InstallationFormsApiResponse<void>> {
        return this.client.updateEnergyProductionFormData(installationFormId, es_message_id, row_version, body);
    }

    protected override createFormDataUpdate(): EnergyProductionUpdate {
        return new EnergyProductionUpdate({
            installationInformationUpdate: this.createInstallationInformationUpdate(),
            payerUpdate: this.createPayerUpdate(),
            instructionDataUpdate: this.createInstructionDataUpdate(),
            energyProductionCommon: this.createEnergyProductionCommon(),
            energyProductionWindPlant: this.createEnergyProductionWindPlant(),
            energyProductionPowerPlant: this.createEnergyProductionPowerPlant(),
            relatedFormsUpdate: this.relatedFormsForm
                .get('relatedForms')
                ?.value.filter((x: RelatedForm) => x.relationType === FormsRelationType.Manual)
                .map((x: RelatedForm) => new RelatedFormUpdate({ formId: x.formId }))
        });
    }

    protected override createPaymentDetailsUpdate(): PaymentDetailsUpdate {
        const update = new PaymentDetailsUpdate({
            contactPerson: this.createContactPersonUpdate(),
            payer: this.createPayerUpdate()
        });
        return update;
    }

    private createContactPersonUpdate(): ContactPerson {
        const contactPersonFormValue = this.contactPersonForm.value;
        return new ContactPerson({
            companyName: getValueOrDefault(contactPersonFormValue.contactPersonCompanyName),
            name: getValueOrDefault(contactPersonFormValue.contactPersonName),
            email: getValueOrDefault(contactPersonFormValue.contactPersonEmail),
            phoneNumber: getValueOrDefault(contactPersonFormValue.contactPersonPhoneNumber)
        });
    }

    private createInstallationInformationUpdate(): EnergyProductionInstallationInformationUpdate {
        const installationInformationFormValue = this.installationInformationForm.value;
        const contactPersonFormValue = this.contactPersonForm.value;
        return new EnergyProductionInstallationInformationUpdate({
            contactPerson: new ContactPerson({
                companyName: getValueOrDefault(contactPersonFormValue.contactPersonCompanyName),
                name: getValueOrDefault(contactPersonFormValue.contactPersonName),
                email: getValueOrDefault(contactPersonFormValue.contactPersonEmail),
                phoneNumber: getValueOrDefault(contactPersonFormValue.contactPersonPhoneNumber)
            }),
            installationInformationCarId: getValueOrDefault(installationInformationFormValue.installationAddress?.id),
            remarksToInstallation: getValueOrDefault(installationInformationFormValue.remarksToInstallation),
            connectionPointId: getValueOrDefault(installationInformationFormValue.connectionPoint?.id),
            meterFrameId: getValueOrDefault(installationInformationFormValue.meterFrame?.id),
            consumptionMeteringPoint: createMeteringPoint(installationInformationFormValue.consumptionMeteringPoint),
            productionMeteringPoint: createMeteringPoint(installationInformationFormValue.productionMeteringPoint),
            tags: installationInformationFormValue.tags || []
        });
    }

    private createPayerUpdate(): PayerUpdate {
        const payerFormValue = this.payerForm.value;
        return new PayerUpdate({
            contactPerson: new ContactPerson({
                email: getValueOrDefault(payerFormValue.payerContactPersonEmail),
                name: getValueOrDefault(payerFormValue.payerContactPersonName),
                phoneNumber: getValueOrDefault(payerFormValue.payerContactPersonPhoneNumber)
            }),
            payerType: payerFormValue.payerType,
            payerCarId: getValueOrDefault(payerFormValue.payerAddress?.id),
            name: getValueOrDefault(payerFormValue.payerName),
            email: getValueOrDefault(payerFormValue.payerEmail),
            requisition: getValueOrDefault(payerFormValue.requisition),
            cvrOrSeNumber: getValueOrDefault(payerFormValue.cvrOrSeNumber),
            eanNumber: getValueOrDefault(payerFormValue.eanNumber)
        });
    }

    private createInstructionDataUpdate(): InstructionDataUpdate {
        const instructionDataFormValue = this.instructionDataForm.value;

        return new InstructionDataUpdate({
            meterResponsible: instructionDataFormValue.meterResponsible as MeterResponsible,
            branchLineResponsible: instructionDataFormValue.branchLineResponsible as BranchLineResponsible,
            connectionFee: instructionDataFormValue.connectionFeeFee
                ? new ConnectionFee({
                      currency: 'DKK', // So far there's no support for different currencies. Hardcoded DKK
                      fee: instructionDataFormValue.connectionFeeFee,
                      validUntilEod: instructionDataFormValue.connectionFeeValidUntilEod
                  })
                : undefined,
            remark: getValueOrDefault(instructionDataFormValue.remark)
        });
    }

    private createEnergyProductionCommon(): EnergyProductionCommon {
        const energyProductionCommonFormValue = this.energyProductionCommonForm.getRawValue();
        return new EnergyProductionCommon({
            energyProductionConnectionType:
                energyProductionCommonFormValue.energyProductionConnectionType as EnergyProductionConnectionType,
            owner: new Owner({
                name: getValueOrDefault(energyProductionCommonFormValue.ownerName),
                ownerIdentification: new OwnerIdentification({
                    ownerIdentificationType: energyProductionCommonFormValue.ownerIdentificationType as OwnerIdentificationType,
                    identifier: getValueOrDefault(
                        energyProductionCommonFormValue.ownerIdentificationType === OwnerIdentificationType.DateOfBirth
                            ? this.formatOwnerIdentifierDateForSave(energyProductionCommonFormValue.ownerIdentifier)
                            : energyProductionCommonFormValue.ownerIdentifier
                    )
                }),
                address: new AddressLookup({
                    carId: getValueOrDefault(energyProductionCommonFormValue.ownerAddress?.id),
                    formattedAddress: getValueOrDefault(energyProductionCommonFormValue.ownerAddress?.text)
                })
            }),
            moreThan125kW: energyProductionCommonFormValue.moreThan125kW,
            includesStorage: energyProductionCommonFormValue.includesStorage,
            commissioningDateSod: getValueOrDefault(energyProductionCommonFormValue.commissioningDateSod),
            connectionAddress: this.createConnectionAddressFromForm(),
            plants: this.createPlants()
        });
    }

    private formatOwnerIdentifierDateForSave(ownerIdentifier: Date | string | undefined): string | null {
        if (!ownerIdentifier) {
            return null;
        }

        let ownerIdentifierDate: Date = <Date>ownerIdentifier;
        if (!ownerIdentifierDate.getDate) {
            ownerIdentifierDate = new Date(ownerIdentifier);
        }

        return `${ownerIdentifierDate.getFullYear()}-${(ownerIdentifierDate.getMonth() + 1).toString().padStart(2, '0')}-${ownerIdentifierDate.getDate().toString().padStart(2, '0')}`;
    }

    private createEnergyProductionWindPlant(): EnergyProductionWindPlant | undefined {
        if (this.formDetails.category !== FormCategory.Wind) {
            return undefined;
        }
        const formValue = this.windPlantForm.value;

        return new EnergyProductionWindPlant({
            certificate: getValueOrDefault(formValue.certificate),
            certificationProvider: getValueOrDefault(formValue.certificationProvider),
            rotorDiameterInMeters: formValue.rotorDiameterInMeters,
            hubHeightInMeters: formValue.hubHeightInMeters
        });
    }

    private createEnergyProductionPowerPlant(): EnergyProductionPowerPlant | undefined {
        if (this.formDetails.category !== FormCategory.PowerPlant) {
            return undefined;
        }

        const formValue = this.powerPlantForm.value;
        return new EnergyProductionPowerPlant({
            primaryEnergySource: getValueOrDefault(formValue.primaryEnergySource),
            primaryEnergyShareInPercent: getValueOrDefault(formValue.primaryEnergyShareInPercent),
            secondaryEnergySource: getValueOrDefault(formValue.secondaryEnergySource),
            secondaryEnergyShareInPercent: getValueOrDefault(formValue.secondaryEnergyShareInPercent),
            plantTypeName: getValueOrDefault(formValue.plantTypeName)
        });
    }

    private createPlants(): Plant[] | undefined {
        let plants = this.plantsForm.get('plants')?.value as Plant[];
        // we have an object that has the same structure as Plant array, but we have to create it with constructor to explicitly have a type
        let newPlants = plants.map(
            (item) =>
                new Plant({
                    phaseCount: item.phaseCount,
                    totalPowerInKw: item.totalPowerInKw,
                    commissioningDateSod: getValueOrDefault(item.commissioningDateSod),
                    brandName: getValueOrDefault(item.brandName),
                    modelName: getValueOrDefault(item.modelName),
                    typeName: getValueOrDefault(item.typeName),
                    solarSystemAreaInM2: item.solarSystemAreaInM2
                })
        );
        return newPlants.length > 0 ? newPlants : undefined;
    }

    isEnergyProductionTypeConsumption = () => {
        return this.getEnergyProductionType() === FormCategory.Consumption;
    };

    isEnergyProductionTypeWind = () => {
        return this.getEnergyProductionType() === FormCategory.Wind;
    };

    isCadastralRegistrationNumberRequired = () => {
        return (
            this.getEnergyProductionType() === FormCategory.Wind &&
            this.connectionAddressForm.get('locationType')?.value === LocationType.Land
        );
    };

    isEnergyProductionTypePowerPlant = () => {
        return this.getEnergyProductionType() === FormCategory.PowerPlant;
    };

    isEnergyProductionTypeSolar = () => {
        return this.getEnergyProductionType() === FormCategory.Solar;
    };

    isEnergyProductionTypeNotWind = () => {
        return this.getEnergyProductionType() !== FormCategory.Wind;
    };

    isEnergyProductionTypeNotConsumption = () => {
        return !this.isEnergyProductionTypeConsumption();
    };

    getEnergyProductionType(): FormCategory {
        return this.formDetails?.category!;
    }

    isConnectionPointCoordinateNotEmpty = (): boolean => {
        if (this.connectionAddressForm) {
            return (
                this.connectionAddressForm.get('connectionPointUtmY')?.value &&
                this.connectionAddressForm.get('connectionPointUtmX')?.value
            );
        }
        return !!(
            this.formDetails?.energyProductionCommon.connectionAddress?.location?.utmX &&
            this.formDetails?.energyProductionCommon.connectionAddress?.location?.utmY
        );
    };

    createConnectionAddressFromForm(): ConnectionAddress | undefined {
        switch (this.formDetails.category) {
            case FormCategory.Consumption:
                return new ConnectionAddress({
                    address: new AddressLookup({
                        carId: getValueOrDefault(this.connectionAddressForm.get('connectionAddress')?.value?.id),
                        formattedAddress: getValueOrDefault(this.connectionAddressForm.get('connectionAddress')?.value?.text)
                    }),
                    consumptionUnitType: this.connectionAddressForm.get('consumptionUnitType')?.value || null
                });
            case FormCategory.PowerPlant:
                return new ConnectionAddress({
                    address: new AddressLookup({
                        carId: getValueOrDefault(this.connectionAddressForm.get('connectionAddress')?.value?.id),
                        formattedAddress: getValueOrDefault(this.connectionAddressForm.get('connectionAddress')?.value?.text)
                    }),
                    buildingNumber: this.connectionAddressForm.get('buildingNumber')?.value || null,
                    propertyNumber: this.connectionAddressForm.get('propertyNumber')?.value || null
                });
            case FormCategory.Wind:
                return new ConnectionAddress({
                    address: new AddressLookup({
                        carId: getValueOrDefault(this.connectionAddressForm.get('connectionAddress')?.value?.id),
                        formattedAddress: getValueOrDefault(this.connectionAddressForm.get('connectionAddress')?.value?.text)
                    }),
                    locationType: this.connectionAddressForm.get('locationType')?.value || null,
                    cadastralDistrictIdentifier: this.connectionAddressForm.get('cadastralDistrictIdentifier')?.value || null,
                    cadastralRegistrationNumber: this.connectionAddressForm.get('cadastralRegistrationNumber')?.value || null,
                    location: new UtmCoordinate({
                        utmX: this.connectionAddressForm.get('locationUtmX')?.value || null,
                        utmY: this.connectionAddressForm.get('locationUtmY')?.value || null
                    }),
                    foundationElevation: this.connectionAddressForm.get('foundationElevation')?.value || null
                });
            case FormCategory.Solar:
                return new ConnectionAddress({
                    address: new AddressLookup({
                        carId: getValueOrDefault(this.connectionAddressForm.get('connectionAddress')?.value?.id),
                        formattedAddress: getValueOrDefault(this.connectionAddressForm.get('connectionAddress')?.value?.text)
                    }),
                    connectionPoint: this.getConnectionPointForSolarPlant()
                });
            default:
                return undefined;
        }
    }

    getConnectionPointForSolarPlant(): UtmCoordinate | undefined {
        if (
            this.connectionAddressForm.get('connectionPointUtmX')?.value === null &&
            this.connectionAddressForm.get('connectionPointUtmY')?.value === null
        ) {
            return undefined;
        } else
            return new UtmCoordinate({
                utmX: this.connectionAddressForm.get('connectionPointUtmX')?.value,
                utmY: this.connectionAddressForm.get('connectionPointUtmY')?.value
            });
    }
}
