import { TestBed } from '@angular/core/testing';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { of } from 'rxjs';
import { BooleanOptionsService } from './boolean-options.service';

describe('BooleanOptionsService', () => {
    let service: BooleanOptionsService;
    let translateServiceMock: { get: jest.Mock };

    const mockTranslations = {
        'common.yesOption': 'Yes',
        'common.noOption': 'No',
        'common.emptyValue': 'All'
    };

    beforeEach(() => {
        translateServiceMock = {
            get: jest.fn().mockReturnValue(of(mockTranslations))
        };

        TestBed.configureTestingModule({
            providers: [BooleanOptionsService, { provide: TranslateService, useValue: translateServiceMock }]
        });

        service = TestBed.inject(BooleanOptionsService);
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    it('should return yes/no options without empty option when includeEmpty is false', (done) => {
        service.getBooleanOptions(false).subscribe((options: SelectItem[]) => {
            expect(options.length).toBe(2);

            expect(options[0].value).toBe(true);
            expect(options[0].label).toBe('Yes');

            expect(options[1].value).toBe(false);
            expect(options[1].label).toBe('No');

            done();
        });

        expect(translateServiceMock.get).toHaveBeenCalledWith(['common.yesOption', 'common.noOption', 'common.emptyValue']);
    });

    it('should return yes/no options with empty option when includeEmpty is true', (done) => {
        service.getBooleanOptions(true).subscribe((options: SelectItem[]) => {
            expect(options.length).toBe(3);

            expect(options[0].value).toBeNull();
            expect(options[0].label).toBe('All');

            expect(options[1].value).toBe(true);
            expect(options[1].label).toBe('Yes');

            expect(options[2].value).toBe(false);
            expect(options[2].label).toBe('No');

            done();
        });

        expect(translateServiceMock.get).toHaveBeenCalledWith(['common.yesOption', 'common.noOption', 'common.emptyValue']);
    });

    it('should use default parameter value (false) when includeEmpty is not provided', (done) => {
        service.getBooleanOptions().subscribe((options: SelectItem[]) => {
            expect(options.length).toBe(2);
            expect(options[0].value).toBe(true);
            expect(options[1].value).toBe(false);
            done();
        });
    });

    it('should handle different translation values correctly', (done) => {
        const customTranslations = {
            'common.yesOption': 'Ja',
            'common.noOption': 'Nein',
            'common.emptyValue': 'Alle'
        };

        translateServiceMock.get.mockReturnValueOnce(of(customTranslations));

        service.getBooleanOptions(true).subscribe((options: SelectItem[]) => {
            expect(options[0].label).toBe('Alle');
            expect(options[1].label).toBe('Ja');
            expect(options[2].label).toBe('Nein');
            done();
        });
    });
});
