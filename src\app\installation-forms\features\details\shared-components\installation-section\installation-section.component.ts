import { Component, Input, OnDestroy, OnInit, signal } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { LocationModel, Node } from '@kmd-elements-ui/topology-selector';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { Observable, Subscription, catchError, finalize, map, of } from 'rxjs';
import {
    ConnectionPoint,
    GetConnectionPointsRequestBody,
    GetMeteringPointsRequestBody,
    GisStatus,
    InstallationFormsClient,
    MeterFrame,
    MeterFramesSearchFilterModel,
    MeterFramesSearchQueryModel,
    MeteringPointType,
    MeteringPointVersion,
    TemporaryInstallationDuration,
    ValueListType
} from 'src/app/api/installation-forms-client';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { convertSupplyTypeToEnum } from 'src/app/core/utils/mappers/supply-type/supply-type.mapper';
import { WidgetWithFormComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget-with-form.component';
import { v4 as uuidv4 } from 'uuid';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';

export interface IMeteringPointsInfo {
    meteringPointsOptions: MeteringPointVersion[];
    selectedMeteringPointVersion?: MeteringPointVersion;
}

@Component({
    selector: 'app-installation-section',
    templateUrl: './installation-section.component.html',
    styleUrls: ['./installation-section.component.scss'],
    standalone: false
})
export class InstallationSectionComponent extends WidgetWithFormComponent implements OnInit, OnDestroy {
    isConnectionPointsOptionsLoading: boolean = false;
    isMeterFramesOptionsLoading: boolean = false;
    isGridAreasOptionsLoading: boolean = false;
    private widgetTranslations: any;

    private consumptionMeteringPointsInfo?: IMeteringPointsInfo;
    private productionMeteringPointsInfo?: IMeteringPointsInfo;

    connectionPointsOptions: SelectItem[] = [];
    @Input() meterFramesOptions!: SelectItem[];
    @Input() form!: FormGroup;
    @Input() consumptionMeteringPointType!: MeteringPointType;
    @Input() productionMeteringPointType?: MeteringPointType;
    @Input() durationType?: TemporaryInstallationDuration;
    @Input() gridAreasOptions!: string[];
    @Input() showGridArea: boolean = false;

    @Input() showTopology: boolean = false;
    @Input() connectionForm?: FormGroup;
    @Input() formHasChanges: boolean = false;

    GisStatus = GisStatus;

    consumptionMeteringPointObjectName = 'consumptionMeteringPoint';
    productionMeteringPointObjectName = 'productionMeteringPoint';

    subscription: Subscription = new Subscription();

    tagsValueListType: ValueListType = ValueListType.InstallationFormTags;

    showMap = signal<boolean>(false);
    showInitialLocationMarker = signal<boolean>(false);
    meterFrameLocation = signal<LocationModel | undefined>(undefined);
    initialTopologyNode = signal<{ nodeId: string; containerId: string } | undefined>(undefined);
    isGisSyncing = false;

    constructor(
        private readonly client: InstallationFormsClient,
        private readonly router: Router,
        private readonly translateService: TranslateService,
        private readonly messageServiceHelper: MessageServiceHelper,
        protected readonly formDataService: FormDataService
    ) {
        super(formDataService);
    }

    ngOnInit(): void {
        this.widgetTranslations = this.translateService.instant('installationWidget');

        this.form.get('connectionPoint')?.valueChanges.subscribe((_) => {
            const control = this.form.get('gridAreaId');
            if (this.isGridAreaReadOnly) {
                control?.disable({ emitEvent: false });
            } else {
                control?.enable({ emitEvent: false });
            }
        });

        // load metering points options to show warning if amount of options is not 1
        this.getOptionsAndUpdateMeteringPoint(
            this.consumptionMeteringPointType,
            this.consumptionMeteringPointObjectName,
            false
        ).subscribe((x) => {
            this.consumptionMeteringPointsInfo = x;
        });

        this.getOptionsAndUpdateMeteringPoint(
            this.productionMeteringPointType,
            this.productionMeteringPointObjectName,
            false
        ).subscribe((x) => {
            this.productionMeteringPointsInfo = x;
        });

        this.loadConnectionPointsOptions();
        this.formDataService.formLoaded$.subscribe(() => {
            this.loadConnectionPointsOptions();
        });
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    newAddressSelected() {
        this.form.patchValue({
            connectionPoint: {
                id: '',
                connectionPointNumber: ''
            },
            meterFrame: {
                id: '',
                meterFrameNumber: ''
            },
            consumptionMeteringPoint: {
                meteringPointId: '',
                meteringPointVersionId: ''
            },
            productionMeteringPoint: {
                meteringPointId: '',
                meteringPointVersionId: ''
            }
        });
        this.loadConnectionPointsOptions(true);
    }

    private loadConnectionPointsOptions(selectIfOne: boolean = false) {
        let carId = this.form.get('installationAddress')?.value?.id;
        if (!carId) {
            this.connectionPointsOptions = [];
            this.form.get('connectionPoint')?.setValue({ id: '', connectionPointNumber: '', electricityConnectionStatusId: '' });
            return;
        }
        this.isConnectionPointsOptionsLoading = true;
        this.client
            .searchConnectionPoints(
                uuidv4(),
                new GetConnectionPointsRequestBody({
                    carId: carId,
                    formType: this.formDataService.type,
                    supplyType: convertSupplyTypeToEnum(this.formDataService.supplyType!),
                    meterNumber: this.formDataService.meterNumber
                })
            )
            .pipe(
                finalize(() => {
                    this.isConnectionPointsOptionsLoading = false;
                })
            )
            .subscribe({
                next: (x) => {
                    let connectionPointsOptions = x.result
                        .filter((connectionPoint): connectionPoint is ConnectionPoint => connectionPoint !== undefined)
                        .map(
                            (connectionPoint) =>
                                ({
                                    label: connectionPoint.connectionPointNumber,
                                    value: connectionPoint
                                }) as SelectItem
                        );

                    const connectionPoint = this.formDataService.connectionPoint;

                    if (connectionPoint?.id && !connectionPointsOptions?.find((x) => x?.value?.id === connectionPoint.id)) {
                        connectionPointsOptions = [
                            {
                                label: `${connectionPoint.connectionPointNumber} (${this.translateService.instant('installationWidget.connectionPoint.connectionStateNotValid')})`,
                                value: connectionPoint
                            } as SelectItem,
                            ...connectionPointsOptions
                        ];
                    }

                    this.connectionPointsOptions = connectionPointsOptions;

                    if (this.connectionPointsOptions.length === 1 && selectIfOne) {
                        this.form.patchValue({
                            connectionPoint: {
                                id: this.connectionPointsOptions[0].value.id,
                                connectionPointNumber: this.connectionPointsOptions[0].value.connectionPointNumber,
                                electricityConnectionStatusId: this.connectionPointsOptions[0].value.electricityConnectionStatusId
                            }
                        });
                        this.onConnectionPointChange();
                    }
                },
                error: (_error) => {
                    this.messageServiceHelper.showError({
                        detail: this.widgetTranslations['connectionPointsSearchError'],
                        key: this.formDataService.formId
                    });
                }
            });
    }

    private loadMeterFramesOptions(selectIfOne: boolean = false) {
        let connectionPointId = this.form.get('connectionPoint')?.value?.id;
        if (!connectionPointId) {
            this.meterFramesOptions = [];
            this.form.get('meterFrame')?.setValue({ id: '', meterFrameNumber: '' });
            return;
        }
        this.isMeterFramesOptionsLoading = true;
        let supplyType = convertSupplyTypeToEnum(this.formDataService.supplyType!);

        const filter = new MeterFramesSearchFilterModel({
            supplyType: supplyType,
            connectionPointId: connectionPointId,
            withValidElectricityPurposeOnly: true
        });

        const queryModel = new MeterFramesSearchQueryModel({ filter: filter });

        this.client
            .searchMeterFrames(uuidv4(), queryModel)
            .pipe(
                map(
                    (response) =>
                        response?.result?.results.map(
                            (item) =>
                                new MeterFrame({
                                    id: item.id,
                                    meterFrameNumber: item.meterFrameNumber
                                })
                        ) || []
                ),
                map((meterFrames) => this.sortMeterFramesByNumber(meterFrames)),
                finalize(() => {
                    this.isMeterFramesOptionsLoading = false;
                })
            )
            .subscribe({
                next: (meterFrames) => {
                    this.meterFramesOptions = meterFrames.map(
                        (meterFrame) =>
                            ({
                                label: meterFrame.meterFrameNumber,
                                value: meterFrame
                            }) as SelectItem
                    );

                    if (this.meterFramesOptions.length === 1 && selectIfOne) {
                        this.form.patchValue({
                            meterFrame: {
                                id: meterFrames[0].id,
                                meterFrameNumber: meterFrames[0].meterFrameNumber
                            }
                        });
                    }
                },
                error: (_error) => {
                    this.messageServiceHelper.showError({
                        detail: this.widgetTranslations['meterFramesSearchError'],
                        key: this.formDataService.formId
                    });
                    this.meterFramesOptions = [];
                }
            });
    }

    private getOptionsAndUpdateMeteringPoint(
        meteringPointType: MeteringPointType | undefined,
        meteringPointObjectName: string,
        shouldPatchMeteringPoint: boolean = true
    ): Observable<IMeteringPointsInfo> {
        let connectionPointId = this.form.get('connectionPoint')?.value?.id;
        let connectionPointElectricityConnectionStatusId =
            this.form.get('connectionPoint')?.value?.electricityConnectionStatusId || undefined;

        if (!meteringPointType || !connectionPointId) {
            if (shouldPatchMeteringPoint) {
                this.patchMeteringPointObject(meteringPointObjectName, undefined);
            }
            return of({
                meteringPointsOptions: [],
                selectedMeteringPointVersion: undefined
            });
        }

        return this.client
            .getLatestMeteringPointVersions(
                uuidv4(),
                new GetMeteringPointsRequestBody({
                    connectionPointId: connectionPointId,
                    connectionPointElectricityConnectionStatusId: connectionPointElectricityConnectionStatusId,
                    meteringPointType: meteringPointType,
                    formType: this.formDataService.type,
                    supplyType: convertSupplyTypeToEnum(this.formDataService.supplyType!),
                    meterNumber: this.formDataService.meterNumber
                })
            )
            .pipe(
                map((response) => {
                    const meteringPointsOptions = response.result?.filter((r) => r);

                    if (meteringPointsOptions?.length === 1) {
                        const meteringPoint = meteringPointsOptions[0];
                        if (shouldPatchMeteringPoint) {
                            this.patchMeteringPointObject(meteringPointObjectName, meteringPoint);
                        }
                        return {
                            meteringPointsOptions,
                            selectedMeteringPointVersion: meteringPoint
                        };
                    } else {
                        if (shouldPatchMeteringPoint) {
                            this.patchMeteringPointObject(meteringPointObjectName, undefined);
                        }
                        return {
                            meteringPointsOptions,
                            selectedMeteringPointVersion: undefined
                        };
                    }
                }),
                catchError((_) => {
                    this.messageServiceHelper.showError({
                        detail: this.widgetTranslations['loadMeteringPointsError'],
                        key: this.formDataService.formId!
                    });
                    return of({
                        meteringPointsOptions: [],
                        selectedMeteringPointVersion: undefined
                    });
                })
            );
    }

    private patchMeteringPointObject(meteringPointObjectName: string, meteringPoint: MeteringPointVersion | undefined) {
        this.form.patchValue({
            [meteringPointObjectName]: {
                meteringPointId: meteringPoint?.meteringPointId || '',
                meteringPointVersionId: meteringPoint?.meteringPointVersionId || ''
            }
        });
    }

    private loadGridAreaOptions(selectIfOne: boolean = false) {
        this.isGridAreasOptionsLoading = true;
        this.client
            .getGridAreas(uuidv4(), uuidv4())
            .pipe(
                catchError((_error) => {
                    this.messageServiceHelper.showError({
                        detail: this.widgetTranslations['loadGridAreasError'],
                        key: this.formDataService.formId
                    });
                    return of(undefined);
                })
            )
            .subscribe((x) => {
                this.gridAreasOptions =
                    x?.result.gridAreas
                        .filter((gridArea): gridArea is string => gridArea !== undefined)
                        .map((gridArea) => {
                            return gridArea;
                        }) || [];

                if (this.gridAreasOptions.length === 1 && selectIfOne) {
                    this.form.patchValue({
                        gridAreaId: this.gridAreasOptions[0]
                    });
                }
                this.isGridAreasOptionsLoading = false;
            });
    }

    onConnectionPointsDropdownShow() {
        this.loadConnectionPointsOptions();
    }

    onGridAreaChange() {
        this.form.get('connectionPoint')?.updateValueAndValidity();
    }

    onConnectionPointChange() {
        this.getOptionsAndUpdateMeteringPoint(
            this.consumptionMeteringPointType,
            this.consumptionMeteringPointObjectName
        ).subscribe((x) => (this.consumptionMeteringPointsInfo = x));
        this.getOptionsAndUpdateMeteringPoint(this.productionMeteringPointType, this.productionMeteringPointObjectName).subscribe(
            (x) => (this.productionMeteringPointsInfo = x)
        );

        this.form.patchValue({
            meterFrame: {
                id: '',
                meterFrameNumber: ''
            }
        });
        this.loadMeterFramesOptions(true);
        this.form.get('gridAreaId')?.updateValueAndValidity();
    }

    onMeterFramesDropdownShow() {
        this.loadMeterFramesOptions();
    }

    onGridAreasDropdownShow() {
        this.loadGridAreaOptions();
    }

    onMeterFrameChange() {
        if (this.showTopology) {
            this.client.getMeterFrameGisProperties(this.form.get('meterFrame')?.value?.id, uuidv4()).subscribe({
                next: (x) => {
                    if (x.result) {
                        this.connectionForm?.patchValue({
                            gisStatus: GisStatus.Ok,
                            containerId: x.result?.equipmentContainerId,
                            nodeId: x.result?.gisId,
                            gisDescription: x.result?.gisDescription,
                            cabinetNumber: x.result?.cabinetNumber,
                            transformerStationNumber: x.result?.transformerStationNumber
                        });
                    } else {
                        this.connectionForm?.patchValue({
                            gisStatus: GisStatus.NotSelected,
                            containerId: undefined,
                            nodeId: undefined,
                            gisDescription: undefined,
                            cabinetNumber: undefined,
                            transformerStationNumber: undefined
                        });
                    }
                },
                error: (_) => {
                    this.messageServiceHelper.showError({
                        detail: this.widgetTranslations['loadMeterFrameGisPropertiesError'],
                        key: this.formDataService.formId
                    });
                }
            });
        }
    }

    get isGridAreaReadOnly() {
        return this.isReadOnly || this.form.get('connectionPoint')?.value?.id;
    }

    onConnectionPointsDropdownHide() {
        let connectionPoint = this.form.get('connectionPoint')?.value;
        if (!connectionPoint) {
            this.form.patchValue({
                connectionPoint: { id: '', connectionPointNumber: '' }
            });
        }
    }

    onMeterFramesDropdownHide() {
        let meterFrame = this.form.get('meterFrame')?.value;
        if (!meterFrame) {
            this.form.patchValue({
                meterFrame: { id: '', meterFrameNumber: '' }
            });
        }
    }

    onGridAreasDropdownHide() {
        let gridAreaId = this.form.get('gridAreaId')?.value;
        if (!gridAreaId) {
            this.form.patchValue({
                gridAreaId: ''
            });
        }
    }

    async navigateToConnectionPoint() {
        if (this.form.get('connectionPoint')?.value) {
            const connectionPoint = this.form.get('connectionPoint')?.value as ConnectionPoint;
            await this.router.navigate([`connection-points/${connectionPoint.id}`]);
        }
    }

    async navigateToMeterFrame() {
        if (this.form.get('connectionPoint')?.value && this.form.get('meterFrame')?.value) {
            const connectionPoint = this.form.get('connectionPoint')?.value as ConnectionPoint;
            const meterFrame = this.form.get('meterFrame')?.value as MeterFrame;
            await this.router.navigate([`connection-points/${connectionPoint.id}/meter-frames/${meterFrame.id}`]);
        }
    }

    async navigateToMeteringPoint(meteringPointVersionId: string | undefined) {
        if (meteringPointVersionId) {
            await this.router.navigate([
                `metering-points/${this.formDataService.supplyType!.toLocaleLowerCase()}/${meteringPointVersionId}`
            ]);
        }
    }

    shouldDisplayConsumptionNoMatchingMeteringPointsWarning(): boolean {
        return this.shouldDisplayNoMatchingMeteringPointsWarning(
            this.consumptionMeteringPointsInfo,
            this.consumptionMeteringPointObjectName
        );
    }

    shouldDisplayProductionNoMatchingMeteringPointsWarning(): boolean {
        return this.shouldDisplayNoMatchingMeteringPointsWarning(
            this.productionMeteringPointsInfo,
            this.productionMeteringPointObjectName
        );
    }

    shouldDisplayConsumptionMultipleMeteringPointsWarning(): boolean {
        return this.shouldDisplayMultipleMeteringPointsWarning(
            this.consumptionMeteringPointsInfo,
            this.consumptionMeteringPointObjectName
        );
    }

    shouldDisplayProductionMultipleMeteringPointsWarning(): boolean {
        return this.shouldDisplayMultipleMeteringPointsWarning(
            this.productionMeteringPointsInfo,
            this.productionMeteringPointObjectName
        );
    }

    private shouldDisplayNoMatchingMeteringPointsWarning(
        meteringPointsInfo: IMeteringPointsInfo | undefined,
        meteringPointObjectName: string
    ): boolean {
        if (this.form.get(meteringPointObjectName)?.value.meteringPointId) {
            return false;
        }
        return !!meteringPointsInfo?.meteringPointsOptions && meteringPointsInfo.meteringPointsOptions.length === 0;
    }

    private shouldDisplayMultipleMeteringPointsWarning(
        meteringPointsInfo: IMeteringPointsInfo | undefined,
        meteringPointObjectName: string
    ): boolean {
        if (this.form.get(meteringPointObjectName)?.value.meteringPointId) {
            return false;
        }
        return !!meteringPointsInfo?.meteringPointsOptions && meteringPointsInfo.meteringPointsOptions.length > 1;
    }

    private sortMeterFramesByNumber(meterFrames: MeterFrame[]): MeterFrame[] {
        const getNumericPart = (str: string | undefined): number => {
            if (!str) return 0;
            const parts = str.split('-');
            if (parts.length !== 2) return 0;
            const num = parseInt(parts[1]);
            return isNaN(num) ? 0 : num;
        };

        return [...meterFrames].sort((a, b) => {
            const numA = getNumericPart(a.meterFrameNumber);
            const numB = getNumericPart(b.meterFrameNumber);
            return numA - numB;
        });
    }

    isSyncGISDisabled() {
        const gisStatus = this.connectionForm?.get('gisStatus')?.value;
        return (
            this.formHasChanges ||
            this.connectionForm?.disabled ||
            gisStatus !== GisStatus.NotSent ||
            this.isGisSyncing ||
            !this.formDataService.meterFrame?.id
        );
    }

    syncGIS() {
        this.isGisSyncing = true;
        this.subscription.add(
            this.client
                .sendGisDataToMeterFrame(this.formDataService.formId!, uuidv4())
                .pipe(
                    finalize(() => {
                        this.isGisSyncing = false;
                    })
                )
                .subscribe({
                    next: () => {
                        this.messageServiceHelper.showSuccess({
                            detail: this.widgetTranslations['gisSyncSuccess'],
                            key: this.formDataService.formId
                        });
                    },
                    error: (_) => {
                        this.messageServiceHelper.showError({
                            detail: this.widgetTranslations['gisSyncError'],
                            key: this.formDataService.formId
                        });
                    }
                })
        );
    }

    topologySelected(event: { container: Node; node: Node }) {
        this.showMap.set(false);
        if (this.connectionForm) {
            this.connectionForm.patchValue({
                containerId: event.container.id,
                nodeId: event.node.id,
                gisStatus: GisStatus.NotSent,
                gisDescription: event.node.name
            });
        }
        // to prevent user clicking unnecesary sync we need to set status that potentially was OK.
        if (
            this.connectionForm &&
            this.formDataService.meterFrame?.id === this.form.get('meterFrame')?.value?.id &&
            this.connectionForm.get('nodeId')?.value === this.formDataService.gisNodeId
        ) {
            this.connectionForm.patchValue({
                gisStatus: this.formDataService.gisStatus
            });
        }
    }

    showMapClick() {
        this.showMap.update((prev) => !prev);
        if (this.showMap()) {
            const addressCarId = this.form.get('installationAddress')?.value?.id;
            const connectionPointId = this.form.get('connectionPoint')?.value?.id;
            const meterFrameId = this.form.get('meterFrame')?.value?.id;
            if (!addressCarId) {
                return;
            }
            this.subscription.add(
                this.client.getDefaultMapLocation(uuidv4(), undefined, addressCarId, connectionPointId, meterFrameId).subscribe({
                    next: (x) => {
                        this.showInitialLocationMarker.set(true);
                        this.meterFrameLocation.set({
                            lat: x.result.y,
                            lon: x.result.x
                        });
                    },
                    error: (error) => {
                        this.showInitialLocationMarker.set(false);
                        if (error.status === 404) {
                            this.messageServiceHelper.showWarning({
                                detail: this.widgetTranslations['unableToSetInitialMapLocation'],
                                key: this.formDataService.formId
                            });
                        } else {
                            this.messageServiceHelper.showError({
                                detail: this.widgetTranslations['loadMapLocationError'],
                                key: this.formDataService.formId
                            });
                        }
                    }
                })
            );
        }
    }
}
