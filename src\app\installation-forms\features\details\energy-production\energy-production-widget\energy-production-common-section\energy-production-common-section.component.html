<div #overlayContainer></div>
<div [formGroup]="form">
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label for="energyProductionType">{{ 'energyProductionSection.common.energyProductionType' | translate }} </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input
                id="energyProductionType"
                type="text"
                pInputText
                [disabled]="true"
                [value]="'enums.category.' + type | translate" />
        </div>
    </div>
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['energyProductionConnectionType']" for="energyProductionConnectionType">
                {{ 'energyProductionSection.common.energyProductionConnectionType' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="energyProductionConnectionType"
                [options]="energyProductionConnectionTypeOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="energyProductionConnectionType">
            </p-select>
            <small [controlValidationErrors]="form.controls['energyProductionConnectionType']" class="p-error"></small>
        </div>
    </div>
    <!-- add accordion tab for owner here -->
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['ownerName']" for="ownerName">
                {{ 'energyProductionSection.common.owner.name' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <input id="ownerName" type="text" pInputText maxlength="100" formControlName="ownerName" />
            <small [controlValidationErrors]="form.controls['ownerName']" class="p-error"></small>
        </div>
    </div>
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['identificationType']" for="identificationType">
                {{ 'energyProductionSection.common.owner.identificationType' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <p-select
                id="ownerIdentificationType"
                [options]="ownerIdentificationTypeOptions"
                optionValue="value"
                optionLabel="label"
                [appendTo]="overlayContainer"
                [placeholder]="'common.selectValue' | translate"
                formControlName="ownerIdentificationType">
            </p-select>
            <small [controlValidationErrors]="form.controls['ownerIdentificationTypeOptions']" class="p-error"></small>
        </div>
    </div>

    <div>
        <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label [labelRequired]="form.controls['ownerIdentifier']" for="ownerIdentifier">
                    {{ 'energyProductionSection.common.owner.identifier' | translate }}
                </label>
            </div>
            <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                <cmbs-calendar
                    *ngIf="form.get('ownerIdentificationType')?.value === 'DateOfBirth'; else identifierAsNumber"
                    inputId="ownerIdentifier"
                    formControlName="ownerIdentifier"
                    [showTime]="false"
                    [appendTo]="overlayContainer">
                </cmbs-calendar>
                <ng-template #identifierAsNumber>
                    <input id="ownerIdentifier" type="text" pInputText maxlength="20" formControlName="ownerIdentifier" />
                </ng-template>
                <small [controlValidationErrors]="form.controls['ownerIdentifier']" class="p-error"></small>
            </div>
        </div>
    </div>

    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['ownerAddress']" for="ownerAddress">
                {{ 'energyProductionSection.common.owner.address' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <app-address-search id="ownerAddress" formControlName="ownerAddress"> </app-address-search>
            <small [controlValidationErrors]="form.controls['ownerAddress']" class="p-error"></small>
        </div>
    </div>
    <!-- accordion tab ends here -->
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label for="moreThan125kW">
                {{ 'energyProductionSection.common.moreThan125kW' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="20" fxFlex.lt-md="50">
            <p-checkbox formControlName="moreThan125kW" [binary]="true"></p-checkbox>
        </div>
    </div>
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label for="includesStorage">
                {{ 'energyProductionSection.common.includesStorage' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="20" fxFlex.lt-md="50">
            <p-checkbox formControlName="includesStorage" [binary]="true"></p-checkbox>
        </div>
    </div>
    <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
        <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
            <label [labelRequired]="form.controls['commissioningDateSod']" for="commissioningDateSod">
                {{ 'energyProductionSection.common.commissioningDateSod' | translate }}
            </label>
        </div>
        <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
            <cmbs-calendar
                inputId="commissioningDateSod"
                formControlName="commissioningDateSod"
                [showTime]="false"
                [appendTo]="overlayContainer">
            </cmbs-calendar>
            <small [controlValidationErrors]="form.controls['commissioningDateSod']" class="p-error"></small>
        </div>
    </div>
</div>
