<ng-container [ngSwitch]="taskGroup">
    <i
        class="fa-solid fa-wand-magic"
        *ngSwitchCase="AllowAutomatizationTaskGroup"
        [pTooltip]="'AllowAutomatization' | taskGroupPathTooltip"></i>
    <i class="fa-solid fa-receipt" *ngSwitchCase="CreateInvoiceTaskGroup" [pTooltip]="'CreateInvoice' | taskGroupPathTooltip"></i>
    <i
        class="fa-solid fa-envelope"
        *ngSwitchCase="AutomaticEmailsTaskGroup"
        [pTooltip]="'AutomaticEmails' | taskGroupPathTooltip"></i>
    <i
        class="fa-solid fa-screwdriver-wrench"
        *ngSwitchCase="CreateWorkOrderTaskGroup"
        [pTooltip]="'CreateWorkOrder' | taskGroupPathTooltip"></i>
    <i
        class="fa-solid fa-database"
        *ngSwitchCase="CreateMasterDataProcessTaskGroup"
        [pTooltip]="'CreateMasterDataProcess' | taskGroupPathTooltip"></i>
    <i
        class="fa-solid fa-database"
        *ngSwitchCase="CreateMasterDataProcessWithCloseDownTaskGroup"
        [pTooltip]="'CreateMasterDataProcessWithCloseDown' | taskGroupPathTooltip"></i>
    <i class="fa-solid fa-box-archive" *ngSwitchCase="ArchivingTaskGroup" [pTooltip]="'Archiving' | taskGroupPathTooltip"></i>

    <i
        class="fa-solid fa-comment"
        *ngSwitchCase="AutomaticInstructionTextTaskGroup"
        [pTooltip]="'AutomaticInstructionText' | taskGroupPathTooltip"></i>
</ng-container>
