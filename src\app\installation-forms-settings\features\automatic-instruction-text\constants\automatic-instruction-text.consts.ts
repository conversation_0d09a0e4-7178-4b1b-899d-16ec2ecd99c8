export const automaticInstructionTextTabName: string = 'AutomaticInstructionText';
export const automaticInstructionTextTranslationPath: string = 'formsSettings.automaticInstructionTexts';

export const automaticInstructionTextMarkdownEnUs: string = `
## Configuration of automatic instruction text for form management

**Rules:** Adding a rule below will cause the system to automatically fill in instruction texts when all criteria defined in the rule are met.

**Trigger:** The selected instruction text will be added to the form when the conditions below are met in a rule:
- The form instruction annotation field is empty. If the caseworker has manually added an instruction text, or if automation has previously added text, then no automation will be run.
- The form has no problems with the severity type "Error".
`;

export const automaticInstructionTextMarkdownDaDK: string = `
## Konfiguration af automatisk anvisningstekster af blanketter

**Regler:** Når der tilføjes en regel herunder, vil systemet automatisk anvisningstekster blanketten, når alle kriterier defineret i reglen er opfyldt.

**Trigger:** Den valgte instruktionstekst vil blive tilføjet til formularen, når nedenstående betingelser er opfyldt i en regel:
- Anmærkningsfeltet til formularinstruktion er tomt. Hvis sagsbehandleren manuelt har tilføjet en instruktionstekst, eller hvis automatisering tidligere har tilføjet tekst, så vil der ikke blive kørt nogen automatisering.
- Blanketten har ingen problemer af alvorlighedsgraden "fejl".
`;
