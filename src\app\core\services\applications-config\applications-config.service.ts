import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map, shareReplay } from 'rxjs/operators';
import { ApplicationTypesConfiguration, InstallationFormsClient, SupplyType } from 'src/app/api/installation-forms-client';
import { v4 as uuidv4 } from 'uuid';

@Injectable({
    providedIn: 'root'
})
export class ApplicationsConfigService {
    private config: Observable<ApplicationTypesConfiguration> | null = null;

    constructor(private readonly client: InstallationFormsClient) {}

    getConfigData(supplyType: string): Observable<ApplicationTypesConfiguration> {
        this.config ??= this.client.getApplicationTypesConfiguration(uuidv4(), supplyType as SupplyType).pipe(
            map((response) => response.result),
            shareReplay(1)
        );
        return this.config;
    }
}
