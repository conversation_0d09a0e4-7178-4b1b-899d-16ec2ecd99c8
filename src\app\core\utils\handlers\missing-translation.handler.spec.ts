import { MissingTranslationHandlerParams } from '@ngx-translate/core';
import { MpmMissingTranslationHandler } from './missing-translation.handler';

describe('MpmMissingTranslationHandler', () => {
    let handler: MpmMissingTranslationHandler;

    beforeEach(() => {
        handler = new MpmMissingTranslationHandler();
    });

    describe('isArrayItemPathRegex', () => {
        it('should match property paths containing array indices', () => {
            const regex = MpmMissingTranslationHandler.isArrayItemPathRegex;

            expect(regex.test('propertyPath.items[0]')).toBeTruthy();
            expect(regex.test('propertyPath.nestedItems[123]')).toBeTruthy();
            expect(regex.test('propertyPath.very.deeply.nested.array[9]')).toBeTruthy();
        });

        it('should not match regular property paths', () => {
            const regex = MpmMissingTranslationHandler.isArrayItemPathRegex;

            expect(regex.test('simple.key')).toBeFalsy();
            expect(regex.test('propertyPath')).toBeFalsy();
            expect(regex.test('propertyPath.nestedProperty')).toBeFalsy();
        });
    });

    describe('handle', () => {
        it('should return the property path without prefix for array item paths', () => {
            const params: MissingTranslationHandlerParams = {
                key: 'propertyPath.items[0]',
                translateService: undefined as any
            };

            const result = handler.handle(params);

            expect(result).toBe('items[0]');
        });

        it('should handle nested array item paths', () => {
            const params: MissingTranslationHandlerParams = {
                key: 'propertyPath.parent.items[5].child',
                translateService: undefined as any
            };

            const result = handler.handle(params);

            expect(result).toBe('parent.items[5].child');
        });

        it('should return a formatted not-found message for regular missing keys', () => {
            const params: MissingTranslationHandlerParams = {
                key: 'missing.translation.key',
                translateService: undefined as any
            };

            const result = handler.handle(params);

            expect(result).toBe('Key - "missing.translation.key" - Not Found');
        });
    });
});
