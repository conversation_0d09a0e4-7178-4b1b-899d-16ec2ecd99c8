import { Injectable } from '@angular/core';
import { BehaviorSubject, Subject } from 'rxjs';
import {
    AddressLookup,
    CaseWorker,
    ContactPerson,
    FormCategory,
    FormProblemSeverity,
    FormScreeningStatus,
    FormState,
    FormType,
    GisStatus,
    IBaseForm,
    IChangeBranchLine,
    IEnergyProduction,
    IExtension,
    IMasterDataCompareResult,
    INewInstallation,
    ITermination,
    MasterDataProcess,
    TerminationScope
} from 'src/app/api/installation-forms-client';

@Injectable({
    providedIn: 'root'
})
export class FormDataService {
    editabilityChange$: Subject<void> = new Subject();
    reloadMasterDataCompare$: Subject<void> = new Subject();
    formLoaded$: Subject<void> = new Subject();

    private formDetails?: IBaseForm;
    private masterDataToCompare?: IMasterDataCompareResult;
    private _isProcessing: boolean = false;
    private _hasUnreadMessages: boolean = false;
    private _hasUserWritePermission: boolean = false;
    private _isCurrentUserAssignedAsCaseWorker: boolean = false;
    private _masterDataCompareToggleValue: boolean = false;

    //shared between widgets
    masterDataProcessesLoaded$: BehaviorSubject<MasterDataProcess[] | null> = new BehaviorSubject<MasterDataProcess[] | null>(
        null
    );

    formLoaded(value: IBaseForm) {
        this.formDetails = value;
        this.masterDataCompareToggleValue = this.showMasterDataCompareToggle;
        this.formLoaded$.next();
    }

    masterDataToCompareLoaded(value: IMasterDataCompareResult) {
        this.masterDataToCompare = value;
    }

    get masterDataToCompareResult() {
        return this.masterDataToCompare;
    }

    processingStarted() {
        this._isProcessing = true;
        this.editabilityChange$.next();
    }

    processingFinished() {
        this._isProcessing = false;
        this.editabilityChange$.next();
    }

    get isProcessing(): boolean {
        return this._isProcessing;
    }

    private get isFormDetailsReadOnly() {
        return this.formDetails?.isReadOnly || this.formDetails?.state == FormState.AwaitingFormSystemUpdate;
    }

    get formId() {
        return this.formDetails?.formId;
    }

    get formNumber() {
        return this.formDetails?.formNumber;
    }

    get meterFrameNumber() {
        return this.formDetails?.meterFrame?.meterFrameNumber;
    }

    get meterFrame() {
        return this.formDetails?.meterFrame;
    }

    get meterNumber() {
        return this.formDetails?.meterNumber;
    }

    get connectionPoint() {
        return this.formDetails?.connectionPoint;
    }

    get installationContact() {
        return this.formDetails?.contactPerson;
    }

    get installerContact() {
        const installer = this.formDetails?.installer;
        if (!installer) {
            return undefined;
        }

        return installer.name || installer.phoneNumber || installer.email
            ? new ContactPerson({
                  name: installer.name,
                  phoneNumber: installer.phoneNumber,
                  email: installer.email
              })
            : new ContactPerson({
                  name: installer.companyName,
                  phoneNumber: installer.companyPhoneNumber,
                  email: installer.companyEmail
              });
    }

    get requestedConnectionDateEod() {
        return (this.formDetails as INewInstallation)?.meterDeliveryOptions?.requestedConnectionDateEod;
    }

    get meterDeliveryCarId() {
        return (this.formDetails as INewInstallation)?.meterDeliveryOptions?.deliveryInformation?.address?.carId;
    }

    get meterDeliveryName() {
        return (this.formDetails as INewInstallation)?.meterDeliveryOptions?.deliveryInformation?.name;
    }

    get meterFramesForRemoveMeter() {
        return (this.formDetails as INewInstallation | ITermination)?.meterFramesForRemoveMeter;
    }

    get meterDeliveryAttention() {
        return (this.formDetails as INewInstallation)?.meterDeliveryOptions?.deliveryInformation?.attention;
    }

    get meterDeliveryOption() {
        return (this.formDetails as INewInstallation)?.meterDeliveryOptions?.deliveryOption;
    }

    get supplyType(): string | undefined {
        return this.formDetails?.supplyType;
    }

    get type(): FormType | undefined {
        return this.formDetails?.type;
    }

    get state(): FormState | undefined {
        return this.formDetails?.state;
    }

    get category(): FormCategory | undefined {
        return this.formDetails?.category;
    }

    get creationDateTime(): Date | undefined {
        return this.formDetails?.createdDate;
    }

    get lastChangedDateTime(): Date | undefined {
        return this.formDetails?.updatedDate;
    }

    get requestedConnectionDateEodTime(): Date | undefined {
        return this.formDetails?.requestedConnectionDateEod;
    }

    get screeningStatus(): FormScreeningStatus | undefined {
        return this.formDetails?.screeningStatus;
    }

    get allowedScreeningStatusTransition(): FormScreeningStatus | undefined {
        return this.formDetails?.allowedScreeningStatusTransition;
    }

    get terminationScope(): TerminationScope | undefined {
        return (this.formDetails as ITermination)?.terminationInformation?.scope;
    }

    get gisNodeId(): string | undefined {
        return (this.formDetails as INewInstallation | IExtension | IChangeBranchLine)?.technicalInformation?.connection?.gisId;
    }

    get gisStatus(): GisStatus | undefined {
        return (this.formDetails as INewInstallation | IExtension | IChangeBranchLine)?.technicalInformation?.connection
            ?.gisStatus;
    }

    get formRequiresAttention(): boolean | undefined {
        return this.formDetails?.formRequiresAttention;
    }

    get formHasErrors(): boolean | undefined {
        return this.formDetails?.problems?.some((x) => x.severity === FormProblemSeverity.Error);
    }

    get installationAddress(): AddressLookup | undefined {
        return this.formDetails?.installationAddress;
    }

    get caseWorker(): CaseWorker | undefined {
        return this.formDetails?.caseWorker;
    }

    get hasUnreadMessages(): boolean {
        return this._hasUnreadMessages;
    }

    set hasUnreadMessages(value: boolean) {
        this._hasUnreadMessages = value;
    }

    get hasUserWritePermission(): boolean {
        return this._hasUserWritePermission;
    }

    set hasUserWritePermission(value: boolean) {
        this._hasUserWritePermission = value;
    }

    get isCurrentUserAssignedAsCaseWorker(): boolean {
        return this._isCurrentUserAssignedAsCaseWorker;
    }

    set isCurrentUserAssignedAsCaseWorker(value: boolean) {
        this._isCurrentUserAssignedAsCaseWorker = value;
    }

    get canCurrentUserEditData() {
        return this.hasUserWritePermission && !this.isFormDetailsReadOnly && this.isCurrentUserAssignedAsCaseWorker;
    }

    get canCurrentUserExecuteActions() {
        return this.hasUserWritePermission && this.canExecuteActionsInState && this.isCurrentUserAssignedAsCaseWorker;
    }

    get canExecuteActionsInState(): boolean {
        return (
            this.state !== FormState.AwaitingFormSystemUpdate &&
            this.state !== FormState.Archived &&
            this.state !== FormState.InTransit &&
            this.state !== FormState.Cancelled &&
            this.state !== FormState.Rejected
        );
    }

    get canChangeFlags() {
        return (
            this.hasUserWritePermission &&
            (this.state === FormState.Registered || this.state === FormState.Instructed) &&
            this.isCurrentUserAssignedAsCaseWorker
        );
    }

    get canUpdatePaymentDetails(): boolean {
        return (
            this.hasUserWritePermission &&
            this.isCurrentUserAssignedAsCaseWorker &&
            (!this.formDetails?.isReadOnly ||
                ((this.formDetails?.canUpdatePaymentDetails || false) && this.formDetails?.isReadOnly))
        );
    }

    get canCloseDownSupply(): boolean {
        return (
            this.type === FormType.NewInstallation ||
            (this.type === FormType.Termination &&
                (<ITermination>this.formDetails)?.terminationInformation?.scope === TerminationScope.EntireInstallation)
        );
    }

    get canCloseDownProduction(): boolean {
        return (
            this.type === FormType.Termination &&
            (<ITermination>this.formDetails)?.terminationInformation?.scope === TerminationScope.EnergyProduction
        );
    }

    get rowVersion() {
        return this.formDetails?.rowVersion as string;
    }

    get masterDataCompareToggleValue() {
        return this._masterDataCompareToggleValue;
    }

    get hasValidPayerAddress() {
        return !!this.formDetails?.payer?.address?.carId;
    }

    set masterDataCompareToggleValue(value: boolean) {
        this._masterDataCompareToggleValue = value;
        if (this.showMasterDataCompareToggle && value) {
            this.reloadMasterDataCompare$.next();
        }
    }

    get showMasterDataCompareToggle() {
        return (
            [
                FormType.NewInstallation,
                FormType.Extension,
                FormType.SealBreach,
                FormType.ChangeMeter,
                FormType.MoveMeter,
                FormType.ChangeBranchLine
            ].includes(this.type as FormType) &&
            [
                FormState.Registered,
                FormState.Instructed,
                FormState.InTransit,
                FormState.AwaitingFormSystemUpdate,
                FormState.Returned
            ].includes(this.state as FormState) &&
            (!!this.formDetails?.meterFrame || !!this.formDetails?.connectionPoint)
        );
    }

    get showMasterDataCompareFields() {
        return this.showMasterDataCompareToggle && this.masterDataCompareToggleValue;
    }

    get canFormBeScreened() {
        return (
            [FormState.Registered, FormState.Instructed, FormState.Returned].includes(this.state as FormState) &&
            !this.formHasErrors
        );
    }

    get electricityNetSettlementGroupIdBeforeChange(): string | undefined {
        return (this.formDetails as IEnergyProduction)?.electricityNetSettlementGroupIdBeforeChange;
    }

    get gridAreaId() {
        return (this.formDetails as INewInstallation)?.gridAreaId;
    }
}
