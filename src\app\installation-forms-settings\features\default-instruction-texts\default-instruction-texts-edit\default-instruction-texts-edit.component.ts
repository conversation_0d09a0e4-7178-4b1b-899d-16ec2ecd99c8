import { Component } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { finalize } from 'rxjs';
import { InstallationFormsClient, InstructionText, InstructionTextCreateOrUpdate } from 'src/app/api/installation-forms-client';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { v4 as uuidv4 } from 'uuid';
import { BaseRuleEditComponent } from '../../base-components/base-rule-edit.component';
import {
    defaultInstructionTextsTabName,
    defaultInstructionTextsTranslationPath,
    instructionTextMaxLength
} from '../constants/default-instruction-texts.consts';

@Component({
    selector: 'app-default-instruction-texts-edit',
    templateUrl: './default-instruction-texts-edit.component.html',
    standalone: false
})
export class DefaultInstructionTextsEditComponent extends BaseRuleEditComponent<InstructionText> {
    constructor(
        private readonly client: InstallationFormsClient,
        private readonly fb: FormBuilder,
        protected readonly translateService: TranslateService,
        protected readonly messageServiceHelper: MessageServiceHelper
    ) {
        super(translateService, messageServiceHelper, defaultInstructionTextsTabName, defaultInstructionTextsTranslationPath);
        this.initForm();
    }

    override initForm(): void {
        this.form = this.fb.group({
            id: [null, Validators.required],
            text: [null, [Validators.required, Validators.maxLength(instructionTextMaxLength)]]
        });
    }

    override setFormValue(rule: InstructionText) {
        this.form.patchValue({
            id: rule.id,
            text: rule.text
        });
    }

    override saveEditedClick() {
        if (this.form.invalid) {
            return;
        }

        const formValue = this.form.value;

        let hasValidationErrors = false;

        if (hasValidationErrors) {
            return;
        }

        const rule: InstructionTextCreateOrUpdate = new InstructionTextCreateOrUpdate({
            text: formValue.text
        });

        this.isProcessing = true;
        this.subscription.add(
            this.client
                .updateInstructionTextById(formValue.id, uuidv4(), rule)
                .pipe(finalize(() => (this.isProcessing = false)))
                .subscribe({
                    next: (response) => {
                        this.ruleEdited.emit(response.result);
                        this.showSuccessMessage();
                    },
                    error: (_) => {
                        this.showErrorMessage(this.widgetTranslations['instructionTextEditError']);
                    }
                })
        );
    }

    getEnumTranslations() {
        // no options to select
    }
}
