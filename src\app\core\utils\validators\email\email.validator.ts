import { AbstractControl, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';

const emailPattern: RegExp = /^[a-zA-Z0-9._%+\-<PERSON>ØÅæøå]+@[a-zA-Z0-9.\-<PERSON><PERSON>Åæøå]+\.[a-zA-Z]{2,}$/;

export function EmailValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        const error = Validators.pattern(emailPattern)(control);
        if (error) {
            return { emailFormatError: true };
        }
        return null;
    };
}
