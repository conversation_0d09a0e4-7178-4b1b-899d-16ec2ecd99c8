import { AbstractControl, ValidationErrors, ValidatorFn } from '@angular/forms';

export function multipleWhitespaceValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        const value = control.value;
        if (!value || typeof value !== 'string') {
            return null;
        }

        // Check for multiple consecutive whitespaces
        if (/\s{2,}/.test(value)) {
            return { multipleWhitespaces: true };
        }

        return null;
    };
}
