import { Component, EventEmitter, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AuthorizationService } from '@kmd-elements-ui/authorization';
import {
    KeyActions,
    KeyboardShortcuts,
    KeyboardShortcutsService,
    KeyboardShortcutsSubscriptionManager
} from '@kmd-elements-ui/shortcuts';
import { TabsService } from '@kmd-elements-ui/tabs';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { BehaviorSubject, filter, finalize, forkJoin, of, Subscription, switchMap, tap } from 'rxjs';
import {
    GetPricesSearchResultItem,
    InstallationFormsClient,
    Price,
    PriceAddOrUpdate,
    SupplyType
} from 'src/app/api/installation-forms-client';
import { Permissions } from 'src/app/core/constants/permissions';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { enumMapper } from 'src/app/core/utils/mappers/enum-to-select-items/enum-to-select-items.mapper';
import { v4 as uuidv4 } from 'uuid';
import {
    priceDefinitionsMarkdownDaDK,
    priceDefinitionsMarkdownEnUs,
    priceDefinitionsTabName,
    priceDefinitionsTranslationPath
} from './price-definitions.consts';

interface EditablePriceDefinition extends Price {
    isEdit?: boolean;
    isNew?: boolean;
    externalPriceName?: string;
    _originalValues?: EditablePriceDefinition;
}

@Component({
    selector: 'app-price-definitions',
    templateUrl: './price-definitions.component.html',
    styleUrls: ['./price-definitions.component.scss'],
    standalone: false
})
export class PriceDefinitionsComponent implements KeyboardShortcutsSubscriptionManager, OnInit, OnDestroy {
    private _isAnyBeingEdited: boolean = false;
    private widgetTranslations: any;
    private _tabId?: string;
    private keyEventsSubscription: Subscription = new Subscription();

    priceDefinitionsMarkdownEnUs = priceDefinitionsMarkdownEnUs;
    priceDefinitionsMarkdownDaDK = priceDefinitionsMarkdownDaDK;
    priceDefinitionsTabName = priceDefinitionsTabName;
    dirty$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);
    hasUserFormConfigurationWritePermission: boolean = false;

    internalPriceDefinitions: EditablePriceDefinition[] = [];
    externalPriceDefinitions: GetPricesSearchResultItem[] = [];
    externalPriceOptions: SelectItem[] = [];

    isProcessing: boolean = false;
    subscription: Subscription = new Subscription();
    supplyTypeOptions: SelectItem[] = [];

    get isAnyBeingEdited(): boolean {
        return this._isAnyBeingEdited;
    }
    set isAnyBeingEdited(value: boolean) {
        this._isAnyBeingEdited = value;
        this.dirty$.next(this._isAnyBeingEdited);
    }

    keyActions: KeyActions = {
        [KeyboardShortcuts.ShiftPlusN]: () => {
            if (!this.isAnyBeingEdited && !this.priceDefinitionToDelete) {
                this.addEmptyPriceDefinitionClick();
            }
        },
        [KeyboardShortcuts.Escape]: () => {
            this.tabsService.close();
        },
        [KeyboardShortcuts.Enter]: () => {
            if (this.isAnyBeingEdited) {
                const indexOfBeingEdited = this.internalPriceDefinitions.findIndex((p) => p.isEdit);
                this.savePriceDefinition(this.internalPriceDefinitions[indexOfBeingEdited]);
            }
        }
    };

    attemptedAction: (() => void) | null = null;
    private priceDefinitionToDelete?: EditablePriceDefinition;

    constructor(
        private readonly client: InstallationFormsClient,
        private readonly messageServiceHelper: MessageServiceHelper,
        private readonly translateService: TranslateService,
        private readonly authService: AuthorizationService,
        protected tabsService: TabsService,
        private readonly activatedRoute: ActivatedRoute,
        private readonly keyboardShortcutsService: KeyboardShortcutsService
    ) {
        this.authService
            .hasPermissions([Permissions.formConfiguration.write])
            .subscribe((x) => (this.hasUserFormConfigurationWritePermission = x));

        this.addTab();
    }

    ngOnInit(): void {
        this.getData();
        this.registerRefreshButtonListener();
        this.getEnumTranslations();

        this.widgetTranslations = this.translateService.instant(priceDefinitionsTranslationPath);
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    subscribeKeyShortcuts(): void {
        this.keyEventsSubscription = this.keyboardShortcutsService.registerKeyboardShortcuts(this.keyActions).subscribe();
    }

    unsubscribeKeyShortcuts(): void {
        this.keyEventsSubscription.unsubscribe();
    }

    getEnumTranslations() {
        this.supplyTypeOptions = enumMapper.map(this.translateService.instant('enums.supplyType'), [
            SupplyType.Electricity,
            SupplyType.Water,
            SupplyType.Heating
        ]);
    }

    getData() {
        this.isProcessing = true;
        this.subscription.add(
            forkJoin({
                internalPriceDefinitions: this.client.getInvoicingPrices(uuidv4(), SupplyType.All),
                externalPriceDefinitions: this.client.searchFees(uuidv4())
            })
                .pipe(finalize(() => (this.isProcessing = false)))
                .subscribe({
                    next: (response) => {
                        this.externalPriceDefinitions = response.externalPriceDefinitions.result;
                        this.internalPriceDefinitions = response.internalPriceDefinitions.result.results.map(
                            (price: Price) =>
                                ({
                                    ...price,
                                    externalPriceName: this.findPriceDefinitionName(price.externalPriceId),
                                    isEdit: false,
                                    isNew: false
                                }) as EditablePriceDefinition
                        );
                        this.updateExternalPriceOptions();
                    },
                    error: (_) => {
                        this.messageServiceHelper.showError({
                            detail: this.widgetTranslations['loadError'],
                            key: priceDefinitionsTabName
                        });
                    }
                })
        );
    }

    updateExternalPriceOptions(editingPriceDefinition?: EditablePriceDefinition) {
        const usedExternalPriceIds = this.internalPriceDefinitions
            .filter((price) => !price.isEdit)
            .map((price) => price.externalPriceId);

        this.externalPriceOptions = this.externalPriceDefinitions
            .filter(
                (price) =>
                    !usedExternalPriceIds.includes(price.priceDefinitionId || 0) ||
                    (editingPriceDefinition && editingPriceDefinition.externalPriceId === price.priceDefinitionId)
            )
            .map(
                (price) =>
                    ({
                        label: `${price.name} (${this.translateService.instant('enums.supplyType.' + price.supplyType)})`,
                        value: price.priceDefinitionId
                    }) as SelectItem
            );
    }

    private findPriceDefinitionName(externalPriceId: number | undefined): string {
        if (!externalPriceId) {
            return '';
        }
        const priceDefinition = this.externalPriceDefinitions.find((pd) => pd.priceDefinitionId === externalPriceId);
        return priceDefinition?.name || '';
    }

    addEmptyPriceDefinitionClick() {
        const newRow: EditablePriceDefinition = {
            displayName: '',
            supplyType: '' as SupplyType,
            isEdit: true,
            isNew: true,
            externalPriceName: ''
        } as EditablePriceDefinition;
        this.internalPriceDefinitions = [newRow, ...this.internalPriceDefinitions];
        this.isAnyBeingEdited = true;
    }

    editPriceDefinitionClick(event: MouseEvent, priceDefinition: EditablePriceDefinition) {
        const originalValues = { ...priceDefinition };
        priceDefinition.isEdit = true;
        priceDefinition._originalValues = originalValues as EditablePriceDefinition;
        this.isAnyBeingEdited = true;
        this.updateExternalPriceOptions(priceDefinition);
    }

    onExternalPriceSelected(externalPriceId: number, editingPrice: EditablePriceDefinition) {
        const selectedPrice = this.externalPriceDefinitions.find((p) => p.priceDefinitionId === externalPriceId);
        if (selectedPrice) {
            editingPrice.externalPriceName = selectedPrice.name;
            editingPrice.supplyType = selectedPrice.supplyType as SupplyType;
        }
    }

    savePriceDefinition(priceDefinition: EditablePriceDefinition) {
        if (!this.isValidPriceDefinition(priceDefinition)) {
            this.messageServiceHelper.showError({
                detail: this.widgetTranslations['validation.requiredFields'],
                key: priceDefinitionsTabName
            });
            return;
        }

        if (priceDefinition.isNew) {
            this.addNewPriceDefinition(priceDefinition);
        } else {
            this.updatePriceDefinition(priceDefinition);
        }
    }

    addNewPriceDefinition(priceDefinition: EditablePriceDefinition) {
        this.isProcessing = true;
        this.subscription.add(
            this.client
                .addInvoicingPrice(uuidv4(), this.mapToCreateOrUpdatePrice(priceDefinition))
                .pipe(
                    finalize(() => {
                        this.isAnyBeingEdited = false;
                        this.isProcessing = false;
                    })
                )
                .subscribe({
                    next: (response) => {
                        const index = this.internalPriceDefinitions.findIndex((p) => p === priceDefinition);
                        if (index !== -1) {
                            this.internalPriceDefinitions[index] = {
                                ...response.result,
                                externalPriceName: this.findPriceDefinitionName(response.result.externalPriceId),
                                isEdit: false
                            } as EditablePriceDefinition;
                        }
                        this.updateExternalPriceOptions();
                    },
                    error: (_) => {
                        this.messageServiceHelper.showError({
                            detail: this.widgetTranslations['addError'],
                            key: priceDefinitionsTabName
                        });
                    }
                })
        );
    }

    updatePriceDefinition(priceDefinition: EditablePriceDefinition) {
        this.isProcessing = true;
        this.subscription.add(
            this.client
                .updateInvoicingPriceById(uuidv4(), priceDefinition.id, this.mapToCreateOrUpdatePrice(priceDefinition))
                .pipe(
                    finalize(() => {
                        this.isAnyBeingEdited = false;
                        this.isProcessing = false;
                    })
                )
                .subscribe({
                    next: (response) => {
                        const index = this.internalPriceDefinitions.findIndex((p) => p === priceDefinition);
                        if (index !== -1) {
                            this.internalPriceDefinitions[index] = {
                                ...response.result,
                                externalPriceName: this.findPriceDefinitionName(response.result.externalPriceId),
                                isEdit: false
                            } as EditablePriceDefinition;
                        }
                        this.updateExternalPriceOptions();
                    },
                    error: (_) => {
                        this.messageServiceHelper.showError({
                            detail: this.widgetTranslations['updateError'],
                            key: priceDefinitionsTabName
                        });
                    }
                })
        );
    }

    mapToCreateOrUpdatePrice(priceDefinition: EditablePriceDefinition) {
        return new PriceAddOrUpdate({
            displayName: priceDefinition.displayName,
            externalPriceId: priceDefinition.externalPriceId!,
            supplyType: priceDefinition.supplyType
        });
    }

    cancelEdit(priceDefinition: EditablePriceDefinition) {
        if (priceDefinition.isNew) {
            this.internalPriceDefinitions = this.internalPriceDefinitions.filter((p) => p !== priceDefinition);
        } else if (priceDefinition._originalValues) {
            Object.assign(priceDefinition, priceDefinition._originalValues);
            priceDefinition.isEdit = false;
            delete priceDefinition._originalValues;
            this.updateExternalPriceOptions();
        }
        this.isAnyBeingEdited = false;
    }

    deletePriceDefinitionClick(event: MouseEvent, priceDefinition: EditablePriceDefinition) {
        event.stopPropagation();
        this.priceDefinitionToDelete = priceDefinition;
        this.attemptedAction = () => this.deletePriceDefinitionConfirmed();
    }

    onConfirmationDialogYesButtonClick() {
        if (this.attemptedAction) {
            this.attemptedAction();
        }
        this.attemptedAction = null;
    }

    onConfirmationDialogNoButtonClick() {
        this.attemptedAction = null;
        this.priceDefinitionToDelete = undefined;
    }

    private deletePriceDefinitionConfirmed() {
        if (!this.priceDefinitionToDelete) {
            return;
        }

        this.isProcessing = true;
        this.subscription.add(
            this.client
                .deleteInvoicingPriceById(uuidv4(), this.priceDefinitionToDelete.id)
                .pipe(finalize(() => (this.isProcessing = false)))
                .subscribe({
                    next: () => {
                        this.internalPriceDefinitions = this.internalPriceDefinitions.filter(
                            (p) => p !== this.priceDefinitionToDelete
                        );
                        this.updateExternalPriceOptions();
                    },
                    error: (_) => {
                        this.messageServiceHelper.showError({
                            detail: this.widgetTranslations['deleteError'],
                            key: priceDefinitionsTabName
                        });
                    }
                })
        );
    }

    isValidPriceDefinition(priceDefinition: EditablePriceDefinition): boolean {
        return (
            !!priceDefinition.externalPriceId &&
            !!priceDefinition.displayName &&
            !!priceDefinition.supplyType &&
            this.isDisplayNameUniqueWithinSupplyType(priceDefinition)
        );
    }

    isDisplayNameUniqueWithinSupplyType(priceDefinition: EditablePriceDefinition): boolean {
        return !this.internalPriceDefinitions.some(
            (p) =>
                p.displayName?.toLowerCase().trim() === priceDefinition.displayName?.toLowerCase().trim() &&
                p.supplyType === priceDefinition.supplyType &&
                p !== priceDefinition
        );
    }

    getDisplayNameError(priceDefinition: EditablePriceDefinition): string | null {
        if (!this.isDisplayNameUniqueWithinSupplyType(priceDefinition)) {
            return this.translateService.instant('formsSettings.priceDefinitions.displayNameNotUnique');
        }
        return null;
    }

    private addTab() {
        this._tabId = this.tabsService.addOrUpdateTab(this.activatedRoute, {
            icon$: of('fas fa-th-list'),
            areaLabel$: of(this.translateService.instant(`${priceDefinitionsTranslationPath}.tabAreaLabel`)),
            label$: of(this.translateService.instant(`${priceDefinitionsTranslationPath}.tabLabel`)),
            tooltip$: of(this.translateService.instant(`${priceDefinitionsTranslationPath}.tabLabel`)),
            dirty$: this.dirty$,
            canReload$: of(true)
        });
    }

    registerRefreshButtonListener(): void {
        this.subscription.add(
            this.tabsService.reloadButtonClicked$
                .pipe(
                    filter((tabId) => tabId === this._tabId),
                    switchMap((tabId) => this.tabsService.reload(tabId)),
                    filter((canBeReloaded) => canBeReloaded),
                    tap(() => {
                        this.getData();
                    })
                )
                .subscribe(() => {
                    this.tabsService.tabReloaded(this._tabId as string);
                    this.isAnyBeingEdited = false;
                })
        );
    }
}
