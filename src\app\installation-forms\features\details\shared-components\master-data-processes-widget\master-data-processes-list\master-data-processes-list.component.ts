import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Subscription, retry, take } from 'rxjs';
import { InstallationFormsClient, MasterDataProcess, PendingUpdateAreaType } from 'src/app/api/installation-forms-client';
import { SMALL_TABLE_ROWS_PER_PAGE } from 'src/app/core/constants/constants';
import { AutomaticFormRefreshService } from 'src/app/core/services/automatic-form-refresh/automatic-form-refresh.service';
import { FormDataService } from 'src/app/core/services/form-data/form-data.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { v4 as uuidv4 } from 'uuid';

@Component({
    selector: 'app-master-data-processes-list',
    templateUrl: './master-data-processes-list.component.html',
    styleUrl: './master-data-processes-list.component.scss',
    standalone: false
})
export class MasterDataProcessesListComponent implements On<PERSON><PERSON>roy, OnInit {
    loading = true;
    columns = ['masterDataProcessType', 'status', 'masterDataProcessStarted'];
    rowsPerPage = SMALL_TABLE_ROWS_PER_PAGE;

    masterDataProcesses: MasterDataProcess[] = [];

    subscription: Subscription = new Subscription();

    constructor(
        private readonly client: InstallationFormsClient,
        private readonly messageServiceHelper: MessageServiceHelper,
        private readonly router: Router,
        private readonly formDataService: FormDataService,
        private readonly automaticFormRefreshService: AutomaticFormRefreshService
    ) {}

    async handleRowClick(row: MasterDataProcess) {
        const selection = window.getSelection();
        if (selection && selection.toString().length > 0) {
            return;
        }
        const type = row.masterDataProcessType;

        if (row.processId) {
            await this.router.navigate([`process-center/master-data/details/${type}/${row.processId}`]);
        }
    }

    ngOnInit(): void {
        this.automaticFormRefreshService.subscribeForRefresh(
            PendingUpdateAreaType.MasterDataProcesses,
            this.getMasterDataProcesses
        );
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
        this.automaticFormRefreshService.unsubscribeForRefresh(PendingUpdateAreaType.MasterDataProcesses);
    }

    getMasterDataProcesses = () => {
        this.subscription.add(
            this.client
                .getMasterDataProcesses(this.formDataService.formId!, uuidv4())
                .pipe(retry({ count: 3, delay: 250 }), take(1))
                .subscribe({
                    next: (response) => {
                        this.masterDataProcesses = response.result;
                        this.formDataService.masterDataProcessesLoaded$.next(this.masterDataProcesses);
                        this.loading = false;
                    },
                    error: (_) => {
                        this.messageServiceHelper.showError({
                            key: this.formDataService.formId!
                        });
                    }
                })
        );
    };
}
