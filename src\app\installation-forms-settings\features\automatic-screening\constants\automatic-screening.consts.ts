export const automaticScreeningTabName: string = 'AutomaticScreening';
export const automaticScreeningTranslationPath: string = 'formsSettings.automaticScreening';

export const automaticScreeningMarkdownEnUs: string = `
## Configuration of automatic 'Allow automatization' for form management

**Rules:** Adding a rule below will cause the system to automatically setup the form to automatization when all criteria defined in the rule are met.

**Trigger:** The form will be setup to allow automatization when all the below conditions are met in a rule:
- The form matches every enabled criteria in the rule.
- The form is not setup to allow automatization already.
- The form has no problems with the severity type "Error".
- No previous automatic change to "Allow automatization" has been run.
`;

export const automaticScreeningMarkdownDaDK: string = `
## Konfiguration af automatisk "Godkend automatisering" for blanketter

**Regler:** Når der tilføjes en regel herunder, vil systemet automatisk opsætte blanketten til automatisering, når alle kriterier defineret i reglen er opfyldt.

**Trigger:** Blanketten vil blive opsat til at tillade automatisering, når alle nedenstående betingelser er opfyldt i en regel:
- Blanketten opfylder alle opsatte kriterier fra reglen.
- Blanketten er ikke allerede opsat til at tillade automatisering.
- Blanketten har ingen problemer af alvorlighedsgraden "fejl".
- Ingen tidligere automatisk ændring til "Godkend automatisering" er blevet udført.
`;
