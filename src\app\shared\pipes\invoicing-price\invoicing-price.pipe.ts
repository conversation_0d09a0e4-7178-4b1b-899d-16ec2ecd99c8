import { Injectable, Pipe, PipeTransform } from '@angular/core';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { Price } from 'src/app/api/installation-forms-client';
import { InvoicingPricesService } from 'src/app/core/services/invoicing-prices/invoicing-prices.service';

@Injectable()
@Pipe({
    name: 'invoicingPrice',
    standalone: false
})
export class InvoicingPricePipe implements PipeTransform {
    constructor(private readonly invoicingPricesService: InvoicingPricesService) {}

    transform(id: string | undefined): Observable<string> {
        if (!id) return of('');

        return this.invoicingPricesService.getAllPrices().pipe(
            map((prices: Price[] | undefined) => {
                if (!prices) {
                    return '';
                }

                const priceById = prices.find((price) => price.id === id);
                return priceById ? priceById.displayName : '';
            })
        );
    }
}
