import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AuthorizationService } from '@kmd-elements-ui/authorization';
import {
    KeyActions,
    KeyboardShortcuts,
    KeyboardShortcutsService,
    KeyboardShortcutsSubscriptionManager
} from '@kmd-elements-ui/shortcuts';
import { TabsService } from '@kmd-elements-ui/tabs';
import { TranslateService } from '@ngx-translate/core';
import { TableRowReorderEvent } from 'primeng/table';
import { BehaviorSubject, catchError, EMPTY, filter, finalize, of, Subscription, switchMap, tap } from 'rxjs';
import {
    AutomaticArchivingRule,
    InstallationFormsClient,
    ReorderModel,
    ValueListType,
    WorkOrderType
} from 'src/app/api/installation-forms-client';
import { Permissions } from 'src/app/core/constants/permissions';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { ValueListsService } from 'src/app/core/services/value-lists/value-lists.service';
import { mapWorkOrderTypeToWorkDescriptionValueList } from 'src/app/core/utils/mappers/work-orders/work-order-type-to-value-list.mapper';
import { v4 as uuidv4 } from 'uuid';
import { RulesListColumn } from '../models/rules-list-column';
import { AutomaticArchivingCreateComponent } from './automatic-archiving-create/automatic-archiving-create.component';
import { AutomaticArchivingEditComponent } from './automatic-archiving-edit/automatic-archiving-edit.component';
import {
    automaticArchivingMarkdownDaDK,
    automaticArchivingMarkdownEnUs,
    automaticArchivingTabName,
    automaticArchivingTranslationPath
} from './constants/automatic-archiving.consts';
import { AUTOMATIC_ARCHIVING_COLUMNS } from './constants/columns';
import { AutomaticArchivingListItem } from './models/automatic-archiving-list-item';
import { AutomaticArchivingMapper } from './services/automatic-archiving-mapper';

@Component({
    selector: 'app-automatic-archiving',
    templateUrl: './automatic-archiving.component.html',
    styleUrl: './automatic-archiving.component.scss',
    standalone: false
})
export class AutomaticArchivingComponent implements KeyboardShortcutsSubscriptionManager, OnInit, OnDestroy {
    private widgetTranslations: any;

    private _isCreating: boolean = false;
    private _ruleInEdit?: AutomaticArchivingRule;
    private _isReordered: boolean = false;

    get isCreating(): boolean {
        return this._isCreating;
    }

    set isCreating(value: boolean) {
        this._isCreating = value;
        this.recalculateDirty();
    }

    get ruleInEdit(): AutomaticArchivingRule | undefined {
        return this._ruleInEdit;
    }

    set ruleInEdit(value: AutomaticArchivingRule | undefined) {
        this._ruleInEdit = value;
        this.recalculateDirty();
    }

    get isReordered(): boolean {
        return this._isReordered;
    }

    set isReordered(value: boolean) {
        this._isReordered = value;
        this.recalculateDirty();
    }

    automaticArchivingRules: AutomaticArchivingListItem[] = [];
    rawAutomaticArchivingRules: AutomaticArchivingRule[] = [];

    subscription: Subscription = new Subscription();

    automaticArchivingMarkdownEnUs = automaticArchivingMarkdownEnUs;
    automaticArchivingMarkdownDaDK = automaticArchivingMarkdownDaDK;

    isProcessing: boolean = false;

    dirty$: BehaviorSubject<boolean> = new BehaviorSubject<boolean>(false);

    automaticArchivingTabName = automaticArchivingTabName;

    hasUserFormConfigurationWritePermission: boolean = false;

    private _tabId?: string;

    columns: RulesListColumn[] = AUTOMATIC_ARCHIVING_COLUMNS;

    private keyEventsSubscription: Subscription = new Subscription();

    @ViewChild('automaticArchivingCreate') automaticArchivingCreate?: AutomaticArchivingCreateComponent;
    @ViewChild('automaticArchivingEdit') automaticArchivingEdit?: AutomaticArchivingEditComponent;

    keyActions: KeyActions = {
        [KeyboardShortcuts.ShiftPlusN]: () => {
            this.startCreating();
        },
        [KeyboardShortcuts.Escape]: () => {
            this.tabsService.close();
        },
        [KeyboardShortcuts.Enter]: () => {
            if (this.ruleInEdit) {
                this.automaticArchivingEdit?.saveEditedClick();
                return;
            }

            if (this.isCreating) {
                this.automaticArchivingCreate?.createClick();
                return;
            }

            if (this.isReordered) {
                this.saveReorder();
            }
        }
    };
    attemptedAction: (() => void) | null = null;
    private ruleToDelete?: AutomaticArchivingListItem;

    private originalRuleOrder: string[] = [];
    alreadyUsedDisplayNames: string[] = [];

    constructor(
        private readonly client: InstallationFormsClient,
        private readonly translateService: TranslateService,
        private readonly messageServiceHelper: MessageServiceHelper,
        private readonly tabsService: TabsService,
        private readonly activatedRoute: ActivatedRoute,
        private readonly authService: AuthorizationService,
        private readonly keyboardShortcutsService: KeyboardShortcutsService,
        private readonly automaticArchivingMapper: AutomaticArchivingMapper,
        private readonly valueListService: ValueListsService
    ) {
        this.addTab();
    }

    ngOnInit(): void {
        this.widgetTranslations = this.translateService.instant(automaticArchivingTranslationPath);

        this.authService
            .hasPermissions([Permissions.formConfiguration.write])
            .subscribe((x) => (this.hasUserFormConfigurationWritePermission = x));

        this.registerRefreshButtonListener();
        this.loadArchivingWithTemplates();
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    subscribeKeyShortcuts(): void {
        this.keyEventsSubscription = this.keyboardShortcutsService.registerKeyboardShortcuts(this.keyActions).subscribe();
    }

    unsubscribeKeyShortcuts(): void {
        this.keyEventsSubscription.unsubscribe();
    }

    private loadArchivingWithTemplates() {
        this.isProcessing = true;
        this.subscription.add(
            this.client
                .getAutomaticArchivingRules(uuidv4())
                .pipe(
                    catchError((_) => {
                        this.messageServiceHelper.showError({
                            key: automaticArchivingTabName,
                            detail: this.widgetTranslations['getArchivingRulesError']
                        });
                        return EMPTY;
                    })
                )
                .pipe(finalize(() => (this.isProcessing = false)))
                .subscribe({
                    next: (response) => {
                        const sortedRules = [...response.result];
                        sortedRules.sort((a, b) => a.order - b.order);
                        this.rawAutomaticArchivingRules = sortedRules;
                        this.automaticArchivingRules = sortedRules.map((rule) =>
                            this.automaticArchivingMapper.mapToAutomaticArchivingListItem(rule)
                        );
                        this.originalRuleOrder = this.automaticArchivingRules.map((x) => x.id);
                        this.alreadyUsedDisplayNames = this.automaticArchivingRules.map((x) => x.displayName);
                    }
                })
        );
    }

    protected editRule(event: MouseEvent, rule: AutomaticArchivingListItem) {
        event.stopPropagation();
        this.ruleInEdit = this.rawAutomaticArchivingRules.find((r) => r.id === rule.id);
        this.isCreating = false;
    }

    protected startCreating() {
        this.isCreating = true;
        this.ruleInEdit = undefined;
    }

    protected cancelCreating() {
        this.isCreating = false;
    }

    protected cancelEditing() {
        this.ruleInEdit = undefined;
    }

    protected onRuleCreated(rule: AutomaticArchivingRule) {
        this.isCreating = false;
        this.rawAutomaticArchivingRules.push(rule);
        this.automaticArchivingRules.push(this.automaticArchivingMapper.mapToAutomaticArchivingListItem(rule));
        this.originalRuleOrder.push(rule.id);
        this.alreadyUsedDisplayNames.push(rule.displayName);
    }

    protected onRuleEdited(updatedRule: AutomaticArchivingRule) {
        const editedIndex = this.automaticArchivingRules.findIndex((r) => r.id === updatedRule.id);
        if (editedIndex === -1) {
            return;
        }

        this.alreadyUsedDisplayNames.splice(
            this.alreadyUsedDisplayNames.indexOf(this.ruleInEdit!.displayName),
            1,
            updatedRule.displayName
        );
        this.rawAutomaticArchivingRules[editedIndex] = updatedRule;
        this.automaticArchivingRules[editedIndex] = this.automaticArchivingMapper.mapToAutomaticArchivingListItem(updatedRule);
        this.ruleInEdit = undefined;
    }

    private addTab() {
        this._tabId = this.tabsService.addOrUpdateTab(this.activatedRoute, {
            icon$: of('fas fa-th-list'),
            areaLabel$: of(this.translateService.instant(`${automaticArchivingTranslationPath}.tabAreaLabel`)),
            label$: of(this.translateService.instant(`${automaticArchivingTranslationPath}.tabLabel`)),
            tooltip$: of(this.translateService.instant(`${automaticArchivingTranslationPath}.tabLabel`)),
            dirty$: this.dirty$,
            canReload$: of(true)
        });
    }

    registerRefreshButtonListener(): void {
        this.subscription.add(
            this.tabsService.reloadButtonClicked$
                .pipe(
                    filter((tabId) => tabId === this._tabId),
                    switchMap((tabId) => this.tabsService.reload(tabId)),
                    filter((canBeReloaded) => canBeReloaded),
                    tap(() => {
                        this.loadArchivingWithTemplates();
                        this.valueListService.fetchValueLists();
                    })
                )
                .subscribe(() => {
                    this.tabsService.tabReloaded(this._tabId as string);
                    this.undoChanges();
                })
        );
    }

    onRowReorder(_: TableRowReorderEvent) {
        this.setIsReordered();
    }

    undoChanges() {
        this.cancelCreating();
        this.cancelEditing();
        this.cancelReorder();
    }

    setIsReordered() {
        this.isReordered = !this.automaticArchivingRules.every((rule, index) => rule.id === this.originalRuleOrder[index]);
    }

    cancelReorder() {
        this.automaticArchivingRules = this.originalRuleOrder.map(
            (id) => this.automaticArchivingRules.find((rule) => rule.id === id)!
        );
        this.setIsReordered();
    }

    protected deleteRule(event: MouseEvent, rule: AutomaticArchivingListItem) {
        event.stopPropagation();
        this.ruleToDelete = rule;
        this.attemptedAction = () => this.performDelete();
    }

    private performDelete() {
        if (!this.ruleToDelete) {
            return;
        }

        this.isProcessing = true;
        this.subscription.add(
            this.client
                .deleteAutomaticArchivingRuleById(this.ruleToDelete.id, uuidv4())
                .pipe(
                    finalize(() => {
                        this.isProcessing = false;
                        this.ruleToDelete = undefined;
                    })
                )
                .subscribe({
                    complete: () => {
                        this.messageServiceHelper.showSuccess({
                            key: automaticArchivingTabName
                        });
                        this.automaticArchivingRules = this.automaticArchivingRules.filter((r) => r.id !== this.ruleToDelete?.id);
                        this.originalRuleOrder = this.originalRuleOrder.filter((id) => id !== this.ruleToDelete?.id);
                        this.rawAutomaticArchivingRules = this.rawAutomaticArchivingRules.filter(
                            (r) => r.id !== this.ruleToDelete?.id
                        );
                        if (this.ruleInEdit?.id === this.ruleToDelete?.id) {
                            this.ruleInEdit = undefined;
                        }

                        this.alreadyUsedDisplayNames = this.alreadyUsedDisplayNames.filter(
                            (name) => name !== this.ruleToDelete?.displayName
                        );
                    },
                    error: (_error) => {
                        this.messageServiceHelper.showError({
                            key: automaticArchivingTabName,
                            detail: this.widgetTranslations['AutomaticArchivingRuleDeleteError']
                        });
                    }
                })
        );
    }

    onConfirmationDialogYesButtonClick() {
        if (this.attemptedAction) {
            this.attemptedAction();
        }
        this.attemptedAction = null;
    }

    onConfirmationDialogNoButtonClick() {
        this.attemptedAction = null;
        this.ruleToDelete = undefined;
    }

    saveReorder() {
        this.isProcessing = true;
        this.subscription.add(
            this.client
                .reorderAutomaticArchivingRules(
                    uuidv4(),
                    new ReorderModel({
                        reorderedIds: this.automaticArchivingRules.map((x) => x.id)
                    })
                )
                .pipe(finalize(() => (this.isProcessing = false)))
                .subscribe({
                    complete: () => {
                        this.messageServiceHelper.showSuccess({
                            key: automaticArchivingTabName
                        });
                        this.originalRuleOrder = this.automaticArchivingRules.map((x) => x.id);
                        this.isReordered = false;
                    },
                    error: (_error) => {
                        this.messageServiceHelper.showError({
                            key: automaticArchivingTabName,
                            detail: this.widgetTranslations['AutomaticArchivingRuleReorderError']
                        });
                    }
                })
        );
    }

    recalculateDirty() {
        this.dirty$.next(this.isCreating || this.isReordered || !!this.ruleInEdit);
    }

    getValueListType(colName: string, workOrderType: WorkOrderType): ValueListType | undefined {
        if (colName !== 'verifiedWorkOrderDescription') {
            return undefined;
        }

        return mapWorkOrderTypeToWorkDescriptionValueList(workOrderType);
    }
}
