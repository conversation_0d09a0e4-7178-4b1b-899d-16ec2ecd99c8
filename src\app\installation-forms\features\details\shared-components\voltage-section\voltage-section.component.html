<div #overlayContainer></div>
<ng-container [formGroup]="form">
    <ng-container>
        <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label for="voltageLevelId" [labelRequired]="form.controls['voltageLevelId']">{{
                    'voltageSection.voltageLevelId' | translate
                }}</label>
                <app-icon-master-data-path [path]="formDataService.masterDataToCompareResult?.voltageLevelId?.path">
                </app-icon-master-data-path>
            </div>
            <div class="zebra-edit" [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70" fxFlex.lt-md="50">
                <cmbs-value-list-dropdown
                    id="voltageLevelId"
                    formControlName="voltageLevelId"
                    [valueListType]="meterFrameElectricityTarifConnectionPointFormsValuesValueListType"
                    [messageKey]="this.formDataService.formId"
                    [valueList]="
                        valueListsService.getValueList(meterFrameElectricityTarifConnectionPointFormsValuesValueListType) | async
                    ">
                </cmbs-value-list-dropdown>
                <small [controlValidationErrors]="form.controls['voltageLevelId']" class="p-error"></small>
            </div>
            <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields">
                <input
                    id="voltageLevelIdMD"
                    type="text"
                    pInputText
                    [value]="
                        formDataService.masterDataToCompareResult?.voltageLevelId?.value ?? ''
                            | valueListSingleItem: meterFrameElectricityTarifConnectionPointValueListType
                            | async
                    "
                    [disabled]="true" />
            </div>
        </div>
        <ng-container *ngIf="isTransformerInfoIncluded()">
            <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
                <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                    <label for="currentTransformer" [labelRequired]="form.controls['currentTransformer']">{{
                        'voltageSection.currentTransformer' | translate
                    }}</label>
                </div>
                <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                    <input id="currentTransformer" type="text" pInputText formControlName="currentTransformer" />
                    <small [controlValidationErrors]="form.controls['currentTransformer']" class="p-error"></small>
                </div>
            </div>
            <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
                <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                    <label for="voltageTransformer" [labelRequired]="form.controls['voltageTransformer']">{{
                        'voltageSection.voltageTransformer' | translate
                    }}</label>
                </div>
                <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                    <input id="voltageTransformer" type="text" pInputText formControlName="voltageTransformer" />
                    <small [controlValidationErrors]="form.controls['voltageTransformer']" class="p-error"></small>
                </div>
            </div>
        </ng-container>
    </ng-container>
</ng-container>
