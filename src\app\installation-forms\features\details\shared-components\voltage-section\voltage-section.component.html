<ng-container [formGroup]="form">
    <ng-container>
        <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
            <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                <label for="voltageLevelId" [labelRequired]="form.controls['voltageLevelId']">{{
                    'voltageSection.voltageLevelId' | translate
                }}</label>
                <app-icon-master-data-path [path]="formDataService.masterDataToCompareResult?.voltageLevelId?.path">
                </app-icon-master-data-path>
            </div>
            <div class="zebra-edit" [fxFlex]="formDataService.showMasterDataCompareFields ? 35 : 70" fxFlex.lt-md="50">
                <cmbs-value-list-dropdown
                    id="voltageLevelId"
                    formControlName="voltageLevelId"
                    [valueListType]="meterFrameElectricityTarifConnectionPointFormsValuesValueListType"
                    [messageKey]="this.formDataService.formId"
                    [valueList]="
                        valueListsService.getValueList(meterFrameElectricityTarifConnectionPointFormsValuesValueListType) | async
                    ">
                </cmbs-value-list-dropdown>
                <small [controlValidationErrors]="form.controls['voltageLevelId']" class="p-error"></small>
            </div>
            <div class="zebra-edit" fxFlex="35" fxFlex.lt-md="50" *ngIf="formDataService.showMasterDataCompareFields">
                <input
                    id="voltageLevelIdMD"
                    type="text"
                    pInputText
                    [value]="
                        formDataService.masterDataToCompareResult?.voltageLevelId?.value ?? ''
                            | valueListSingleItem: meterFrameElectricityTarifConnectionPointValueListType
                            | async
                    "
                    [disabled]="true" />
            </div>
        </div>
        <ng-container *ngIf="isCurrentTransformerIncluded()">
            <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
                <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                    <label for="currentTransformerId" [labelRequired]="form.controls['currentTransformerId']">{{
                        'voltageSection.currentTransformer' | translate
                    }}</label>
                </div>
                <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                    <cmbs-value-list-dropdown
                        id="currentTransformerId"
                        formControlName="currentTransformerId"
                        [valueListType]="currentTransformerValueListType"
                        [messageKey]="this.formDataService.formId"
                        [valueList]="valueListsService.getValueList(currentTransformerValueListType) | async">
                    </cmbs-value-list-dropdown>
                    <small [controlValidationErrors]="form.controls['currentTransformerId']" class="p-error"></small>
                </div>
            </div>
        </ng-container>
        <ng-container *ngIf="isVoltageTransformerIncluded()">
            <div class="zebra-item" fxLayout="row" fxLayout.lt-sm="column" fxLayoutGap="15px">
                <div class="zebra-caption" fxFlex="30" fxFlex.lt-md="50" fxLayoutAlign="start center">
                    <label for="voltageTransformerId" [labelRequired]="form.controls['voltageTransformerId']">{{
                        'voltageSection.voltageTransformer' | translate
                    }}</label>
                </div>
                <div class="zebra-edit" fxFlex="70" fxFlex.lt-md="50">
                    <cmbs-value-list-dropdown
                        id="voltageTransformerId"
                        formControlName="voltageTransformerId"
                        [valueListType]="voltageTransformerValueListType"
                        [messageKey]="this.formDataService.formId"
                        [valueList]="valueListsService.getValueList(voltageTransformerValueListType) | async">
                    </cmbs-value-list-dropdown>
                    <small [controlValidationErrors]="form.controls['voltageTransformerId']" class="p-error"></small>
                </div>
            </div>
        </ng-container>
    </ng-container>
</ng-container>
<div #overlayContainer></div>
