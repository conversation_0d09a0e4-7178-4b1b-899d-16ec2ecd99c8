// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
    production: false,
    ui: '/mf/installation-forms',
    backendBasePath: '/api/installation-forms-bff',
    attachmentsApiBasePath: '/api/attachments',
    userPreferencesBffUrl: '/api/tenants-configuration',
    automaticRefresh: {
        initialRefreshIntervalInSeconds: 5,
        intervalExtensions: [
            {
                afterSeconds: 5 * 60,
                newIntervalInSeconds: 10
            },
            {
                afterSeconds: 10 * 60,
                newIntervalInSeconds: 60
            }
        ]
    }
};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
