import { AbstractControl, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';

export default function conditionallyRequiredAddressLookupValidator(shouldBeRequired: () => boolean): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
        if (shouldBeRequired()) {
            let value = control.value;
            if (!!value === false || value.text.trim() === '') {
                return { carIdIsEmpty: true };
            }
            return null;
        }
        return null;
    };
}
