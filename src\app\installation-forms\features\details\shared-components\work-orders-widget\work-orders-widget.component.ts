import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { AuthorizationService } from '@kmd-elements-ui/authorization';
import { Subscription } from 'rxjs';
import { Permissions } from 'src/app/core/constants/permissions';
import { WidgetComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget.component';
import { FormDataService } from '../../../../../core/services/form-data/form-data.service';

@Component({
    selector: 'app-work-orders-widget',
    templateUrl: './work-orders-widget.component.html',
    styleUrls: ['./work-orders-widget.component.scss'],
    standalone: false
})
export class WorkOrdersWidgetComponent extends WidgetComponent implements OnDestroy, OnInit {
    public showCreateWorkOrder: boolean = false;
    public subscription: Subscription = new Subscription();
    public hasWorkOrderReadPermission = false;
    public hasWorkOrderWritePermission = false;

    constructor(
        private readonly authService: AuthorizationService,
        readonly formDataService: FormDataService
    ) {
        super(formDataService);
    }

    ngOnInit(): void {
        this.authService.hasPermissions([Permissions.workOrders.read]).subscribe((x) => (this.hasWorkOrderReadPermission = x));
        this.authService.hasPermissions([Permissions.workOrders.write]).subscribe((x) => (this.hasWorkOrderWritePermission = x));
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    closeCreateWorkOrder() {
        this.showCreateWorkOrder = false;
    }

    protected showCreateWorkOrderButton = () =>
        this.formDataService.canCurrentUserExecuteActions && this.hasWorkOrderWritePermission;

    protected createWorkOrderButtonClick() {
        this.showCreateWorkOrder = true;
    }
}
