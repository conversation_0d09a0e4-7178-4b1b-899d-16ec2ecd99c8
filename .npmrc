registry=https://registry.npmjs.org/

@kmd-elements:registry=https://kmddk.pkgs.visualstudio.com/_packaging/Combas/npm/registry/
@kmd-elements-ui:registry=https://pkgs.dev.azure.com/kmddk/COMBAS/_packaging/Kmd-Elements-UI/npm/registry/

always-auth=true
//kmddk.pkgs.visualstudio.com/_packaging/Combas/npm/registry/:username=VssSessionToken
; This is an unencrypted authentication token. Treat it like a password. DO NOT share this value with anyone, including Microsoft support.
//kmddk.pkgs.visualstudio.com/_packaging/Combas/npm/registry/:_password=b3RkNzJjbHM0cmRmYTZkem02bmh6NzUzaHl1cmhmenpwNG9kazM2dWhrZnF2Yng1dXhhcQ==
; The npm client won't use username and _password without an email set, but the service doesn't use the email value. The following is an arbitrarily made-up address.
//kmddk.pkgs.visualstudio.com/_packaging/Combas/npm/registry/:email=<EMAIL>
//pkgs.dev.azure.com/kmddk/COMBAS/_packaging/Kmd-Elements-UI/npm/registry/:username=VssSessionToken
; This is an unencrypted authentication token. Treat it like a password. DO NOT share this value with anyone, including Microsoft support.
//pkgs.dev.azure.com/kmddk/COMBAS/_packaging/Kmd-Elements-UI/npm/registry/:_password=QjFmNDBYSGdWTXZrU1R2VDBxenl5ejhJWVUzbU5oTFRzalJjUHFoSkpPeHVJNk15MnZ6M0pRUUo5OUJEQUNBQUFBQWY2dTZEQUFBU0FaRE80RHBF
; The npm client won't use username and _password without an email set, but the service doesn't use the email value. The following is an arbitrarily made-up address.
//pkgs.dev.azure.com/kmddk/COMBAS/_packaging/Kmd-Elements-UI/npm/registry/:email=<EMAIL>
