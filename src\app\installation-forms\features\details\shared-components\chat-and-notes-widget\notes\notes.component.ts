import { Component, EventE<PERSON>ter, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { <PERSON><PERSON><PERSON>y, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { finalize, Subscription } from 'rxjs';
import { CreateOrUpdateNote, InstallationFormsClient, Note, PendingUpdateAreaType } from 'src/app/api/installation-forms-client';
import { WidgetNames } from 'src/app/core/constants/widget-names';
import { AutomaticFormRefreshService } from 'src/app/core/services/automatic-form-refresh/automatic-form-refresh.service';
import { FormDataService } from 'src/app/core/services/form-data/form-data.service';
import { MessageServiceHelper } from 'src/app/core/services/message/message.service';
import { WidgetWithFormComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget-with-form.component';
import { v4 as uuidv4 } from 'uuid';

@Component({
    selector: 'app-notes',
    templateUrl: './notes.component.html',
    styleUrl: './notes.component.scss',
    standalone: false
})
export class NotesComponent extends WidgetWithFormComponent implements OnInit, OnDestroy {
    @Output() notesCountEmitter = new EventEmitter<number>();
    private _notes: Note[] = [];
    widgetName: string = WidgetNames.notesWidget;

    set notes(value: Note[]) {
        this._notes = value;
        this.notesCountEmitter.emit(value.length);
        this.supplyFormData();
    }

    get notes(): Note[] {
        return this._notes;
    }

    private commonTranslations: any;
    private widgetTranslations: any;

    subscription: Subscription = new Subscription();

    form: FormGroup;
    notesForm: FormGroup;

    currentlyEditedNoteId?: string;

    attemptedAction: (() => void) | null = null;
    private noteIdToDelete?: string;

    constructor(
        private readonly client: InstallationFormsClient,
        private readonly messageServiceHelper: MessageServiceHelper,
        private readonly translateService: TranslateService,
        private readonly fb: FormBuilder,
        readonly formDataService: FormDataService,
        private readonly automaticFormRefreshService: AutomaticFormRefreshService
    ) {
        super(formDataService);
        this.form = this.fb.group({
            content: ['']
        });

        this.notesForm = this.fb.group({
            notes: this.fb.array([])
        });
    }

    ngOnInit(): void {
        this.commonTranslations = this.translateService.instant('common');
        this.widgetTranslations = this.translateService.instant('chatAndNotesWidget.notes');

        this.automaticFormRefreshService.subscribeForRefresh(PendingUpdateAreaType.Notes, this.getNotes);
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
        this.automaticFormRefreshService.unsubscribeForRefresh(PendingUpdateAreaType.Notes);
    }

    get notesFormControls(): FormGroup[] {
        if (!this.notesForm) {
            return [];
        }
        return (this.notesForm.get('notes') as FormArray).controls as FormGroup[];
    }

    private getNotes = () => {
        this.subscription.add(
            this.client.getNotes(this.formDataService.formId!, uuidv4()).subscribe({
                error: (_) => {
                    this.messageServiceHelper.showError({
                        detail: this.widgetTranslations['getNotesError'],
                        key: this.formDataService.formId!
                    });
                },
                next: (x) => {
                    if (!this.areNotesEqual(this.notes, x.result)) {
                        this.notes = x.result;
                    }
                }
            })
        );
    };

    protected createNewNote() {
        if (!this.form.valid) {
            this.messageServiceHelper.showWarning({
                detail: this.commonTranslations['allRequiredFieldsShouldBeCompleted'],
                key: this.formDataService.formId
            });
            return;
        }
        this.processingStarted();
        this.subscription.add(
            this.client
                .createNewNote(
                    this.formDataService.formId!,
                    uuidv4(),
                    new CreateOrUpdateNote({
                        content: this.form.get('content')?.value?.trim() || ''
                    })
                )
                .pipe(finalize(() => this.processingFinished()))
                .subscribe({
                    error: (_) => {
                        this.messageServiceHelper.showError({
                            detail: this.widgetTranslations['createNewNoteError'],
                            key: this.formDataService.formId!
                        });
                    },
                    next: (x) => {
                        this.notes = [x.result, ...this.notes];
                        this.form.reset();
                    }
                })
        );
    }

    protected modifyNote(noteForm: FormGroup) {
        if (!noteForm.valid) {
            this.messageServiceHelper.showWarning({
                detail: this.commonTranslations['allRequiredFieldsShouldBeCompleted'],
                key: this.formDataService.formId
            });
            return;
        }
        this.processingStarted();
        const note = noteForm.value as Note;
        this.subscription.add(
            this.client
                .modifyNote(
                    this.formDataService.formId!,
                    note.id,
                    uuidv4(),
                    new CreateOrUpdateNote({
                        content: note.content?.trim() || ''
                    })
                )
                .pipe(
                    finalize(() => {
                        this.processingFinished();
                        this.currentlyEditedNoteId = undefined;
                    })
                )
                .subscribe({
                    error: (_) => {
                        this.messageServiceHelper.showError({
                            detail: this.widgetTranslations['modifyNoteError'],
                            key: this.formDataService.formId!
                        });
                    },
                    next: (x) => {
                        // replacing existing note with updated, map will trigger new array created so form will be updated as well in set
                        const updatedNotes = this.notes
                            .map((item) => {
                                if (item.id === x.result.id) {
                                    return x.result;
                                }
                                return item;
                            })
                            .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
                        this.notes = updatedNotes;
                    }
                })
        );
    }

    protected deleteNote(event: MouseEvent, note: FormGroup) {
        event.stopPropagation();
        this.noteIdToDelete = note.get('id')?.value;
        this.attemptedAction = () => this.performDelete();
    }

    private performDelete() {
        if (!this.noteIdToDelete) {
            return;
        }

        this.processingStarted();
        this.subscription.add(
            this.client
                .deleteNote(this.formDataService.formId!, this.noteIdToDelete, uuidv4())
                .pipe(
                    finalize(() => {
                        this.processingFinished();
                        this.noteIdToDelete = undefined;
                    })
                )
                .subscribe({
                    error: (_) => {
                        this.messageServiceHelper.showError({
                            detail: this.widgetTranslations['deleteNoteError'],
                            key: this.formDataService.formId!
                        });
                    },
                    complete: () => {
                        this.notes = this.notes.filter((x) => x.id !== this.noteIdToDelete);
                    }
                })
        );
    }

    protected onConfirmationDialogYesButtonClick() {
        if (this.attemptedAction) {
            this.attemptedAction();
        }
        this.attemptedAction = null;
    }

    protected onConfirmationDialogNoButtonClick() {
        this.attemptedAction = null;
        this.noteIdToDelete = undefined;
    }

    protected startModifyNote(note: FormGroup) {
        if (!this.currentlyEditedNoteId) {
            this.currentlyEditedNoteId = note.get('id')?.value;
        }
    }

    protected cancelModifyNote(note: FormGroup) {
        const originalNote = this.notes.find((item) => item.id === note.get('id')?.value);
        note.patchValue({
            content: originalNote?.content
        });
        this.currentlyEditedNoteId = undefined;
    }

    private supplyFormData() {
        const noteFormArray = this.notesForm.get('notes') as FormArray;
        noteFormArray.clear();
        this.currentlyEditedNoteId = undefined;
        this.notes.forEach((note) => {
            noteFormArray.push(this.createNoteFormGroup(note));
        });
    }

    private createNoteFormGroup(note: Note): FormGroup {
        return this.fb.group({
            id: [note.id],
            content: [note.content],
            author: [note.author],
            timestamp: [note.timestamp]
        });
    }

    // we want to prevent rerendering of the widget when the notes are equal, because of animations
    private areNotesEqual(currentNotes: Note[], newNotes: Note[]): boolean {
        if (currentNotes.length !== newNotes.length) {
            return false;
        }

        for (let i = 0; i < currentNotes.length; i++) {
            if (
                currentNotes[i].id !== newNotes[i].id ||
                currentNotes[i].timestamp.getTime() !== newNotes[i].timestamp.getTime()
            ) {
                return false;
            }
        }
        return true;
    }

    protected formNoteIsEmpty = () => this.form.get('content')?.value?.length === 0;
}
