import { FormType, WorkOrderType } from 'src/app/api/installation-forms-client';

// Multiple to multiple mapping: [FormType_1, FormType_2]: [WorkOrderType_1, WorkOrderType_2]
export const WORK_ORDER_TYPES_PER_FORM_TYPE = {
    [FormType.Termination]: [WorkOrderType.RemoveMeter]
};

export function findMatchingWorkOrderTypes(selectedFormTypes?: FormType[]): WorkOrderType[] | null {
    if (!selectedFormTypes) {
        return null;
    }
    const entries = Object.entries(WORK_ORDER_TYPES_PER_FORM_TYPE).map(([key, value]) => ({
        formTypes: key.split(',').map((type) => type as FormType),
        workOrderTypes: value
    }));

    const matchingEntry = entries.find((entry) => selectedFormTypes.every((selected) => entry.formTypes.includes(selected)));

    return matchingEntry?.workOrderTypes || null;
}
