<p-panel [header]="'flagsWidget.title' | translate" [toggleable]="true" *ngIf="flagControls.length">
    <div fxLayout="column" class="mt-10 zebra-container">
        <div
            class="zebra-item"
            fxLayout="row"
            fxLayout.lt-sm="column"
            fxLayoutGap="15px"
            *ngFor="let flagControl of flagControls">
            <container-element [ngSwitch]="flagControl.value.type" fxFlexFill>
                <app-ready-for-meter-flag-template
                    *ngSwitchCase="flagTypes.ReadyForMeter"
                    [flagControl]="flagControl"
                    [readyForMeterDate]="readyForMeterDate"></app-ready-for-meter-flag-template>
                <app-send-to-internal-resource-template
                    *ngSwitchCase="flagTypes.SentToInternalResource"
                    [flagControl]="flagControl"></app-send-to-internal-resource-template>
                <app-default-flag-template
                    *ngSwitchCase="flagTypes.ConnectionFeePaid"
                    [flagControl]="flagControl"
                    [canBeSetAutomatically]="true"></app-default-flag-template>
                <app-default-flag-template
                    *ngSwitchDefault
                    [flagControl]="flagControl"
                    [canBeSetAutomatically]="false"></app-default-flag-template>
            </container-element>
        </div>
    </div>
    <ng-template [ngIf]="shouldShowChangesPanel() && changesAmount > 0">
        <div class="mt-10" fxLayout="row" fxLayoutAlign="space-between center">
            <div class="app-updated-text">
                <ng-container *ngIf="changesAmount === 1; else elseTemplate">
                    {{ 'common.singleChangedFieldInformation' | translate: { amount: changesAmount } }}
                </ng-container>
                <ng-template #elseTemplate>
                    {{ 'common.pluralChangedFieldsInformation' | translate: { amount: changesAmount } }}
                </ng-template>
            </div>
            <div>
                <button
                    id="cancelButton"
                    [disabled]="isProcessing"
                    type="button"
                    pButton
                    pRipple
                    class="mr-2 p-button-secondary"
                    (click)="onCancelChangesClicked()">
                    {{ 'common.cancelChanges' | translate | titlecase }}
                </button>
                <button id="saveButton" type="button" pButton pRipple (click)="onSaveClicked()">
                    <span *ngIf="isProcessing" class="fa fa-spin fa-spinner"></span>
                    {{ 'common.saveChanges' | translate | titlecase }}
                </button>
            </div>
        </div>
    </ng-template>
</p-panel>
