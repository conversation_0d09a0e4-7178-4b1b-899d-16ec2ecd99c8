import { FormType } from 'src/app/api/installation-forms-client';

export const SEAL_RESPONSIBLE_FORM_TYPES = [
    FormType.ChangeMeter,
    FormType.SealBreach,
    FormType.Extension,
    FormType.MoveMeter,
    FormType.ChangeBranchLine
] as const;

export const METER_RESPONSIBLE_FORM_TYPES = [
    FormType.NewInstallation,
    FormType.Extension,
    FormType.ChangeMeter,
    FormType.EnergyProduction
] as const;

export const METER_NEEDS_CHANGE_FORM_TYPES = [FormType.Extension, FormType.EnergyProduction] as const;

export const INVOICE_PAID_FORM_TYPES = [FormType.NewInstallation, FormType.Extension] as const;

export const METER_READY_FORM_TYPES = [FormType.NewInstallation, FormType.Extension] as const;

export const SUPPLIER_SELECTED_FORM_TYPES = [FormType.NewInstallation] as const;

export const STARTS_AS_CONSTRUCTION_FORM_TYPES = [FormType.NewInstallation] as const;
