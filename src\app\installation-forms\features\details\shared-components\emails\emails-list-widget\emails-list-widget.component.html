<ng-container *ngIf="emails && emails.length; else emptyMessage">
    <p-table
        [columns]="columns"
        [value]="emails"
        [rowHover]="true"
        responsiveLayout="scroll"
        [paginator]="emails.length > rowsPerPage"
        [rows]="rowsPerPage"
        [loading]="loading"
        [customSort]="false"
        [sortField]="'createdDate'"
        [sortOrder]="-1">
        <ng-template pTemplate="header" let-columns>
            <tr>
                <th *ngFor="let col of columns" [pSortableColumn]="col" [id]="col">
                    <div fxLayout="row">
                        {{ 'emailsWidget.columnNames.' + col | translate }}
                    </div>
                </th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-rowData let-columns="columns">
            <tr class="zebra-item" [ngClass]="{ 'cursor-auto': !isRowClickable(rowData) }" (click)="handleRowClick(rowData)">
                <td>{{ rowData['templateName'] }}</td>
                <td>{{ rowData['type'] }}</td>
                <td>{{ rowData['recipientAddress'] }}</td>
                <td>
                    <app-email-status [emailStatus]="rowData['status']" [emailType]="rowData['type']"></app-email-status>
                </td>
                <td>{{ rowData['createdDate'] | formatDateTime }}</td>
            </tr>
        </ng-template>
        <ng-template pTemplate="paginatorright"> </ng-template>
    </p-table>
</ng-container>

<ng-template #emptyMessage>
    <p>{{ 'emailsWidget.noEmailsFound' | translate }}</p>
</ng-template>
