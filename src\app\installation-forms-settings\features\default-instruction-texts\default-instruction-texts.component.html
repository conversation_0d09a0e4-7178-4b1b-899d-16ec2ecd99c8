<p-card>
    <app-markdown-viewer
        [markdownEnUs]="defaultInstructionTextsMarkdownEnUs"
        [markdownDaDK]="defaultInstructionTextsMarkdownDaDK">
    </app-markdown-viewer>
    <button
        pButton
        label="{{ 'formsSettings.defaultInstructionTexts.addText' | translate }}"
        (click)="startCreating()"
        class="p-button-outlined mt-20 mb-20"
        *ngIf="!isCreating && hasUserFormConfigurationWritePermission"></button>
    <app-default-instruction-texts-create
        #defaultInstructionTextsCreate
        *ngIf="isCreating"
        [isProcessing]="isProcessing"
        (cancelCreating)="cancelCreating()"
        (ruleCreated)="onRuleCreated($event)"></app-default-instruction-texts-create>
    <app-default-instruction-texts-edit
        #defaultInstructionTextsEdit
        *ngIf="defaultInstructionTextInEdit"
        [isProcessing]="isProcessing"
        [ruleInEdit]="defaultInstructionTextInEdit"
        (cancelEditing)="cancelEditing()"
        (ruleEdited)="onRuleEdited($event)"></app-default-instruction-texts-edit>
    <p-table
        [value]="defaultInstructionTexts"
        [columns]="columns"
        [reorderableColumns]="true"
        *ngIf="defaultInstructionTexts.length > 0"
        (onRowReorder)="onRowReorder($event)"
        [scrollable]="true">
        <ng-template pTemplate="header">
            <tr>
                <th id="reorder-column" class="w-3rem" *ngIf="hasUserFormConfigurationWritePermission"></th>
                <th
                    *ngFor="let col of columns"
                    [id]="'defaultInstructionTexts-' + col.field"
                    [pTooltip]="col.header | translate"
                    tooltipPosition="top">
                    {{ col.header | translate }}
                </th>
                <th id="instruction-texts-action-edit" pFrozenColumn></th>
                <th id="instruction-texts-action-delete" pFrozenColumn></th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-item let-index="rowIndex">
            <tr class="zebra-item table-row" [pReorderableRow]="index">
                <td *ngIf="hasUserFormConfigurationWritePermission">
                    <span class="pi pi-bars" pReorderableRowHandle></span>
                </td>
                <td *ngFor="let col of columns">
                    <ng-container>
                        {{ item[col.field] }}
                    </ng-container>
                </td>
                <td class="actions-column" pFrozenColumn alignFrozen="right">
                    <button
                        *ngIf="
                            hasUserFormConfigurationWritePermission &&
                            (!defaultInstructionTextInEdit || defaultInstructionTextInEdit.id !== item.id)
                        "
                        [disabled]="isProcessing"
                        pButton
                        icon="fa-solid fa-pen"
                        class="p-button-outlined border-none py-1"
                        (click)="editRule($event, item)"></button>
                </td>
                <td class="actions-column" pFrozenColumn alignFrozen="right">
                    <button
                        *ngIf="
                            hasUserFormConfigurationWritePermission &&
                            (!defaultInstructionTextInEdit || defaultInstructionTextInEdit.id !== item.id)
                        "
                        [disabled]="isProcessing"
                        pButton
                        icon="fa-solid fa-trash-can"
                        class="p-button-outlined border-none py-1"
                        (click)="deleteRule($event, item)"></button>
                </td>
            </tr>
        </ng-template>
    </p-table>
    <div class="flex justify-content-end mt-2" *ngIf="isReordered">
        <button
            id="cancelButton"
            type="button"
            pButton
            pRipple
            (click)="cancelReorder()"
            class="mr-2 p-button-secondary"
            [disabled]="isProcessing">
            {{ 'common.cancelChanges' | translate | titlecase }}
        </button>
        <button id="saveButton" type="button" pButton pRipple (click)="saveReorder()" [disabled]="isProcessing">
            {{ 'common.saveChanges' | translate }}
        </button>
    </div>
</p-card>

<app-confirmation-dialog
    [headerKey]="'formsSettings.defaultInstructionTexts.deleteConfirmationTitle'"
    [messageKey]="'formsSettings.defaultInstructionTexts.deleteConfirmationMessage'"
    [visible]="!!attemptedAction"
    (confirmEvent)="onConfirmationDialogYesButtonClick()"
    (cancelEvent)="onConfirmationDialogNoButtonClick()">
</app-confirmation-dialog>

<p-toast [key]="defaultInstructionTextsTabName"></p-toast>

<p-toast #genericMessagesToast></p-toast>
