import { TestBed } from '@angular/core/testing';
import { AuthorizationService } from '@kmd-elements-ui/authorization';
import { of } from 'rxjs';
import { CaseWorkerService } from './case-worker.service';

describe('CaseWorkerService', () => {
    let service: CaseWorkerService;
    let mockAuthService: AuthorizationService;

    beforeEach(() => {
        // Create mock for AuthorizationService without default implementation
        mockAuthService = {
            getUser: jest.fn()
        } as Partial<AuthorizationService> as AuthorizationService;

        TestBed.configureTestingModule({
            providers: [CaseWorkerService, { provide: AuthorizationService, useValue: mockAuthService }]
        });

        service = TestBed.inject(CaseWorkerService);
        mockAuthService = TestBed.inject(AuthorizationService);
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    describe('isCurrentUser$', () => {
        it('should return false when email is undefined or null', (done) => {
            service.isCurrentUser$(undefined).subscribe((result) => {
                expect(result).toBe(false);
                expect(mockAuthService.getUser).not.toHaveBeenCalled();
                done();
            });
        });

        it('should return true when emails match (case insensitive)', (done) => {
            (mockAuthService.getUser as jest.Mock).mockImplementation(() => of({ email: '<EMAIL>' }));

            service.isCurrentUser$('<EMAIL>').subscribe((result) => {
                expect(result).toBe(true);
                expect(mockAuthService.getUser).toHaveBeenCalledTimes(1);
                done();
            });
        });

        it('should return false when emails do not match', (done) => {
            (mockAuthService.getUser as jest.Mock).mockImplementation(() => of({ email: '<EMAIL>' }));

            service.isCurrentUser$('<EMAIL>').subscribe((result) => {
                expect(result).toBe(false);
                expect(mockAuthService.getUser).toHaveBeenCalledTimes(1);
                done();
            });
        });
    });
});
