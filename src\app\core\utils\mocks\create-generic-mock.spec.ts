import { createGenericMock } from './create-generic-mock';

describe('createGenericMock', () => {
    // Test interface with various property types
    interface TestInterface {
        id: string;
        name: string;
        count: number;
        isActive: boolean;
        tags: string[];
        items: Array<any>;
        metadata: {
            createdAt: Date;
            updatedAt: Date;
        };
        settings: {
            notifications: boolean;
            theme: string;
        };
        problems: { code: string; severity: string }[];
        getDetails(): string;
    }

    // Test with class that has array properties
    class TestClass {
        id: string = '';
        applications: any[] = [];
        readings: any[] = [];
        settings = { enabled: true };
    }

    it('should return properties defined in overrides', () => {
        const mock = createGenericMock<TestInterface>({
            id: '123',
            name: 'Test Object',
            count: 5
        });

        expect(mock.id).toBe('123');
        expect(mock.name).toBe('Test Object');
        expect(mock.count).toBe(5);
    });

    it('should create empty objects for undefined object properties (first level only)', () => {
        const mock = createGenericMock<TestInterface>({ id: '123' });

        expect(mock.metadata).toEqual({});
        expect(mock.settings).toEqual({});

        // With current implementation, nested properties beyond first level are undefined
        expect(mock.metadata.createdAt).toBeUndefined();
        expect(mock.settings.theme).toBeUndefined();
    });

    it('should create empty objects for array properties', () => {
        const mock = createGenericMock<TestInterface>({ id: '123' });

        expect(mock.tags).toEqual({});
        expect(mock.items).toEqual({});
        expect(mock.problems).toEqual({});
    });

    it('should handle class instances with array properties', () => {
        const mock = createGenericMock<TestClass>({ id: 'class-1' });

        expect(mock.id).toBe('class-1');
        expect(mock.applications).toEqual({});
        expect(mock.readings).toEqual({});
        expect(mock.settings).toEqual({});
    });

    it('should not interfere with standard object methods', () => {
        const mock = createGenericMock<TestInterface>({});

        expect(mock.toString).toBeDefined();
        expect(typeof mock.toString).toBe('function');
        expect(mock.hasOwnProperty).toBeDefined();
        expect(typeof mock.hasOwnProperty).toBe('function');
    });

    it('should handle properties starting with underscore', () => {
        const mockWithPrivate = createGenericMock<any>({
            _id: 'private-id',
            publicId: 'public-id'
        });

        expect(mockWithPrivate._id).toBe('private-id');
        expect(mockWithPrivate.publicId).toBe('public-id');
        expect(mockWithPrivate._undefinedProp).toBeUndefined();
    });

    it('should handle array functions properly when arrays are provided', () => {
        const mock = createGenericMock<TestInterface>({
            tags: ['tag1', 'tag2']
        });

        expect(mock.tags.length).toBe(2);
        expect(mock.tags[0]).toBe('tag1');
        expect(mock.tags.map).toBeDefined();

        const mappedTags = mock.tags.map((tag) => tag.toUpperCase());
        expect(mappedTags).toEqual(['TAG1', 'TAG2']);
    });

    it('should handle promises and thenable properties correctly', () => {
        const mock = createGenericMock<any>({});

        expect(mock.then).toBeUndefined();
        expect(mock.catch).toBeUndefined();
        expect(mock.finally).toBeUndefined();
    });

    it('should handle complex nested structures (first level only)', () => {
        interface ComplexInterface {
            user: {
                profile: {
                    addresses: {
                        street: string;
                        city: string;
                    }[];
                    contacts: string[];
                };
            };
        }

        const mock = createGenericMock<ComplexInterface>({});

        expect(mock.user).toEqual({});
        // With current implementation, nested properties beyond first level are undefined
        expect(mock.user.profile).toBeUndefined();
    });

    it('should handle functions', () => {
        const mock = createGenericMock<TestInterface>({
            getDetails: () => 'Mock details'
        });

        expect(typeof mock.getDetails).toBe('function');
        expect(mock.getDetails()).toBe('Mock details');
    });
});
