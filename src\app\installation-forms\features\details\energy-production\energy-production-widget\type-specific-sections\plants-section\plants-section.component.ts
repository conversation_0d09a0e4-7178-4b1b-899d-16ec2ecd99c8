import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { SelectItem } from 'primeng/api';
import { Subscription } from 'rxjs';
import { FormCategory, Plant } from 'src/app/api/installation-forms-client';
import { PhaseCountOptions } from 'src/app/core/constants/constants';
import { FormDataService } from 'src/app/core/services/form-data/form-data.service';
import conditionallyRequiredValidator from 'src/app/core/utils/validators/conditionally-required/conditionally-required.validator';
import dateMustNotBePastValidator from 'src/app/core/utils/validators/date-time/date-must-not-be-past.validator';
import { WidgetWithFormComponent } from 'src/app/installation-forms/features/details/base-components/widgets/widget-with-form.component';

@Component({
    selector: 'app-plants-section',
    templateUrl: './plants-section.component.html',
    styleUrl: './plants-section.component.scss',
    standalone: false
})
export class PlantsSectionComponent extends WidgetWithFormComponent implements OnInit, OnDestroy {
    private _type!: FormCategory;
    private _totalProductionCapacityInKw?: number;

    public phaseCountOptions: SelectItem[] = PhaseCountOptions;
    public calculatedTotalPowerSum: number = 0;
    public currentCalculatedTotalProductionCapacity: number = 0;

    allowMultiplePlants: boolean = false;

    subscription: Subscription = new Subscription();

    @Input() form!: FormGroup;

    @Input() windPlantForm!: FormGroup;
    @Input() powerPlantForm!: FormGroup;

    @Input() get totalProductionCapacityInKw(): number | undefined {
        return this._totalProductionCapacityInKw;
    }
    set totalProductionCapacityInKw(totalProductionCapacityInKw: number | undefined) {
        this._totalProductionCapacityInKw = totalProductionCapacityInKw;
        this.calculateTotalPower();
    }

    @Input() get type(): FormCategory {
        return this._type;
    }
    set type(type: FormCategory) {
        this._type = type;
        this.setAllowMultiplePlants(type);
    }

    constructor(
        readonly formDataService: FormDataService,
        private fb: FormBuilder
    ) {
        super(formDataService);
    }

    ngOnInit(): void {
        this.calculateTotalPower();
    }

    ngOnDestroy(): void {
        this.subscription.unsubscribe();
    }

    onValueChange() {
        this.form.patchValue({
            hasPlantsChanged: true
        });
    }

    onTotalPowerChange() {
        this.onValueChange();
        this.calculateTotalPower();
    }

    getPlants(): FormArray {
        return this.form.get('plants') as FormArray;
    }

    createPlant() {
        const plants = this.getPlants();
        plants.push(this.initEmptyPlant());
        this.form.patchValue({
            hasPlantsChanged: true
        });
    }

    //Only solar type can have >1 plants, so all required.
    initEmptyPlant() {
        return this.fb.group({
            phaseCount: [undefined, [Validators.required, Validators.min(1), Validators.max(3)]],
            totalPowerInKw: [undefined, [Validators.required]],
            commissioningDateSod: [
                null,
                [
                    (control: AbstractControl) => conditionallyRequiredValidator(control, this.commissioningDateSodVisible),
                    (control: AbstractControl) => dateMustNotBePastValidator(control)
                ]
            ],
            brandName: [undefined, [Validators.required]],
            modelName: [undefined, [Validators.required]],
            typeName: [undefined, [Validators.required]],
            solarSystemAreaInM2: [undefined, [Validators.required]]
        });
    }

    removePlant(index: number): void {
        const plants = this.getPlants();
        plants.removeAt(index);
        this.form.patchValue({
            hasPlantsChanged: true
        });
        this.calculateTotalPower();
    }

    private setAllowMultiplePlants(type: FormCategory) {
        if (type === FormCategory.Solar) {
            this.allowMultiplePlants = true;
        }
    }

    calculateTotalPower() {
        this.calculatedTotalPowerSum = Number(
            (
                (this.form.get('plants')?.value as Plant[])
                    .filter((plant) => plant?.totalPowerInKw !== undefined)
                    .map((plant) => plant.totalPowerInKw)
                    .reduce((acc, curr) => acc! + curr!, 0) ?? 0
            ).toFixed(1)
        );

        this.currentCalculatedTotalProductionCapacity = Number(
            ((this._totalProductionCapacityInKw ?? 0) + this.calculatedTotalPowerSum).toFixed(1)
        );
    }

    commissioningDateSodVisible = () => {
        return this.type !== FormCategory.Wind;
    };

    brandNameVisible() {
        return this.type !== FormCategory.Consumption;
    }

    modelNameVisible() {
        return this.type !== FormCategory.Consumption;
    }

    typeNameVisible() {
        return this.type === FormCategory.Solar;
    }

    solarSystemAreaInM2Visible() {
        return this.type === FormCategory.Solar;
    }
}
