<#
.SYNOPSIS
This script is wrapping NSwag cli to generate files based on structured configuration.

By default it intends to use specifications(& templates) from other repository (can be modified within .nswag file), thus requires env variable set for api specification repository.

.PARAMETER RootPath
Solution uses relative paths to this root path, if left empty, script will use git repository root (thus it requires git to run)

.PARAMETER ConfigurationFileRootRelativePath
Relative to RootPath path to config file.

      Configuration file structure (psd1 file):
      @{
        UniqueConfiguarionName = @{
          NswagSettingsPath = "nswag settings path"
      }

      Sample:
      @{

        DocumentStorageControllerV1 = @{
          NswagSettingsPath = "scripts/open-api-tools/cscontrollerV1.nswag"
        }
        DocumentStorageControllerV2 = @{
          NswagSettingsPath = "scripts/open-api-tools/cscontrollerV2.nswag"
        }
      }

.PARAMETER GenerateFilter
If you have multiple nodes in generate-configuration.psd1 you can specify filtering here to only generate wanted one.

.EXAMPLE
    ./generate.ps1
    ./generate.ps1 -RootPath 'C:\workspace\Tools.DotnetBootstrap\templates\ElementsAksWebApp'

#>
#
param(
  [string]$RootPath = "",
  [string]$ConfigurationFileRootRelativePath = "scripts/open-api-tools/generate-configuration.psd1",
  [string]$GenerateFilter = ""
)

<#
    .SYNOPSIS
      Prints a horizontal separator
  #>
function WriteHostSeparator {
  "-" * 80 | Write-Host
}

<#
    .SYNOPSIS
      Returns root path based on git repository settings.
  #>
function Get-GitRootPath {
  Push-Location $PSScriptRoot
  $RootPath = $(git rev-parse --show-toplevel)
  Pop-Location
  return $RootPath
}

function CreateBundledFile([string]$BundledFileRelativePath, [string]$CoreFileRelativePath) {
    $outputPath = Join-Path  -Path $apiSpecificationRepoRootPath -ChildPath $BundledFileRelativePath
    $sourcePath = Join-Path  -Path $apiSpecificationRepoRootPath -ChildPath $CoreFileRelativePath
  
	redocly bundle $sourcePath -o $outputPath
}

function DeleteBundledFile([string]$BundledFileRelativePath) {    
    $pathToFileToRemove = Join-Path  -Path $apiSpecificationRepoRootPath -ChildPath $BundledFileRelativePath
	
    rm $pathToFileToRemove
}

function Generate {
    WriteHostSeparator
    ">> Generating: $c`n" | Write-Host
    $cfg = $config[$c]
    $settPath = Join-Path $RootPath $cfg.NswagSettingsPath
      
    nswag run $settPath "/variables:ApiSpecificationRepoRootPath=$apiSpecificationRepoRootPath"
}

if ($RootPath -eq "") {
  $RootPath = Get-GitRootPath
}

$configurationFullPath = Join-Path $RootPath $ConfigurationFileRootRelativePath

WriteHostSeparator
"Generating" | Write-Host
" - Root:   $RootPath" | Write-Host
" - Config: $configurationFullPath" | Write-Host

$config = Import-PowerShellDataFile -Path $configurationFullPath

Push-Location $RootPath

if ($config.Keys.Count -eq 0) {
  Write-Warning "No files will be generated, empty config file"
}

$apiSpecificationRepoRootPath = [Environment]::GetEnvironmentVariable('ApiSpecificationRepoRootPath');
if ($apiSpecificationRepoRootPath -eq $null) {
  Write-Error "ApiSpecificationRepoRootPath environmental variable is required for this script to work"
  Write-Error "For powershell type: [System.Environment]::SetEnvironmentVariable('ApiSpecificationRepoRootPath','REPLACE',[System.EnvironmentVariableTarget]::User)"
  Write-Error "Remember to restart your terminal after that"
  return;
}

foreach ($c in $config.Keys) {
    Write-Host "Config $c"
   
    $item = $config.Item($c)
   
    if ($item.FilesToBundle.Keys.Count -gt 0) {
        CreateBundledFile $item.FilesToBundle["BundledFileRelativePath"] $item.FilesToBundle["CoreFileRelativePath"]
        Generate
        DeleteBundledFile $item.FilesToBundle["BundledFileRelativePath"]
    }
    else {
        Generate
    }
}

Pop-Location
